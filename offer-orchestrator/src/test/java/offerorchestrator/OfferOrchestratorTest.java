package offerorchestrator;

import static org.mockito.Mockito.*;

import com.flipkart.fintech.offer.orchestrator.dao.FileBasedOfferEvaluatorDao;
import com.flipkart.fintech.offer.orchestrator.dao.LenderOfferDao;
import com.flipkart.fintech.offer.orchestrator.dao.OfferEvaluatorDao;
import com.flipkart.fintech.offer.orchestrator.filters.FilterService;
import com.flipkart.fintech.offer.orchestrator.filters.LenderBasedFilter;
import com.flipkart.fintech.offer.orchestrator.offerDistributor.OfferDistributor;
import com.flipkart.fintech.offer.orchestrator.offerDistributor.OfferDistributorImpl;
import com.flipkart.fintech.offer.orchestrator.offerfetcher.OfferFetcher;
import com.flipkart.fintech.offer.orchestrator.cohortfinder.CohortFinder;
import com.flipkart.fintech.offer.orchestrator.cohortfinder.CohortFinderImpl;
import org.junit.Before;
import org.junit.Test;

public class OfferOrchestratorTest {

    private CohortFinder cohortFinder;
    private CohortFinderImpl cohortFinderImpl;
    private OfferFetcher offerFetcher;
    private LenderBasedFilter lenderBasedFilter;
    private OfferDistributorImpl offerDistributor;
    private LenderOfferEntityDaoTest lenderOfferEntityDaoTest;
    private FileBasedOfferEvaluatorDao fileBasedOfferEvaluatorDao;
    private OfferEvaluatorDao offerEvaluatorDao;
    private FilterService filterService;
    private OfferDistributor temp;

    LenderOfferDao lenderOfferDaoMock = mock(LenderOfferDao.class);


    private LenderOfferDao lenderOfferDao;
    private OfferEvaluatorDao offerEvaluatorDaoMock;

    @Before
    public void setUp() {
        cohortFinder = mock(CohortFinder.class);
        cohortFinderImpl = mock(CohortFinderImpl.class);
        offerFetcher = mock(OfferFetcher.class);
        lenderBasedFilter = mock(LenderBasedFilter.class);
        lenderOfferDao = mock(LenderOfferDao.class);
        lenderOfferEntityDaoTest = mock(LenderOfferEntityDaoTest.class);
        offerEvaluatorDao = mock(FileBasedOfferEvaluatorDao.class);
        offerDistributor = mock(OfferDistributorImpl.class);
        filterService = mock(FilterService.class);
//        temp = new OfferDistributorImpl(new FileBasedOfferEvaluatorDao());
//        offerOrchestratorImpl = new OfferOrchestratorImpl(offerFetcher, lenderBasedFilter, cohortFinder, temp, filterService);
//        fileBasedOfferEvaluatorDao = new FileBasedOfferEvaluatorDao();
        offerEvaluatorDao = mock(FileBasedOfferEvaluatorDao.class);

    }

    @Test
    public void testGetOfferForLead() {
        // lead with all valid details, pincode, pan, mobile, age, salaried/ non-salaried:
        // pre-approved offers:
        // filters for all passing
        // filters for some passing
        // filters for Pre-approved -> none passing
        // filters for OM -> 1/2 passing
        // filters for OM -> None passing
        // no pre-approved offers, only OM:
        // filters for all passing
        // filters for some passing
        // filters for OM -> None passin


//        LeadDetails lead = new LeadDetails(LeadDetails.LeadState.BASIC_DETAILS, "as", new ProfileServiceResponse(123L, "No errors", new HashMap<>()));
//        List<LenderOfferEntity> offers = getOffersForUserProfile_MultipleOffers1("profile1");
//        UserProfileCohortEntity userProfileCohort = new UserProfileCohortEntity("USER_PROFILE_COHORT_1", true, "test_1");
////        when(cohortFinder.getCohortForUserProfile(lead.getUserProfile())).thenReturn(userProfileCohort);
//        when(cohortFinderImpl.getCohortForUserProfile(lead.getUserProfile())).thenCallRealMethod();
//        when(lenderBasedFilter.filter(any(LenderOfferEntity.class), any(UserProfileCohortEntity.class), any(ProfileServiceResponse.class))).thenReturn(true);
//        when(lenderOfferDao.getOffersForUserProfile(lead.getUserProfile())).thenReturn(getOffersForUserProfile_MultipleOffers1("profile1"));
//        when(offerFetcher.getOffersForLead(any(LeadDetails.class))).thenReturn(getOffersForUserProfile_MultipleOffers1("profile1"));
////        when(cohortFinder.getCohortForUserProfile(lead.getUserProfile())).thenReturn(userProfileCohort);
//        when(offerEvaluatorDao.getEvaluators(any(), any())).thenCallRealMethod();
//        when(offerDistributor.getActiveEvaluator(
//                offers, userProfileCohort
//        )).thenCallRealMethod();
//        Optional<LenderOfferEntity> optionalOffer = offerOrchestratorImpl.getOfferForLead(lead);
//        LenderOfferEntity result = optionalOffer.orElse(null);
//        assertNull(result);
    }
}
