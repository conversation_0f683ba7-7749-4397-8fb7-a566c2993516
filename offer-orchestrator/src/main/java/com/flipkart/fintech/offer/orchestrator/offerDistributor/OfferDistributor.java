package com.flipkart.fintech.offer.orchestrator.offerDistributor;


import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.OfferEvaluatorEntity;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;

import java.util.List;

public interface OfferDistributor {
    public OfferEvaluatorEntity getActiveEvaluator(List<LenderOfferEntity> offers, UserProfileCohortEntity cohort);
}
