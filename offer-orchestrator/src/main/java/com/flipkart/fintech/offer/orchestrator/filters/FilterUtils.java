package com.flipkart.fintech.offer.orchestrator.filters;

import com.flipkart.fintech.offer.orchestrator.dao.LenderPincodeServiceabilityDao;
import com.flipkart.fintech.offer.orchestrator.model.LenderPincodeServiceabilityEntity;
import com.flipkart.fintech.pinaka.api.enums.Lender;

import java.util.Arrays;
import java.util.List;

class FilterUtils {


    private final LenderPincodeServiceabilityDao lenderPincodeServiceabilityDao;
    private final List<Lender> dedupeFilterEnabledLenders = Arrays.asList(Lender.FIBE, Lender.MONEYVIEW); // temporary config, will change with Lender object
    private final List<Lender> pincodeFilterEnabledLenders = Arrays.asList(Lender.AXIS, Lender.MONEYVIEW, Lender.FIBE); // temporary config, will change with Lender object



    FilterUtils(LenderPincodeServiceabilityDao lenderPincodeServiceabilityDao) {
        this.lenderPincodeServiceabilityDao = lenderPincodeServiceabilityDao;
    }

    Boolean isDuplicateApplicationForLender(Lender lender) { // update with user profile PASS_pandora Request
        return false;
//        if (dedupeFilterEnabledLenders.contains(lender)) return false;
//      check dedupe with pandora
//        return true;
    }


    Boolean checkLenderServiceabilityForPincode(Lender lender, String pincode) {
        if (pincodeFilterEnabledLenders.contains(lender)) return true;
        LenderPincodeServiceabilityEntity lenderPincodeServiceabilityEntity = lenderPincodeServiceabilityDao.getActive(lender, pincode);
        return lenderPincodeServiceabilityEntity != null;
    }

}
