package com.flipkart.fintech.offer.orchestrator.dao;


import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import io.dropwizard.hibernate.AbstractDAO;
import org.hibernate.Criteria;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;

import java.util.List;

public class LenderOfferDaoImpl extends AbstractDAO<LenderOfferEntity> implements LenderOfferDao {
    public LenderOfferDaoImpl(SessionFactory sessionFactory) {
        super(sessionFactory);
    }

    private static final String USER_PROFILE_ID = "user_profile_id";
    private static final String USER_ID = "user_id";
    private static final String OFFER_STATE = "offer_state";
    @Override
    public List<LenderOfferEntity> getOffersForUserProfile(ProfileDetailedResponse userProfile) {
        Criteria criteria = criteria().add(Restrictions.eq(USER_ID, userProfile.getMerchantUserId()))
                .add(Restrictions.eq(USER_PROFILE_ID, userProfile.getProfileId()))
                .add(Restrictions.eq(OFFER_STATE, "active"));
        return list(criteria);
    }
}
