package com.flipkart.fintech.offer.orchestrator.offerfetcher;

import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;

import java.util.List;

public interface OfferFetcher {
    public List<LenderOfferEntity> getOffersForLead(LeadDetails lead, MerchantUser merchantUser);

}