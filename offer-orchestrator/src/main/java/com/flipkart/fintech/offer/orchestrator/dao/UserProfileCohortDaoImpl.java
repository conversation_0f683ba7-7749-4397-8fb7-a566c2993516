package com.flipkart.fintech.offer.orchestrator.dao;

import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.google.inject.Inject;
import io.dropwizard.hibernate.AbstractDAO;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;

import java.util.List;

public class UserProfileCohortDaoImpl extends AbstractDAO<UserProfileCohortEntity> implements UserProfileCohortDao {


    private final SessionFactory sessionFactory;

    @Inject
    public UserProfileCohortDaoImpl(SessionFactory sessionFactory) {
        super(sessionFactory);
        this.sessionFactory = sessionFactory;
    }

    @Override
    public UserProfileCohortEntity getCohortById(String cohortId) {
        Criteria criteria = criteria().add(Restrictions.eq("id", cohortId));
        return uniqueResult(criteria);
    }

    @Override
    public UserProfileCohortEntity getCohortByName(String name) {
        try (Session session = sessionFactory.openSession()){
            Criteria criteria = criteria().add(Restrictions.eq("name", name));
            return uniqueResult(criteria);
        } catch (Exception e) {
            throw new RuntimeException("Error while getting UserProfileCohortEntity by name ", e);
        }

    }

    @Override
    public List<UserProfileCohortEntity> getAllCohorts() {
        Criteria criteria = criteria();
        return list(criteria);
    }
}
