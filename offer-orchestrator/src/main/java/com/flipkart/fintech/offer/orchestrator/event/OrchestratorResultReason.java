package com.flipkart.fintech.offer.orchestrator.event;

public class OrchestratorResultReason {
    public static final String SUCCESS = "SUCCESS";
    public static final String ORCHESTRATOR_WHITELISTED = "ORCHESTRATOR_WHITELISTED";
    public static final String ORCHESTRATOR_INELIGIBLE_USER = "ORCHESTRATOR_INELIGIBLE_USER";
    public static final String ORCHESTRATOR_NO_ELIGIBLE_OFFER = "ORCHESTRATOR_NO_ELIGIBLE_OFFER";
    public static final String ORCHESTRATOR_NO_EVALUATORS_FOUND = "ORCHESTRATOR_NO_EVALUATORS_FOUND";
}
