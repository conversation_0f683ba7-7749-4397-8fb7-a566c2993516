[{"lender": "AXIS", "filters": [{"name": "PinCodeServiceabilityFilter", "enabled": true, "descriptor": "{\"pincodeTypes\":  [\"shippingPincode\", \"userEnteredPincode\"]}"}, {"name": "<PERSON><PERSON><PERSON>er", "enabled": true, "descriptor": "{\"minAge\": 21,\"maxAge\": 58}"}]}, {"lender": "FIBE", "filters": [{"name": "PinCodeServiceabilityFilter", "enabled": true, "descriptor": "{\"pincodeTypes\":  [\"userEnteredPincode\"]}"}, {"name": "<PERSON><PERSON><PERSON>er", "enabled": true, "descriptor": "{\"minAge\": 19,\"maxAge\": 55}"}, {"name": "EmploymentFilter", "enabled": true, "descriptor": ""}, {"name": "DuplicateApplicationFilter", "enabled": true, "descriptor": ""}]}, {"lender": "IDFC", "filters": [{"name": "<PERSON><PERSON><PERSON>er", "enabled": true, "descriptor": "{\"minAge\": 21,\"maxAge\": 60}"}, {"name": "PinCodeServiceabilityFilter", "enabled": true, "descriptor": "{\"pincodeTypes\":  [\"shippingPincode\"]}"}]}, {"lender": "MONEYVIEW", "filters": [{"name": "PinCodeServiceabilityFilter", "enabled": true, "descriptor": "{\"pincodeTypes\":  [\"userEnteredPincode\"]}"}, {"name": "<PERSON><PERSON><PERSON>er", "enabled": true, "descriptor": "{\"minAge\": 21,\"maxAge\": 57}"}, {"name": "DuplicateApplicationFilter", "enabled": true, "descriptor": ""}]}, {"lender": "MONEYVIEWOPENMKT", "filters": [{"name": "PinCodeServiceabilityFilter", "enabled": true, "descriptor": "{\"pincodeTypes\":  [\"userEnteredPincode\"]}"}, {"name": "<PERSON><PERSON><PERSON>er", "enabled": true, "descriptor": "{\"minAge\": 21,\"maxAge\": 57}"}, {"name": "DuplicateApplicationFilter", "enabled": true, "descriptor": ""}]}, {"lender": "DMI", "filters": [{"name": "PinCodeServiceabilityFilter", "enabled": true, "descriptor": "{\"pincodeTypes\":  [\"userEnteredPincode\"]}"}, {"name": "<PERSON><PERSON><PERSON>er", "enabled": true, "descriptor": "{\"minAge\": 23,\"maxAge\": 55}"}]}, {"lender": "OMNI", "filters": [{"name": "PinCodeServiceabilityFilter", "enabled": true, "descriptor": "{\"pincodeTypes\":  [\"userEnteredPincode\"]}"}, {"name": "<PERSON><PERSON><PERSON>er", "enabled": true, "descriptor": "{\"minAge\": 21,\"maxAge\": 58}"}]}]