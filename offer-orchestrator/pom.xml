<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.flipkart.fintech</groupId>
        <artifactId>pinaka</artifactId>
        <version>3.4.26-SM</version>
    </parent>

    <artifactId>offer-orchestrator</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.14.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.26</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>javax.persistence-api</artifactId>
            <version>2.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pandora-client</artifactId>
            <version>${pandora.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.jersey.media</groupId>
                    <artifactId>jersey-media-multipart</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.dropwizard</groupId>
                    <artifactId>dropwizard-jersey</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.hk2.external</groupId>
                    <artifactId>aopalliance-repackaged</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.core</groupId>
                    <artifactId>jersey-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.minidev</groupId>
                    <artifactId>json-smart</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.rholder</groupId>
                    <artifactId>guava-retrying</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>jakarta.xml.bind</groupId>
                    <artifactId>jakarta.xml.bind-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech.pinaka.common</groupId>
            <artifactId>pinaka-common</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.fintech</groupId>
                    <artifactId>pinaka-service</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>5.4.12.Final</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.69</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pinaka-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>profile-service</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>ams-connector</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>