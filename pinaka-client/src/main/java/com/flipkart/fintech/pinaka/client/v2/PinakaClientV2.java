package com.flipkart.fintech.pinaka.client.v2;

import com.flipkart.fintech.pinaka.api.request.*;
import com.flipkart.fintech.pinaka.api.request.v2.ApplicationConfirmationRequest;
import com.flipkart.fintech.pinaka.api.request.v2.PostProcessingTaskRequest;
import com.flipkart.fintech.pinaka.api.response.*;
import com.flipkart.fintech.pinaka.client.PinakaClientException;


/**
 * Created by su<PERSON><PERSON><PERSON>.r on 04/09/17.
 */
public interface PinakaClientV2 {

    ApplicationCreateResponse createApplication(
            ApplicationCreateRequest createRequest, String merchant) throws PinakaClientException;

    AutopayMandateCreateResponse createAutopayMandate(
            AutopayMandateCreateRequest createRequest, String merchant) throws PinakaClientException;

    ApplicationUpdateResponse updateApplication(
            ApplicationUpdateRequest updateRequest, String merchant) throws PinakaClientException;

    PostProcessingTaskResponse applicationPostProcessingTask(
            PostProcessingTaskRequest request, String merchant) throws PinakaClientException;

    FetchApplicationResponse fetchActiveApplication(String merchant, String userId, String productType)
            throws PinakaClientException;

    FetchApplicationResponse fetchActiveApplication(String merchant, String userId, String productType, String workflow)
            throws PinakaClientException;

    void applicationConfirmation(String merchantId, ApplicationConfirmationRequest request) throws PinakaClientException;

    KycSchedulerResponse schedule(KycSchedulerRequest kycSchedulerRequest, String merchantId) throws PinakaClientException;

    KycSchedulerResponse updateKycSch(KycSchedulerRequest kycSchedulerRequest, String merchantId) throws PinakaClientException;

    void updateVkyc(VkycApplicationRequest vkycApplicationRequest, String merchantId) throws PinakaClientException;

}
