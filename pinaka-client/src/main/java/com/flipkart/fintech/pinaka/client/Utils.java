package com.flipkart.fintech.pinaka.client;

import com.flipkart.fintech.logger.utils.LoggerUtils;
import javax.ws.rs.client.Invocation;
import org.apache.commons.lang3.StringUtils;

import static com.flipkart.fintech.pinaka.client.Constants.X_TRACE_ID;

public class Utils {
    public static void addTraceId(Invocation.Builder invocationBuilder) {
        String traceId = LoggerUtils.getTraceId();
        if(StringUtils.isNotBlank(traceId)) {
            invocationBuilder.header(X_TRACE_ID, traceId);
        }
    }
}
