package com.flipkart.fintech.pinaka.client.v4;

import com.flipkart.fintech.pinaka.api.response.FetchApplicationResponse;
import com.flipkart.fintech.pinaka.client.PinakaClientException;

import java.util.List;

/**
 * <AUTHOR>
 * @since 04/12/20.
 */

public interface PinakaClientV4 {

    List<FetchApplicationResponse> fetchApplicationForKycPrePopulation(String merchantAccountId, String financialProvider) throws PinakaClientException;
}
