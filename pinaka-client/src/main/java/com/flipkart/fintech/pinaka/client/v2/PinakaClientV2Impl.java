package com.flipkart.fintech.pinaka.client.v2;

import com.flipkart.fintech.pinaka.api.request.*;
import com.flipkart.fintech.pinaka.api.request.v2.ApplicationConfirmationRequest;
import com.flipkart.fintech.pinaka.api.request.v2.PostProcessingTaskRequest;
import com.flipkart.fintech.pinaka.api.response.*;
import com.flipkart.fintech.pinaka.client.Constants;
import com.flipkart.fintech.pinaka.client.PinakaClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.google.inject.Inject;
import lombok.CustomLog;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;

import static com.flipkart.fintech.pinaka.client.Constants.DEFAULT;
import static com.flipkart.fintech.pinaka.client.Constants.WORKFLOW;
import static com.flipkart.fintech.pinaka.client.Utils.addTraceId;

/**
 * Created by sujeetkumar.r on 04/09/17.
 */
@CustomLog
public class PinakaClientV2Impl implements PinakaClientV2 {

    private final PinakaClientConfig clientConfig;
    private WebTarget webTarget;
    private static final String ERROR_PINAKA = "error response from pinaka service";

    @Inject
    public PinakaClientV2Impl(PinakaClientConfig clientConfig, Client client){
        this.clientConfig = clientConfig;
        this.webTarget = client.target(clientConfig.getUrl()).path(Constants.APP_CONTEXT_PATH);
    }

    @Override
    public ApplicationCreateResponse createApplication(
            ApplicationCreateRequest applicationCreateRequest, String merchant) throws PinakaClientException{
        Response response = null;
        ApplicationCreateResponse applicationCreateResponse = null;
        try{
            Invocation.Builder invocationBuilder = webTarget.path(Constants.CREATE_APPLICATION_V2_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(applicationCreateRequest));
            if(response.getStatus() == 201){
                applicationCreateResponse = response.readEntity(ApplicationCreateResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
        return applicationCreateResponse;
    }

    @Override
    public AutopayMandateCreateResponse createAutopayMandate(
            AutopayMandateCreateRequest autopayMandateCreateRequest, String merchant) throws PinakaClientException{
        Response response = null;
        AutopayMandateCreateResponse autopayMandateCreateResponse = null;
        try{
            Invocation.Builder invocationBuilder = webTarget.path(Constants.CREATE_AUTOPAY_MANDATE_V1_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(autopayMandateCreateRequest));
            if(response.getStatus() == 201){
                autopayMandateCreateResponse = response.readEntity(AutopayMandateCreateResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
        return autopayMandateCreateResponse;
    }

    @Override
    public ApplicationUpdateResponse updateApplication(ApplicationUpdateRequest updateRequest,
                                                       String merchant) throws PinakaClientException {
        Response response = null;
        ApplicationUpdateResponse updateResponse;
        String path = String.format(Constants.UPDATE_APPLICATION_V2_PATH, updateRequest.getTrackingId());

        try{
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(updateRequest));
            if(response.getStatus() == 200){
                updateResponse = response.readEntity(ApplicationUpdateResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
        return updateResponse;
    }

    @Override
    public PostProcessingTaskResponse applicationPostProcessingTask(PostProcessingTaskRequest request, String merchant) throws PinakaClientException {
        Response response = null;
        PostProcessingTaskResponse taskResponse;
        String path = String.format(Constants.POST_PROCESSING_TASK_V2_PATH, request.getTrackingId());

        try{
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(request));
            if(response.getStatus() == 200){
                taskResponse = response.readEntity(PostProcessingTaskResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
        return taskResponse;
    }

    @Override
    public FetchApplicationResponse fetchActiveApplication(String merchant, String userId, String productType)
            throws PinakaClientException {
        return fetchActiveApplication(merchant, userId, productType, DEFAULT);
    }

    @Override
    public FetchApplicationResponse fetchActiveApplication(String merchant, String userId, String productType, String workflow)
            throws PinakaClientException {
        Response response = null;
        FetchApplicationResponse activeApplication = null;
        String path = String.format(Constants.FETCH_ACTIVE_APPLICATION_V2_PATH, userId, productType.toUpperCase(), workflow.toUpperCase());
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).queryParam(WORKFLOW, workflow).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                activeApplication = response.readEntity(FetchApplicationResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return activeApplication;
    }

    @Override
    public void applicationConfirmation(String merchantId, ApplicationConfirmationRequest request) throws PinakaClientException {
        Response response = null;

        try{
            Invocation.Builder invocationBuilder = webTarget.path(Constants.APPLICATION_CONFIRMATION_PATH).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(request));
            if(response.getStatus() != 200){
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
    }


    private String generateAuthToken(String merchant) {
        StringBuilder keyReverse = new StringBuilder(merchant);
        String input = merchant+":"+keyReverse.reverse();
        return Base64.getEncoder().encodeToString(input.getBytes());
    }

    public KycSchedulerResponse schedule(KycSchedulerRequest kycSchedulerRequest, String merchantId) throws PinakaClientException {
        Response response = null;
        KycSchedulerResponse kycSchedulerResponse = null;
        String path = Constants.KYC_SCHEDULER_PATH;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(kycSchedulerRequest));
            if (response.getStatus() == 200) {
                kycSchedulerResponse = response.readEntity(KycSchedulerResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return kycSchedulerResponse;
    }

    public KycSchedulerResponse updateKycSch(KycSchedulerRequest kycSchedulerRequest, String merchantId) throws PinakaClientException {
        Response response = null;
        KycSchedulerResponse kycSchedulerResponse = null;
        String path = Constants.KYC_SCHEDULER_UPDATE_PATH;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(kycSchedulerRequest));
            if (response.getStatus() == 200) {
                kycSchedulerResponse = response.readEntity(KycSchedulerResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return kycSchedulerResponse;
    }

    public void updateVkyc(VkycApplicationRequest vkycApplicationRequest, String merchantId) throws PinakaClientException {
        Response response = null;
        String path = Constants.VKYC_UPDATE_PATH;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(vkycApplicationRequest));
            if (response.getStatus() != 200) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }


}
