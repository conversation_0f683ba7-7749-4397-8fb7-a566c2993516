package com.flipkart.fintech.pinaka.client.v7;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.kafka.models.amsEvents.ApplicationEvent;
import com.flipkart.fintech.pinaka.api.request.creditRequest.CreditRequest;
import com.flipkart.fintech.pinaka.api.request.v6.*;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequestV2;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.api.response.v6.*;
import com.flipkart.fintech.pinaka.client.Constants;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.client.PinakaClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.profile.request.BureauDataRequestSm;
import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;
import com.flipkart.rome.datatypes.request.fintech.calm.IfscSearchRequest;
import com.flipkart.rome.datatypes.response.fintech.calm.IfscSearchResponse;
import com.google.inject.Inject;
import lombok.CustomLog;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;
import org.glassfish.jersey.media.multipart.MultiPartFeature;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

import static com.flipkart.fintech.pinaka.client.Utils.addTraceId;

@CustomLog
public class PinakaClientV7Impl implements PinakaClientV7 {
    private static final String ERROR_PINAKA = "error response from pinaka service";
    private final PinakaClientConfig clientConfig;
    private final ObjectMapper objectMapper = ObjectMapperUtil.get();
    private WebTarget webTarget;

    @Inject
    public PinakaClientV7Impl(PinakaClientConfig clientConfig, Client client) {
        this.clientConfig = clientConfig;
        this.webTarget = client.target(clientConfig.getUrl()).path(Constants.APP_CONTEXT_PATH).register(
                MultiPartFeature.class);
    }

    @Override
    public PageActionResponse applyNow(LandingPageRequest landingPageRequest, String merchantUserId, String smUserId,
                                       String merchantId, String requestId, String userAgent) throws PinakaClientException {
        Response response = null;
        PageActionResponse pageActionResponse;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.APPLY_NOW_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_MERCHANT_USER_ID, merchantUserId);
            invocationBuilder.header(Constants.X_SM_USER_ID, smUserId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(landingPageRequest)));
            if (response.getStatus() == 200) {
                pageActionResponse = objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pageActionResponse;
    }

    @Override
    public PageActionResponse resumeJourney(LandingPageRequest landingPageRequest, String merchantUserId, String smUserId,
                                            String merchantId, String requestId, String userAgent) throws PinakaClientException {
        Response response = null;
        PageActionResponse pageActionResponse;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.RESUME_JOURNEY_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_MERCHANT_USER_ID, merchantUserId);
            invocationBuilder.header(Constants.X_SM_USER_ID, smUserId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(landingPageRequest)));
            if (response.getStatus() == 200) {
                pageActionResponse = objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pageActionResponse;
    }

    @Override
    public PageActionResponse submitForm(UserActionRequest submitRequest, String merchantUserId, String smUserId,
                                         String merchantId, String requestId) throws PinakaClientException {
        Response response = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.SUBMIT_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_USER_ID, merchantUserId);
            invocationBuilder.header(Constants.X_SM_USER_ID, smUserId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(submitRequest)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }


    @Override
    public PageActionResponse resume(ResumePageRequest resumePageRequest, String merchantUserId, String smUserId,
                                     String merchantId, String requestId, String userAgent) throws PinakaClientException {
        Response response = null;
        PageActionResponse pageActionResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.RESUME_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_MERCHANT_USER_ID, merchantUserId);
            invocationBuilder.header(Constants.X_SM_USER_ID, smUserId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(resumePageRequest)));
            if (response.getStatus() == 200) {
                pageActionResponse = objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pageActionResponse;
    }

    @Override
    public PageActionResponse submitUserAction(SubmitRequest submitRequest, String merchantUserId, String smUserId,
                                               String merchantId, String requestId) throws PinakaClientException {
        Response response = null;
        PageActionResponse pageActionResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.SUBMIT_USER_ACTION).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_USER_ID, merchantUserId);
            invocationBuilder.header(Constants.X_SM_USER_ID, smUserId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(submitRequest)));
            if (response.getStatus() == 200) {
                pageActionResponse = objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pageActionResponse;
    }

    @Override
    public WebhooksResponse submitLenderEvent(AxisWebhooksRequest webhooksRequest, String requestId) throws PinakaClientException {
        Response response = null;
        WebhooksResponse webhooksResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.SUBMIT_LENDER_EVENT).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            //Here we are adding temp merchant as lender events doesn't have merchant awareness // TODO later fix it
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(webhooksRequest)));
            if (response.getStatus() == 200) {
                webhooksResponse = objectMapper.readValue(response.readEntity(String.class), WebhooksResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return webhooksResponse;
    }

    @Override
    public WebhooksResponse submitSandboxLenderEvent(SandboxWebhooksRequest webhooksRequest, String requestId) throws PinakaClientException {
        Response response = null;
        WebhooksResponse webhooksResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.SUBMIT_SANDBOX_LENDER_EVENT).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            //Here we are adding temp merchant as lender events doesn't have merchant awareness // TODO later fix it
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(webhooksRequest)));
            if (response.getStatus() == 200) {
                webhooksResponse = objectMapper.readValue(response.readEntity(String.class), WebhooksResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return webhooksResponse;
    }


    @Override
    public KfsResponse getKfsDetails(KfsRequest kfsRequest, String merchantId, String requestId) throws PinakaClientException {
        Response response = null;
        KfsResponse kfsResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.KFS_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(kfsRequest)));
            if (response.getStatus() == 200) {
                kfsResponse = objectMapper.readValue(response.readEntity(String.class), KfsResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return kfsResponse;
    }

    @Override
    public StatusResponse getStatus(String applicationId, String requestId, String merchantUserId, String smUserId,
                                    String merchantId) throws PinakaClientException {
        Response response = null;
        StatusResponse statusResponse = null;
        try {
            String path = String.format(Constants.GET_STATUS_PATH, applicationId);
            Invocation.Builder invocationBuilder = webTarget.path(path).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            // TODO For backward compatibility, later remove it
            invocationBuilder.header(Constants.X_ACCOUNT_ID, merchantUserId);
            invocationBuilder.header(Constants.X_MERCHANT_USER_ID, merchantUserId);
            invocationBuilder.header(Constants.X_SM_USER_ID, smUserId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                statusResponse = objectMapper.readValue(response.readEntity(String.class), StatusResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return statusResponse;
    }

    @Override
    public SearchResponse getEmpSearchSug(String prefix, String merchantId) throws PinakaClientException {
        Response response = null;
        SearchResponse searchResponse;
        try {
            String path = String.format(Constants.PL_EMP_SEARCH_PATH, prefix);
            Invocation.Builder invocationBuilder = webTarget.path(path).queryParam(Constants.PREFIX, prefix)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                searchResponse = objectMapper.readValue(response.readEntity(String.class), SearchResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return searchResponse;
    }

    @Override
    public SearchResponse getEmployerSuggestions(String prefix, String merchantId) throws PinakaClientException {
        Response response = null;
        SearchResponse searchResponse;
        try {
            String path = String.format(Constants.PL_EMP_SEARCH_PATH_V2, prefix);
            Invocation.Builder invocationBuilder = webTarget.path(path).queryParam(Constants.PREFIX, prefix)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                searchResponse = objectMapper.readValue(response.readEntity(String.class), SearchResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return searchResponse;
    }

    private String generateAuthToken(String merchant) {
        StringBuilder keyReverse = new StringBuilder(merchant);
        String input = merchant + ":" + keyReverse.reverse();
        return Base64.getEncoder().encodeToString(input.getBytes());
    }

    public FetchBulkDataResponse fetchBulkData(FetchBulkDataRequest fetchBulkDataRequest, String merchantUserId,
                                               String smUserId, String merchantId, String requestId) throws PinakaClientException {
        Response response = null;
        FetchBulkDataResponse fetchBulkDataResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.FETCH_BULK_DATA_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_MERCHANT_USER_ID, merchantUserId);
            invocationBuilder.header(Constants.X_SM_USER_ID, smUserId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(fetchBulkDataRequest)));
            if (response.getStatus() == 200) {
                fetchBulkDataResponse = objectMapper.readValue(response.readEntity(String.class), FetchBulkDataResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return fetchBulkDataResponse;

    }

    @Override
    public SecurityKeyResponse getEncryptionKey(String requestId, String merchantId) throws PinakaClientException {
        Response response = null;
        SecurityKeyResponse securityKeyResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.PL_ENCRYPTION_KEY_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                securityKeyResponse = objectMapper.readValue(response.readEntity(String.class), SecurityKeyResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return securityKeyResponse;
    }

    @Override
    public PageActionResponse submit(UserActionRequest submitRequest, String merchantUserId, String smUserId,
                                     String merchantId, String requestId, String userAgent) throws PinakaClientException {
        Response response = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.USER_ACTION_SUBMIT).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_USER_ID, merchantUserId);
            invocationBuilder.header(Constants.X_SM_USER_ID, smUserId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(submitRequest)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public StateChangeResponse hasStateChanged(StateChangeRequest stateChangeRequest, String merchantUserId,
                                               String smUserId, String merchant, String requestId)
            throws PinakaClientException {
        Response response = null;
        try {
            String path = Constants.PL_STATE_CHANGE_PATH;
            Invocation.Builder invocationBuilder = webTarget.path(path)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_USER_ID, merchantUserId);
            invocationBuilder.header(Constants.X_SM_USER_ID, smUserId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(stateChangeRequest)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), StateChangeResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public FetchBulkDataResponseV2 fetchBulkDataV2(PageServiceRequest pageServiceRequest, String merchantUserId,
                                                   String smUserId, String merchant, String requestId)
            throws PinakaClientException, IOException {
        Response response = null;
        FetchBulkDataResponseV2 fetchBulkDataResponseV2;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.FETCH_BULK_DATA_PATH_V2)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_USER_ID, merchantUserId);
            invocationBuilder.header(Constants.X_SM_USER_ID, smUserId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(pageServiceRequest)));
            if (response.getStatus() == 200) {
                fetchBulkDataResponseV2 = objectMapper.readValue(response.readEntity(String.class), FetchBulkDataResponseV2.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return fetchBulkDataResponseV2;
    }

    @Override
    public IfscSearchResponse searchIfsc(IfscSearchRequest ifscSearchRequest, String merchant, String requestId)
            throws PinakaClientException {
        Response response = null;
        try {
            String path = Constants.PL_IFSC_SEARCH_PATH;
            Invocation.Builder invocationBuilder = webTarget.path(path)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(ifscSearchRequest)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), IfscSearchResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public PageActionResponse dummyResponse(String url, String merchant, String requestId) throws PinakaClientException {
        Response response = null;
        PageActionResponse pageActionResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.DUMMY_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(url)));
            if (response.getStatus() == 200) {
                pageActionResponse = objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pageActionResponse;
    }

    @Override
    public DocumentUploadResponse documentUpload(String merchantUserId, String smUserId, String merchant,
                                                 String documentType, String applicationId, String encryptedSymmetricKey,
                                                 String publicKeyRefId, InputStream documentInputStream, String documentPassword,
                                                 String requestId) throws PinakaClientException {
        Response response = null;
        try {
            String path = String.format(Constants.DOCUMENT_UPLOAD, documentType, applicationId);

            FormDataMultiPart multiPart = new FormDataMultiPart()
                    .field("document", documentInputStream, MediaType.MULTIPART_FORM_DATA_TYPE)
                    .field("encrypted_symmetric_key", encryptedSymmetricKey)
                    .field("public_key_ref_id", publicKeyRefId)
                    .field("document_password", documentPassword);

            response = webTarget.path(path)
                    .request()
                    .header(Constants.X_ACCOUNT_ID, merchantUserId) // TODO: remove it once migration changes are fully deployed
                    .header(Constants.X_MERCHANT_USER_ID, merchantUserId)
                    .header(Constants.X_SM_USER_ID, smUserId)
                    .header(Constants.X_REQUEST_ID, requestId)
                    .header(Constants.X_MERCHANT_ID, merchant)
                    .header(Constants.X_CLIENT_ID, clientConfig.getClient())
                    .header(Constants.X_AUTHORIZATION, generateAuthToken(merchant))
                    .post(Entity.entity(multiPart, MediaType.MULTIPART_FORM_DATA_TYPE));

            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), DocumentUploadResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }


    @Override
    public PincodeDetailsResponse checkPincodeExistence(String pincode, String merchantId) throws PinakaClientException {
        Response response = null;
        PincodeDetailsResponse pincodeDetailsResponse = null;
        String path = Constants.CHECK_PINCODE_VALIDITY;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).queryParam("pincode", pincode).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                pincodeDetailsResponse = response.readEntity(PincodeDetailsResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pincodeDetailsResponse;
    }

    @Override
    public void migrateApplications(MigrateApplicationsRequest migrateApplicationsRequest, String requestId) throws PinakaClientException {
        Response response = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.MIGRATE_APPLICATIONS_PATH)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(migrateApplicationsRequest)));
            if (response.getStatus() != 200) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public void insertApplicationEvents(ApplicationEvent applicationEvent) throws PinakaClientException {
        Invocation.Builder invocationBuilder = webTarget.path(Constants.APPLICATION_EVENTS_PATH)
            .request(MediaType.APPLICATION_JSON_TYPE);
        try (Response response = invocationBuilder.post(
            Entity.json(objectMapper.writeValueAsString(applicationEvent)))) {
            if (response.getStatus() != 200) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        }
    }

    public PageActionResponse tryWithAnotherLender(ResumePageRequest resumePageRequest, String requestId, String userAgent, String merchantId) throws PinakaClientException {
        Response response = null;
        PageActionResponse pageActionResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.TRY_ANOTHER_LENDER_PATH).
                request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(resumePageRequest)));
            if (response.getStatus() == 200) {
                pageActionResponse = objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pageActionResponse;
    }

    @Override
    public PageActionResponse refreshPageFetchBureau(String merchantUserId, String smUserId, String requestId, String userAgent,String merchantId) throws PinakaClientException {
        Response response = null;
        try {
            CreditRequest creditRequest  = new CreditRequest();
            creditRequest.setSmAccountId(smUserId);
            creditRequest.setMerchantAccountId(merchantUserId);
            Invocation.Builder invocationBuilder = webTarget.path(Constants.REFRESH_PAGE_FETCH_FOR_BUREAU).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(creditRequest)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public PageActionResponse pageFetchBureau(String merchantUserId, String smUserId, String requestId, String userAgent,String merchantId) throws PinakaClientException {
        Response response = null;
        try {
            CreditRequest creditRequest  = new CreditRequest();
            creditRequest.setSmAccountId(smUserId);
            creditRequest.setMerchantAccountId(merchantUserId);
            Invocation.Builder invocationBuilder = webTarget.path(Constants.PAGE_FETCH_FOR_BUREAU).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(creditRequest)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public PageActionResponse formSubmit(FormSubmitRequestV2 submitRequest, String requestId, String userAgent,String merchantId) throws PinakaClientException {
        Response response = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.ACTION_SUBMIT).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(submitRequest)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public PageActionResponse refreshSMPageFetchBureau(String smUserId,String requestId, String userAgent,String merchantId) throws PinakaClientException {
        Response response = null;
        try {

            Invocation.Builder invocationBuilder = webTarget.path(Constants.SM_REFRESH_BUREAU_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(smUserId));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public PageActionResponse getSMPageFetchBureau(BureauDataRequestSm bureauDataRequestSm, String requestId, String userAgent, String merchantId) throws PinakaClientException {
        Response response = null;
        try {

            Invocation.Builder invocationBuilder = webTarget.path(Constants.SM_FETCH_BUREAU_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(bureauDataRequestSm)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public PageActionResponse putSMCrossMerchantConsent(CrossMerchantConsentRequest crossMerchantConsentRequest, String requestId, String userAgent, String merchantId) throws PinakaClientException {
        Response response = null;
        try {

            Invocation.Builder invocationBuilder = webTarget.path(Constants.SM_CS_CROSS_MERCHANT_CONSENT_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(crossMerchantConsentRequest)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public UIEventIngestionResponse uiEventIngester(UIEventIngestionRequest uiEventIngestionRequest, String requestId, String merchantId) throws PinakaClientException {
        Response response = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.PL_UI_EVENT_INGESTER).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(uiEventIngestionRequest)));
            if (response.getStatus() == 200){
                return objectMapper.readValue(response.readEntity(String.class), UIEventIngestionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch(Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if(response != null) {
                response.close();
            }
        }
    }
}
