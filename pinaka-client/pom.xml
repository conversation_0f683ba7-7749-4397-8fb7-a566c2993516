<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>pinaka</artifactId>
        <groupId>com.flipkart.fintech</groupId>
        <version>3.4.26-SM</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>pinaka-client</artifactId>
    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.google.inject</groupId>
            <artifactId>guice</artifactId>
            <version>${guice.version}</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pinaka-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.media</groupId>
            <artifactId>jersey-media-multipart</artifactId>
            <version>2.30.1</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-client</artifactId>
            <version>2.30.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.hk2.external</groupId>
                    <artifactId>javax.inject</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>ardour-api</artifactId>
            <version>${ardour.api.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.sensitive</groupId>
                    <artifactId>sensitive-annotation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-logger</artifactId>
            <version>${fintech.logger.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.affordability</groupId>
            <artifactId>affordability-service-clients</artifactId>
            <version>${affordability.service.client.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>bnpl-tijori-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jetbrains</groupId>
                    <artifactId>annotations-java5</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-all</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>model-commons</artifactId>
                    <groupId>com.flipkart.rome</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.rome</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.10</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-commons</artifactId>
            <version>1.0.122</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>winterfell-client</artifactId>
            <version>${winterfell.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.jersey.connectors</groupId>
                    <artifactId>jersey-apache-connector</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-kafka-consumers</artifactId>
            <version>1.1.22-SM</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>
