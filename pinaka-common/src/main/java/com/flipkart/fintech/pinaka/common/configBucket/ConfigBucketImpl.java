package com.flipkart.fintech.pinaka.common.configBucket;

import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;

public class ConfigBucketImpl implements ConfigBucket {

    private final DynamicBucket dynamicBucket;

    @Inject
    public ConfigBucketImpl(DynamicBucket dynamicBucket) {
        this.dynamicBucket = dynamicBucket;
    }


    @Override
    public Boolean getBoolean(String key) {
        return dynamicBucket.getBoolean(key);
    }
    @Override
    public Double getDouble(String key) {
        return dynamicBucket.getDouble(key);
    }
}
