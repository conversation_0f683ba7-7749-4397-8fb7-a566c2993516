package com.flipkart.fintech.pinaka.common.dexterClient;

import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileRequest;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.google.inject.ImplementedBy;

@ImplementedBy(DexterClientImpl.class)
public interface DexterClient {
    FetchUserProfileResponse fetchUserProfile(FetchUserProfileRequest fetchUserProfileRequest, String clientId, String requestId) throws RuntimeException;
}
