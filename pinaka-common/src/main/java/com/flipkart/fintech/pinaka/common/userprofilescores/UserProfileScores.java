package com.flipkart.fintech.pinaka.common.userprofilescores;

import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.google.inject.ImplementedBy;
import com.sumo.crisys.api.CremoScores;
import com.sumo.crisys.client.CrisysClientException;

@ImplementedBy(UserProfileScoresImpl.class)
public interface UserProfileScores {

    UserProfileResponseV3 getUserProfile(MerchantUser merchantUser);

    FetchUserProfileResponse getUserProfileByDexter(String requestId, MerchantUser merchantUser);

    Double getUserBinScore(MerchantUser merchantUser, String requestId);

    Double getUserBin(MerchantUser merchantUser, String requestId);

    CremoScores getCremoScore(MerchantUser merchantUser) throws CrisysClientException;

    Double getUserExperianScore(MerchantUser merchantUser);
}
