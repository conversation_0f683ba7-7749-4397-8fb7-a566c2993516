package com.flipkart.fintech.pinaka.common.varadhi;

import com.flipkart.fintech.pinaka.client.Constants;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.client.VaradhiClentConfig;
import lombok.CustomLog;

import javax.inject.Inject;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import static com.flipkart.fintech.pinaka.client.Constants.VARADHI_EVENT_PUSH;

@CustomLog
public class VaradhiClientImpl implements VaradhiClient{
    private WebTarget webTarget;
    private final VaradhiClentConfig clientConfig;
    private static final String ERROR_VARADHI = "error response from varadhi service";

    @Inject
    public VaradhiClientImpl(VaradhiClentConfig clientConfig, Client client) {
        this.webTarget = client.target(clientConfig.getUrl());
        this.clientConfig = clientConfig;
    }

    @Override
    public void pushEvent(RobinhoodEventRequest request) throws PinakaClientException {
        Response response = null;
        String path = String.format(VARADHI_EVENT_PUSH,clientConfig.getTopicName());
        try{
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_RESTBUS_MESSAGE_ID,request.getMsgId());
            invocationBuilder.header(Constants.X_RESTBUS_GROUP_ID, request.getGrpId());
            response = invocationBuilder.post(Entity.json(request.getPersonalLoanUserDetailsV2()));
            if(response.getStatus() != 200){
                log.error("Error status : {} from varadhi with response_body : {}",response.getStatus(),response.toString());
                throw new PinakaClientException(ERROR_VARADHI + response.readEntity(String.class));
            }
            else{
                log.info("Successfully pushed to varadhi : {}",request.toString());
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
    }
}
