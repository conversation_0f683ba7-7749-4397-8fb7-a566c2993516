package com.flipkart.fintech.pinaka.common.decrypter;

import com.flipkart.fintech.security.aes.AESService;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class DecrypterImpl implements Decrypter {

    private String encryptKey = "cGlpRW5jcnlwdGlvbktleQ==";

    @Override
    public String decryptString(String encryptedString) {
        byte[] plaintextBytes = AESService.decrypt(encryptKey, Base64.getDecoder().decode(encryptedString.getBytes(StandardCharsets.UTF_8)));
        return new String(plaintextBytes, StandardCharsets.UTF_8);
    }
}
