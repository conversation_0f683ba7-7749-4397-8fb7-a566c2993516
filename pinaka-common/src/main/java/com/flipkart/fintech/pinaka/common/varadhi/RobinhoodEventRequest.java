package com.flipkart.fintech.pinaka.common.varadhi;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.affordability.robinhood.api.model.personalLoan.PersonalLoanUserDetailsV2;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.UUID;

@Getter
@ToString
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@RequiredArgsConstructor
public class RobinhoodEventRequest{

    private String msgId;
    private String grpId;
    public RobinhoodEventRequest(PersonalLoanUserDetailsV2 personalLoanUserDetailsV2) {
        this.personalLoanUserDetailsV2 = personalLoanUserDetailsV2;
        this.msgId = generateMsgId();
        this.grpId = generateGrpId(personalLoanUserDetailsV2);
    }

    private String generateGrpId(PersonalLoanUserDetailsV2 personalLoanUserDetailsV2) {
        return personalLoanUserDetailsV2.getAccountId();
    }

    private String generateMsgId() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString();
    }

    private PersonalLoanUserDetailsV2 personalLoanUserDetailsV2;
}
