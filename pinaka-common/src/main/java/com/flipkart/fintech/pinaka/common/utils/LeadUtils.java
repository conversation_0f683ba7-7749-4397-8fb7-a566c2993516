package com.flipkart.fintech.pinaka.common.utils;

import lombok.CustomLog;

@CustomLog
public class LeadUtils {
    public static Double getDoubleFromString(String input) {
        if(input == null) {
            return null;
        }
        try {
            return Double.parseDouble(input);
        } catch (Exception e) {
            log.error("getting exception while double conversion for string input {}", input, e);
        }
        return null;
    }
}
