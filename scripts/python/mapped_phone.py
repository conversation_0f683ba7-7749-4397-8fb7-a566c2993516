import requests
import csv
import sys

base_url = 'http://pandora-service.sm-pandora-prod.fkcloud.in'
phone_fetch_end_point = '/pandora/users/phone_fetch'

def map_phone(input_file_path, output_file_path):
   f = open(input_file_path, 'r')
   result = []
   first_name = ''
   primary_phone = ''
   for x in f:
      account_id = str(x).replace('\n', '')
      print(account_id)
      acc_url = base_url + phone_fetch_end_point + '?merchantAccountId=' +account_id
      req = requests.request("POST", acc_url)
      if req.status_code == 200:
         json_response = req.json()
         print(json_response)
         primary_phone = json_response["primaryPhone"]
         first_name = json_response["firstName"]
      else:
         print(req.status_code)
      if first_name == None:
         first_name = ''
      if primary_phone == None:
         primary_phone = ''
      result.append(account_id+","+first_name+","+primary_phone)
      print(account_id,primary_phone)
   with open(output_file_path, mode='w', newline='') as csvfile:
      csv_writer = csv.writer(csvfile)
      csv_writer.writerow(["AccountID, Name, Number"])

   with open(output_file_path, mode='a', newline='') as csvfile:
      csv_writer = csv.writer(csvfile)
      csv_writer.writerows([result])
   result.clear()


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python script.py <input_csv_file_path> <output_csv_file_path>")
        sys.exit(1)

    input_csv_file_path = sys.argv[1]
    output_csv_file_path = sys.argv[2]
    map_phone(input_csv_file_path, output_csv_file_path)
