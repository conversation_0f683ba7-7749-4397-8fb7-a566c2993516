import csv
from datetime import datetime

from sqlalchemy import String, Column, Integer, Text, Date, Boolean, DateTime
from sqlalchemy import create_engine
from sqlalchemy import select, update
from sqlalchemy.sql import func
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.orm import Session

# Run using the command:

prod_db = \
    "mysql+mysqlconnector://sm_rw:<EMAIL>:3306/pinaka"
preprod_db = \
    "mysql+mysqlconnector://sm_rw:<EMAIL>:3306/pinaka"

# UPDATE AND DOUBLE CHECK BELOW PARAMETERS BEFORE RUNNING
cur_db = preprod_db
write_batch_size = 4
csv_path = "scripts/data/sample/idfc/idfc_pl_sample.csv"

whitelistId = 192


class Base(DeclarativeBase):
    pass


class Borrowers(Base):
    __tablename__ = "borrowers"
    id = Column(Integer, autoincrement=True, primary_key=True)
    whitelistId = Column(name="whitelist_id", type_=Integer, nullable=False)
    enabled = Column(type_=Boolean, default=True)
    externalId = Column(name="external_id", type_=String(256))
    metadata_ = Column(name="metadata", type_=Text)
    createdAt = Column(DateTime(timezone=True), server_default=func.now(), name="created_at")
    updatedAt = Column(DateTime(timezone=True), server_default=func.now(), name="updated_at")

    @staticmethod
    def mapToBorrower(csv_row):
        borrower = Borrowers()
        borrower.externalId = csv_row[1]
        borrower.whitelistId = whitelistId
        borrower.enabled = 1
        borrower.metadata_ = f"""{chr(123)}"display_id": "", "cohort": "", "offer_id": "{csv_row[0]}"{chr(125)} """
        return borrower


def createMySqlConnection():
    return create_engine(cur_db)


def getBorrowers(external_ids, session: Session):
    stmt = select(Borrowers).where(Borrowers.externalId.in_(external_ids))
    return session.scalars(stmt)


def fetchRowsFromCsv(path):
    with open(path, newline='') as f:
        reader = csv.reader(f)
        return list(reader)


def addBorrowersToDbUtil(borrowers: list, session):
    for i in range(1, len(borrowers), write_batch_size):
        persistBorrowers(borrowers[i:i + write_batch_size], session)


def persistBorrowers(borrowers, session):
    borrowers = list(map(Borrowers.mapToBorrower, borrowers))
    session.add_all(borrowers)
    session.commit()
    print("Committed batch of:", len(borrowers), "records")
    print("Last borrower:", borrowers[-1].externalId)


if __name__ == "__main__":
    print("DB:", cur_db)
    print("BatchSize:", write_batch_size)
    print("CSVFile:", csv_path)
    confirmation = input("Enter y to confirm\n")
    if confirmation != "y":
        print("Exiting")
        exit(1)

    session = Session(createMySqlConnection())
    borrowers = fetchRowsFromCsv(csv_path)
    print("Adding borrowers: ", len(borrowers) - 1)
    addBorrowersToDbUtil(borrowers, session)
    print("Added borrowers: ", len(borrowers) - 1)
    exit(-1)
