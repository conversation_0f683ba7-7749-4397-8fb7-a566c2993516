FROM jfrog.fkinternal.com/fk-base-images/python:3.7.13-debian11.3-slim-20220527

ARG user=lending
ARG group=fpg
ARG uid=122
ARG gid=118

# Create a directory for the application
RUN groupadd -g ${gid} ${group} && \
    useradd -u ${uid} -g ${gid} -m -s /bin/bash ${user} && \
    mkdir -p /${user} && \
    chown -R ${user}:${group} /${user} && \
    chmod -R u+rwx /${user} && \
    mkdir -p /var/log/supermoney/ && \
    chmod -R u+rwx /var/log/supermoney/ 

# Switch to the new user
USER ${user}

WORKDIR /${user}/axis_reconsile_script

# Copy only the necessary files
COPY /scripts/python/axis_reconsile_script.py /scripts/python/requirements.txt ./

# Install Python dependencies
RUN pip3 install -i https://jfrog.fkinternal.com/artifactory/api/pypi/python_virtual/simple --trusted-host jfrog.fkinternal.com requests

ENTRYPOINT [ "python" ] 
CMD [ "-u", "axis_reconsile_script.py"]
