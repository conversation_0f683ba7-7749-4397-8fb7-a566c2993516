import os
import time
from datetime import datetime, timedelta
from concurrent import futures
from functools import partial
import logging

import mysql.connector
import requests
from mysql.connector import Error

PINAKA_STATUS_URL = "http://{0}/pinaka/6/pl/fetch-status/application-id/{1}"
PINAKA_STATUS_URL_ALL_LENDERS = "http://{0}/pinaka/6/pl/get-application-status/application-id/{1}"
WINTERFELL_APPLICATION_DATA_URL = "http://{0}/fintech-winterfell/1/application/{1}"

SANDBOX_LENDERS = ["MONEYVIEW", "MONEYVIEWV2", "FIBE", "FIBEV2", "DMI", "MON<PERSON>YVIEWOPENMKT", "SMARTCOIN", "RING", "FINNABLE"]
STATE_LIST = ["LENDER_PLATFORM", "APPLICATION_COMPLETED"]

OFFSET_DAYS = os.environ.get("offset_days")
HOUR_DELTA = os.environ.get("hour_delta")
TOTAL_DURATION = os.environ.get("total_duration")

AMS_HEADERS = {
    "Content-Type": "application/json",
    "X-Client-Id": "pinaka",
    "X-Merchant-Id": "flipkart",
    "X-Request-ID": "8a3d5ba8-4d4c-11ee-be56-0242ac120002",
    "X-Tenant-Id": "CALM",
    "X-Trace-ID": "trace1",
    "cache-control": "no-cache",
    "X-Is-Sm-Request": "true",
}

STATE_CHANGE_HEADERS = {
    "Content-Type": "application/json",
    "X-Client-Id": "pinaka",
    "X-Merchant-Id": "flipkart",
    "X-Request-ID": "8a3d5ba8-4d4c-11ee-be56-0242ac120002",
    "X-Tenant-Id": "CALM",
    "X-Trace-ID": "trace1",
    "cache-control": "no-cache",
}

log_directory = "/var/log/recon/"
os.makedirs(log_directory, exist_ok=True)

script_name = "axis-reconciliation-script"
if int(TOTAL_DURATION) > 168:
    script_name += "-weekly"

execution_timestamp = datetime.datetime.now().strftime("%Y%m%d")
filename = os.path.join(log_directory, f"{script_name}-{execution_timestamp}.log")

logging.basicConfig(
    filename=filename,
    filemode="a",
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
)


def fetch_applications(state):
    application_id_list = []
    execution_time = datetime.now() - timedelta(days=OFFSET_DAYS)
    end_time = execution_time - timedelta(hours=TOTAL_DURATION)

    try:
        connection = mysql.connector.connect(
            host=os.environ.get("db_host"),
            database=os.environ.get("db_name"),
            user=os.environ.get("db_user"),
            password=os.environ.get("db_password"),
        )

        if connection.is_connected():
            cursor = connection.cursor(dictionary=True)
            while execution_time > end_time:
                start_time = execution_time - timedelta(hours=HOUR_DELTA)
                query = """
                    SELECT id
                    FROM application
                    WHERE product_type = 'PERSONAL_LOAN'
                    AND state = %s
                    AND updated_at BETWEEN %s AND %s
                """
                cursor.execute(query, (state, start_time, execution_time))
                results = cursor.fetchall()

                for row in results:
                    application_id_list.append(row["id"])

                execution_time = start_time

    except Error as e:
        logging.error("Error while connecting to MySQL: %s", e)
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

    return application_id_list


def fetch_account_id_from_winterfell(app_id):
    url = WINTERFELL_APPLICATION_DATA_URL.format(os.environ.get("winterfell_ip"), app_id)
    try:
        response = requests.get(url=url, data={}, headers=AMS_HEADERS)
        json_response = response.json()
        return (
            json_response["external_user_id"],
            json_response["application_data"]["financial_provider"],
        )
    except Exception as e:
        logging.error("Exception in fetching account id for application: %s", app_id)
        return None, None


def fetch_status(app_id):
    acc_id, lender = fetch_account_id_from_winterfell(app_id)
    url = None
    if lender == "AXIS":
        url = PINAKA_STATUS_URL.format(os.environ.get("pinaka_ip"), app_id)
    if lender in SANDBOX_LENDERS:
        url = PINAKA_STATUS_URL_ALL_LENDERS.format(os.environ.get("pinaka_ip"), app_id)
    if url is None:
        return None, None, None
    time.sleep(0.05)
    try:
        PINAKA_STATUS_HEADERS = {
            "X-Request-Id": "bffa68b2-4d4c-11ee-be56-0242ac120002",
            "X-Account-Id": acc_id,
            "X-Merchant-Id": "FLIPKART",
        }
        response = requests.get(url=url, data={}, headers=PINAKA_STATUS_HEADERS)
        status = "NA"
        if response.status_code == 200:
            json_response = response.json()
            status = json_response["status"]
        return app_id, status, response.status_code
    except Exception as e:
        logging.error("Error occurred: %s", e)
        return None, None, None


if __name__ == "__main__":
    applications_to_recon = []

    for state in STATE_LIST:
        applications = fetch_applications(state)
        for app in applications:
            applications_to_recon.append(app)

    print("count of applications : " + str(len(applications_to_recon)))

    with futures.ThreadPoolExecutor(max_workers=1) as executor:
        process = partial(fetch_status)
        results = list(executor.map(process, applications_to_recon))
    print(results)
    axis_result = [
        (app_id, status, response_code)
        for app_id, status, response_code in results
        if app_id
    ]
