import concurrent.futures
import csv
import json
import requests
import sys
from queue import Queue

base_url = 'http://winterfell-service.sm-winterfell-prod.fkcloud.in'

def get_application_id(account_id):
    api_endpoint = base_url + '/fintech-winterfell/1/application'

    params = {
        'external_user_id': account_id
    }

    headers = {
        'Content-Type': 'application/json',
        'X-Client-Id': 'pinaka',
        'X-Merchant-Id': 'mp_flipkart',
        'X-Request-ID': 'discard-request',
        'X-Tenant-Id': 'CALM',
        'X-Trace-ID': 'discard-trace',
        'cache-control': 'no-cache',
    }

    response = requests.get(api_endpoint, headers=headers, params=params)
    if response.status_code == 200:
        data = json.loads(response.text)
        if data["application_list"]:
            result = {
                'lead': [],
                'application': []
            }
            for key, value in data['application_list'].items():
                if key == 'LEAD':
                    result['lead'] = value
                else:
                    result['application'].extend(value)
            return result
    else:
        print(f'Error in API call with parameter {account_id}, {application_type}: {response.text}')
    return None

def discard_application(application_id, error_queue):
    api_endpoint = base_url + '/fintech-winterfell/1/admin/application/' + application_id + '/discard'
    headers = {
        'Content-Type': 'application/json',
        'X-Client-Id': 'pinaka',
        'X-Merchant-Id': 'mp_flipkart',
        'X-Request-ID': 'abc123',
        'X-Tenant-Id': 'CALM',
        'X-Trace-ID': 'trace123',
        'cache-control': 'no-cache',
        'X-External-User-Id': "acc_id",
    }
    response = requests.post(api_endpoint, headers=headers)

    if response.status_code == 200:
        print(f'Discarding: {application_id}')
        return 1
    else:
        print(f'Error in discarding application with parameter {application_id}: {response.text}')
        error_queue.put(application_id)
        return 0

def parallel(function, task_list, error_queue, app_type, error_acc_queue):
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        future_to_task = {executor.submit(function, account_id, error_queue, app_type, error_acc_queue): account_id for account_id in task_list}
        for future in concurrent.futures.as_completed(future_to_task):
            task = future_to_task[future]
            try:
                data = future.result()
            except Exception as exc:
                print(f'{task} generated an exception: {exc}')
            else:
                print(f'{task} task completed')

def main(csv_file_path, app_type):
    account_ids = []
    error_queue = Queue()
    error_acc_queue = Queue()
    with open(csv_file_path, newline='') as csvfile:
        csv_reader = csv.reader(csvfile)
        next(csv_reader)
        for row in csv_reader:
            account_ids.append(row[0])

    parallel(execute_one_account, account_ids, error_queue, app_type, error_acc_queue)

    # Write the error_list to error_app_ids.csv
    with open('error_app_ids.csv', 'w', newline='') as error_file:
        writer = csv.writer(error_file)
        writer.writerow(['application_id'])
        while not error_queue.empty():
            writer.writerow([error_queue.get()])

    with open('error_acc_ids.csv', 'w', newline='') as error_file:
        writer = csv.writer(error_file)
        writer.writerow(['acc_id'])
        while not error_acc_queue.empty():
            writer.writerow([error_acc_queue.get()])

def execute_one_account(account_id, error_queue, app_type, error_acc_queue):
    result = get_application_id(account_id)
    if result is None:
        error_acc_queue.put(account_id)
        return
    if ((app_type == 0 or app_type == 2) and result['lead']):
        for lead_id in result['lead']:
            discard_application(lead_id, error_queue)

    if (app_type == 1 or app_type == 2) and result['application']:
        for app_id in result['application']:
            discard_application(app_id, error_queue)

# call python discard_app.py <csv_file_path> app_type (l - lead, a - application, la - both)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python discard.py <csv_file_path> app_type")
        sys.exit(1)

    csv_file_path = sys.argv[1]
    discard_type = sys.argv[2]
    app_type = 3
    if discard_type == 'l':
        app_type = 0
    elif discard_type == 'a':
        app_type = 1
    elif discard_type == 'la':
        app_type = 2
    if app_type == 3:
        print("Wrong discard type. Exiting...")
        sys.exit(1)
    main(csv_file_path, app_type)
