import csv
import re
import sys
import time

import requests
import concurrent.futures
import json

TARGET_URL ='http://***********/pinaka/1/borrower/{}/{}/remove'


headers = {
            "Content-Type": "application/json",
            "X-Merchant-Id": "mp_flipkart",
            "X-Authorization": "bXBfZmxpcGthcnQ6dHJha3BpbGZfcG0=",
        }

productType = sys.argv[2]


executor = concurrent.futures.ThreadPoolExecutor(max_workers=10)
session = requests.Session()
adapter = requests.adapters.HTTPAdapter(pool_connections=50, pool_maxsize=50, pool_block=False)
session.mount("http://", adapter)

def detarget(accountId):
    payload = {}
    url = TARGET_URL.format(accountId, productType)
    try:
        response = session.post(url, data=json.dumps(payload), headers=headers)
        if (response.status_code == 201 or response.status_code == 200):
            pass
        else:
            print("{} Non2xx reason - {}".format(accountId, response.content))
    except Exception as e:
        print("{} exception- {}".format(accountId, e))


def DeTarget(path) :
    with open(path, "r") as sourceFile:
        #with open("DETARGET_SUCCESS_{}_{}.txt".format(productType, int(time.time())), "a+") as successFile:
        #    with open("DETARGET_FAILURE_{}_{}.txt".format(productType, int(time.time())), "a+") as failureFile:
        count = 0;
                                                                                                                                                                                  1,10          Top
        for line in sourceFile:
            accountId = line.strip("\n").strip("\r")
            executor.submit(detarget(accountId))
            count=count+1
            print count


DeTarget(sys.argv[1])
