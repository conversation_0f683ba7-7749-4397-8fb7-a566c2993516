## Running IDFC ingestion script
#### Install python3 env in the root directory
- python3 -m venv .venv
#### Activate the venv  
- source .venv/bin/activate
#### Install pip
- python3 -m pip install --upgrade pip
#### Install dependencies
- python3 -m pip install sqlalchemy
- python3 -m pip install mysql-connector
#### Run the script
- python3 scripts/python/ingest_idfc_preapproved.py
#### Deactivate the venv
- deactivate