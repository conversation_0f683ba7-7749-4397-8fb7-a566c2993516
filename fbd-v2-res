{"widget_entity_list": [{"slotId": 1, "widgetType": "CARD_SUMMARY_LIST", "widgetData": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/0b69cf71-634c-4584-a916-097bd0f981b0.png?q={@quality}", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/0b69cf71-634c-4584-a916-097bd0f981b0.png?q={@quality}", "width": 105, "height": 132}, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Congrats <PERSON>,", "showClipboard": false, "textColor": "#98A2B3", "style": {"color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "ANEK_LATIN", "fontWeight": "bold"}, "specialTextsMapper": {"Prakash": {"color": "#1D2939"}}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "An instant cash of upto", "showClipboard": false, "textColor": "#98A2B3", "style": {"color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "ANEK_LATIN", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 6, "padding": 8, "alignItems": "flex-start", "paddingBottom": 0}, "textContainerStyle": {"display": "flex", "flexDirection": "column", "alignItems": "flex-start"}, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "PORTRAIT", "requestContext": null, "cardType": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": null, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "₹10,00,000*", "showClipboard": false, "textColor": "#4D43FE", "style": {"fontSize": 56, "lineHeight": 60, "fontFamily": "ANEK_LATIN", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "is reserved for you!", "showClipboard": false, "textColor": "#98A2B3", "style": {"color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "ANEK_LATIN", "fontWeight": "bold", "paddingTop": 12.0}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 6, "padding": 8, "alignItems": "baseline", "paddingTop": 0}, "textContainerStyle": {}, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": null, "listTitle": null, "footerTag": null}}, {"slotId": 2, "widgetType": "CARD_SUMMARY_LIST", "widgetData": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/ad0137e2-703b-4b1f-9f45-9d294e9c4c0e.png?q={@quality}", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/ad0137e2-703b-4b1f-9f45-9d294e9c4c0e.png?q={@quality}", "width": 40, "height": 40}, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Get a ₹500 Flipkart voucher", "showClipboard": false, "textColor": "#667085", "style": {"color": "#1D2939", "fontSize": 16, "lineHeight": 20, "fontFamily": "ANEK_LATIN", "fontWeight": "normal", "marginTop": 8.0, "whiteSpace": "nowrap"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "once the cash is in your account", "showClipboard": false, "textColor": "#667085", "style": {"color": "#1D2939", "fontSize": 16, "lineHeight": 20, "fontFamily": "ANEK_LATIN", "fontWeight": "normal", "marginTop": 8.0, "whiteSpace": "nowrap"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 8, "padding": 8, "display": "flex", "flexDirection": "row", "alignItems": "center", "gap": 4}, "textContainerStyle": null, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/e934feb1-9201-4301-b02b-7479ed984074.png?q={@quality}", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/e934feb1-9201-4301-b02b-7479ed984074.png?q={@quality}", "width": 40, "height": 40}, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "3-10 months of easy EMI's", "showClipboard": false, "textColor": "#344054", "style": {"color": "#1D2939", "fontSize": 16, "lineHeight": 20, "fontFamily": "ANEK_LATIN", "fontWeight": "normal", "marginTop": 8.0, "whiteSpace": "nowrap"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "repay as per your comfort", "showClipboard": false, "textColor": "#344054", "style": {"color": "#1D2939", "fontSize": 16, "lineHeight": 20, "fontFamily": "ANEK_LATIN", "fontWeight": "normal", "marginTop": 8.0, "whiteSpace": "nowrap"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "tagValue": null, "overlayTagValue": null, "style": {"background": "linear-gradient(90deg, #E5E8FD 0%, #F9FAFB 100%)", "borderRadius": 8, "padding": 8, "display": "flex", "flexDirection": "row", "alignItems": "center", "gap": 4}, "textContainerStyle": null, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/438662f9-024a-43f1-9072-361c08cf756b.png?q={@quality}", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/438662f9-024a-43f1-9072-361c08cf756b.png?q={@quality}", "width": 40, "height": 40}, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Safe and secure cash", "showClipboard": false, "textColor": "#344054", "style": {"color": "#1D2939", "fontSize": 16, "lineHeight": 20, "fontFamily": "ANEK_LATIN", "fontWeight": "normal", "marginTop": 8.0, "whiteSpace": "nowrap"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Trusted by over 20L+ customers", "showClipboard": false, "textColor": "#344054", "style": {"color": "#1D2939", "fontSize": 16, "lineHeight": 20, "fontFamily": "ANEK_LATIN", "fontWeight": "normal", "marginTop": 8.0, "whiteSpace": "nowrap"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 8, "padding": 8, "display": "flex", "flexDirection": "row", "alignItems": "center", "gap": 4}, "textContainerStyle": null, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "VERTICAL_CARD_ANIMATION", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": null, "listTitle": null, "footerTag": null}}, {"slotId": 3, "widgetType": "SUBMIT_BUTTON_WIDGET", "widgetData": {"subWidget": null, "clientPowered": false, "renderableComponents": null, "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": null, "listTitle": null, "formId": null, "persistFormData": false, "resultStoreKey": null, "title": null, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Unlock offer now", "buttonColor": "#B5EF85", "buttonTextColor": "#000", "borderColor": "#B5EF85"}, "action": {"screenType": null, "type": "CLIENT__INLINE_NAVIGATION", "omnitureData": null, "originalUrl": null, "url": "6/pl/apply-now", "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "#F9FAFB", "orientation": "", "theme": "light"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": "", "pageSubtitle": "", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCE47ACFD48746449697FBE83F59F78BAFZ", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2506271952401125469337524298134457195", "loginStatus": "login:<PERSON><PERSON><PERSON>"}}}, "pageMeta": {"baseImpressionId": "25b04894-2a9b-4b55-b6aa-6343b6654005", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#F9FAFB", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 0, "widgetWidth": 12}, "dataId": "-********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "REVIEW_PAGE_1", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "title": {"text": " ", "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "url": "https://rukminim1.flixcart.com/www/288/{@height}/promos/17/06/2025/1419ed91-7577-4a76-af8b-160b93fc96d7.png?q=100", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/288/{@height}/promos/17/06/2025/1419ed91-7577-4a76-af8b-160b93fc96d7.png?q=100", "height": 144, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/06/2025/1419ed91-7577-4a76-af8b-160b93fc96d7.png?q=100", "width": 288}}, "subTitle": {"text": "Make sure your name is as per PAN", "style": {"fontFamily": "<PERSON><PERSON>", "color": "#4D43FE", "fontSize": 20, "fontWeight": "bold", "width": 100}}, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {}, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Name", "mandatory": true, "name": "Name", "noOfErrors": 1, "placeholder": "Enter your first name", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": 400, "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": 400, "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": 400}}, "value": "<PERSON>"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValue", "disabled": true, "formFieldType": "TEXT_BOX", "label": "Phone no.", "mandatory": true, "name": "phoneNumber", "placeholder": "Enter mobile number", "inputType": "PHONE_PAD", "value": "7310818220", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid mobile number", "validateOnSubmit": false, "regex": "^[6-9]{1}[0-9]{9}$"}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": 400, "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": 400}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": 400}}}}], "resultStoreKey": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "Ez--nQ_x5UZcoOfX", "taskKey": "leadV4LandingPage", "applicationId": "APP2506271952401125469337524298134457195", "taskId": "IG9i_gx6Rmn9b9oA", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmAtBs7kvHiPjJIinhZ8JmZGbhdto/YyPEEid+v4ImEbfq/i4zCCaegOL5sIJDyV3ELiUmatxlRahy1Fj9KChlTA"}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form?page=review_details_2", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Confirm", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "borderRadius": 8, "richText": {"type": "RichTextValue", "style": {"color": "#1D2939", "fontSize": 18, "fontWeight": "bold", "fontFamily": "<PERSON><PERSON>"}}, "text": "Continue"}}, "buttonOrientation": null, "consent": {"checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "I hereby grant express consent and authorize Scapic Innovations Pvt. Ltd (SIPL) to collect, process, record my personal data including personal information and sensitive personal information such as PAN, DOB, gender, contact details, income source ('Personal Data') during loan journey on the platform/application and share Personal Data with SIPL’s Partners to complete Know Your Customer (KYC) requirements as required under applicable laws and for the purpose of processing my loan application ('Purpose'). I further consent to and authorize the SIPL’s Partners to further share my Personal Data with their service providers and/or third parties for the Purpose including Tele-calling, SMS or WhatsApp. I also understand and authorize the SIPL’s Partners to pull my credit report separately from any credit information companies' basis my Personal Data shared for the Purpose. I hereby consent to and expressly authorize SIPL to pull credit reports from credit information companies (Read Experian Specific terms here), to evaluate my credit worthiness/eligibility/identity for financial services and products such as personal loans, credit cards, buy now pay later /or for my loan application respectively (End Use Purpose). SIPL does not share credit information received from credit information companies with third parties without your express consent. I hereby also confirm that I am an Indian citizen and accept that I have read the terms and conditions and privacy policy of Scapic Innovations Pvt. Ltd.."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "BUREAU_PULL", "consentId": "001", "consentType": "CHECKBOX"}, "consentList": null, "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": "NO_SHADOW"}}, "tracking": {}}}]}, "processInstanceId": "Ez--nQ_x5UZcoOfX", "taskKey": "leadV4LandingPage", "applicationId": "APP2506271952401125469337524298134457195", "taskId": "IG9i_gx6Rmn9b9oA", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmAtBs7kvHiPjJIinhZ8JmZGbhdto/YyPEEid+v4ImEbfq/i4zCCaegOL5sIJDyV3ELiUmatxlRahy1Fj9KChlTA"}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": null, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": null}, "formGroups": null}}]}