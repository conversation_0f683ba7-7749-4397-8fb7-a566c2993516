sonar.projectKey=supermoney:sm-pinaka
sonar.projectName=sm-pinaka
sonar.tests=./ams-bridge/src/test, ./ams-connector/src/test, ./offer-orchestrator/src/test, ./pinaka-api/src/test, ./pinaka-client/src/test, ./pinaka-common/src/test, ./pinaka-service/src/test, ./profile-service/src/test
sonar.sources=./ams-bridge/src/main, ./ams-connector/src/main, ./offer-orchestrator/src/main, ./page-service/src/main, ./pinaka-api/src/main, ./pinaka-client/src/main, ./pinaka-common/src/main, ./pinaka-service/src/main, ./profile-api/src/main, ./profile-page-handler/src/main, ./profile-service/src/main, ./profile-service-client/src/main
sonar.exclusions=**/generated/*,**/*.test.*
sonar.projectVersion=1.1
sonar.sourceEncoding = UTF-8
sonar.verbose=true
sonar.coverage.jacoco.xmlReportPaths=ams-bridge/target/site/jacoco-ut/jacoco.xml,ams-connector/target/site/jacoco-ut/jacoco.xml,offer-orchestrator/target/site/jacoco-ut/jacoco.xml,pinaka-api/target/site/jacoco-ut/jacoco.xml,pinaka-client/target/site/jacoco-ut/jacoco.xml,pinaka-common/target/site/jacoco-ut/jacoco.xml,pinaka-service/target/site/jacoco-ut/jacoco.xml,profile-service/target/site/jacoco-ut/jacoco.xml
sonar.java.binaries=ams-bridge/target/classes,ams-connector/target/classes,offer-orchestrator/target/classes,pinaka-api/target/classes,pinaka-client/target/classes,pinaka-common/target/classes,pinaka-service/target/classes,profile-service/target/classes