namespace: pinaka

fcp:
  team: "Lending"
  appId: "fintech-pinaka-prod"
  mail: "fpg-cf-engg"
  service: "pinaka"

mtl:
  cosmos:
    config: cosmos-config
  asterix: disabled
  logsvc:
    enabled: true
    envName: hyd
    sourceLoc: rsyslog.conf

pod:
  replicaCount: 2

image:
  service:
    tag: "101075.37022c.1189"
    repository: lending-los/pinaka
    runAsUser: 108
    runAsGroup: 112
    volumes:
      - app-config
      - app-log
      - cosmos-config
      - cfg-svc-metadata
      - relayer-config
    livenessCheck:
      initialDelay: 90
      checkInterval: 15
      method: tcp
      port: 9091
    readinessCheck:
      initialDelay: 60
      checkInterval: 15
      method: http
      port: 9091
      path: /admin/healthcheck
    ports:
      - name: application
        containerPort: 9090
      - name: admin
        containerPort: 9091
      - name: jmx
        containerPort: 9311
    maxCpuLimit: 3
    maxCpuAsk: 3
    maxMemoryLimit: 6Gi
    maxMemoryAsk: 6Gi
    envVars:
      - name: JVM_PARAMS
        source: "value"
        value: "-Xms2393m -Xmx2393m -XX:MaxDirectMemorySize=1G"
#      - name: NEW_RELIC_LICENSE_KEY
#        source: "secret"
#        secretName: "nr-secret"
#        secretKey: "nr_license_key"
#      - name: NEW_RELIC_APP_NAME
#        source: value
#        value: "fintech-pinaka-prod-hyd"
#      - name: NEW_RELIC_PROXY_HOST
#        source: value
#        value: "*************"
#      - name: NEW_RELIC_PROXY_PORT
#        source: value
#        value: '"80"'
#    containerStartCommand: ["/bin/sh"]
#    containerStartCommandArgs: ["-c",  "java -Xmx6g -Xms4g -Djava.net.preferIPv4Stack=true -DaopType=GUICE  -Duser.language=en -Duser.region=CA -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -XX:+UseConcMarkSweepGC -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+PrintTenuringDistribution -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -Duser.timezone=\"Asia/Kolkata\" -jar /var/lib/app/service.jar server /etc/config/config.yml",  ";"]
#    containerStartCommandArgs: ["-f",  "/dev/null",  ";"]


volumes:
  - name: app-config
    type: configMap
    value: app-config
    mountPath: /etc/config/
  - name: app-log
    type: emptyDir
    value: {}
    mountPath: /var/log/
  - name: cfg-svc-metadata
    type: configMap
    value: cfg-svc-metadata
    mountPath: /etc/default/
  - name: relayer-config
    type: configMap
    value: relayer-config
    mountPath: /etc/fk-sc-mq/



configMaps:
  envName: hyd
  configs:
    - name: app-config
      source : dir
      sourceLoc : app
    - name: relayer-config
      source: dir
      sourceLoc: relayer
    - name: cosmos-config
      source: dir
      sourceLoc: cosmos
    - name: cfg-svc-metadata
      source: data
      data:
        cfg-api: |
          host=**********
          port=80
        fk-env: |
          prod

services:
  - name: pinaka
    type: LoadBalancer
    ports:
      - port: 9090 # useless but not to be changed
        targetPort: 9090
        protocol: TCP
        name: pinaka
    LoadBalancer:
      vip: fintech-pinaka-prod-vip-1
      healthCheckPort: 9091
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: fintech-pinaka-prod-vip-1
      backendPort: 9090
      frontendPort: 80
      mode: http

#secrets:
#  - name: nr-secret
#    type: Opaque
#    data:
#      - key: nr_license_key
#        value: "eyJkYXRhIjoidmF1bHQ6djE6MkpkVEZ3cXp1WDgzVjhKZUhaaFVSaW8vazdQTWlUQWdsejNXMjJzR1pmY3V3U1ArWVNkUUQxendaWTgyZFBLMjJpYjlDVW51cGRxbmd3bkFEOTdVTm9zRlpRUT0iLCJrZXlJZCI6ImFwbTo6bnItbGljZW5zZS1rZXkifQ=="
