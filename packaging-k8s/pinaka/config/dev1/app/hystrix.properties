hystrix.threadpool.USER_SERVICE.coreSize=100
hystrix.threadpool.USER_SERVICE.queueSizeRejectionThreshold=10
hystrix.threadpool.USER_SERVICE.maxQueueSize=10

hystrix.command.GET_ADDRESS_DETAILS.execution.isolation.thread.timeoutInMilliseconds=1000

hystrix.command.GET_USER_PROFILE.execution.isolation.thread.timeoutInMilliseconds=1000

hystrix.command.GET_NON_VERIFIED_USER_PROFILE.execution.isolation.thread.timeoutInMilliseconds=1000

hystrix.command.GET_ADDRESS_FROM_ACCOUNT_ID.execution.isolation.thread.timeoutInMilliseconds=1000

hystrix.command.GET_ALTERNATE_NUMBERS.execution.isolation.thread.timeoutInMilliseconds=1000

hystrix.threadpool.LOGIN_SERVICE.coreSize=100
hystrix.threadpool.LOGIN_SERVICE.queueSizeRejectionThreshold=10
hystrix.threadpool.LOGIN_SERVICE.maxQueueSize=10

hystrix.command.GENERATE_OTP_USING_ACCOUNT_ID.execution.isolation.thread.timeoutInMilliseconds=1000

hystrix.command.VERIFY_OTP_USING_ACCOUNT_ID_AND_LOGIN_ID.execution.isolation.thread.timeoutInMilliseconds=1000

hystrix.command.GET_VERIFIED_USER_RESPONSE.execution.isolation.thread.timeoutInMilliseconds=1000

hystrix.command.GET_USER_BY_LOGIN_ID.execution.isolation.thread.timeoutInMilliseconds=1000

hystrix.command.GET_CRI_DECISION.execution.isolation.thread.timeoutInMilliseconds=500

hystrix.command.FETCH_USER_COHORT.execution.isolation.thread.timeoutInMilliseconds=200

# default Settings

# API_POOL Settings
hystrix.threadpool.PL_WINTERFELL_API_POOL.coreSize=65
hystrix.threadpool.PL_WINTERFELL_API_POOL.maxQueueSize=100
hystrix.command.PL_CREATE_APPLICATION.execution.isolation.strategy=THREAD
hystrix.command.PL_CREATE_APPLICATION.execution.isolation.thread.timeoutInMilliseconds=50000
hystrix.command.PL_FETCH_APPLICATION_DATA.execution.isolation.strategy=THREAD
hystrix.command.PL_FETCH_APPLICATION_DATA.execution.isolation.thread.timeoutInMilliseconds=50000
hystrix.command.PL_RESUME_APPLICATION.execution.isolation.strategy=THREAD
hystrix.command.PL_RESUME_APPLICATION.execution.isolation.thread.timeoutInMilliseconds=30000
hystrix.command.PL_FETCH_ACTIVE_APPLICATION.execution.isolation.strategy=THREAD
hystrix.command.PL_FETCH_ACTIVE_APPLICATION.execution.isolation.thread.timeoutInMilliseconds=50000
hystrix.threadpool.PL_PANDORA_API_POOL.coreSize=25
hystrix.threadpool.PL_PANDORA_API_POOL.maxQueueSize=50
hystrix.threadpool.PL_PANDORA_EO_POOL.coreSize=20
hystrix.threadpool.PL_PANDORA_EO_POOL.maxQueueSize=40
hystrix.command.IDENTIFY_CUSTOMER.execution.isolation.strategy=THREAD
hystrix.command.IDENTIFY_CUSTOMER.execution.isolation.thread.timeoutInMilliseconds=90000
hystrix.command.ELIGIBLE_OFFER.execution.isolation.strategy=THREAD
hystrix.command.ELIGIBLE_OFFER.execution.isolation.thread.timeoutInMilliseconds=280000
hystrix.command.SUBMIT_OFFER.execution.isolation.strategy=THREAD
hystrix.command.SUBMIT_OFFER.execution.isolation.thread.timeoutInMilliseconds=60000
hystrix.command.KFS_PL.execution.isolation.strategy=THREAD
hystrix.command.KFS_PL.execution.isolation.thread.timeoutInMilliseconds=6000
hystrix.command.STATUS_PL.execution.isolation.strategy=THREAD
hystrix.command.STATUS_PL.execution.isolation.thread.timeoutInMilliseconds=6000
hystrix.threadpool.PL_ALFRED_API_POOL.coreSize=10
hystrix.threadpool.PL_ALFRED_API_POOL.maxQueueSize=20
hystrix.command.FETCH_USER_PROFILE.execution.isolation.strategy=THREAD
hystrix.command.FETCH_USER_PROFILE.execution.isolation.thread.timeoutInMilliseconds=20000
