{"servers": [{"prefix": "sm-pinaka-preprod", "port": "9311", "host": "localhost", "process": "sm-pinaka-preprod", "queries": [{"obj": "instrumentation:name=*", "saperator": ".", "attr": ["50thPercentile", "95thPercentile", "99thPercentile", "999thPercentile", "Max", "Mean", "Min", "OneMinuteRate", "RateUnit", "Count", "DurationUnit", "Value"], "typeNameModifiers": [{"typeNameMatcher": "instrumentation.(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*)", "typeNameGenerator": "metrics.${1}", "tags": [{"name": "${2}", "valueGenerator": "${3}"}, {"name": "${4}", "valueGenerator": "${5}"}, {"name": "${6}", "valueGenerator": "${7}"}, {"name": "${8}", "valueGenerator": "${9}"}]}, {"typeNameMatcher": "instrumentation.(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*)", "typeNameGenerator": "metrics.${1}", "tags": [{"name": "${2}", "valueGenerator": "${3}"}, {"name": "${4}", "valueGenerator": "${5}"}, {"name": "${6}", "valueGenerator": "${7}"}]}, {"typeNameMatcher": "instrumentation.(.*?)___(.*?)___(.*?)___(.*?)___(.*)", "typeNameGenerator": "metrics.${1}", "tags": [{"name": "${2}", "valueGenerator": "${3}"}, {"name": "${4}", "valueGenerator": "${5}"}]}, {"typeNameMatcher": "instrumentation.(.*?)___(.*?)___(.*)", "typeNameGenerator": "metrics.${1}", "tags": [{"name": "${2}", "valueGenerator": "${3}"}]}]}, {"obj": "metrics:name=*", "saperator": ".", "typeNameModifiers": [{"typeNameMatcher": "metrics.(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___\\.(.*)", "typeNameGenerator": "metrics.${1}.${10}", "tags": [{"name": "${2}", "valueGenerator": "${3}"}, {"name": "${4}", "valueGenerator": "${5}"}, {"name": "${6}", "valueGenerator": "${7}"}, {"name": "${8}", "valueGenerator": "${9}"}]}, {"typeNameMatcher": "metrics.(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___\\.(.*)", "typeNameGenerator": "metrics.${1}.${8}", "tags": [{"name": "${2}", "valueGenerator": "${3}"}, {"name": "${4}", "valueGenerator": "${5}"}, {"name": "${6}", "valueGenerator": "${7}"}]}, {"typeNameMatcher": "metrics.(.*?)___(.*?)___(.*?)___(.*?)___(.*?)___\\.(.*)", "typeNameGenerator": "metrics.${1}.${6}", "tags": [{"name": "${2}", "valueGenerator": "${3}"}, {"name": "${4}", "valueGenerator": "${5}"}]}, {"typeNameMatcher": "metrics.(.*?)___(.*?)___(.*?)___\\.(.*)", "typeNameGenerator": "metrics.${1}.${4}", "tags": [{"name": "${2}", "valueGenerator": "${3}"}]}, {"typeNameMatcher": "metrics.(.*)", "typeNameGenerator": "metrics.${1}", "tags": []}]}], "numQueryThreads": 2}]}