hystrix.threadpool.USER_SERVICE.coreSize=100
hystrix.threadpool.USER_SERVICE.queueSizeRejectionThreshold=10
hystrix.threadpool.USER_SERVICE.maxQueueSize=10
hystrix.command.GET_ADDRESS_DETAILS.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_USER_PROFILE.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_NON_VERIFIED_USER_PROFILE.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_ADDRESS_FROM_ACCOUNT_ID.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_ALTERNATE_NUMBERS.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.threadpool.LOGIN_SERVICE.coreSize=100
hystrix.threadpool.LOGIN_SERVICE.queueSizeRejectionThreshold=10
hystrix.threadpool.LOGIN_SERVICE.maxQueueSize=10
hystrix.command.GENERATE_OTP_USING_ACCOUNT_ID.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.VERIFY_OTP_USING_ACCOUNT_ID_AND_LOGIN_ID.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_VERIFIED_USER_RESPONSE.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_USER_BY_LOGIN_ID.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.threadpool.WINTERFELL.coreSize=1000
hystrix.threadpool.WINTERFELL.queueSizeRejectionThreshold=3000
hystrix.threadpool.WINTERFELL.maxQueueSize=5000
hystrix.command.CREATE_APPLICATION.execution.isolation.thread.timeoutInMilliseconds=5000
hystrix.command.RESUME_APPLICATION.execution.isolation.thread.timeoutInMilliseconds=30000
hystrix.command.FETCH_APPLICATION_DATA.execution.isolation.thread.timeoutInMilliseconds=5000
hystrix.command.FETCH_ACTIVE_APPLICATION.execution.isolation.thread.timeoutInMilliseconds=500
hystrix.threadpool.TIJORI.coreSize=100
hystrix.threadpool.TIJORI.queueSizeRejectionThreshold=10
hystrix.threadpool.TIJORI.maxQueueSize=10
hystrix.command.FETCH_LOAN_ACCOUNT.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.threadpool.ROBINHOOD.coreSize=200
hystrix.threadpool.ROBINHOOD.queueSizeRejectionThreshold=10
hystrix.threadpool.ROBINHOOD.maxQueueSize=200
hystrix.command.FETCH_ACCOUNT_STATUS.execution.isolation.thread.timeoutInMilliseconds=100
hystrix.threadpool.LOCKIN.coreSize=100
hystrix.threadpool.LOCKIN.queueSizeRejectionThreshold=10
hystrix.threadpool.LOCKIN.maxQueueSize=10
hystrix.command.FETCH_PLUS_CUSTOMER.execution.isolation.thread.timeoutInMilliseconds=500
hystrix.threadpool.GEO_LOCATION.coreSize=100
hystrix.threadpool.GEO_LOCATION.queueSizeRejectionThreshold=10
hystrix.threadpool.GEO_LOCATION.maxQueueSize=10
hystrix.command.FETCH_GEO_LOCATION.execution.isolation.thread.timeoutInMilliseconds=3500
hystrix.threadpool.CRI.coreSize=200
hystrix.threadpool.CRI.queueSizeRejectionThreshold=10
hystrix.threadpool.CRI.maxQueueSize=200
hystrix.command.GET_CRI_DECISION.execution.isolation.thread.timeoutInMilliseconds=500
hystrix.threadpool.ALFRED.coreSize=200
hystrix.threadpool.ALFRED.queueSizeRejectionThreshold=10
hystrix.threadpool.ALFRED.maxQueueSize=200
hystrix.command.FETCH_USER_COHORT.execution.isolation.thread.timeoutInMilliseconds=200
hystrix.threadpool.CAISHEN.coreSize=100
hystrix.threadpool.CAISHEN.queueSizeRejectionThreshold=10
hystrix.threadpool.CAISHEN.maxQueueSize=10
hystrix.command.FETCH_CAISHEN_PROFILE.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.threadpool.FKPAY.coreSize=100
hystrix.threadpool.FKPAY.queueSizeRejectionThreshold=10
hystrix.threadpool.FKPAY.maxQueueSize=10
hystrix.command.FETCH_BANK_ACCOUNT.execution.isolation.thread.timeoutInMilliseconds=1000

# default Settings

# API_POOL Settings
hystrix.threadpool.PL_WINTERFELL_API_POOL.coreSize=65
hystrix.threadpool.PL_WINTERFELL_API_POOL.maxQueueSize=100
hystrix.command.PL_CREATE_APPLICATION.execution.isolation.strategy=THREAD
hystrix.command.PL_CREATE_APPLICATION.execution.isolation.thread.timeoutInMilliseconds=5000
hystrix.command.PL_FETCH_APPLICATION_DATA.execution.isolation.strategy=THREAD
hystrix.command.PL_FETCH_APPLICATION_DATA.execution.isolation.thread.timeoutInMilliseconds=5000
hystrix.command.PL_RESUME_APPLICATION.execution.isolation.strategy=THREAD
hystrix.command.PL_RESUME_APPLICATION.execution.isolation.thread.timeoutInMilliseconds=30000
hystrix.command.PL_FETCH_ACTIVE_APPLICATION.execution.isolation.strategy=THREAD
hystrix.command.PL_FETCH_ACTIVE_APPLICATION.execution.isolation.thread.timeoutInMilliseconds=500
hystrix.threadpool.PL_PANDORA_API_POOL.coreSize=25
hystrix.threadpool.PL_PANDORA_API_POOL.maxQueueSize=50
hystrix.threadpool.PL_PANDORA_EO_POOL.coreSize=20
hystrix.threadpool.PL_PANDORA_EO_POOL.maxQueueSize=40
hystrix.command.IDENTIFY_CUSTOMER.execution.isolation.strategy=THREAD
hystrix.command.IDENTIFY_CUSTOMER.execution.isolation.thread.timeoutInMilliseconds=9000
hystrix.command.ELIGIBLE_OFFER.execution.isolation.strategy=THREAD
hystrix.command.ELIGIBLE_OFFER.execution.isolation.thread.timeoutInMilliseconds=28000
hystrix.command.SUBMIT_OFFER.execution.isolation.strategy=THREAD
hystrix.command.SUBMIT_OFFER.execution.isolation.thread.timeoutInMilliseconds=6000
hystrix.command.KFS_PL.execution.isolation.strategy=THREAD
hystrix.command.KFS_PL.execution.isolation.thread.timeoutInMilliseconds=6000
hystrix.command.STATUS_PL.execution.isolation.strategy=THREAD
hystrix.command.STATUS_PL.execution.isolation.thread.timeoutInMilliseconds=6000
hystrix.threadpool.PL_ALFRED_API_POOL.coreSize=10
hystrix.threadpool.PL_ALFRED_API_POOL.maxQueueSize=20
hystrix.command.FETCH_USER_PROFILE.execution.isolation.strategy=THREAD
hystrix.command.FETCH_USER_PROFILE.execution.isolation.thread.timeoutInMilliseconds=200
hystrix.command.GET_TNS_DETAILS.execution.isolation.strategy=THREAD
hystrix.command.GET_TNS_DETAILS.execution.isolation.thread.timeoutInMilliseconds=150
