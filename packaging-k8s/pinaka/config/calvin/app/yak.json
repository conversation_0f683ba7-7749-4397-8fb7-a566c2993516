{"pool_size": 200, "bootstrap_timeout": 30, "default_configs": {"hbase.client.retries.number": "3", "hbase.client.max.perserver.tasks": "20", "hbase.client.max.perregion.tasks": "2", "hbase.client.ipc.pool.type": "RoundRobinPool", "hbase.client.ipc.pool.size": "20", "hbase.client.operation.timeout": "1000", "hbase.client.meta.operation.timeout": "1000", "hbase.rpc.shortoperation.timeout": "400", "hbase.rpc.timeout": "1000", "hbase.yak.payload.threshold": "102400"}, "zone_configs": {"IN_CHENNAI_1": {"sites": {"siteA": {"hbaseConfig": {"hbase.zookeeper.quorum": "prod-yak-archive-ch-3-zk-0.prod-yak-archive-ch-3.yak-archive-prod.svc.cluster.local,prod-yak-archive-ch-3-zk-1.prod-yak-archive-ch-3.yak-archive-prod.svc.cluster.local,prod-yak-archive-ch-3-zk-2.prod-yak-archive-ch-3.yak-archive-prod.svc.cluster.local,prod-yak-archive-ch-3-zk-3.prod-yak-archive-ch-3.yak-archive-prod.svc.cluster.local,prod-yak-archive-ch-3-zk-4.prod-yak-archive-ch-3.yak-archive-prod.svc.cluster.local", "zookeeper.session.timeout": "1000", "zookeeper.recovery.retry": "0", "hbase.client.retries.number": "3", "hbase.client.max.perserver.tasks": "20", "hbase.client.max.perregion.tasks": "2", "hbase.client.ipc.pool.type": "RoundRobinPool", "hbase.client.ipc.pool.size": "20", "hbase.client.operation.timeout": "1000", "hbase.client.meta.operation.timeout": "1000", "hbase.rpc.shortoperation.timeout": "400", "hbase.rpc.timeout": "1000", "hbase.yak.payload.threshold": "102400"}}}}}}