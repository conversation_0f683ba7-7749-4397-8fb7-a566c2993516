server:
  type: default
  applicationContextPath: /pinaka
  adminContextPath: /admin
  applicationConnectors:
    - type: http
      port: 9090
  adminConnectors:
    - type: http
      port: 9091
  requestLog:
    # Note:
    # 1. We do not specify a logFormat because the default log format is Common Log Format (CLF), which is good
    # 2. If we specify a logFormat, note that the format specifiers for access logs are different
    # 3. Access log accepts ONLY ONE appender - we cannot get access logs on both console and file simultaneously

    appenders:
      - type: file
        currentLogFilename: /var/log/pinaka/access.log
        threshold: ALL
        archive: true
        archivedLogFilenamePattern: /var/log/pinaka/access-%i.log.gz
        archivedFileCount: 5
        timeZone: IST
        maxFileSize: 250MB


logging:
  level: INFO
  loggers:
    "org.hibernate": ERROR
    "com.flipkart.affordability.clients.oauth": ERROR
    "com.flipkart.restbus.client.shards": ERROR
    "com.flipkart.abservice.resources": ERROR
    "org.hibernate.SQL":
      level: INFO
      additive: false
      appenders:
        - type: file
          currentLogFilename: /var/log/pinaka/pinaka-sql.log
          archivedLogFilenamePattern: /var/log/pinaka/pinaka-sql-%i.log.gz
          archivedFileCount: 6
          maxFileSize: 250MB
  appenders:
    - type: file
      threshold: ALL
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] %highlight([%level]) [%thread] [%X{trace_id}] [%X{X-Request-ID}] [%cyan(%logger{36})]: %message %replace(%exception){'\n',' | '} %n %nopex"
      currentLogFilename: /var/log/pinaka/pinaka.log
      archivedLogFilenamePattern: /var/log/pinaka/pinaka-%i.log.gz
      archivedFileCount: 5
      timeZone: IST
      maxFileSize: 250MB
    - type: console
      threshold: ALL
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] %highlight([%level]) [%thread] [%X{trace_id}] [%X{X-Request-ID}] [%cyan(%logger{36})]: %message%n"
      timeZone: IST
      target: stdout

#healthCheckName: PinakaHealth

swagger:
  title: Pinaka Service
  description: APIs for Pinaka Service
  contact: <EMAIL>
  resourcePackage: com.flipkart.fintech.pinaka.service.web

profileServiceConfig:
  experianConfig:
    ttl: 15552000000
    showRefreshButton: 129600
  experianMocked: false
  smUserServiceClientConfig:
    url: "http://kavach-service-prod.kavach-prod.fkcloud.in"
    clientId: "supermoney"
  upiUserServiceClientConfig:
    url: "http://sm-upi-user-service.sm-upi-user-service-prod.fkcloud.in/sm/upi/user-svc"
  publisherConfig:
    projectId: sm-personal-loans
    topicId: preapproved-blackbox-offers

profileClientConfiguration:
  url: http://pinaka-service.sm-pinaka-prod.fkcloud.in/
  client: profile-client

eventPublishEnvironment: "PROD"


databaseConfig:
  encryptedUrl: eyJkYXRhIjoidmF1bHQ6djE6clhsZG9meXZ2bGVHSGZEMXZrdFhoVFpucmo2SEVwOGg0UTAwb0NXNkZxa2lVeVNybTgwTG8ya0YveWFUcmhYM01pOXBTUU1yUk1rQ3lHYVZYcDdrRTFJU2hWR0UzR3hzVkJXZUFZUWhCcjdLUDJxSERXWDlDb0lDUGZUWmNLWjlkYnZzOHNLZmp4TGd5Tkl1SlZuQ0FxYWQzc21xalFtTmRsVThuYXNibFArTnMyYXhnd254VUs3NVpHWWg1V2J2Zkg1WmY3a2FXbjE5Q0w5dDRDNEZ6UT09Iiwia2V5SWQiOiJMZW5kaW5nX0xPUzo6ZGVmYXVsdCJ9
  encryptedUser: eyJkYXRhIjoidmF1bHQ6djE6NWNrQncranBWMFlKVXQ5MHp1eWtHVFZ5QnNMRVowb0YyT29zc1dHbFU4bDVvRXpDSzhlZDRGdGx4R0k9Iiwia2V5SWQiOiJMZW5kaW5nX0xPUzo6ZGVmYXVsdCJ9
  encryptedPassword: eyJkYXRhIjoidmF1bHQ6djE6SC9BOERCczZBaEkvMlluSm9jbU5peWwxNUNTcEdlZkcyditObUVTWVFhM1ZzOFMwT3BuS1FIamM4aFlkZlF5MCIsImtleUlkIjoiTGVuZGluZ19MT1M6OmRlZmF1bHQifQ==
  driverClass: com.mysql.cj.jdbc.Driver
  properties:
    charSet: UTF-8
    hibernate.dialect: org.hibernate.dialect.MySQL57Dialect
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 8
    # the minimum number of connections to keep open
    minSize: 8
    # the maximum number of connections to keep open
    maxSize: 400
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

databaseSlaveConfig:
  slaveEncryptedUrl: eyJkYXRhIjoidmF1bHQ6djE6ZkNGTHByWVN1MTZhMTdybzVCZ2VicFBtTUswYmpUUFFoakE3cFNMb1NKemJIVGlVNUNXNVYyK0JLOFNIdzRzR2tMVUk0cldUNmJkR1oyMXlSZjFVbzdoczFCdXFoQk4ra2c1cWxGYWJ0Zmx0eTdiZXBvOFRwc2o2QjhSYW55dDdxcCt3amJPNXBZRjlzOHVoTU5OR1hMY1hNNFZRMVF3RXdvOW54RTNKQ0l2aGU1OW1uZlNITHBvVzN3YlFWNmxkUWRKeEpYQVRRbU1FSkY2aVJxeHpYY0psVHJhcCIsImtleUlkIjoiTGVuZGluZ19MT1M6OmRlZmF1bHQifQ==
  slaveEncryptedUser: eyJkYXRhIjoidmF1bHQ6djE6NWNrQncranBWMFlKVXQ5MHp1eWtHVFZ5QnNMRVowb0YyT29zc1dHbFU4bDVvRXpDSzhlZDRGdGx4R0k9Iiwia2V5SWQiOiJMZW5kaW5nX0xPUzo6ZGVmYXVsdCJ9
  slaveEncryptedPassword: eyJkYXRhIjoidmF1bHQ6djE6SC9BOERCczZBaEkvMlluSm9jbU5peWwxNUNTcEdlZkcyditObUVTWVFhM1ZzOFMwT3BuS1FIamM4aFlkZlF5MCIsImtleUlkIjoiTGVuZGluZ19MT1M6OmRlZmF1bHQifQ==
  driverClass: com.mysql.cj.jdbc.Driver
  properties:
    charSet: UTF-8
    hibernate.dialect: org.hibernate.dialect.MySQL57Dialect
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 2
    # the minimum number of connections to keep open
    minSize: 2
    # the maximum number of connections to keep open
    maxSize: 1000
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

profileDatabaseConfig:
  encryptedUrl: ************************************************************************************
  encryptedUser: sm_rw
  encryptedPassword: CksdfqaQyI
  driverClass: com.mysql.cj.jdbc.Driver
  properties:
    charSet: UTF-8
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 8
    # the minimum number of connections to keep open
    minSize: 8
    # the maximum number of connections to keep open
    maxSize: 400
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

rateLimitingConfig:
  [
    {"limiterKey" : "FETCH_UI",  "timeoutInMs":5},
    {"limiterKey" : "PAN_SUBMIT",  "timeoutInMs":5},
    {"limiterKey" : "AADHAR_VERIFICATION",  "timeoutInMs":5},
    {"limiterKey" : "GENERATE_OTP",  "timeoutInMs":5},
    {"limiterKey" : "CREATE_APPLICATION",  "timeoutInMs":5},
    { "limiterKey": "PENNY_DROP_SUBMIT_DETAILS",  "timeoutInMs":5 },
    { "limiterKey": "FETCH_LOADER",  "timeoutInMs":5 },
    { "limiterKey": "VERIFY_OTP", "timeoutInMs":5 },
    { "limiterKey": "GEO_LOCATION", "timeoutInMs": 5 }
  ]

cryptexConfiguration:
  cryptexBundleEnabled: true
  authNClientConfig:
    url: https://service.authn-prod.fkcloud.in
    clientId: cryptex-pinaka
    clientSecret: J5WKAwxBOxWgOQVEx+0LHFOO3TefTQ2cguFovYFpbFeDEVPX

  cryptexClientConfig:
    endpoint: https://service.cryptex-prod.fkcloud.in
    maxConnections: 5
    connectTimeOut: 1500
    readTimeOut: 1500

  dynamicBucketConfig:
    bucketName: sm-pinaka-prod
    enableLocalDynamicBucket: false

pinakaClientConfig:
  url: http://************:80
  client: pinaka

ardourClientConfig:
  url: http://10.83.37.172:80
  client: pinaka

userServiceClientConfig:
  usUrl: http://10.83.37.92:80
  usClientId: affordability

oAuthServiceClientConfig:
  oAuthUrl: http://************:80
  oAuthClientID : c2c95e0075c04b2e9b83e1bc8a09f57e
  oAuthClientSecret : FVMWGlJ22AoWfQ+ithjYn+M0tjr8xgakuHl06J5A+DELtVtc
  cachedAccessTokenTTL : 120

loginServiceClientConfig:
  loginServiceUrl: http://***********:80
  loginServiceClientId: affordability

fluxAsyncClientConfig:
  url: dummy
  clientId: dummy
  exchangeName: dummy

connektClientConfig:
  exchangeName: fintech_los_connekt
  callbackUrl: http://************/pinaka/communications
  domain: flipkart
  emailUrl: http://************
  pnUrl: http://************
  smsUrl: http://************
  inAppUrl: http://************
  emailApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  pnApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  smsApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  inAppApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  emailAppName: flipkart
  pnAppName: RetailApp
  smsAppName: flipkart
  inAppName: flipkart
  emailBucket: alerts
  emailSubBucket: pay
  inAppBucket: alerts
  inAppSubBucket: pay
  pnBucket: alerts
  pnSubBucket: pay
  smsBucket: alerts
  smsSubBucket: pay
  pnChannelId: fk_channel_order_payments
  transactionalEmail: true
  transactionalPN: true
  transactionalSMS: true
  transactionalInApp: true


robinhoodAsyncClientConfig:
  url: http://************
  clientId: pinaka
  exchangeName: "%s_onboarding_2"

pandoraClientConfig:
  url: http://pandora-service.sm-pandora-prod.fkcloud.in/pandora
  connectTimeout: 20000
  readTimeout: 20000
  client: pinaka

varadhiClientConfig:
  url: http://************/
  topicName: sm_pl_application_state_prod


pandoraLiteClientConfig:
  url: http://**********/pandoralite
  client: pinaka

fluxConfiguration:
  fluxRuntimeUrl: dummy
  connectionTimeout: 10000
  socketTimeout: 10000

onboardingClientConfig:
  url: http://***********:8080
  client: pinaka

gibraltarConfig:
  maxPublicKeyCount: 100
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  trustStorePath: "/etc/gibraltar/prod-client-truststore.jks"
  trustStorePass: password
  keyStorePath: "/etc/gibraltar/prod-client-keystore.jks"
  keyStorePass: password
  certificateAlias: gibraltarSelfSigned
  url: https://*************:8443,https://***********:8443
  generateKeysOnStartUp: true
  connectionTimeoutInMs: 150
  socketTimeoutInMs: 300
  httpRequestRetryCount: 5

cryptoConfig:
  algo: AES
  secretKey: "Cd1/SXipT)So3=19"
  cbcTelesaleSecretKey: "cbc/Tele$@les(=3"

underwritingConfig:
  endPoint: "http://***********:80/fintech-underwriting"
lenderConfiguration:
  configurations:
    EFA:
      -
        consentTextKey: efa-citi-consent-text
        hasAdditionalForm: true
        interestRate: 12
        emiSupported: true
        paylaterSupported: false
        lateFees: 15
        lender: CITI
        postApprovalTncKey: efa-citi-postapproval-tnc
        preApprovalTncKey: efa-citi-preapproval-tnc
        showKycDetails: false
        primaryDataEditable: false
      -
        consentTextKey: efa-consent-text
        hasAdditionalForm: false
        interestRate: 20
        emiSupported: true
        paylaterSupported: true
        lateFees: 20
        lender: KISSHT
        postApprovalTncKey: efa-postapproval-tnc
        preApprovalTncKey: efa-preapproval-tnc
        showKycDetails: true
        primaryDataEditable: true
      -
        consentTextKey: efa-consent-text
        hasAdditionalForm: false
        interestRate: 20
        emiSupported: true
        paylaterSupported: true
        lateFees: 20
        lender: INDIA_BULLS
        postApprovalTncKey: efa-postapproval-tnc
        preApprovalTncKey: efa-preapproval-tnc
        showKycDetails: true
        primaryDataEditable: true

    BNPL:
      -
        consentTextKey: dummy
        hasAdditionalForm: false
        interestRate: 0
        emiSupported: false
        paylaterSupported: true
        lateFees: 0
        lender: KISSHT
        postApprovalTncKey: dummy
        preApprovalTncKey: dummy
        showKycDetails: false
        primaryDataEditable: false
        minimumAgeRequired: 0
      -
        hasAdditionalForm: false
        lender: IDFC
        primaryDataEditable: true
        nameMatchThreshold: 65
        nameValidationEnabled: true
        panValidationRequired: false
        postApprovalTncKey: dummy
        preApprovalTncKey: dummy
        consentTextKey: dummy

    FLIPKART_ADVANZ:
      -
        hasAdditionalForm: false
        lender: IDFC
        primaryDataEditable: true
        nameMatchThreshold: 70
        nameValidationEnabled: true
        panValidationRequired: false
        postApprovalTncKey: dummy
        preApprovalTncKey: dummy
        consentTextKey: dummy
        minimumAgeRequired: 18

    CBC:
      -
        lender: AXIS

tijoriConfig:
  url: http://************:80
  clientName: robinhood

tijoriAsyncClientConfig:
  merchant: mp_flipkart
  exchangeName: lms_onboarding_queue
  tijoriUrl: http://************:80

hawkeyeAsyncConfig:
  client: FK_CONSUMER_CREDIT
  exchangeName: hawkeye_dedupe_ingestion
  url: http://************/cf-events/v1/identity-aggregation

external_client_config:
  page_service_config:
    host: ***********
    port: 80

collectionsClientConfiguration:
  scheme: http
  host: ************
  port: 7980

contextWiseWhitelistConfig:
  contextMap:
    NUDGING-EFA-INDIA_BULLS:
      isEnabledForEveryone: true
      whitelistedAccountIds: []
    NUDGING-CBC-AXIS:
      isEnabledForEveryone: true
      whitelistedAccountIds: ["ACCB4DEB84ADC7F4558B066A1ED494509ECR","ACCD1FAFCA251E848B48031CB2D42EBD100H"]
    NUDGING-CBC_SC_ELITE-AXIS:
      isEnabledForEveryone: true
      whitelistedAccountIds: [ ]
    NUDGING-CBC_SELECTION-AXIS:
      isEnabledForEveryone: true
      whitelistedAccountIds: []
    NPS-CBC-AXIS:
      isEnabledForEveryone: true
      whitelistedAccountIds: ["ACC1A75EB1E55584AE89225A4B9CC7232FCT"]
    NUDGING-FLIPKART_ADVANZ-IDFC:
      isEnabledForEveryone: true
      whitelistedAccountIds: []

heliosProxyConfig:
  maxConnections: 1024
  maxConnectionsPerHost: 1024
  pooledConnectionIdleTimeoutInMS: 6000
  executorThreadsCount: 32
  executionIsolationSemaphoreMaxConcurrentRequests: 1024
  executionTimeoutInMS: 10000
  hystrixCommandKey: PinakaServiceHystrixCommand
  hystrixGroupKey: PinakaServiceHystrixGroup
  rewriteProtocal: http
  rewriteHostName: 127.0.0.1
  rewritePortNumber: 80
  enableProxy: false


fldgConfiguration:
  enableFldg: false

abConfiguration:
  endpoint: ***********:80
  layerList: "Affordability"
  clientId: "Mapi.affordability"
  testAccounts: ["ACC14125653191935118","ACC2DEDB3BC47054F818FAA8E680072E011R","ACC8B2427BE8D354EAF9D94046347FDA13ES","ACC13751787976165289","ACCFGSS5XIRIFCRNJN4ESU8U0C16CYFD","ACVJUFRNA0C62TSCCU8X6LY1OFGWJ9KC","ACCF152B52D9AD444E0B7BFF9781CF15672U","ACC209807A5FBBD42A6866DD717E9F63190V","ACC13902857290074280","ACCE73189B3192B406AA7A5EF89A31CAC51M", "ACCD2A42BF9E2744EC2AA90D68A66CC05B7F", "ACC14159601674158924", "ACC18D1EA7880024BC8BF3C1B2503D19375U", "ACC0963D770D9D34715B0CBCC68731BC12D4", "ACC00A1D9FD30B148329F8B7A5C2C51BD40E", "ACC2CBE947F34F94764B1C7A2D2A134E012C", "ACCB92329A0DAB047A89FED58A61552C01F3", "ACC0D47F8B9AE4B4D88A133354DD8D23723M", "ACCCBE6B65DCCF546C78D6B6CC4C97851E2O", "ACC0ACDB8083FCB40C4BAE7DABD4971A28FZ", "ACCF15CDBB28108421B972ACD9021AA29C9O", "ACC2C0136173EF746E4AD7F941E9108A7CFG", "ACC5CD31F79023C4084B6183B7D66A299D9C", "ACC14076817150162100", "ACCACC54415130E4D5196C215FF591E6C28Q", "ACC338CF791A95C42CF824458C07F6BC9B5"]
  unlayeredAbEnabled: true
  clientSecretKey: fintech-pinaka-prod-ee4b7347a0ee409f88cccb70d54238c1

onboardingConfig:
  upgradeJourneyLimitReductionAllowed: false
  genericFlowEnabled: true
  whitelistChangeEntityEnabled: true
  genericFlowAccountIds: ["ACC8B2427BE8D354EAF9D94046347FDA13ES","ACC76FCADFC7B3A443F939DFE42003603AAI","ACC13949080388889691","ACCFGSS5XIRIFCRNJN4ESU8U0C16CYFD","AC3QOAOVQY564L9UUF32F1IK337QJSJI","AC2NB2C5YC767IW4L0BWERK8QLHZ7NZV","ACC76FCADFC7B3A443F939DFE42003603AAI","ACCA82A18F3B68446A0B6778880AD04B619A","AC3QOAOVQY564L9UUF32F1IK337QJSJI","AC6XS5CP996NNX4ORNUBFFRY0AFLIDK7","ACCDE528BE0481E44D8A1AA659CE5C16552R","********************************","ACZR6TK7PW29IW7THD7EN5P4ORU0NBH5","ACC3CF41AD0525A48F8AC35FE4F4A7E55EBR","ACC13650015713567535","ACC13707716386068457","ACGCV7V9OWMYM7II11B3R2GIKI209AFJ","ACC14020001643342490","ACDJS2DWR3QKMKXB4JIFS30T0HJMUR4T","ACCA82A18F3B68446A0B6778880AD04B619A","ACC7D1E281CA77448E084C1430D789B4679N","ACMM3GTMZ561LL5B8TEIBSSFTU722MTU","ACCFGSS5XIRIFCRNJN4ESU8U0C16CYFD","ACC14125653191935118","ACC3CF41AD0525A48F8AC35FE4F4A7E55EBR","ACC4C5070D22EBD4F26A017B71167444CFCW","ACSCAJ0F4M9XEIQ1LZUFRE06NUSG7QGR","ACC1390841791736163","AC2NB2C5YC767IW4L0BWERK8QLHZ7NZV","ACC76FCADFC7B3A443F939DFE42003603AAI","ACCA82A18F3B68446A0B6778880AD04B619A","AC3QOAOVQY564L9UUF32F1IK337QJSJI","AC6XS5CP996NNX4ORNUBFFRY0AFLIDK7","ACCDE528BE0481E44D8A1AA659CE5C16552R","********************************","ACZR6TK7PW29IW7THD7EN5P4ORU0NBH5","ACC3CF41AD0525A48F8AC35FE4F4A7E55EBR","ACC13650015713567535","ACC13707716386068457","ACGCV7V9OWMYM7II11B3R2GIKI209AFJ","ACC14020001643342490","ACQ23LU0JFDB2RR6K07NG1IJ7ZL5NQYI","ACLW8SA2B1HF8REY6E874HIC6RWY1PU4","ACC13906123955101848","ACXQ0MEGDT4TMQQK2L6MKVNC1EF4Z5LG","ACC13448758795321772","ACC34E011F6573C49F99454A57DDFCA0233A","ACX3S9XRMJCHNKMH24QUW32787JTPLG2","AC1YGH19NG7TA2EHE96X3KJAKKRKCIG9","ACC13949080388889691","ACLBEUUIJDCIIH8O9GIAGTBVQS27UT40","ACC13697461245573315","ACC89DF564F153749BFBAEE53BD69E006C7W","ACC13494141454499528","ACC13697461245573315","ACX3S9XRMJCHNKMH24QUW32787JTPLG2","ACC13909443445886072","ACC14241680542752590","ACCF5245FAC1FFD4AE78CE7A40D23375D88D","AC5KM3V9NOUYTQ9NAYL4V1SH8HHKRCVB","ACVLYN1JH42Y56COO3VHMB2E1R6K48MG","ACJFFESW6TOWJS6RKG2TI0XG0UCSCXZ9","ACC14051029814219831","ACL4KJTNNZBC5MKAZUA70XDRZYVIOR1M","ACC13765478998789162","ACCD11FB4A20FC94FEEACA4D04F40E4749EW","ACC13455580597092172"]
  plusWhitelistId: 135
  

alfredClientConfig:
  url: "http://************:80"
  secretKey: "123rewedrgrvcz"

dexterClientConfig:
  url: "http://************:80"
  targetClientId: "dexter-prod"

neoCrmClientConfig:
  exchangeName: "fintech_cf_scheduler"
  authToken: "LVNKY7IAABXXAAZB"
  clientId: "Fintech"
  url: "http://***********"
  path: "/v1/crm/publish"

uiConfiguration:
  payLaterCohortPriorityList: ["CASH","PAY_LATER", "EMI", "FSUP"]
  fsupCohortPriorityList: ["CASH", "FSUP", "PAY_LATER", "EMI"]
  cohorts:
    CASH:
      name: Cash Loan
      full_description: For withdrawal upto ₹%s
    EMI:
      name: EMIs
      full_description: For purchases upto ₹%s
    PAY_LATER:
      name: Pay Later
      full_description: Pay next month for shopping upto ₹%s
      minimized_description: Flipkart Pay Later enabled
      logo_url: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/07/2021/9a80ffd5-d436-48b8-bec9-4abcf4ca444e.png?q={@quality}
    FSUP:
      name: Smart Upgrade
      full_description: Get your phone by paying just a fraction of amount upfront
      minimized_description: Flipkart Smart Upgrade enabled
      logo_url: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/08/2021/76e1349c-dc49-4cd7-bc04-c4aa36b2a035.png?q={@quality}
  uiConfigMap:
    APPROVED:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle : "We thank you for showing interest and taking the effort to complete the application, however, based on the information provided, our financing partner’s internal policies, and anti-fraud checks, we are unable to extend “Flipkart Pay Later” to you. Please continue your shopping using numerous other payment modes available on Flipkart."
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month, or pay in easy EMIs of upto 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/24/09/2020/7db826c0-8301-45fb-8b04-a787a6ac294d.png?q=100"
      knowMoreText: "Learn more about Pay Later"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Go to Home Page"
      payLaterSubmitButtonUrl: "https://www.flipkart.com/"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 5th of that month. After 5th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          -
            description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          -
            description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          -
            description: "Pay your bills by 5th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    LOAN_CREATION_IN_PROGRESS:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle: "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "knowMoreText"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          - description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          - description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          - description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    CONDITIONALLY_APPROVED:
      title: "Congratulations"
      nonUpgradedTitle: "Congratulations"
      nonUpgradedSubtitle : "Based on your details, here are some exciting benefits for you!"
      emiSubtitle: "Based on your details, here are some exciting benefits for you!"
      payLaterSubtitle: "Based on your details, here are some exciting benefits for you!"
      imageUrl: ""
      nonUpgradedImageUrl: ""
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/24/09/2020/7db826c0-8301-45fb-8b04-a787a6ac294d.png?q=100"
      knowMoreText: "Know more about Flipkart Pay Later"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Activate Flipkart Pay Later"
      payLaterSubmitButtonUrl: "https://www.flipkart.com/"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 5th of that month. After 5th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          -
            description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          -
            description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          -
            description: "Pay your bills by 5th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
    POST_PROCESSING_PENDING:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle: "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "knowMoreText"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          - description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          - description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          - description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    REJECTED:
      title: "Thank you for your interest in %s"
      subtitle: "Due to our financial partner policies, we are not able to provide a credit line at this moment"
      payLaterSubmitButtonText: "Go to Home Page"
      payLaterSubmitButtonUrl: "https://www.flipkart.com/"
      fsupSubmitButtonText: "Back to product page"
      imageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      nonUpgradedSubtitle : "We thank you for showing interest and taking the effort to complete the application, however, based on the information provided, our financing partner’s internal policies, and anti-fraud checks, we are unable to extend “Flipkart Pay Later” to you. Please continue your shopping using numerous other payment modes available on Flipkart."



mysqlLockConfig:
  prefix: ""
  timeout: 0

dataPointConfiguration:
  enableDataPoint:
    TS:
      enable: false
      testAccountList: ["ACC8B2427BE8D354EAF9D94046347FDA13ES"]

redisConfig:
  masterName: fintech-redis-prod
  timeout: 5000
  sentinelAddresses: ["redis://*************:26379","redis://************:26379","redis://*************:26379"]
  password: ABtr@25
  masterConnectionMinimumIdleSize: 3
  masterConnectionPoolSize: 100
  lockWaitTime: 10
  lockReleaseTime: 3

sourceAttributionConfig:
  enabled: true
  redisTtlInSeconds: 54000

rotation:
  enableRotator: true
  defaultRotationStatus: true

winterfellClientConfig:
  host: http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell
  clientId: pinaka
  connectionTimeout: 32000
  readTimeout: 32000


lockinClientConfig:
  url: http://************
  clientId: Fintech

coreLogisticsClientConfig:
  url: http://**********
  referer: "http://www.fintech.com"
  key: "79a271ce-b870-c03b-cdbb-a52861a90ea9"

pandoraAsyncClientConfig:
  url: http://************/pandora
  exchangeName: advanz_onboarding_queue
  exchangeType: queue



kafkaConsumerConfiguration:
  enableConsumer: true
  kafkaConsumerProps:
    AMS:
      bootStrapServers: ************:9092,**********:9092,*************:9092,************:9092,************:9092
      groupId: fk-consumer-credit-pinaka-prod-group
      sessionTimeoutMs: 30000
      noOfThreads: 3
      topic: fintech_los_fk_consumer_credit_topic
      pollTimeOut: 100
      enableDlq: true
      dlqTopicName: fintech_los_fk_consumer_credit_topic_dlq
    CALM:
      bootStrapServers: ************:9092,**********:9092,*************:9092,************:9092,************:9092
      groupId: calm-pinaka-prod-group
      sessionTimeoutMs: 30000
      noOfThreads: 1 #increase this with traffic/consumer lag
      topic: fintech_calm_topic
      pollTimeOut: 100
      enableDlq: true
      dlqTopicName: fintech_calm_topic_dlq

encryptionKeys:
  kycKey: 9D81B4B4791B791D50315E7A9BB93C4E
  cbcKey: 59AC6C2B95DEFC3EC76C56CF232AF829
  default: 59AC6C2B95DEFC3EC76C56CF232AF829

securityKeyConfiguration:
  keys:
    keyRef0:
      privateKey: ${REF0_PRIVATE_KEY}
      publicKey: ${REF0_PUBLIC_KEY}
    keyRef1:
      privateKey: ${REF1_PRIVATE_KEY}
      publicKey: ${REF1_PUBLIC_KEY}

pinakaAsyncClientConfig:
  url: http://************
  exchangeName: advanz_onboarding_queue
  ebcExchangeName: production.pandora.ebc.callback
  changeEntityEventTopic: fintech.change.entity
  cfaExchangeName: cfa_onboarding_queue
  advanzDedupeExchangeName: efa_onboarding_2
  cbcCardSerNoExchangeName: cbc_card_ser_queue
  cbcCardSerNoHostUrl: http://************:80
  cbcCardSerNoPath: /pinaka/5/applications/process-application

plutusClientConfig:
  url: http://************:80

fintechUserServiceClientConfig:
  uslUrl: http://************:80
  exchangeName: fintech_usl_ingestion_queue

caishenClientConfig:
  url: http://************
  targetClientId: caishen_flipkart

fkPayClientConfig:
  url: http://**********
  clientId: Fintech
  clientSecret: ypg37lwetecaj96pih96
  encryptionKey: FINTECH_AES_KEYS

stratumD42Configuration:
  accessKey: GOOG1E42KDDZR4BDJO4RXCEMLX2RMZTTE7EUXMDNW4ENC2VJSLNFXPYKBLZU6
  secretKey: 9qDg6wG5wW0s/BuznjennwsoxUL0vnpi2qg0ZYow
  endPoint: https://storage.googleapis.com
  maxConnections: 10
  connectionTimeout: 3000

outboundBundleConfiguration:
  defaultDaysToKeep: 15
  shardNames: ["default","queues"]
  databaseName: pinaka

uslClientConfig:
  host: http://************:80
  clientId: pinaka
  connectionTimeout: 2000
  readTimeout: 2000

consentClientConfig:
  host: http://************:80
  clientId: pinaka
  connectionTimeout: 2000
  readTimeout: 2000

consentConfig:
  consentVersionMap:
    KYC-PAGE-CONSENT-V0: 1678065961719
    EXPERIAN-BUREAU-PULL-CONSENT-V0: 1678065920927
    DEVICE-DETAILS-UPLOAD-CONSENT-V0: 1678065854822
    LIMIT_CONSENT_KYC_UPGRADE_JOURNEY: *************
  consentGroups:
    ONBOARDING_CONSENTS:
      - KYC-PAGE-CONSENT-V0
      - EXPERIAN-BUREAU-PULL-CONSENT-V0
      - DEVICE-DETAILS-UPLOAD-CONSENT-V0

audienceManagerConfiguration:
  kafkaProducerConfiguration:
    bootstrapServer: *************:9092,*************:9092,************:9092,************:9092
    acks: 1
    retries: 3
    batchSize: 20
    lingerInMilliSecond: 5
    maxInFlightRequestsPerConnection: 1
  realtimeSegmentConfiguration:
    enabled: true
    testAccounts: []
    topicName: tagger-kafka-fintech
    enabledJourneyList: ["LOAN_APPLICATION_JOURNEY","WHITELIST_JOURNEY"]
  redisTtlInSeconds: 7776000

financialProviderConfig:
  kycMethodConfig:
    AADHAAR_XML_EKYC:
      metadata:
        IDFC:
          tncUrl: https://www.flipkart.com/pages/pay-later-tnc-v12
          tollFreeNo: 1860 500 9900
          email: <EMAIL>
          xmlKycTncUrl: https://www.flipkart.com/pages/advanz-service-tnc
          xmlKycDisclaimer: If you do not wish to share your Aadhaar number, you can submit your KYC documents at the nearest IDFC FIRST Bank branch.
          reviewAndSubmitDisclaimer: I agree to (1) share the above details with Flipkart Advanz  (“FAPL”)  & its lending partners (2) allow FAPL's lending partners to obtain my credit information from a credit information company (3) provide my express consent and instruction to FAPL to obtain, and Experian CIC (“Bureau”) to provide a copy of my credit information to FAPL, from time to time upon FAPL's request, for the purposes of this facility offered by Flipkart Advanz and checking my eligibility for current & future products & services of FAPL (“FAPL P&S“). I understand that this consent is valid for a period of 12 months from today. I also provide my consent to being contacted by FAPL or its Affiliates (defined in T&Cs) for offers of current & future FAPL P&S. I acknowledge that my credit information may be used by FAPL or an Extract (defined in T&Cs) may be shared by FAPL with its partner lenders for the purposes of this facility or to assess eligibility for current and future FAPL P&S. I understand that I shall have the option to opt-out/unsubscribe from this facility and confirm that I have read and accepted the T&Cs and Privacy Policy, in accordance with which FAPL will store, use and share this information. (4) FAPL
          reviewAndSubmitTncUrl: https://www.flipkart.com/pages/advanz-service-tnc
          reviewAndSubmitTncText: Terms & Conditions
          consentContext: KYC-PAGE-CONSENT-V0
          kycType: UPGRADABLE
      validation:
        IDFC:
          nameMatchThreshold: 50
          nameValidationEnabled: true
          panValidationRequired: true
      validityInYears: 1
      validityExtensionPeriod : 9
      supportedAndroidAppVersion: 1090100
      supportedChannelList: ["ANDROID"]
    EKYC:
      metadata:
        IDFC:
          consentContext: KYC-PAGE-CONSENT-V0
          kycType: PARTIAL
      validation:
        IDFC:
          nameMatchThreshold: 50
          nameValidationEnabled: true
          panValidationRequired: true
      validityInYears: 1
      validityExtensionPeriod : 0
    CKYC:
      metadata:
        IDFC:
          tncUrl: https://www.flipkart.com/pages/pay-later-tnc-v12
          tollFreeNo: 1860 500 9900
          email: <EMAIL>
          disclaimer: If you do not wish to share your Aadhaar number, you can submit your KYC documents at the nearest IDFC FIRST Bank branch.
          reviewAndSubmitDisclaimer: I agree to (1) share the above details with Flipkart Advanz  (“FAPL”)  & its lending partners (2) allow FAPL's lending partners to obtain my credit information from a credit information company (3) provide my express consent and instruction to FAPL to obtain, and Experian CIC (“Bureau”) to provide a copy of my credit information to FAPL, from time to time upon FAPL's request, for the purposes of this facility offered by Flipkart Advanz and checking my eligibility for current & future products & services of FAPL (“FAPL P&S“). I understand that this consent is valid for a period of 12 months from today. I also provide my consent to being contacted by FAPL or its Affiliates (defined in T&Cs) for offers of current & future FAPL P&S. I acknowledge that my credit information may be used by FAPL or an Extract (defined in T&Cs) may be shared by FAPL with its partner lenders for the purposes of this facility or to assess eligibility for current and future FAPL P&S. I understand that I shall have the option to opt-out/unsubscribe from this facility and confirm that I have read and accepted the T&Cs and Privacy Policy, in accordance with which FAPL will store, use and share this information. (4) FAPL
          reviewAndSubmitTncUrl: https://www.flipkart.com/pages/advanz-service-tnc
          reviewAndSubmitTncText: Terms & Conditions
          consentContext: /idfcCkycConsentContext
          kycType: UPGRADABLE
          incorrectDetailsPopupHeading: Complete KYC using another method
          incorrectDetailsPopupText: Since you noticed incorrect information - we recommend you to restart KYC using another method.
          incorrectDetailsPopupImageUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/06/2020/5f99d43c-5433-4c09-93bb-75f9caa6edda.png?q={@quality}
          incorrectDetailsPopupButtonText: Continue to complete KYC
      validation:
        IDFC:
          nameMatchThreshold: 50
          nameValidationEnabled: true
          panValidationRequired: true
      validityInYears: 1
      validityExtensionPeriod : 9

orchestratorClientConfig:
  url: http://***********:80

hystrixModuleConfiguration:
  hystrixPropertiesFileName: hystrix.properties
  resourcesFilePath: /etc/config/

multiTenantOAuthClientConfig:
  cachedAccessTokenTTL: 120
  oAuthUrl: http://************:80
  oAuthServiceClientConfigMap:
    FK_CONSUMER_CREDIT:
      oAuthClientID: c2c95e0075c04b2e9b83e1bc8a09f57e
      oAuthClientSecret: FVMWGlJ22AoWfQ+ithjYn+M0tjr8xgakuHl06J5A+DELtVtc
      cachedAccessTokenTTL: 120
      oAuthUrl: http://************:80

multiTenantConnektClientConfig:
  tenantConnektClientConfigMap:
    FK_CONSUMER_CREDIT:
      exchangeName: fintech_los_connekt
      callbackUrl: http://************/pinaka/communications
      domain: flipkart
      emailUrl: http://************
      pnUrl: http://************
      smsUrl: http://************
      inAppUrl: http://************
      emailApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      pnApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      smsApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      inAppApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      emailAppName: flipkart
      pnAppName: RetailApp
      smsAppName: flipkart
      inAppName: flipkart
      emailBucket: alerts
      emailSubBucket: pay
      inAppBucket: alerts
      inAppSubBucket: pay
      pnBucket: alerts
      pnSubBucket: pay
      smsBucket: alerts
      smsSubBucket: pay
      pnChannelId: fk_channel_order_payments
      transactionalEmail: true
      transactionalPN: true
      transactionalSMS: true
      transactionalInApp: true

turboConfig:
  singleDbWriteEnabled: false
  multiDbWriteEnabled: true
  turboOutboundWithoutTrxEnabled: false
  sharding: true
  appDbType: "mysql"
  queue_shard_strategy:
    default_suffix: default
    clusters:
      queues:
        - advanz_onboarding_queue
        - fintech_los_connekt
        - fintech_cf_scheduler
        - lms_onboarding_queue
        - hawkeye_dedupe_ingestion

  mysql:
    hibernate.dialect: org.hibernate.dialect.MySQL57Dialect
    hibernate.c3p0.idle_test_period: "60000"
    hibernate.c3p0.max_size: "100"
    hibernate.c3p0.max_statements: "50"
    hibernate.c3p0.min_size: "20"
    hibernate.c3p0.timeout: "300"
    hibernate.connection.driver_class: com.mysql.jdbc.Driver
    hibernate.connection.password: "0BQRvJpVii-0GoinS1Aq"
    hibernate.connection.url: "**********************************************************************************************************************************************************"
    hibernate.connection.username: "v_fint_uUK6dvXhT"
    hibernate.connection.autocommit: true
    hibernate.show_sql: "true"
    hibernate.connection.zeroDateTimeBehavior: convertToNull

lendingGatewayConfig:
  host: http://lg-service.sm-lending-gateway-prod.fkcloud.in/lg

gibraltarClientConfig:
  host: https://************
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  targetClientId: gibraltar-prod
  maxPublicKeyCount: 100

criClientConfig:
  host: http://************
  maxRetries: 3

coinManagerClientConfig:
  url: http://************
  clientId: "CBC"
  merchantId: "MERCH220714214155975QVPWD"
  requestId: "ABCD"

skylerClientConfig:
  url: http://************:80/fintech-skyler/
  clientId: "pinaka"
  exchangeName: "cbc_bill_initiation_queue"

esClientConfig:
  hostName: "sm-es-prod-elasticsearch.sm-es-prod.fkcloud.in"
  port: 80
  connectionTimeout : 3000

khaataClientConfig:
  url: http://**********:8980
  clientName: "pinaka"
  
viestiConfig:
  enableConsumer: false
  viestiConsumerProps:
    CALM:
      pulsarClientConfig:
        authnSecret: 6mzlqJnCzxp1X4Z+jrHTEUAsJxFBNfmO8YQFgB+B6eNGd01m
        authnClientId: sm_pl_ams_consumer
        issuerUrl: https://service.authn-prod.fkcloud.in
        viestiEndPoint: http://************:80
      pulsarConsumerConfiguration:
        topic: persistent://prod-yak-archive/prod_supermoney_los/prod_sm_los_calm
        subscriptionName: supermoney-engg/ams_event_consumer
        numberOfConsumers: 1
    STRATUM:
      pulsarClientConfig:
        authnSecret: 6mzlqJnCzxp1X4Z+jrHTEUAsJxFBNfmO8YQFgB+B6eNGd01m
        authnClientId: sm_pl_ams_consumer
        issuerUrl: https://service.authn-prod.fkcloud.in
        viestiEndPoint: http://************:80
      pulsarConsumerConfiguration:
        topic: persistent://prod-yak-archive/prod_supermoney_los/prod_sm_los_calm
        subscriptionName: supermoney-engg/robinhood_event_consumer
        numberOfConsumers: 1

gcpPinakaClientConfig:
  url: http://pinaka-service.sm-pinaka-prod.fkcloud.in
  client: pinaka-client
  merchant: mp_flipkart


fintechLogConfig:
  serviceTag: pinaka
  bucketNames: ["pinaka-prod"]

smUserServiceClientConfig:
  url: "http://kavach-service-prod.kavach-prod.fkcloud.in"
  clientId: "supermoney"

merchantJourneyUrlConfig:
  FLIPKART:
    statusUrl: "https://www.flipkart.com/sumo-3p/pl/pages/status"
    callbackUrl: "https://dl.flipkart.com/sumo-3p/pl/pages/status"
    resumeUrl: "https://dl.flipkart.com/sumo-3p/pl/pages/journey"
  SHOPSY:
    statusUrl: "https://www.shopsy.in/sumo-3p/pl/pages/status"
    callbackUrl: "https://dl.shopsy.in/sumo-3p/pl/pages/status"
    resumeUrl: "https://dl.shopsy.in/sumo-3p/pl/pages/journey"
  MYNTRA:
    statusUrl: "https://super.money/sumo-3p/pl/pages/status"
    callbackUrl: "https://www.myntra.com/sumo-3p/pl/pages/status"
    resumeUrl: "https://www.myntra.com/sumo-3p/pl/pages/journey"


crisysClientConfig:
  host: http://crisys-service.sm-crisys-prod.fkcloud.in

offerServiceConfig:
  url: http://offer-service.sm-offer-service-prod.fkcloud.in

pinakaCalvinClientConfig:
  url: http://************
  client : pinaka

bigtableConfig:
  projects:
    - projectId: "sm-personal-loans"
      instances:
        - instanceId: "sumo-credit-reports-prod"
          tables:
            credit-report:
              tableId: "credit-report"


pageServiceConfig:
  isWinterfellMocked: false
  dataAPIPath: "com.flipkart.sm.pages.dp.apis"
  widgetAdapterPath: "com.flipkart.sm.pages.dp.widget.adapters"
  pageConfigResolverName: calm
  pageConfigPollingInterval: 5
  pageServiceStaticResourcesBucket:
    bucketName: "sm-pinaka-prod"
    enableLocalDynamicBucket: false
  featureFlagConfigBucket:
    bucketName: "sm-feature-flag-prod"
    enableLocalDynamicBucket: false
  widgetTemplateMap:
    # cardSummaryList widgets
    ACCOUNTS_CARD_IDENTIFIER__ACTIVE: "template/creditscore/ActiveAccountsRichCard.json"
    ACCOUNTS_CARD_IDENTIFIER__INACTIVE: "template/creditscore/InactiveAccountsRichCard.json"
    ON_TIME_PAYMENT_CREDIT_METRIC_CARD_SUMMARY_LIST_IDENTIFIER: "template/creditscore/OnTimePaymentsCreditMetricCard.json"
    CREDIT_UTILIZATION_CREDIT_METRIC_CARD_SUMMARY_LIST_IDENTIFIER: "template/creditscore/CreditUtilisationCreditMetricCard.json"
    # card widgets
    CREDIT_SCORE_CARD_IDENTIFIER__ON_TIME_PAYMENTS: "template/creditscore/OnTimePaymentsCreditScoreCard.json"
    CREDIT_SCORE_CARD_IDENTIFIER__CREDIT_UTILISATION: "template/creditscore/CreditUtilisationCreditScoreCard.json"
    CREDIT_SCORE_CARD_IDENTIFIER__CREDIT_AGE: "template/creditscore/CreditAgeCreditScoreCard.json"
    CREDIT_SCORE_CARD_IDENTIFIER__CREDIT_MIX: "template/creditscore/CreditMixCreditScoreCard.json"
    CREDIT_SCORE_CARD_IDENTIFIER__CREDIT_ENQUIRES: "template/creditscore/CreditEnquireCreditScoreCard.json"
    # key value widgets
    CREDIT_METRIC_KEY_VALUE: "template/creditscore/CreditMetricImpactKeyValue.json"
    CREDIT_SCORE_TABLE: "template/creditscore/CreditScoreTable.json"
    CHECK_CREDIT_SCORE_BUTTON: template/creditscore/CheckCreditScoreSubmitButton.json
    EXPERIAN_FOOTER: template/creditscore/ExperianFooter.json
    CHECK_CREDIT_SCORE_BANNER: template/creditscore/CheckCreditScoreBanner.json
    CHECK_CREDIT_SCORE_PL_BANNER: template/creditscore/CheckCreditScorePlBanner.json
    CREDIT_SCORE_METRICS_TITLE: template/creditscore/CreditScoreMetricsHeader.json
    CREDIT_SCORE_METRICS_CARDS: template/creditscore/CreditScoreMetrics.json
    IMPROVE_CREDIT_SCORE: template/creditscore/HowToImproveCSButton.json
    CS_REFRESH_BUTTON: template/creditscore/RefreshCreditScore.json
    CREDIT_SCORE_SCALE: template/creditscore/CreditScoreScale.json
    NO_CS_CARD: template/creditscore/NoCSCard.json
    NO_CS_BUTTON: template/creditscore/NoCSButton.json
    NO_CS_TIPS_LIST: template/creditscore/NoCSTips.json
    NAV_BAR_BACK_BUTTON: template/creditscore/BackNavBar.json
    CS_FROM_PARTNER_CARD: template/creditscore/CreditScoreFromPartner.json
    SUPERCASH_NAV_BAR: template/creditscore/SupercashNavBar.json
    SUPERCASH_NAV_BAR_WITH_BACK: template/creditscore/SupercashNavBarWithBack.json
    EXPERIAN_TERMS_AND_CONDITIONS: template/creditscore/TnC.json
    EXPERIAN_TnC_HEADER: template/creditscore/TncHeader.json
    POWERED_BY_UPI_FOOTER: template/creditscore/PoweredByUpiFooter.json
    NAV_COMING_SOON_CASH: template/creditscore/ComingSoonNavBar.json
    CASH_COMING_SOON_CARD: template/creditscore/ComingSoonCard.json
  widgetConfigMap:
    PAGE_TITLE_TEXT: "widgetConfig/PageTitle.json"

pubsubconfigCs:
  projectId: sm-apps-core
  topicId: credit-score-event-prod
  messageOrderingEnabled: true

loanDisbursalDataSubscriberConfig:
  projectId: sm-data-platform
  subscriptionId: PinakaService_Disbursal_Data_Subscription_PROD