[SERVICE]
    HTTP_Server    On
    HTTP_Listen    0.0.0.0
    HTTP_PORT      2020
    Flush          1
    Daemon         Off
    Log_Level      debug
[INPUT]
    Name              tail
    Tag               *
    Path             /var/log/pinaka/pinaka.log
    Mem_Buf_Limit     35MB
    Buffer_Chunk_size 5MB
    Buffer_Max_size   5MB
    DB                /var/log/buffer.db
[Output]
    Name grafana-loki
    Match *
    Url http://kurama-prod-gateway.sm-observability-v2-prod.fkcloud.in/loki/api/v1/push
    TenantID PL
    BatchWait 1
    BatchSize 1048576
    Labels {service_name="sm-pinaka",env="prod"}
    RemoveKeys kubernetes,stream
    AutoKubernetesLabels false
    # LabelMapPath /fluent-bit/etc/labelmap.json
    LineFormat json
    LogLevel debug