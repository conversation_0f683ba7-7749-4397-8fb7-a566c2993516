namespace: sm-pinaka-playground
fcp:
  team: "supermoney-engg"
  appId: "sm-pinaka-preprod"
  mail: "supermoney"
  service: "sm-pinaka-preprod-dev2"
  env: "dev2"
mtl:
  cosmos:
    statsd: enabled
    tail: enabled
    config: cosmos-config-dev2
  asterix: disabled

pod:
  replicaCount: 1

hostpopulator:
  state: enabled
  cpu: 0.2
  nameservers:
    - dns.fkinternal.com

image:
  service:
    tag: "2.0.0-v2"
    repository: lending-los/pinaka-preprod
    runAsUser: 108
    runAsGroup: 112
    volumes:
      - app-dev2-config
      - app-log
      - cosmos-config-dev2
      - cfg-svc-metadata-dev2
      - relayer-cosmos-config-dev2
      - gib-certs
      - host-populator-logs
      - host-d
      - host-populator-config-dev2
    livenessCheck:
      initialDelay: 90
      checkInterval: 15
      method: http
      port: 9091
      path: /admin/healthcheck
    readinessCheck:
      initialDelay: 60
      checkInterval: 15
      method: http
      port: 9091
      path: /admin/healthcheck
    ports:
      - name: application
        containerPort: 9090
      - name: admin
        containerPort: 9091
      - name: jmx
        containerPort: 9311
        protocol: TCP
    maxCpuLimit: 1
    maxCpuAsk: 1
    maxMemoryLimit: 3Gi
    maxMemoryAsk: 3Gi
    envVars:
      - name: REF0_PRIVATE_KEY
        source: secret
        secretName: secret-security-key-dev2
        secretKey: ref0_private_key
      - name: REF0_PUBLIC_KEY
        source: secret
        secretName: secret-security-key-dev2
        secretKey: ref0_public_key
      - name: REF1_PRIVATE_KEY
        source: secret
        secretName: secret-security-key-dev2
        secretKey: ref1_private_key
      - name: REF1_PUBLIC_KEY
        source: secret
        secretName: secret-security-key-dev2
        secretKey: ref1_public_key
  fluentbit:
    registry: edge.fkinternal.com/docker-external
    repository: grafana/fluent-bit-plugin-loki
    tag: main
    maxCpuLimit: 0.1
    maxCpuAsk: 0.1
    maxMemoryLimit: 100Mi
    maxMemoryAsk: 100Mi
    volumes:
      - fluent-bit-config-volume
      - app-log
#    containerStartCommand: ["/bin/sh"]
#    containerStartCommandArgs: ["-c",  "java -Xmx6g -Xms4g -Djava.net.preferIPv4Stack=true -DaopType=GUICE  -Duser.language=en -Duser.region=CA -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -XX:+UseConcMarkSweepGC -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+PrintTenuringDistribution -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -Duser.timezone=\"Asia/Kolkata\" -jar /var/lib/app/service.jar server /etc/config/config.yml",  ";"]
#    containerStartCommandArgs: ["-f",  "/dev/null",  ";"]

volumes:
  - name: app-dev2-config
    type: configMap
    value: app-dev2-config
    mountPath: /etc/config/
  - name: app-log
    type: emptyDir
    value: {}
    mountPath: /var/log/
  - name: cfg-svc-metadata-dev2
    type: configMap
    value: cfg-svc-metadata-dev2
    mountPath: /etc/default/
  - name: relayer-cosmos-config-dev2
    type: configMap
    value: relayer-cosmos-config-dev2
    mountPath: /etc/fk-sc-mq/
  - name: host-populator-logs
    type: emptyDir
    value: { }
    mountPath: /var/log/hosts-populator/
  - name: host-d
    type: emptyDir
    value: { }
    mountPath: /etc/hosts.d/
  - name: fluent-bit-config-volume
    type: configMap
    value: fluent-bit-config-map
    mountPath: /fluent-bit/etc/

configMaps:
  envName: dev2
  configs:
    - name: app-dev2-config
      source : dir
      sourceLoc : app
    - name: relayer-cosmos-config-dev2
      source: dir
      sourceLoc: relayer
    - name: cosmos-config-dev2
      source: dir
      sourceLoc: cosmos
    - name: host-populator-config-dev2
      source: dir
      sourceLoc: host-populator
    - name: cfg-svc-metadata-dev2
      source: data
      data:
        cfg-api: |
          host=************
          port=80
        fk-env: |
          prod


services:
  - name: sm-pinaka-dev2-preprod
    type: LoadBalancer
    ports:
      - port: 9090 # useless but not to be changed
        targetPort: 9090
        protocol: TCP
        name: sm-pinaka-dev2-preprod
    LoadBalancer:
      vip: sm-pinaka-dev2-preprod-elb
      healthCheckPort: 9091
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: sm-pinaka-dev2-preprod-elb
      backendPort: 9090
      frontendPort: 80
      mode: http

secrets:
  - name: secret-security-key-dev2
    type: Opaque
    data:
      - key: ref0_private_key
        value: |-
          eyJkYXRhIjoidmF1bHQ6djE6bmdVeHc3dTFJYk0yZXp0Yjh0dEMwVFM2cHlBaFIvcWphOHZQVVdUQXFaUVIyR1MxeCtIa2pNNHFBdUxyemxEQ1NHYXFmRnJCRFJQaEc3SHBMTHJtWk5oMW5qekliQjFReEw4TXRxT24zWm4vdU5BM0UwQjFLWWJvM0tiNUFGRzdyQzhETDEwV1MzNG92OWNkRitBWmVQcEJGSWs5VW1KR1o0V1Vucmt5TnFsTWZuOUZQczJhMWpXRUFxSGgwWTB0eDBJZUVqcC85bVp5VXVxc00vbEloN3N1S21icnYxWStZOGFtSnRESnhqalU2d1ZiRlpEQktTeEV1YjhPRElmTDhXK212VC9RWlcrcmFFWWgrWHd0S3U5L2lDOTZWUEQ3UlpneVFTY1luUjFIZERTcWt1OHNBZ2ljbmx2ZnJ2V29QcWlFRStYSTJreFlReCtRWW5keFRYUFlsYlpXODkvT0JyTkR5M1JtK3dvQ1V4SjRqRFhGV2ZvNStWQnZ5Qzh2R0REUkdISmErVFBDdlpIc2x0OHVDeUhUZ3c0N2ZWM2JTS25DSmlKZ09QQlpKNmVsRmhKTTYxY1YyUnJNZFpCdHBLWmlrMC9YNWlEOWZDR1VEOWlzcmNkNm4wa0VXSGlBR1pyK0dESVh2djlEYTFYbWVJbUloRUZmNkNicy9TRzI2RU5FOHFFNjgxSWR5MGZqemdlVmgrbkY0bzM0NEc1N3A2bkRPeWdTSDFGbDBxb0p3b0xBOTBoK3JrRlFHdFNWQUNSemtuRlJUK0xkaVE5M3dLb3puNUN6eGdmWXZCY25FWjlIVytHQXZQNmFyeHlxZmo2NWx3RG1GcmFlbGU2QjduZ0Jha3lQNU56b0RQaVRRWXBlWFFBZ3JCa2dPMGpTU2VwM25SUnZhOFo5VG9HbmY2Uk91eDhNUm9WUGZQcG4wYnVKNjZ2N2toL1l2K1U2Yk5MbmRwRFV3d2pWOWU2MXdjU25wVHBiaUNIMGNvWVFHUDIyaytNWWg4UGZlRzNUcU00MkV0Q040Q0ZsVlg0QzZickJxazFMNzlxMTg0aU9VdHF1NlUwNFdncXRNUlNCbmNSMjF2OTNzYnU3ZW1GYU9JV1lDUnV4UFdBU2UwRjdpa3lNSDdDVnNYVXRQMUg5QUFsckNNWEdoMWs5c1pxV3grT2JwTkxRUDh0OWJ3enRTSXR4bVJZMDlUY041UU5icnpXYkxJQWRSdlRxVU0vS1FrT2JvRFp1U2ZXK2oxUzBTZmxtcFphSkRRNFZNUTk3RkdERlRpV3BEZmZpYW84dytxUktjODIxc2JoV1V0Q1dXMDdnRkRPS3ZQMktrcEk4WkwyM3JhTndYdU9VT3R3cWtqay9lSk1QT204WDhxWHlXWVFKYkUwbzNpQjBxYWw2WFBSTE1wREJSdlorLy9rajlWc0MweUdQZ25qc1o5ZEJHT2Z4M0RoVDJZaVF5blFVNXZBMDlYMStpL3BLV2cwOXF1OXdoVjdvQk1vSlVGcEE2NE9YQ2wxMHRoM3Q2UlREd3VZNHZYVHRuNkk9Iiwia2V5SWQiOiJhcHBpZDpzbS1waW5ha2EtcHJlcHJvZDo6c20tcGluYWthLXByZXByb2Qta2V5In0=
      - key: ref0_public_key
        value: |-
          eyJkYXRhIjoidmF1bHQ6djE6UnloYkFmbkF5dm9xUC9BQkFBbVBhcHJoZ0dGWnluWXo4a2hrZkxPaXJDSkxkdWR1Wk9Hbk5ob2YxanljdEJUanpGR25WcUxIMXc1bjd4dVQ0WlhaZTlTQzdmU2lxNnZzT2xNazF5NzgwWFcycnVRSCs4K1UwNW82bUIrT2VIVXhzLzJuM3pYcndhTXVnQnJSOCt0d1drekczN24wN2s2UlllWC91V01neFdtSlFVTnZMWENDMHZmTlNpQjVnSUF0QWhIMzFmSDBabUFsNGkvYTNDWEZYbm1VbE1MMkgwMHMra3Z3eDBBbEE4T1Brci8wSkxsUC93QVhnUWJTQmFlNy8vOHU1eFVLajVwa3U5Q0Q5aHZiUWVUamZCZHU2cUp1ZFFWMUVyTVRHc0R6QllmRUkzVGR3enlkclR3aEd3MXJPUzJUTFE9PSIsImtleUlkIjoiYXBwaWQ6c20tcGluYWthLXByZXByb2Q6OnNtLXBpbmFrYS1wcmVwcm9kLWtleSJ9
      - key: ref1_private_key
        value: |-
          eyJkYXRhIjoidmF1bHQ6djE6bmdVeHc3dTFJYk0yZXp0Yjh0dEMwVFM2cHlBaFIvcWphOHZQVVdUQXFaUVIyR1MxeCtIa2pNNHFBdUxyemxEQ1NHYXFmRnJCRFJQaEc3SHBMTHJtWk5oMW5qekliQjFReEw4TXRxT24zWm4vdU5BM0UwQjFLWWJvM0tiNUFGRzdyQzhETDEwV1MzNG92OWNkRitBWmVQcEJGSWs5VW1KR1o0V1Vucmt5TnFsTWZuOUZQczJhMWpXRUFxSGgwWTB0eDBJZUVqcC85bVp5VXVxc00vbEloN3N1S21icnYxWStZOGFtSnRESnhqalU2d1ZiRlpEQktTeEV1YjhPRElmTDhXK212VC9RWlcrcmFFWWgrWHd0S3U5L2lDOTZWUEQ3UlpneVFTY1luUjFIZERTcWt1OHNBZ2ljbmx2ZnJ2V29QcWlFRStYSTJreFlReCtRWW5keFRYUFlsYlpXODkvT0JyTkR5M1JtK3dvQ1V4SjRqRFhGV2ZvNStWQnZ5Qzh2R0REUkdISmErVFBDdlpIc2x0OHVDeUhUZ3c0N2ZWM2JTS25DSmlKZ09QQlpKNmVsRmhKTTYxY1YyUnJNZFpCdHBLWmlrMC9YNWlEOWZDR1VEOWlzcmNkNm4wa0VXSGlBR1pyK0dESVh2djlEYTFYbWVJbUloRUZmNkNicy9TRzI2RU5FOHFFNjgxSWR5MGZqemdlVmgrbkY0bzM0NEc1N3A2bkRPeWdTSDFGbDBxb0p3b0xBOTBoK3JrRlFHdFNWQUNSemtuRlJUK0xkaVE5M3dLb3puNUN6eGdmWXZCY25FWjlIVytHQXZQNmFyeHlxZmo2NWx3RG1GcmFlbGU2QjduZ0Jha3lQNU56b0RQaVRRWXBlWFFBZ3JCa2dPMGpTU2VwM25SUnZhOFo5VG9HbmY2Uk91eDhNUm9WUGZQcG4wYnVKNjZ2N2toL1l2K1U2Yk5MbmRwRFV3d2pWOWU2MXdjU25wVHBiaUNIMGNvWVFHUDIyaytNWWg4UGZlRzNUcU00MkV0Q040Q0ZsVlg0QzZickJxazFMNzlxMTg0aU9VdHF1NlUwNFdncXRNUlNCbmNSMjF2OTNzYnU3ZW1GYU9JV1lDUnV4UFdBU2UwRjdpa3lNSDdDVnNYVXRQMUg5QUFsckNNWEdoMWs5c1pxV3grT2JwTkxRUDh0OWJ3enRTSXR4bVJZMDlUY041UU5icnpXYkxJQWRSdlRxVU0vS1FrT2JvRFp1U2ZXK2oxUzBTZmxtcFphSkRRNFZNUTk3RkdERlRpV3BEZmZpYW84dytxUktjODIxc2JoV1V0Q1dXMDdnRkRPS3ZQMktrcEk4WkwyM3JhTndYdU9VT3R3cWtqay9lSk1QT204WDhxWHlXWVFKYkUwbzNpQjBxYWw2WFBSTE1wREJSdlorLy9rajlWc0MweUdQZ25qc1o5ZEJHT2Z4M0RoVDJZaVF5blFVNXZBMDlYMStpL3BLV2cwOXF1OXdoVjdvQk1vSlVGcEE2NE9YQ2wxMHRoM3Q2UlREd3VZNHZYVHRuNkk9Iiwia2V5SWQiOiJhcHBpZDpzbS1waW5ha2EtcHJlcHJvZDo6c20tcGluYWthLXByZXByb2Qta2V5In0=
      - key: ref1_public_key
        value: |-
          eyJkYXRhIjoidmF1bHQ6djE6UnloYkFmbkF5dm9xUC9BQkFBbVBhcHJoZ0dGWnluWXo4a2hrZkxPaXJDSkxkdWR1Wk9Hbk5ob2YxanljdEJUanpGR25WcUxIMXc1bjd4dVQ0WlhaZTlTQzdmU2lxNnZzT2xNazF5NzgwWFcycnVRSCs4K1UwNW82bUIrT2VIVXhzLzJuM3pYcndhTXVnQnJSOCt0d1drekczN24wN2s2UlllWC91V01neFdtSlFVTnZMWENDMHZmTlNpQjVnSUF0QWhIMzFmSDBabUFsNGkvYTNDWEZYbm1VbE1MMkgwMHMra3Z3eDBBbEE4T1Brci8wSkxsUC93QVhnUWJTQmFlNy8vOHU1eFVLajVwa3U5Q0Q5aHZiUWVUamZCZHU2cUp1ZFFWMUVyTVRHc0R6QllmRUkzVGR3enlkclR3aEd3MXJPUzJUTFE9PSIsImtleUlkIjoiYXBwaWQ6c20tcGluYWthLXByZXByb2Q6OnNtLXBpbmFrYS1wcmVwcm9kLWtleSJ9
