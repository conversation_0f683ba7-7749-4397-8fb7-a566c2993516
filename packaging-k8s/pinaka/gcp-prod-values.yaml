namespace: sm-pinaka-prod

fcp:
  team: "supermoney-engg"
  appId: "sm-pinaka-prod"
  mail: "supermoney"
  service: "sm-pinaka-prod"

mtl:
  cosmos:
    config: cosmos-config
  asterix: disabled
  logsvc:
    enabled: false
    envName: calvin
    sourceLoc: rsyslog.conf

pod:
  replicaCount: 3

hostpopulator:
  state: enabled
  cpu: 0.2
  nameservers:
    - dns.fkinternal.com

image:
  service:
    tag: "1741119-2632-flow-487f683961d877d96ebc45cd08c49e306c237272-13-Jan-23_12-52-53"
    repository: lending-los/pinaka
    runAsUser: 108
    runAsGroup: 112
    volumes:
      - app-config
      - app-log
      - cosmos-config
      - cfg-svc-metadata
      - relayer-config
      - host-populator-logs
      - host-d
      - host-populator-config
      - sumo-observability-volume
    livenessCheck:
      initialDelay: 200
      checkInterval: 15
      method: tcp
      port: 9091
    readinessCheck:
      initialDelay: 90
      checkInterval: 15
      method: http
      port: 9091
      path: /admin/healthcheck
    ports:
      - name: application
        containerPort: 9090
      - name: admin
        containerPort: 9091
      - name: jmx
        containerPort: 9311
    maxCpuLimit: 3
    maxCpuAsk: 3
    maxMemoryLimit: 7Gi
    maxMemoryAsk: 7Gi
    envVars:
      - name: JVM_PARAMS
        source: "value"
        value: "-Xms2393m -Xmx2393m -XX:MaxDirectMemorySize=1G"
      - name: REF0_PRIVATE_KEY
        source: secret
        secretName: secret-security-key
        secretKey: ref0_private_key
      - name: REF0_PUBLIC_KEY
        source: secret
        secretName: secret-security-key
        secretKey: ref0_public_key
      - name: REF1_PRIVATE_KEY
        source: secret
        secretName: secret-security-key
        secretKey: ref1_private_key
      - name: REF1_PUBLIC_KEY
        source: secret
        secretName: secret-security-key
        secretKey: ref1_public_key
      - name: SUMO_OBSERVABILITY_ENABLED
        source: value
        value: "TRUE"
  fluentbit:
    registry: edge.fkinternal.com/docker-external
    repository: grafana/fluent-bit-plugin-loki
    tag: main-e2ed1c0
    maxCpuLimit: 0.1
    maxCpuAsk: 0.1
    maxMemoryLimit: 500Mi
    maxMemoryAsk: 500Mi
    volumes:
      - fluent-bit-config-volume
      - app-log
#      - name: NEW_RELIC_LICENSE_KEY
#        source: "secret"
#        secretName: "nr-secret"
#        secretKey: "nr_license_key"
#      - name: NEW_RELIC_APP_NAME
#        source: value
#        value: "fintech-pinaka-prod-ch"
#      - name: NEW_RELIC_PROXY_HOST
#        source: value
#        value: "*************"
#      - name: NEW_RELIC_PROXY_PORT
#        source: value
#        value: '"80"'
#    containerStartCommand: ["/bin/sh"]
#    containerStartCommandArgs: ["-c",  "java -Xmx6g -Xms4g -Djava.net.preferIPv4Stack=true -DaopType=GUICE  -Duser.language=en -Duser.region=CA -XX:+UnlockCommercialFeatures -XX:+FlightRecorder -XX:+UseConcMarkSweepGC -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+PrintTenuringDistribution -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=9311 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -Duser.timezone=\"Asia/Kolkata\" -jar /var/lib/app/service.jar server /etc/config/config.yml",  ";"]
#    containerStartCommandArgs: ["-f",  "/dev/null",  ";"]

volumes:
  - name: app-config
    type: configMap
    value: app-config
    mountPath: /etc/config/
  - name: app-log
    type: emptyDir
    value: {}
    mountPath: /var/log/
  - name: cfg-svc-metadata
    type: configMap
    value: cfg-svc-metadata
    mountPath: /etc/default/
  - name: relayer-config
    type: configMap
    value: relayer-config
    mountPath: /etc/fk-sc-mq/
  - name: host-populator-logs
    type: emptyDir
    value: { }
    mountPath: /var/log/hosts-populator/
  - name: host-d
    type: emptyDir
    value: { }
    mountPath: /etc/hosts.d/
  - name: fluent-bit-config-volume
    type: configMap
    value: fluent-bit-config-map
    mountPath: /fluent-bit/etc/
  - name: sumo-observability-volume
    type: configMap
    value: sumo-observability-config
    mountPath: /etc/sumo-observability/

configMaps:
  envName: gcp-prod
  configs:
    - name: app-config
      source : dir
      sourceLoc : app
    - name: relayer-config
      source: dir
      sourceLoc: relayer
    - name: cosmos-config
      source: dir
      sourceLoc: cosmos
    - name: host-populator-config
      source: dir
      sourceLoc: host-populator
    - name: sumo-observability-config
      source: dir
      sourceLoc: sumo-observability
    - name: fluent-bit-config-map
      source: dir
      sourceLoc: fluent-bit
    - name: cfg-svc-metadata
      source: data
      data:
        cfg-api: |
          host=api.aso1.cfgsvc-prod.fkcloud.in
          port=80
        fk-env: |
          prod
          

serviceAccount:
  name: sm-pinaka-user
  annotations:
    gcp_service_account: "<EMAIL>"


services:
  - name: pinaka-service
    type: LoadBalancer
    ports:
      - port: 9090 # useless but not to be changed
        targetPort: 9090
        protocol: TCP
        name: sm-pinaka-prod
    LoadBalancer:
      vip: sm-pinaka-prod-elb
      healthCheckPort: 9091
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: sm-pinaka-prod-elb
      backendPort: 9090
      frontendPort: 80
      mode: http


#secrets:
#  - name: nr-secret
#    type: Opaque
#    data:
#      - key: nr_license_key
#        value: "eyJkYXRhIjoidmF1bHQ6djE6MkpkVEZ3cXp1WDgzVjhKZUhaaFVSaW8vazdQTWlUQWdsejNXMjJzR1pmY3V3U1ArWVNkUUQxendaWTgyZFBLMjJpYjlDVW51cGRxbmd3bkFEOTdVTm9zRlpRUT0iLCJrZXlJZCI6ImFwbTo6bnItbGljZW5zZS1rZXkifQ=="

secrets:
  - name: secret-security-key
    type: Opaque
    data:
      - key: ref0_private_key
        value: |-
          eyJkYXRhIjoidmF1bHQ6djE6aGNhR3daMU5xZEU1Wk1aQXN5aExtUlhUamR4d2V1bzRxOGl1OW0vRjFqalAwSUdFZlFGUkVieWpYdk95aVJUNG1zbFZ3cGVpMnAveitvT2tjTERmd09mN1M4UGJhQUtWWWN3SVdpTWxKaFJEOEdKclk4MUZ4RU1BcFlLQVpsZWhNaWE5OGRwWXdzSWpnbnlndjZoOWM0a2NiTmlHTDVSYitvNmZpV3JvN3ZXay93OVdmaEVQY3ZrTFlMWU9TMFlPSlp4TGRiSXNQSHhrYVpQenpodTF6b2tla2hyRVJib2R6MGxpUkNWWnZZSCs2UlNnU3M3Qk0yQmRmdXByMWN6N1FjK3ZwOWFsTlViT0NqZXJkSDRuUXUxcER1UkxML1pxaGZoM21SSlpFczZBUmpYeEwxcEl6ZVNWSnRDUEREWXJhY3preVJtUUdONCtpZ3ZRYXU3YWg3TkRUbnlLZEk5SnhKc09tUGFvK3dtMXUvSDNSQ3hFKy90ZkRYQVNCbXRNcjUxaXZrV1QxRTdYSmNVOVRiZXNRdmRZRitkRU9JTUM1Q1VQZG85cmZ4MFArU3JPTHFva2kra2FpNEFOZHVFY3ZTMVYwcXRBK2tmb1dzbmNBZHRtMEF6VHZKVkQ2bThlamlsNDJXMGxzdy9qN0FHRGFBcGNhMFAzTmIvSTFkbVQyUEMxaEVmdi9ueG1PQ0x1TU1uVVJVR291Tk12bDZnWGQvbHhXeFJBd0tHajFCU3A4RFVCWTZJZWlNb0VzUzZkTkFqY0ZHdm5mMUNKVUVYNi80NzNCdVptMVhzdzdXSUp6anhnZUQyelZ5ZEptVWljYmVSTTZORXJXUERmdGEwTE1ienNDd3dNUFdoU2JaQ1kwSmJlMFJKaC8yd0l0M0FpWXJPVzYxd1V4TEduaDdnVW0zSEhsWTNBcEc0YjYxUmJlRHVpRnY5MklwWTlmcGovSzhlUFVjVnVGMGtrY3BneUhnN3dZVWRtdnNyeUR3MjhaYndkUWxaSURPVGhRNHg2S2tvRG5DZ3gyYW1ZYmNMU1VZUXdhSlQ1aFV1bUxhcjRXUWNlTnhYdWFrNXBmbE96eXBjYThUbjZET2NWUmhNY0g5NjU1MHpINDFQbFBMQ3FWalZjZ2F1S3VHRGxZcVc2K3dua05JUlJJeHVTV01INjg0LytUcm1vN2pDR1J5TElhaDh2RG9ZTmV1TzZrRlBpYjhZVlJMbFNocEc2djhPcEQrZy8xN0ZJd3hTSlhENWtEc0JRN3NOOFF0b0pDRFEzRWx6Y2duR24wd1UwSEpxNDV4RFgzZDVQWjlxVlhUWDJONkJIbUFxanZWcU5icWYwOXBLSEIxODNJeWtBSWNKUCs3bmxOdXhmbUt1aXJwczBMSi8xSU5XdFltR3Bsb3Y2K1FGMHRDeTZoVDFZTnMrWVo3a3liT1B5cW5ibDhMS2VQbndxT1A2RElIN08yMnVZTWJxZDZ0eUZkZVpDUThiVGxVd3RDeENtZllsWXhzTURmMS8rRmxtNCs1OElrTnlxcGg4cmZieUFZcWN1eitrbCIsImtleUlkIjoiYXBwaWQ6c20tcGluYWthLXByb2Q6OnNtLXBpbmFrYS1wcm9kLWtleSJ9
      - key: ref0_public_key
        value: |-
          eyJkYXRhIjoidmF1bHQ6djE6dmZxWmkwRHhBZjR4N1dPWkdEZVJYRHRaSXFBVUt1SGlSZE9xTnc2czFLTTkweWFuM0NGWlM0K2pBMHpHUVN1MFBGT1J2STl3WmdNUUpTMFZ3WndMMHdMWGpFd2U0cndCVzg0RVhaRzFMVHJyMWR5ZTZicnNRS0tQdy95alN1ZC9ZdlVIYk5nOWsrR2loR3FzQ0ZTSTlaRUlxNER3WHZHUnJRR0R0T1gvMkRSdGU3NExFdjJqb3d5blJnS0FGUnNGclJhZ1VnQkFOVmF0bDkxYUMzbVUrMTFnUWs3L1VmaG1zUFZxV1ZTM3o4MW80ZDVVRUlZM1ZFM2ZERUpoM01EZFN4M3pVTGUzWlVYVUZGbTVlbC9KYWRCOTlLM0hSQS9sTC94Lzh5c3RuSGhZQkhueUNHY21paHZFVWFuZi9FQ3lhZkh6U1E9PSIsImtleUlkIjoiYXBwaWQ6c20tcGluYWthLXByb2Q6OnNtLXBpbmFrYS1wcm9kLWtleSJ9
      - key: ref1_private_key
        value: |-
          eyJkYXRhIjoidmF1bHQ6djE6UWNLOHdqcExoY2JxekFaaXRkTEdldk9BVHhKYlFwRXp4OUd1OWhzS0NMMER5Z1FlUU4zRDgycjNlUit3Uis2dTJ1UzZWeWsvWDREWXBuWVlzVkZGRGNzR0I2aHlRcTBGbksrcmRmK0M1TXlVTnVQVjlOaEs5Zld5L3orQjFQYWtUOThFaFRKSXdxQytqRDFndkx3SFd4WDZCd3lUMkRMaTNyUVpFbktCNCtCSHJadFdmQ0xUbkFDN3EyN2F4bDhPQ0YyVVJIVzBIOUVhWitFaWY1L0RWbDlERHpjeHZYdzNyN016ZnQ2YWxDMFNleXQwV0pZM2xkVEdEV0RqOEJoNkhEVFpGcll4cmRyRWc0bWpIeTVBckNpaEIyWnZOUkZhU0Zmd083Y0JwSG9GZzd6L3FLS2NIUDlxSE01ek53L1NIYU82MGd0aFpEOTFQaFJPM242ZXZLT2RtNE8vdEhFVGkxaFg4Nzc0YWpraXppbmg0RTF4R2hGWm9EbDh4b09hUDU5aHNDcTUwenV5a1R3WnMrRUdZQ2VRZ2dWalowSHpNV1FsWG1GRk1uQ29zZytoNngyTGRQSTBjeE5CTEVkcjRVQytDMk1BRkwzNEV4MVNzZ3NuQ3ZXajU4cWI2R1RCOGR6TzhqWTY3bTE3RmdoRmlVTU5ERnROU1Vqd2pNNFJ2UmR5UVFrRFdyRFVodXNtd2FHWUhOcWR5NDNEakVDRHVGZ1c4ZWpxdEhhL2hrR0dHQU1IQTZ1aWw0a2t4SjE4SnR4QU9scXVuRDNyK2d1TXNLTzhiYnRGVzNnUDl3ZzBnUS9aQ29VdkUzZHdSVkp5a3FNNVBoVytHcTRFN0NsSy9HUytObFBrWmdSdU5qVUhERHJqc3RlcXdtREZpcGdxRERaY0FxYk8xWkRtclY5aEMwUTI5ME5oR3R6S2V6QWxNYjY2cHUzNlVmckJUVkxFRGhoSCtPdXFTWUYydk1BamYvTWR5MnRuZWljNkxEeGZDZE5URDZ0WTZ3aCtadnVyZmEvQkNSWENHVGVmeU9TVGpOMVp5Y3ErMHlzZnZCeEZGS1FrelI4ZzJHVkVBTnVvS1VoQVUzZ1MzL2xydlVpM2NUNG5OZUJJMVRKQTBJOFNJSHZ1T3M1ZWZ1OFlTeEpIS01QNjhuOHI2WTV2c2JZZE5zaUQ5VGdGWjNaSDVrQUFpK1l5c0srcDJwOFpZYnVCeHkvemMwTW1ERW9MYVhVbmd6cGlRMnFVVGZISk1zNVh6eUZJVVozb3I1c1dhN0hucFZ2VFByRy8zekRRZDZqbzQwSEIydGh3WmRyWFg3dlpBM2gzeHJlOXk0a1ltelBaUDBrVnZvMUFYQmZhOXYyc0hWd2lZZXdrSmlRWE5yZThBbWhrMHh0KzdTVFg5UkVTK2VZY2hMbWdJMmVpUzZuMEIzU2g3WDN2RWdtTm0wYmdFNmRqa3krMjIydTZXV1FwRnVjVFdBQ3BOcjU2Tnl5SW5aM2FnQVJPTzkvMWN1M2krcEduS2V2RGNHVVBpNHprVmV4UUF4YzVVN2s9Iiwia2V5SWQiOiJhcHBpZDpzbS1waW5ha2EtcHJvZDo6c20tcGluYWthLXByb2Qta2V5In0=
      - key: ref1_public_key
        value: |-
          eyJkYXRhIjoidmF1bHQ6djE6QXp1c0QyMGdSVlFVbFYrNis0UktST3NJU2VyWEZaT1pQUjJQb3Bhb1Vvd1g0R29BVzVZZ1hNYkNydW9ZYXIzdmc1MlIzTWx4SnBuMjc1UlVQWE1BN1JRYUhsM0d0S3JnS29QM1IzNG9STDhJdE1vVmF1eGlNNUVEK09FNG5udE1iTHo3R0NUK01lenJibXV0bkFBNVhWZVJNeEFXL3Y5aVZzbEhtalk4c0tvRlJ5eURBYlZ5ZHg5M3Z1QjFaY3RoQW00NW9UWnhQaDlzblp5Z2dobEx2K3MrM2JUUDFuMXptc04rOWhEc29yMWdNbFd5c1dWZ09oN3p3MlJuMkVoSTZaMmlTWkdpQ044a044TFMyMkpPVVRJdXlhdFl6Uk1ody9wRUNLSUtWaS9Cd2JOTmJxbWFHQTRvTUduNnF2T2pwUmNLNkE9PSIsImtleUlkIjoiYXBwaWQ6c20tcGluYWthLXByb2Q6OnNtLXBpbmFrYS1wcm9kLWtleSJ9