apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    sf.app/mail: {{ .Values.fcp.mail}}
    sf.app/name: {{ .Values.fcp.appId}}
    sf.app/team: {{ .Values.fcp.team}}
    sf.app/version: {{ .Chart.AppVersion | quote }}
    sf.chart/version: {{ .Chart.Version | quote }}
    sf.fcp/appid: {{ .Values.fcp.appId}}
    sf.helmchart/version: {{ .Chart.Version | quote }}
  name: axis-recon-weekly

spec:
  schedule: {{ .Values.cronjobspec.weeklySchedule | quote }}
  concurrencyPolicy: Forbid
  suspend: false
  successfulJobsHistoryLimit: {{default 5 .Values.cronjobspec.successfulJobsHistoryLimit}}
  failedJobsHistoryLimit: {{default 5 .Values.cronjobspec.failedJobsHistoryLimit}}
  jobTemplate:
    spec:
      parallelism: {{ default 1 .Values.parallelism }}
      completions:  {{ default 1 .Values.completions }}
      backoffLimit:  {{ default 8 .Values.backoffLimit }}
      activeDeadlineSeconds: {{ default 600 .Values.activeDeadlineSeconds }}
      template:
        metadata:
          labels:
            sf.app : {{ .Values.fcp.appId}}
            sf.app.pod/appId : {{ .Values.fcp.appId}}
          annotations:      
        spec:
          securityContext: 
          volumes:
          - name: podinfo-service
            downwardAPI:
              items:
              - path: "cpu_request"
                resourceFieldRef:
                  containerName: service
                  resource: requests.cpu
              - path: "mem_request"
                resourceFieldRef:
                  containerName: service
                  resource: requests.memory    
          restartPolicy:  OnFailure
          containers:      
            - name: service
              image: {{ if .Values.image.service.registry }}{{ .Values.image.service.registry }}{{ else }}edge.fkinternal.com{{ end }}/{{ .Values.image.service.repository }}:{{ .Values.image.service.tag }}
              securityContext:
                runAsGroup:  {{ .Values.image.service.runAsGroup}}
                runAsUser: {{ .Values.image.service.runAsUser}}
                runAsNonRoot: true
                capabilities:
                  add:
                    - NET_BIND_SERVICE
              env:
              - name: "winterfell_ip"
                value: {{ .Values.image.envVars.winterfell_ip | quote }}
              - name: "pinaka_ip"
                value: {{ .Values.image.envVars.pinaka_ip | quote }}
              - name: "db_host"
                value: {{ .Values.image.envVars.db_host | quote }}
              - name: "db_name"
                value: {{ .Values.image.envVars.db_name | quote }}
              - name: "db_user"
                value: {{ .Values.image.envVars.db_user | quote }}
              - name: "db_password"
                value: {{ .Values.image.envVars.db_password | quote }}
              - name: "offset_days"
                value: {{ .Values.image.weeklyReconEnvVars.offset_days | quote }}
              - name: "hour_delta"
                value: {{ .Values.image.weeklyReconEnvVars.hour_delta | quote }}
              - name: "total_duration"
                value: {{ .Values.image.weeklyReconEnvVars.total_duration | quote }}

              imagePullPolicy: IfNotPresent
              
              resources:
                limits:
                  cpu: {{ .Values.image.service.maxCpuLimit}}
                  memory: {{ .Values.image.service.maxMemoryLimit}}
                requests:
                  cpu: {{ .Values.image.service.maxCpuAsk}}
                  memory: {{ .Values.image.service.maxMemoryAsk}}
              volumeMounts:
                - name: podinfo-service
                  mountPath: /etc/podinfo
