namespace: sm-comms-scheduler-playground

fcp:
  team: "supermoney-engg"
  appId: "sm-pinaka-preprod"
  mail: "supermoney"
  service: "sm-pinaka-preprod"

pod:
  replicaCount: 1

cronjobspec:
  dailySchedule: "*/10 * * * *"
  weeklySchedule: "0 1 * * 2"
  successfulJobsHistoryLimit: 10
  failedJobsHistoryLimit: 10

image:
  service:
    tag: "3165441--flow-004c1581b3d3f79ac845a50d2278e58e8dcc9044-20-Dec-23_12-46-48"
    repository: supermoney-engg/axis-recon/
    registry: edge.fkinternal.com
    runAsUser: 122
    runAsGroup: 118
    maxCpuLimit: 10
    maxCpuAsk: 10
    maxMemoryLimit: 10Gi
    maxMemoryAsk: 10Gi
  envVars:
    winterfell_ip: winterfell-service.sm-winterfell-playground.fkcloud.in
    pinaka_ip: pinaka-service.sm-pinaka-playground.fkcloud.in
    db_host: master.supermoney-dev-sm-mysql-preprod.prod.altair.fkcloud.in
    db_name: sm_winterfell_preprod
    db_user: sm_r
    db_password: UPSEVdpTVR
  dailyReconEnvVars:
    offset_days: 0
    hour_delta: 3
    total_duration: 36
  weeklyReconEnvVars:
    offset_days: 7
    hour_delta: 9
    total_duration: 180

activeDeadlineSeconds: 86400
