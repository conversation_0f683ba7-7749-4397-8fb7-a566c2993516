fcp:
  team: Lending_LOS
  appId: fintech-pinaka-relayer-prod
  mail: FPG-CF-LOS-Engg
  service: pinaka-relayer
configMaps:
  envName: calvin
  configs:
    - name: relayer-config    #this name should not be changed
      source: dir
      sourceLoc: app   #make sure the config file name inside config dir is relayer.yml only

sf-turbo-relayer:
  namespace: fintech-pinaka-relayer-prod
  fcp:
    team: Lending_LOS
    appId: fintech-pinaka-relayer-prod
    mail: bnpl-oncall
    service: pinaka-relayer
  pod:
    replicaCount: 2     #this is optional, default is 1
  image:
    turbo-relayer:
      tag: v3.0.9-flow-0c7caf7b16dcaeab2b47be9a4542a014cb7ebb66-02-Mar-23_15-32-04
      maxCpuLimit: 3
      maxCpuAsk: 3
      maxMemoryLimit: 7Gi
      maxMemoryAsk: 7Gi
      envVars:
        - name: THREADSTACKSIZE_RELAYER
          source: "value"
          value: "10m"
        - name: MEMORY
          source: "value"
          value: "5600m"
  secrets:
    - name: relayer-secrets     #this name should not be changed
      data:
        - key: mysql.fintech-pinaka-relayer-prod-app.password
          value: "eyJkYXRhIjoidmF1bHQ6djE6OHJvNnNSMENUNWtsd2xrL2FqUW45K2Z6S2QzWmI1d1gxWE5tVFcyd0pvbzBrVGFpaStvSTNWY084cUx2M0JPNiIsImtleUlkIjoiTGVuZGluZ19MT1M6OmRlZmF1bHQifQ=="
        - key: mysql.fintech-pinaka-relayer-prod-outbound.password
          value: "eyJkYXRhIjoidmF1bHQ6djE6OHJvNnNSMENUNWtsd2xrL2FqUW45K2Z6S2QzWmI1d1gxWE5tVFcyd0pvbzBrVGFpaStvSTNWY084cUx2M0JPNiIsImtleUlkIjoiTGVuZGluZ19MT1M6OmRlZmF1bHQifQ=="