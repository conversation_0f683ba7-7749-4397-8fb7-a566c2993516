partitionConfiguration:
  mode: AUTO
  size: 5000000
  noOfExtraPartitions: 30
  noOfDaysToPreserve: 2
  archivalPath: "/tmp"
  frequencyInHrs: 12
  scheduleJobRetryTime: 120000
  scheduleJobTries: 3
  archivalDestination: "/tmp"
  monitorThreadSleepTime: 5000
  deadlockQueryExecutionTime: 60
  jobTime: "04:50:00"
mysql:
  fintech-pinaka-relayer-prod-app:
    hibernate.c3p0_idle_test_period: '6000'
    hibernate.c3p0_max_size: '20'
    hibernate.c3p0_max_statements: '50'
    hibernate.c3p0_min_size: '2'
    hibernate.c3p0_timeout: '300'
    hibernate.connection.driver_class: com.mysql.jdbc.Driver
    hibernate.id: app
    hibernate.show_sql: 'false'
    hibernate.connection.url: "****************************************************************************************************************************************************"
    hibernate.connection.username: 'v_fint_uUK6dvXhT'
    hibernate.connection.zeroDateTimeBehavior: convertToNull
  fintech-pinaka-relayer-prod-outbound:
    hibernate.c3p0_idle_test_period: '6000'
    hibernate.c3p0_max_size: '300'
    hibernate.c3p0_max_statements: '50'
    hibernate.c3p0_min_size: '20'
    hibernate.c3p0_timeout: '300'
    hibernate.connection,driver_class: com.mysql.jdbc.Driver
    hibernate.id: outbound
    hibernate.connection.show_sql: 'false'
    hibernate.connection.url: "**********************************************************************************************************************************************************"
    hibernate.connection.username: 'v_fint_uUK6dvXhT'
    hibernate.connection.zeroDateTimeBehavior: convertToNull
authConfig:
  authnUrl: http://10.47.0.167/
  clientSecret: VdsO8BjanfTADqp3RNaaDm0bkm14iyVGJ3lUuGdaSTS+7Ba0
  clientId: pinaka-service
defaultVaridhiServer: http://***********/
hashingAlgorithm: JAVA_HASHCODE
alertzEndpoint:  "http://10.47.0.149"
defaultCreateAlert:  true
relayers:
  - name: default
    active: true
    readerMainQueueSize: 20000
    readerBatchSize: 10000
    readerParallelismDegree: 5
    readerSleepTime: 100
    processorParallelismDegree: 53
    processorQueueSize: 1000
    lastProcessedPersistInterval: 1000
    maxApplicationTransactionTime: '300000'
    varidhiServer: http://***********/
    appDbRef:
      type: mysql
      id: fintech-pinaka-relayer-prod-app
    turboReadMode: SEQUENCE_READER
    outboundDbRef:
      type: mysql
      id: fintech-pinaka-relayer-prod-outbound
    leaderElectionEnabled: true
  - name: queues
    active: true
    readerMainQueueSize: 20000
    readerBatchSize: 10000
    readerParallelismDegree: 5
    readerSleepTime: 100
    processorParallelismDegree: 23
    processorQueueSize: 1000
    lastProcessedPersistInterval: 1000
    maxApplicationTransactionTime: '300000'
    varidhiServer: http://***********/
    appDbRef:
      type: mysql
      id: fintech-pinaka-relayer-prod-app
    turboReadMode: SEQUENCE_READER
    outboundDbRef:
      type: mysql
      id: fintech-pinaka-relayer-prod-outbound
    leaderElectionEnabled: true
appLogLevel: ERROR
relayingLogLevel: ERROR
emailConfiguration:
  smtpHost: "localhost"
  fromAddress: <EMAIL>
  toAddress: <EMAIL>
appName: fintech-pinaka-relayer-prod
appUserName: varadhi
teamName: Lending_LOS