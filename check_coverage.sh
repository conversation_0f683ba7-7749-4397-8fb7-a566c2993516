#!/bin/zsh

# Script to check test coverage for changed files without sending to SonarQube
# Usage: ./check_coverage.sh

set -e  # Exit on error

echo "===== Generating test coverage reports for sm-pinaka ====="

# Path to project
PROJECT_PATH="/Users/<USER>/projects/sm-pinaka"
cd $PROJECT_PATH

# Run tests and generate Jacoco reports
echo "Running tests and generating Jacoco reports..."
mvn clean test -Dmaven.test.failure.ignore=true -q

echo "\n===== Overall Coverage by Module ====="
for module in ams-bridge ams-connector offer-orchestrator pinaka-api pinaka-client pinaka-common pinaka-service profile-service; do
  if [[ -f "$module/target/site/jacoco-ut/index.html" ]]; then
    echo "Coverage for $module:"
    grep -A 5 "Total" $module/target/site/jacoco-ut/index.html | grep -o '<td class="ctr2">[^<]*' | cut -d'>' -f2 | head -3 | paste -sd " " -
  else
    echo "No coverage report found for $module"
  fi
done

echo "\n===== Coverage for Changed Files ====="
for file in $(cat changed_files.txt | grep -v "json\|resources\|template"); do
  # Skip non-Java files and test files
  if [[ "$file" != *.java || "$file" == */test/* ]]; then
    continue
  fi
  
  echo "\nChecking coverage for $file"
  module=$(echo $file | cut -d'/' -f1)
  class_path=$(echo $file | sed 's/.*src\/main\/java\///g' | sed 's/\.java$//g' | sed 's/\//\./g')
  
  # Check if a test file exists
  test_file=$(echo $file | sed 's/\/main\//\/test\//g' | sed 's/\.java$/Test.java/')
  if [[ -f "$test_file" ]]; then
    echo "✅ Test file exists: $test_file"
  else
    echo "⚠️ Test MISSING: $test_file"
  fi
  
  if [[ -f "$module/target/site/jacoco-ut/jacoco.xml" ]]; then
    # Try to extract coverage information using different patterns
    coverage_info=$(grep -A 20 "$class_path" "$module/target/site/jacoco-ut/jacoco.xml" | grep -E "line-rate=\"[0-9\.]*\"" | head -1)
    
    if [[ ! -z "$coverage_info" ]]; then
      line_rate=$(echo $coverage_info | grep -o 'line-rate="[0-9.]*"' | cut -d'"' -f2)
      if [[ ! -z "$line_rate" ]]; then
        coverage_pct=$(echo "scale=2; $line_rate * 100" | bc)
        
        # Determine if it meets the threshold
        if (( $(echo "$coverage_pct < 80" | bc -l) )); then
          echo "❌ Coverage: $coverage_pct% (below 80% threshold)"
        else
          echo "✅ Coverage: $coverage_pct% (meets or exceeds 80% threshold)"
        fi
      else
        echo "⚠️ Could not determine coverage percentage"
      fi
    else
      echo "⚠️ No coverage information found for $class_path"
    fi
  else
    echo "⚠️ No Jacoco report found for $module"
  fi
done

echo "\n===== Summary of Files Below 80% Coverage ====="
below_threshold=0
for file in $(cat changed_files.txt | grep -v "json\|resources\|template"); do
  # Skip non-Java files and test files
  if [[ "$file" != *.java || "$file" == */test/* ]]; then
    continue
  fi
  
  module=$(echo $file | cut -d'/' -f1)
  class_path=$(echo $file | sed 's/.*src\/main\/java\///g' | sed 's/\.java$//g' | sed 's/\//\./g')
  
  if [[ -f "$module/target/site/jacoco-ut/jacoco.xml" ]]; then
    coverage_info=$(grep -A 20 "$class_path" "$module/target/site/jacoco-ut/jacoco.xml" | grep -E "line-rate=\"[0-9\.]*\"" | head -1)
    
    if [[ ! -z "$coverage_info" ]]; then
      line_rate=$(echo $coverage_info | grep -o 'line-rate="[0-9.]*"' | cut -d'"' -f2)
      if [[ ! -z "$line_rate" ]]; then
        coverage_pct=$(echo "scale=2; $line_rate * 100" | bc)
        
        if (( $(echo "$coverage_pct < 80" | bc -l) )); then
          echo "$file: $coverage_pct%"
          below_threshold=$((below_threshold + 1))
        fi
      fi
    else
      echo "$file: No coverage data (likely uncovered)"
      below_threshold=$((below_threshold + 1))
    fi
  fi
done

echo "\n===== Coverage Status ====="
total_files=$(cat changed_files.txt | grep -v "json\|resources\|template" | grep "\.java$" | grep -v "/test/" | wc -l | tr -d ' ')

if [[ $below_threshold -gt 0 ]]; then
  echo "❌ $below_threshold out of $total_files Java files are below the 80% coverage threshold"
  exit 1
else
  echo "✅ All changed Java files meet the 80% coverage threshold"
  exit 0
fi
