1. Create directories:
```bash
sudo mkdir "/var/log/pinaka"
sudo mkdir "/etc/config"
```
2. Copy file
```bash
sudo cp ../sm-pinaka/packaging-k8s/pinaka/config/gcp-preprod/app/hystrix.properties /etc/config/
```
3. Ensure that you have copied the certificate from bastion profile screen to ~/.ssh/id_rsa-cert.pub
4. Start tunnel, if not already running:
```bash
sshuttle -r iaas-cli-0001.ch.flipkart.com  10.0.0.0/8
```
5. Build the project (against java 8)
```bash
mvn clean install -DskipTests
```
6. Run the following command to start the application:
```bash
java -jar target/pinaka-service-3.4.10-SM.jar server .../sm-pinaka/packaging-k8s/pinaka/config/dev1/app/config.yml
```
7. To start the application within intellij:
   1. [Add configuration](https://www.jetbrains.com/help/idea/run-debug-configuration.html#createExplicitly)
   2. Set the main class as `com.flipkart.fintech.pinaka.service.application.PinakaApplication`
   3. Point configuration to `server /Users/<USER>/projects/sm-pinaka/packaging-k8s/pinaka/config/gcp-preprod/app/config.yml` in program arguments
   4. You can now run or debug the application

## Troubleshooting
1. If you are facing issues with bucket
```
Error in custom provider, com.flipkart.kloud.config.error.NotFoundException: Bucket not found: sm-pinaka-preprod
  at com.flipkart.fintech.cryptex.CryptexBundleModule.providesDynamicBucket(CryptexBundleModule.java:52)
  at com.flipkart.fintech.cryptex.CryptexBundleModule.providesDynamicBucket(CryptexBundleModule.java:52)
  while locating com.flipkart.kloud.config.DynamicBucket
```
run the following command:
```bash
{
    echo "host"="api.aso1.cfgsvc-prod.fkcloud.in"
    echo "port=80"
} | sudo tee /etc/default/cfg-api
```

2. If there are issues with Application Default Credentials:
```
[ERROR] Errors: 
[ERROR]   NewCreditReportDaoTest.setUp:36 » IO Your default credentials were not found. To set up Application Default Credentials for your environment, see https://cloud.google.com/docs/authentication/external/set-up-adc.
[ERROR]   NewCreditReportDaoTest.setUp:36 » IO Your default credentials were not found. To set up Application Default Credentials for your environment, see https://cloud.google.com/docs/authentication/external/set-up-adc.
[ERROR]   NewCreditReportDaoTest.setUp:36 » IO Your default credentials were not found. To set up Application Default Credentials for your environment, see https://cloud.google.com/docs/authentication/external/set-up-adc.
[ERROR]   BureauDataManagerImplTest.testAutoRefreshBureauData_WhenHitIdIsNotNull:118 » NullPointer
```
You can choose to run mvn install without test cases
```bash
mvn install -DskipTests
```