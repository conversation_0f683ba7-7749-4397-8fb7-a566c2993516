[INFO] Scanning for projects...
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for com.flipkart.fintech:pinaka-service:jar:2.2.67-SM1-SNAPSHOT
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: com.flipkart.fintech:winterfell-client:jar -> duplicate declaration of version ${winterfell.version} @ line 1433, column 21
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: com.flipkart.fintech:consent-client:jar -> version 1.0.23 vs 1.0.24 @ line 1517, column 21
[WARNING] 
[WARNING] It is highly recommended to fix these problems because they threaten the stability of your build.
[WARNING] 
[WARNING] For this reason, future Maven versions might no longer support building such malformed projects.
[WARNING] 
[INFO] 
[INFO] ----------------< com.flipkart.fintech:pinaka-service >-----------------
[INFO] Building pinaka-service 2.2.67-SM1-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[WARNING] The artifact org.mapstruct:mapstruct-jdk8:jar:1.3.0.Final has been relocated to org.mapstruct:mapstruct:jar:1.3.0.Final
[INFO] 
[INFO] --- dependency:2.4:tree (default-cli) @ pinaka-service ---
[INFO] artifact joda-time:joda-time: checking for updates from fk-jfrog-release
[WARNING] While downloading org.mapstruct:mapstruct-jdk8:1.3.0.Final
  This artifact has been relocated to org.mapstruct:mapstruct:1.3.0.Final.


[INFO] com.flipkart.fintech:pinaka-service:jar:2.2.67-SM1-SNAPSHOT
[INFO] +- com.flipkart.kloud.config:client-java:jar:1.5.9:compile
[INFO] |  +- com.flipkart.kloud.config:commons-java:jar:1.5.9:compile
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.13.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-core:jar:3.1.0:compile - omitted for conflict with 4.1.5)
[INFO] |  |  +- (com.google.guava:guava:jar:18.0:compile - omitted for conflict with 23.0)
[INFO] |  |  +- com.flipkart.security:cryptex-client-interface:jar:1.0.3:compile
[INFO] |  |  |  \- com.flipkart.security:cryptex-data-models:jar:1.1.0:compile
[INFO] |  |  |     +- (org.hibernate:hibernate-core:jar:5.4.3.Final:compile - omitted for conflict with 5.4.4.Final)
[INFO] |  |  |     +- (javax.validation:validation-api:jar:2.0.1.Final:compile - omitted for duplicate)
[INFO] |  |  |     +- (com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.8.9:compile - omitted for conflict with 2.10.3)
[INFO] |  |  |     \- (org.junit.jupiter:junit-jupiter:jar:5.6.1:compile - omitted for conflict with 5.4.2)
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.6:compile - omitted for duplicate)
[INFO] |  +- (org.apache.httpcomponents:httpclient:jar:4.3.3:compile - omitted for conflict with 4.5.8)
[INFO] |  \- org.slf4j:slf4j-api:jar:1.7.6:compile
[INFO] +- com.flipkart.affordability:bnpl-khaata-client:jar:1.1.93:compile
[INFO] +- org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.4.10:compile
[INFO] |  +- org.jetbrains.kotlin:kotlin-stdlib:jar:1.4.10:compile
[INFO] |  |  +- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.4.10:compile
[INFO] |  |  \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  \- org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.4.10:compile
[INFO] |     \- (org.jetbrains.kotlin:kotlin-stdlib:jar:1.4.10:compile - omitted for duplicate)
[INFO] +- com.google.code.gson:gson:jar:2.8.9:compile
[INFO] +- com.squareup.okhttp3:okhttp:jar:4.9.1:compile
[INFO] |  +- com.squareup.okio:okio:jar:2.8.0:compile
[INFO] |  |  +- (org.jetbrains.kotlin:kotlin-stdlib:jar:1.4.0:compile - omitted for conflict with 1.4.10)
[INFO] |  |  \- (org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.4.0:compile - omitted for conflict with 1.4.10)
[INFO] |  \- (org.jetbrains.kotlin:kotlin-stdlib:jar:1.4.10:compile - omitted for duplicate)
[INFO] +- org.eclipse.jetty:jetty-servlet:jar:9.4.37.v20210219:compile
[INFO] |  +- org.eclipse.jetty:jetty-security:jar:9.4.37.v20210219:compile
[INFO] |  |  \- (org.eclipse.jetty:jetty-server:jar:9.4.37.v20210219:compile - omitted for conflict with 9.4.30.v20200611)
[INFO] |  \- org.eclipse.jetty:jetty-util-ajax:jar:9.4.37.v20210219:compile
[INFO] |     \- (org.eclipse.jetty:jetty-util:jar:9.4.37.v20210219:compile - omitted for duplicate)
[INFO] +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] +- org.elasticsearch.client:elasticsearch-rest-high-level-client:jar:7.8.0:compile
[INFO] |  +- org.elasticsearch:elasticsearch:jar:7.8.0:compile
[INFO] |  |  +- org.elasticsearch:elasticsearch-core:jar:7.8.0:compile
[INFO] |  |  +- org.elasticsearch:elasticsearch-secure-sm:jar:7.8.0:compile
[INFO] |  |  +- org.elasticsearch:elasticsearch-x-content:jar:7.8.0:compile
[INFO] |  |  |  +- (org.elasticsearch:elasticsearch-core:jar:7.8.0:compile - omitted for duplicate)
[INFO] |  |  |  +- (org.yaml:snakeyaml:jar:1.26:compile - omitted for conflict with 1.24)
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.4:compile - omitted for conflict with 2.13.3)
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-smile:jar:2.10.4:compile
[INFO] |  |  |  +- (com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.10.4:compile - omitted for conflict with 2.10.3)
[INFO] |  |  |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:jar:2.10.4:compile
[INFO] |  |  +- org.elasticsearch:elasticsearch-geo:jar:7.8.0:compile
[INFO] |  |  +- org.apache.lucene:lucene-core:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-analyzers-common:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-backward-codecs:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-grouping:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-highlighter:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-join:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-memory:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-misc:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-queries:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-queryparser:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-sandbox:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-spatial-extras:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-spatial3d:jar:8.5.1:compile
[INFO] |  |  +- org.apache.lucene:lucene-suggest:jar:8.5.1:compile
[INFO] |  |  +- org.elasticsearch:elasticsearch-cli:jar:7.8.0:compile
[INFO] |  |  |  +- net.sf.jopt-simple:jopt-simple:jar:5.0.2:compile
[INFO] |  |  |  \- (org.elasticsearch:elasticsearch-core:jar:7.8.0:compile - omitted for duplicate)
[INFO] |  |  +- com.carrotsearch:hppc:jar:0.8.1:compile
[INFO] |  |  +- (joda-time:joda-time:jar:2.10.4:compile - omitted for conflict with 2.9.9)
[INFO] |  |  +- com.tdunning:t-digest:jar:3.2:compile
[INFO] |  |  +- (org.hdrhistogram:HdrHistogram:jar:2.1.9:compile - omitted for duplicate)
[INFO] |  |  +- org.apache.logging.log4j:log4j-api:jar:2.11.1:compile
[INFO] |  |  \- org.elasticsearch:jna:jar:4.5.1:compile
[INFO] |  +- org.elasticsearch.client:elasticsearch-rest-client:jar:7.8.0:compile
[INFO] |  |  +- (org.apache.httpcomponents:httpclient:jar:4.5.10:compile - omitted for conflict with 4.3.3)
[INFO] |  |  +- (org.apache.httpcomponents:httpcore:jar:4.4.12:compile - omitted for conflict with 4.3.2)
[INFO] |  |  +- org.apache.httpcomponents:httpasyncclient:jar:4.1.4:compile
[INFO] |  |  +- org.apache.httpcomponents:httpcore-nio:jar:4.4.12:compile
[INFO] |  |  +- (commons-codec:commons-codec:jar:1.11:compile - omitted for conflict with 1.6)
[INFO] |  |  \- (commons-logging:commons-logging:jar:1.1.3:compile - omitted for duplicate)
[INFO] |  +- org.elasticsearch.plugin:mapper-extras-client:jar:7.8.0:compile
[INFO] |  +- org.elasticsearch.plugin:parent-join-client:jar:7.8.0:compile
[INFO] |  +- org.elasticsearch.plugin:aggs-matrix-stats-client:jar:7.8.0:compile
[INFO] |  +- org.elasticsearch.plugin:rank-eval-client:jar:7.8.0:compile
[INFO] |  \- org.elasticsearch.plugin:lang-mustache-client:jar:7.8.0:compile
[INFO] |     \- com.github.spullara.mustache.java:compiler:jar:0.9.6:compile
[INFO] +- org.gnu:gnu-crypto:jar:2.0.1:compile
[INFO] +- com.ffb.cse:credit_service_clients:jar:1.1.8:compile
[INFO] |  +- org.glassfish.jersey.ext:jersey-proxy-client:jar:2.29:compile
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-common:jar:2.29:compile - omitted for conflict with 2.19)
[INFO] |  |  \- (jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile - omitted for duplicate)
[INFO] |  +- org.glassfish.jersey.inject:jersey-hk2:jar:2.29:compile
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-common:jar:2.29:compile - omitted for duplicate)
[INFO] |  |  \- org.glassfish.hk2:hk2-locator:jar:2.5.0:compile
[INFO] |  |     +- (org.glassfish.hk2.external:jakarta.inject:jar:2.5.0:compile - omitted for duplicate)
[INFO] |  |     +- org.glassfish.hk2.external:aopalliance-repackaged:jar:2.5.0:compile
[INFO] |  |     +- (org.glassfish.hk2:hk2-api:jar:2.5.0:compile - omitted for conflict with 2.4.0-b25)
[INFO] |  |     +- org.glassfish.hk2:hk2-utils:jar:2.5.0:compile
[INFO] |  |     |  +- (jakarta.annotation:jakarta.annotation-api:jar:1.3.4:compile - omitted for duplicate)
[INFO] |  |     |  \- (org.glassfish.hk2.external:jakarta.inject:jar:2.5.0:compile - omitted for duplicate)
[INFO] |  |     +- (jakarta.annotation:jakarta.annotation-api:jar:1.3.4:compile - omitted for duplicate)
[INFO] |  |     \- (org.javassist:javassist:jar:3.22.0-CR2:compile - omitted for conflict with 3.27.0-GA)
[INFO] |  +- (org.apache.httpcomponents:httpclient:jar:4.5.6:compile - omitted for conflict with 4.3.3)
[INFO] |  +- com.amazonaws:aws-java-sdk-s3:jar:1.9.3:compile
[INFO] |  |  \- (com.amazonaws:aws-java-sdk-core:jar:1.9.3:compile - omitted for conflict with 1.9.4)
[INFO] |  +- joda-time:joda-time:jar:2.9.9:compile
[INFO] |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.9.6:compile
[INFO] +- com.flipkart.fintech:consent-client:jar:1.0.24:compile
[INFO] |  \- org.glassfish.jersey.core:jersey-client:jar:2.30.1:compile
[INFO] |     +- (jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile - omitted for conflict with 2.1.6)
[INFO] |     +- (org.glassfish.jersey.core:jersey-common:jar:2.30.1:compile - omitted for conflict with 2.29)
[INFO] |     \- (org.glassfish.hk2.external:jakarta.inject:jar:2.6.1:compile - omitted for duplicate)
[INFO] +- io.reactivex:rxjava:jar:1.3.8:compile
[INFO] +- org.liquibase:liquibase-core:jar:3.8.5:compile
[INFO] |  +- javax.xml.bind:jaxb-api:jar:2.3.0:compile
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.28:compile - omitted for conflict with 1.7.6)
[INFO] |  \- ch.qos.logback:logback-classic:jar:1.2.3:compile
[INFO] |     +- ch.qos.logback:logback-core:jar:1.2.3:compile
[INFO] |     \- (org.slf4j:slf4j-api:jar:1.7.25:compile - omitted for conflict with 1.7.6)
[INFO] +- org.testng:testng:jar:6.9.8:compile
[INFO] |  +- org.beanshell:bsh:jar:2.0b4:compile
[INFO] |  \- com.beust:jcommander:jar:1.48:compile
[INFO] +- com.flipkart.fintech:fintech-common-exception:jar:1.0.22:compile
[INFO] |  +- (org.projectlombok:lombok:jar:1.18.12:compile - omitted for conflict with 1.18.10)
[INFO] |  \- javax.ws.rs:javax.ws.rs-api:jar:2.0.1:compile
[INFO] +- com.h2database:h2:jar:1.4.195:compile
[INFO] +- org.mapstruct:mapstruct-processor:jar:1.3.0.Final:compile
[INFO] +- org.mapstruct:mapstruct:jar:1.3.0.Final:compile
[INFO] +- (org.mapstruct:mapstruct:jar:1.3.0.Final:compile - omitted for duplicate)
[INFO] +- org.hibernate:hibernate-envers:jar:5.4.4.Final:compile
[INFO] |  +- org.jboss.logging:jboss-logging:jar:3.3.2.Final:compile
[INFO] |  \- org.hibernate:hibernate-core:jar:5.4.4.Final:compile
[INFO] |     +- (org.jboss.logging:jboss-logging:jar:3.3.2.Final:compile - omitted for duplicate)
[INFO] |     +- (javax.persistence:javax.persistence-api:jar:2.2:compile - omitted for duplicate)
[INFO] |     +- net.bytebuddy:byte-buddy:jar:1.9.11:compile
[INFO] |     +- antlr:antlr:jar:2.7.7:compile
[INFO] |     +- org.jboss.spec.javax.transaction:jboss-transaction-api_1.2_spec:jar:1.1.1.Final:compile
[INFO] |     +- org.jboss:jandex:jar:2.0.5.Final:compile
[INFO] |     +- com.fasterxml:classmate:jar:1.3.4:compile
[INFO] |     +- javax.activation:javax.activation-api:jar:1.2.0:compile
[INFO] |     +- org.dom4j:dom4j:jar:2.1.1:compile
[INFO] |     +- org.hibernate.common:hibernate-commons-annotations:jar:5.1.0.Final:compile
[INFO] |     |  \- (org.jboss.logging:jboss-logging:jar:3.3.2.Final:compile - omitted for duplicate)
[INFO] |     +- (javax.xml.bind:jaxb-api:jar:2.3.1:compile - omitted for conflict with 2.3.0)
[INFO] |     \- org.glassfish.jaxb:jaxb-runtime:jar:2.3.1:compile
[INFO] |        +- (javax.xml.bind:jaxb-api:jar:2.3.1:compile - omitted for conflict with 2.3.0)
[INFO] |        +- org.glassfish.jaxb:txw2:jar:2.3.1:compile
[INFO] |        +- com.sun.istack:istack-commons-runtime:jar:3.0.7:compile
[INFO] |        +- org.jvnet.staxex:stax-ex:jar:1.8:compile
[INFO] |        +- com.sun.xml.fastinfoset:FastInfoset:jar:1.2.15:compile
[INFO] |        \- (javax.activation:javax.activation-api:jar:1.2.0:compile - omitted for duplicate)
[INFO] +- com.fasterxml.jackson.core:jackson-databind:jar:2.10.2:compile
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.2:compile - omitted for conflict with 2.9.6)
[INFO] |  \- com.fasterxml.jackson.core:jackson-core:jar:2.10.2:compile
[INFO] +- com.flipkart.ca.pages:model:jar:2.0.10:compile
[INFO] +- com.flipkart.fintech:pandora-client:jar:1.2.39:compile
[INFO] |  +- com.flipkart.fintech:pandora-api:jar:1.2.39:compile
[INFO] |  |  +- org.codehaus.jackson:jackson-mapper-asl:jar:1.5.0:compile
[INFO] |  |  |  \- org.codehaus.jackson:jackson-core-asl:jar:1.5.0:compile
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:*******:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.flipkart.sensitive:sensitive-annotation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.10.3:compile - omitted for conflict with 2.7.8)
[INFO] |  |  \- (org.apache.maven.shared:maven-shared-utils:jar:3.3.4:compile - omitted for duplicate)
[INFO] |  +- (com.google.inject:guice:jar:3.0:compile - omitted for conflict with 4.0)
[INFO] |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.0.1:compile - omitted for duplicate)
[INFO] |  +- javax.inject:javax.inject:jar:1:compile
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.24:compile - omitted for conflict with 1.7.6)
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.3.2:compile
[INFO] |  +- (joda-time:joda-time:jar:2.9.7:compile - omitted for conflict with 2.9.9)
[INFO] |  +- (com.flipkart.fintech:fintech-common-headers:jar:1.0.40:compile - omitted for conflict with ********)
[INFO] |  +- com.flipkart.fintech:pandora-service-client:jar:1.2.39:compile
[INFO] |  |  +- (org.eclipse.jetty:jetty-servlet:jar:9.4.37.v20210219:compile - omitted for duplicate)
[INFO] |  |  +- (org.eclipse.jetty:jetty-http:jar:9.4.37.v20210219:compile - omitted for conflict with 9.4.30.v20200611)
[INFO] |  |  +- (org.eclipse.jetty:jetty-util:jar:9.4.37.v20210219:compile - omitted for duplicate)
[INFO] |  |  +- (com.squareup.okhttp3:okhttp:jar:3.14.0:compile - omitted for conflict with 4.9.1)
[INFO] |  |  +- com.flipkart.kloud.authn:jwt-validator:jar:**********:compile
[INFO] |  |  |  +- (com.mashape.unirest:unirest-java:jar:1.4.5:compile - omitted for duplicate)
[INFO] |  |  |  \- (com.flipkart.kloud.authn:commons:jar:**********:compile - omitted for duplicate)
[INFO] |  |  +- com.nimbusds:nimbus-jose-jwt:jar:5.13:compile
[INFO] |  |  |  \- com.github.stephenc.jcip:jcip-annotations:jar:1.0-1:compile
[INFO] |  |  +- (com.flipkart.affordability:bnpl-tijori-api:jar:2.0.129:compile - omitted for conflict with 2.0.117)
[INFO] |  |  +- (org.apache.commons:commons-lang3:jar:3.5:compile - omitted for conflict with 3.3.2)
[INFO] |  |  +- (com.flipkart.fintech:fintech-cryptex-bundle:jar:1.0.57:compile - omitted for duplicate)
[INFO] |  |  +- (org.apache.httpcomponents:httpclient:jar:4.4:compile - omitted for conflict with 4.3.3)
[INFO] |  |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.0.1:compile - omitted for duplicate)
[INFO] |  |  +- (com.google.inject:guice:jar:3.0:compile - omitted for duplicate)
[INFO] |  |  +- (javax.inject:javax.inject:jar:1:compile - omitted for duplicate)
[INFO] |  |  +- (com.netflix.hystrix:hystrix-core:jar:1.5.5:compile - omitted for conflict with 1.5.18)
[INFO] |  |  +- (io.reactivex:rxjava:jar:1.2.0:compile - omitted for conflict with 1.3.8)
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.24:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- (joda-time:joda-time:jar:2.9.7:compile - omitted for conflict with 2.9.9)
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-common:jar:2.30.1:compile - omitted for conflict with 2.29)
[INFO] |  |  +- (com.amazonaws:aws-java-sdk-s3:jar:1.9.3:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-testing:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (com.flipkart.fintech:fintech-common-headers:jar:1.0.40:compile - omitted for duplicate)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.2:compile - omitted for duplicate)
[INFO] |  |  +- (com.flipkart.sensitive:sensitive-annotation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (com.flipkart.fintech:fintech-commons:jar:1.0.106:compile - omitted for conflict with 1.0.40)
[INFO] |  |  \- (org.apache.maven.shared:maven-shared-utils:jar:3.3.4:compile - omitted for duplicate)
[INFO] |  \- org.apache.maven.shared:maven-shared-utils:jar:3.3.4:compile
[INFO] |     \- (commons-io:commons-io:jar:2.6:compile - omitted for duplicate)
[INFO] +- net.minidev:json-smart:jar:2.3.1:compile
[INFO] |  \- net.minidev:accessors-smart:jar:2.3.1:compile
[INFO] |     \- (org.ow2.asm:asm:jar:5.0.4:compile - omitted for duplicate)
[INFO] +- com.github.jknack:handlebars-maven-plugin:jar:4.1.2:compile
[INFO] |  \- com.github.jknack:handlebars:jar:4.1.2:compile
[INFO] |     \- (org.slf4j:slf4j-api:jar:1.7.25:compile - omitted for conflict with 1.7.6)
[INFO] +- com.flipkart.fintech:pandora-lite-client:jar:1.0.14:compile
[INFO] |  +- com.flipkart.fintech:pandora-lite-models:jar:1.0.14:compile
[INFO] |  |  +- (org.codehaus.jackson:jackson-mapper-asl:jar:1.5.0:compile - omitted for duplicate)
[INFO] |  |  \- (com.flipkart.sensitive:sensitive-annotation:jar:2.0.5:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (com.google.inject:guice:jar:3.0:compile - omitted for duplicate)
[INFO] |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.0.1:compile - omitted for duplicate)
[INFO] |  +- (javax.inject:javax.inject:jar:1:compile - omitted for duplicate)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.24:compile - omitted for conflict with 1.7.6)
[INFO] |  +- (org.apache.commons:commons-lang3:jar:3.3.2:compile - omitted for duplicate)
[INFO] |  \- (joda-time:joda-time:jar:2.9.7:compile - omitted for conflict with 2.9.9)
[INFO] +- com.flipkart.affordability:bnpl-tijori-api:jar:2.0.117:compile
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- com.flipkart.fintech:skyler-api-models:jar:1.8.14:compile
[INFO] |  |  +- (com.flipkart.affordability:bnpl-tijori-api:jar:2.0.72:compile - omitted for cycle)
[INFO] |  |  +- (com.flipkart.fintech:skyler-billing:jar:1.8.11:compile - omitted for conflict with 1.8.7)
[INFO] |  |  +- (com.flipkart.affordability:bnpl-khaata-client:jar:1.1.87:compile - omitted for conflict with 1.1.93)
[INFO] |  |  +- (org.projectlombok:lombok:jar:1.18.20:compile - omitted for conflict with 1.18.12)
[INFO] |  |  +- (joda-time:joda-time:jar:2.10.10:compile - omitted for conflict with 2.9.9)
[INFO] |  |  +- (com.google.guava:guava:jar:30.1.1-jre:compile - omitted for conflict with 18.0)
[INFO] |  |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 3.0)
[INFO] |  |  +- (mysql:mysql-connector-java:jar:5.1.37:compile - omitted for conflict with 8.0.25)
[INFO] |  |  +- org.glassfish.jersey.bundles.repackaged:jersey-guava:jar:2.6:compile
[INFO] |  |  +- (com.flipkart.fintech:fintech-common-exception:jar:1.0.97:compile - omitted for conflict with 1.0.22)
[INFO] |  |  +- in.cleartax.dropwizard:sharding-core:jar:0.2.201-fk-2:compile
[INFO] |  |  |  +- (org.projectlombok:lombok:jar:1.16.18:compile - omitted for conflict with 1.18.12)
[INFO] |  |  |  +- (io.dropwizard:dropwizard-core:jar:1.3.2:compile - omitted for conflict with 2.0.4)
[INFO] |  |  |  +- (io.dropwizard:dropwizard-hibernate:jar:1.3.2:compile - omitted for conflict with 2.0.4)
[INFO] |  |  |  +- (com.google.inject:guice:jar:4.2.0:compile - omitted for conflict with 3.0)
[INFO] |  |  |  +- org.glassfish.hk2:guice-bridge:jar:2.5.0-b36:compile
[INFO] |  |  |  |  +- (javax.inject:javax.inject:jar:1:compile - omitted for duplicate)
[INFO] |  |  |  |  \- (org.glassfish.hk2:hk2-api:jar:2.5.0-b36:compile - omitted for conflict with 2.5.0)
[INFO] |  |  |  \- ru.vyarus:dropwizard-guicey:jar:4.0.1:compile
[INFO] |  |  |     +- (com.google.inject:guice:jar:4.1.0:compile - omitted for conflict with 3.0)
[INFO] |  |  |     +- com.google.inject.extensions:guice-servlet:jar:4.1.0:compile
[INFO] |  |  |     |  \- (com.google.inject:guice:jar:4.1.0:compile - omitted for conflict with 3.0)
[INFO] |  |  |     +- (com.google.inject.extensions:guice-multibindings:jar:4.1.0:compile - omitted for duplicate)
[INFO] |  |  |     +- (io.dropwizard:dropwizard-core:jar:1.0.5:compile - omitted for conflict with 1.3.2)
[INFO] |  |  |     \- ru.vyarus:generics-resolver:jar:2.0.1:compile
[INFO] |  |  +- (com.flipkart.fintech:fintech-common-CT:jar:1.0.73:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-testing:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (org.apache.commons:commons-collections4:jar:4.1:compile - omitted for conflict with 4.3)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.2:compile - omitted for duplicate)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.2:compile - omitted for duplicate)
[INFO] |  |  +- (com.palominolabs.metrics:metrics-guice:jar:3.2.2:compile - omitted for conflict with 3.1.3)
[INFO] |  |  +- (io.dropwizard:dropwizard-core:jar:2.0.4:compile - omitted for conflict with 1.0.0)
[INFO] |  |  +- (io.dropwizard:dropwizard-hibernate:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (org.hibernate:hibernate-core:jar:5.4.3.Final:compile - omitted for conflict with 5.4.4.Final)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-annotation:jar:4.1.5:compile - omitted for conflict with 3.1.0)
[INFO] |  |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (com.smoketurner:dropwizard-swagger:jar:1.3.17-1:compile - omitted for conflict with 1.0.0-1)
[INFO] |  |  +- (com.flipkart.fintech:fintech-redis-client:jar:1.0.86:compile - omitted for duplicate)
[INFO] |  |  \- (com.flipkart.affordability:bnpl-collections-model:jar:1.4.12:compile - omitted for conflict with 1.2.6)
[INFO] |  +- com.flipkart.fintech:skyler-core-sdk:jar:1.8.11:compile
[INFO] |  |  +- com.flipkart.fintech:skyler-common:jar:1.8.11:compile
[INFO] |  |  |  +- (com.flipkart.scheduler:scheduler-client-api:jar:0.10.0:compile - omitted for duplicate)
[INFO] |  |  |  +- (com.flipkart.restbus.hibernate:turbo-client:jar:2.3.1-hibernate-5.4.10:compile - omitted for duplicate)
[INFO] |  |  |  +- (com.flipkart.affordability:affordability-service-clients:jar:4.0.20:compile - omitted for conflict with 1.1.43)
[INFO] |  |  |  +- (com.flipkart.cp.usercluster:pathfinder:jar:1.9.2:compile - omitted for duplicate)
[INFO] |  |  |  +- (com.flipkart.affordability:bnpl-khaata-client:jar:1.1.87:compile - omitted for conflict with 1.1.93)
[INFO] |  |  |  +- (org.projectlombok:lombok:jar:1.18.20:compile - omitted for conflict with 1.18.12)
[INFO] |  |  |  +- (joda-time:joda-time:jar:2.10.10:compile - omitted for conflict with 2.9.9)
[INFO] |  |  |  +- (com.google.guava:guava:jar:30.1.1-jre:compile - omitted for conflict with 18.0)
[INFO] |  |  |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 3.0)
[INFO] |  |  |  +- (mysql:mysql-connector-java:jar:5.1.37:compile - omitted for duplicate)
[INFO] |  |  |  +- (org.glassfish.jersey.bundles.repackaged:jersey-guava:jar:2.6:compile - omitted for duplicate)
[INFO] |  |  |  +- (com.flipkart.fintech:fintech-common-exception:jar:1.0.97:compile - omitted for conflict with 1.0.22)
[INFO] |  |  |  +- (in.cleartax.dropwizard:sharding-core:jar:0.2.201-fk-2:compile - omitted for duplicate)
[INFO] |  |  |  +- (com.flipkart.fintech:fintech-common-CT:jar:1.0.73:compile - omitted for duplicate)
[INFO] |  |  |  +- (io.dropwizard:dropwizard-testing:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  |  +- (org.apache.commons:commons-collections4:jar:4.1:compile - omitted for conflict with 4.3)
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.2:compile - omitted for duplicate)
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.2:compile - omitted for duplicate)
[INFO] |  |  |  +- (com.palominolabs.metrics:metrics-guice:jar:3.2.2:compile - omitted for duplicate)
[INFO] |  |  |  +- (io.dropwizard:dropwizard-core:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  |  +- (io.dropwizard:dropwizard-hibernate:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  |  +- (org.hibernate:hibernate-core:jar:5.4.3.Final:compile - omitted for conflict with 5.4.4.Final)
[INFO] |  |  |  +- (io.dropwizard.metrics:metrics-annotation:jar:4.1.2:compile - omitted for duplicate)
[INFO] |  |  |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  |  +- (com.smoketurner:dropwizard-swagger:jar:1.3.17-1:compile - omitted for duplicate)
[INFO] |  |  |  +- (com.flipkart.fintech:fintech-redis-client:jar:1.0.86:compile - omitted for duplicate)
[INFO] |  |  |  \- (com.flipkart.affordability:bnpl-collections-model:jar:1.4.12:compile - omitted for duplicate)
[INFO] |  |  +- (com.flipkart.affordability:bnpl-khaata-client:jar:1.1.87:compile - omitted for conflict with 1.1.93)
[INFO] |  |  +- (org.projectlombok:lombok:jar:1.18.20:compile - omitted for conflict with 1.18.12)
[INFO] |  |  +- (joda-time:joda-time:jar:2.10.10:compile - omitted for conflict with 2.9.9)
[INFO] |  |  +- (com.google.guava:guava:jar:30.1.1-jre:compile - omitted for conflict with 18.0)
[INFO] |  |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 3.0)
[INFO] |  |  +- (mysql:mysql-connector-java:jar:5.1.37:compile - omitted for duplicate)
[INFO] |  |  +- (org.glassfish.jersey.bundles.repackaged:jersey-guava:jar:2.6:compile - omitted for duplicate)
[INFO] |  |  +- (com.flipkart.fintech:fintech-common-exception:jar:1.0.97:compile - omitted for conflict with 1.0.22)
[INFO] |  |  +- (in.cleartax.dropwizard:sharding-core:jar:0.2.201-fk-2:compile - omitted for duplicate)
[INFO] |  |  +- (com.flipkart.fintech:fintech-common-CT:jar:1.0.73:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-testing:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (org.apache.commons:commons-collections4:jar:4.1:compile - omitted for conflict with 4.3)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.2:compile - omitted for duplicate)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.2:compile - omitted for duplicate)
[INFO] |  |  +- (com.palominolabs.metrics:metrics-guice:jar:3.2.2:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-core:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-hibernate:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (org.hibernate:hibernate-core:jar:5.4.3.Final:compile - omitted for conflict with 5.4.4.Final)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-annotation:jar:4.1.2:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (com.smoketurner:dropwizard-swagger:jar:1.3.17-1:compile - omitted for duplicate)
[INFO] |  |  +- (com.flipkart.fintech:fintech-redis-client:jar:1.0.86:compile - omitted for duplicate)
[INFO] |  |  \- (com.flipkart.affordability:bnpl-collections-model:jar:1.4.12:compile - omitted for duplicate)
[INFO] |  +- com.flipkart.affordability:bnpl-shylock-model:jar:1.0.29:compile
[INFO] |  |  +- (joda-time:joda-time:jar:2.9.1:compile - omitted for conflict with 2.9.9)
[INFO] |  |  \- (org.projectlombok:lombok:jar:1.18.12:compile - omitted for duplicate)
[INFO] |  +- (com.flipkart.fintech:fintech-common-headers:jar:1.0.43:compile - omitted for conflict with 1.0.40)
[INFO] |  +- (com.flipkart.affordability:bnpl-tijori-api:jar:2.0.72:compile - omitted for cycle)
[INFO] |  +- com.flipkart.affordability:bnpl-khaata-model:jar:1.1.93:compile
[INFO] |  \- cglib:cglib-nodep:jar:2.2:compile
[INFO] +- com.flipkart.affordability:bnpl-tijori-client:jar:2.0.117:compile
[INFO] |  +- (com.flipkart.fintech:fintech-common-headers:jar:1.0.43:compile - omitted for conflict with 1.0.40)
[INFO] |  +- com.flipkart.kloud.authn:client:jar:**********:compile
[INFO] |  |  +- com.mashape.unirest:unirest-java:jar:1.4.5:compile
[INFO] |  |  |  +- (org.apache.httpcomponents:httpclient:jar:4.3.6:compile - omitted for conflict with 4.3.3)
[INFO] |  |  |  +- (org.apache.httpcomponents:httpasyncclient:jar:4.0.2:compile - omitted for conflict with 4.1.4)
[INFO] |  |  |  \- (org.apache.httpcomponents:httpmime:jar:4.3.6:compile - omitted for duplicate)
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.7:compile - omitted for conflict with 1.7.6)
[INFO] |  |  \- com.flipkart.kloud.authn:commons:jar:**********:compile
[INFO] |  |     +- (com.mashape.unirest:unirest-java:jar:1.4.5:compile - omitted for duplicate)
[INFO] |  |     \- (org.slf4j:slf4j-api:jar:1.7.7:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (cglib:cglib-nodep:jar:2.2:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech:ardour-api:jar:1.0.83:compile
[INFO] |  \- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.7.8:compile
[INFO] +- com.flipkart.sensitive:sensitive-annotation:jar:2.0.4:compile
[INFO] |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 3.0)
[INFO] |  +- org.slf4j:slf4j-simple:jar:1.7.28:compile
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.28:compile - omitted for conflict with 1.7.6)
[INFO] |  \- javax.persistence:javax.persistence-api:jar:2.2:compile
[INFO] +- com.flipkart.fintech:fintech-journey:jar:1.0.0:compile
[INFO] |  +- (com.flipkart.scheduler:scheduler-client-api:jar:0.10.0:compile - omitted for duplicate)
[INFO] |  +- com.flipkart.fintech:fintech-common-communication:jar:1.0.32:compile
[INFO] |  |  +- (com.flipkart.fintech:fintech-commons:jar:1.0.32:compile - omitted for conflict with 1.0.40)
[INFO] |  |  +- (com.flipkart.affordability:affordability-service-clients:jar:1.1.43:compile - omitted for conflict with 1.1.88)
[INFO] |  |  +- (javax.persistence:javax.persistence-api:jar:2.2:compile - omitted for duplicate)
[INFO] |  |  +- (org.hibernate:hibernate-core:jar:5.4.12.Final:compile - omitted for conflict with 5.4.4.Final)
[INFO] |  |  +- (com.google.inject:guice:jar:4.0:compile - omitted for conflict with 3.0)
[INFO] |  |  +- (com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.6.7:compile - omitted for duplicate)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.6.7:compile
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.6.7:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.6.7:compile - omitted for conflict with 2.10.2)
[INFO] |  |  \- (com.google.guava:guava:jar:15.0:compile - omitted for conflict with 18.0)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  \- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] +- com.flipkart.fintech:fintech-common-security:jar:1.0.89:compile
[INFO] |  +- (com.flipkart.kloud.authn:client:jar:**********:compile - omitted for conflict with **********)
[INFO] |  +- com.flipkart:gibraltar-api:jar:1.0.14:compile
[INFO] |  |  +- (javax.validation:validation-api:jar:1.1.0.Final:compile - omitted for conflict with 2.0.1.Final)
[INFO] |  |  +- (org.apache.commons:commons-lang3:jar:3.4:compile - omitted for conflict with 3.3.2)
[INFO] |  |  \- (javax.ws.rs:javax.ws.rs-api:jar:2.0:compile - omitted for conflict with 2.0.1)
[INFO] |  +- (org.glassfish.jersey.core:jersey-common:jar:2.19:compile - omitted for conflict with 2.30.1)
[INFO] |  +- (org.apache.httpcomponents:httpclient:jar:4.5.2:compile - omitted for conflict with 4.3.3)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for conflict with 3.0)
[INFO] +- com.flipkart.fintech:fintech-common-headers:jar:********:compile
[INFO] |  +- (com.flipkart.fintech:fintech-common-exception:jar:********:compile - omitted for conflict with 1.0.22)
[INFO] |  +- (org.apache.commons:commons-lang3:jar:3.7:compile - omitted for conflict with 3.3.2)
[INFO] |  +- (com.flipkart.fintech:fintech-commons:jar:1.0.75:compile - omitted for conflict with 1.0.122)
[INFO] |  +- org.apache.commons:commons-collections4:jar:4.3:compile
[INFO] |  +- (commons-io:commons-io:jar:2.6:compile - omitted for conflict with 2.5)
[INFO] |  +- (com.flipkart.fintech:fintech-journey:jar:********:compile - omitted for conflict with 1.0.0)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for conflict with 3.0)
[INFO] +- com.flipkart.fintech:fintech-commons:jar:1.0.122:compile
[INFO] |  +- io.jsonwebtoken:jjwt:jar:0.9.1:compile
[INFO] |  |  \- (com.fasterxml.jackson.core:jackson-databind:jar:2.9.6:compile - omitted for conflict with 2.10.2)
[INFO] |  +- io.jsonwebtoken:jjwt-api:jar:0.11.2:compile
[INFO] |  +- io.jsonwebtoken:jjwt-impl:jar:0.11.2:runtime
[INFO] |  |  \- (io.jsonwebtoken:jjwt-api:jar:0.11.2:runtime - omitted for duplicate)
[INFO] |  +- io.jsonwebtoken:jjwt-jackson:jar:0.11.2:runtime
[INFO] |  |  +- (io.jsonwebtoken:jjwt-api:jar:0.11.2:runtime - omitted for duplicate)
[INFO] |  |  \- (com.fasterxml.jackson.core:jackson-databind:jar:********:runtime - omitted for conflict with 2.10.2)
[INFO] |  +- io.dropwizard:dropwizard-lifecycle:jar:2.0.2:compile
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- (com.google.guava:guava:jar:28.2-jre:compile - omitted for conflict with 18.0)
[INFO] |  |  +- (org.eclipse.jetty:jetty-server:jar:9.4.26.v20200117:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  |  +- (jakarta.servlet:jakarta.servlet-api:jar:4.0.3:compile - scope updated from runtime; omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-util:jar:2.0.2:compile - omitted for conflict with 2.0.4)
[INFO] |  |  \- (io.dropwizard.metrics:metrics-core:jar:4.1.2:compile - omitted for conflict with 3.1.0)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for conflict with 3.0)
[INFO] +- org.apache.httpcomponents:httpclient:jar:4.5.8:compile
[INFO] |  +- org.apache.httpcomponents:httpcore:jar:4.4.11:compile
[INFO] |  +- commons-logging:commons-logging:jar:1.2:compile
[INFO] |  \- (commons-codec:commons-codec:jar:1.11:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech:ardour-client:jar:1.0.83:compile
[INFO] |  +- (com.google.inject:guice:jar:4.1.0:compile - omitted for conflict with 3.0)
[INFO] |  +- (com.flipkart.fintech:ardour-api:jar:1.0.83:compile - omitted for duplicate)
[INFO] |  \- (com.flipkart.affordability:affordability-service-clients:jar:1.1.88:compile - omitted for conflict with 4.0.271)
[INFO] +- com.flipkart.fintech:pinaka-api:jar:2.2.67-SM1-SNAPSHOT:compile
[INFO] |  +- (com.flipkart.sensitive:sensitive-annotation:jar:2.0.5:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.10.2:compile - omitted for conflict with 2.7.8)
[INFO] |  +- (javax.validation:validation-api:jar:2.0.1.Final:compile - omitted for duplicate)
[INFO] |  +- (org.glassfish.jersey.media:jersey-media-multipart:jar:2.30.1:compile - omitted for duplicate)
[INFO] |  +- (com.google.inject:guice:jar:4.0:compile - omitted for conflict with 3.0)
[INFO] |  +- (com.flipkart.fintech:fintech-commons:jar:1.0.122:compile - omitted for duplicate)
[INFO] |  +- (com.flipkart.fintech:pandora-api:jar:1.2.39:compile - omitted for duplicate)
[INFO] |  +- com.flipkart.affordability:fintech-underwriting-model:jar:1.8.23:compile
[INFO] |  |  +- (com.google.inject:guice:jar:4.0:compile - omitted for conflict with 3.0)
[INFO] |  |  +- (com.google.guava:guava:jar:23.0:compile - omitted for conflict with 18.0)
[INFO] |  |  \- (com.flipkart.fintech:fintech-commons:jar:1.0.42:compile - omitted for conflict with 1.0.122)
[INFO] |  +- (com.flipkart.cri:alfred-api:jar:4.3.14:compile - omitted for duplicate)
[INFO] |  +- com.flipkart.fintech:winterfell-api:jar:1.0.49:compile
[INFO] |  |  +- (io.dropwizard:dropwizard-jackson:jar:1.1.5:compile - omitted for conflict with 2.0.4)
[INFO] |  |  +- (com.flipkart.fintech:citadel-api:jar:1.0.32:compile - omitted for conflict with 1.0.35)
[INFO] |  |  +- (com.flipkart.fintech:stratum-api:jar:1.0.74:compile - omitted for conflict with 1.0.92)
[INFO] |  |  +- (org.eclipse.jetty:jetty-util:jar:9.4.37.v20210219:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-validation:jar:1.3.21:compile - omitted for conflict with 2.0.4)
[INFO] |  |  \- (com.flipkart.fintech:fintech-commons:jar:1.0.94:compile - omitted for conflict with 1.0.122)
[INFO] |  +- (com.flipkart.fintech:ardour-api:jar:1.0.83:compile - omitted for duplicate)
[INFO] |  +- com.flipkart.fintech:stratum-api:jar:1.0.92:compile
[INFO] |  |  +- (io.dropwizard:dropwizard-jackson:jar:1.3.5:compile - omitted for conflict with 2.0.4)
[INFO] |  |  +- (javax.validation:validation-api:jar:1.1.0.Final:compile - omitted for conflict with 2.0.1.Final)
[INFO] |  |  +- (com.flipkart.fintech:winterfell-api:jar:1.0.47:compile - omitted for conflict with 1.0.49)
[INFO] |  |  +- (org.eclipse.jetty:jetty-util:jar:9.4.37.v20210219:compile - omitted for duplicate)
[INFO] |  |  +- (com.flipkart.sensitive:sensitive-annotation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (com.flipkart.kloud.config:commons-java:jar:1.5.1:compile - omitted for conflict with 1.5.9)
[INFO] |  |  \- (com.amazonaws:aws-java-sdk-s3:jar:1.9.4:compile - omitted for conflict with 1.9.3)
[INFO] |  +- (org.elasticsearch:elasticsearch:jar:7.8.0:compile - omitted for duplicate)
[INFO] |  \- (com.flipkart.affordability:affordability-service-clients:jar:4.0.271:compile - omitted for conflict with 1.1.88)
[INFO] +- com.google.guava:guava:jar:23.0:compile
[INFO] |  +- com.google.errorprone:error_prone_annotations:jar:2.0.18:compile
[INFO] |  +- com.google.j2objc:j2objc-annotations:jar:1.1:compile
[INFO] |  \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.14:compile
[INFO] +- com.flipkart.fintech:pinaka-workflow:jar:2.2.67-SM1-SNAPSHOT:compile
[INFO] |  +- (com.flipkart.affordability:affordability-service-clients:jar:4.0.271:compile - omitted for conflict with 1.1.88)
[INFO] |  +- com.flipkart.fintech:altdata-orchestrator-api:jar:0.1.3:compile
[INFO] |  |  \- com.flipkart.fintech:altdata-common-model:jar:0.1.0:compile
[INFO] |  +- (org.eclipse.jetty:jetty-server:jar:9.4.30.v20200611:compile - omitted for duplicate)
[INFO] |  \- (org.eclipse.jetty:jetty-util:jar:9.4.37.v20210219:compile - omitted for duplicate)
[INFO] +- com.smoketurner:dropwizard-swagger:jar:1.0.0-1:compile
[INFO] |  +- (io.dropwizard:dropwizard-core:jar:1.0.0:compile - omitted for conflict with 2.0.4)
[INFO] |  +- io.dropwizard:dropwizard-assets:jar:1.0.0:compile
[INFO] |  |  +- (io.dropwizard:dropwizard-core:jar:1.0.0:compile - omitted for duplicate)
[INFO] |  |  \- (io.dropwizard:dropwizard-servlets:jar:1.0.0:compile - omitted for duplicate)
[INFO] |  +- io.dropwizard:dropwizard-views:jar:1.0.0:compile
[INFO] |  |  \- (io.dropwizard:dropwizard-core:jar:1.0.0:compile - omitted for duplicate)
[INFO] |  +- io.dropwizard:dropwizard-views-freemarker:jar:1.0.0:compile
[INFO] |  |  +- (io.dropwizard:dropwizard-views:jar:1.0.0:compile - omitted for duplicate)
[INFO] |  |  \- org.freemarker:freemarker:jar:2.3.23:compile
[INFO] |  +- io.swagger:swagger-jersey2-jaxrs:jar:1.5.9:compile
[INFO] |  |  \- io.swagger:swagger-jaxrs:jar:1.5.9:compile
[INFO] |  |     +- io.swagger:swagger-core:jar:1.5.9:compile
[INFO] |  |     |  \- io.swagger:swagger-models:jar:1.5.9:compile
[INFO] |  |     |     \- io.swagger:swagger-annotations:jar:1.5.9:compile
[INFO] |  |     \- org.reflections:reflections:jar:0.9.10:compile
[INFO] |  |        \- com.google.code.findbugs:annotations:jar:2.0.1:compile
[INFO] |  \- xml-apis:xml-apis:jar:1.4.01:compile
[INFO] +- io.dropwizard:dropwizard-core:jar:2.0.4:compile
[INFO] |  +- io.dropwizard:dropwizard-util:jar:2.0.4:compile
[INFO] |  |  +- (com.google.guava:guava:jar:28.2-jre:compile - omitted for conflict with 23.0)
[INFO] |  |  \- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  +- io.dropwizard:dropwizard-validation:jar:2.0.4:compile
[INFO] |  |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (com.fasterxml:classmate:jar:1.5.1:compile - omitted for conflict with 1.3.4)
[INFO] |  |  +- (org.hibernate.validator:hibernate-validator:jar:6.1.2.Final:compile - omitted for duplicate)
[INFO] |  |  +- (com.google.guava:guava:jar:28.2-jre:compile - omitted for conflict with 23.0)
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  |  \- (org.glassfish:jakarta.el:jar:3.0.3:compile - scope updated from runtime; omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-configuration:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- io.dropwizard:dropwizard-logging:jar:2.0.4:compile
[INFO] |  |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for conflict with 3.1.0)
[INFO] |  |  +- io.dropwizard.metrics:metrics-logback:jar:4.1.5:compile
[INFO] |  |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for conflict with 3.1.0)
[INFO] |  |  |  +- (ch.qos.logback:logback-classic:jar:1.2.3:compile - omitted for duplicate)
[INFO] |  |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- (org.apache.commons:commons-lang3:jar:3.9:compile - omitted for conflict with 3.3.2)
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- org.slf4j:jul-to-slf4j:jar:1.7.30:compile
[INFO] |  |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- (ch.qos.logback:logback-core:jar:1.2.3:compile - omitted for duplicate)
[INFO] |  |  +- (ch.qos.logback:logback-classic:jar:1.2.3:compile - omitted for duplicate)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  |  +- io.dropwizard.logback:logback-throttling-appender:jar:1.1.0:compile
[INFO] |  |  +- org.slf4j:log4j-over-slf4j:jar:1.7.30:runtime
[INFO] |  |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:runtime - omitted for conflict with 1.7.6)
[INFO] |  |  \- (org.slf4j:jcl-over-slf4j:jar:1.7.30:compile - scope updated from runtime; omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-metrics:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- io.dropwizard:dropwizard-servlets:jar:2.0.4:compile
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-annotation:jar:4.1.5:compile - omitted for conflict with 4.1.5)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for conflict with 3.1.0)
[INFO] |  |  +- (ch.qos.logback:logback-classic:jar:1.2.3:compile - omitted for duplicate)
[INFO] |  |  \- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  +- io.dropwizard:dropwizard-jetty:jar:2.0.4:compile
[INFO] |  |  +- (io.dropwizard:dropwizard-logging:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for conflict with 3.1.0)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  |  +- (org.apache.commons:commons-lang3:jar:3.9:compile - omitted for conflict with 3.3.2)
[INFO] |  |  +- (org.eclipse.jetty:jetty-io:jar:9.4.27.v20200227:compile - omitted for conflict with 9.4.30.v20200611)
[INFO] |  |  +- (org.eclipse.jetty:jetty-security:jar:9.4.27.v20200227:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- (org.eclipse.jetty:jetty-servlet:jar:9.4.27.v20200227:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  |  +- org.eclipse.jetty:jetty-servlets:jar:9.4.27.v20200227:compile
[INFO] |  |  |  +- (org.eclipse.jetty:jetty-continuation:jar:9.4.27.v20200227:compile - omitted for conflict with 9.4.26.v20200117)
[INFO] |  |  |  +- (org.eclipse.jetty:jetty-http:jar:9.4.27.v20200227:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  |  |  \- (org.eclipse.jetty:jetty-io:jar:9.4.27.v20200227:compile - omitted for conflict with 9.4.30.v20200611)
[INFO] |  |  \- (org.eclipse.jetty:jetty-http:jar:9.4.27.v20200227:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  +- (io.dropwizard:dropwizard-lifecycle:jar:2.0.4:compile - omitted for conflict with 2.0.2)
[INFO] |  +- io.dropwizard.metrics:metrics-core:jar:4.1.5:compile
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  +- io.dropwizard.metrics:metrics-jetty9:jar:4.1.5:compile
[INFO] |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  +- io.dropwizard.metrics:metrics-jvm:jar:4.1.5:compile
[INFO] |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  +- io.dropwizard.metrics:metrics-jmx:jar:4.1.5:compile
[INFO] |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  +- io.dropwizard.metrics:metrics-servlets:jar:4.1.5:compile
[INFO] |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-healthchecks:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  |  +- io.dropwizard.metrics:metrics-json:jar:4.1.5:compile
[INFO] |  |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.9.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-jvm:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  |  +- com.helger:profiler:jar:1.1.1:compile
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.9.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  +- io.dropwizard.metrics:metrics-healthchecks:jar:4.1.5:compile
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  +- io.dropwizard:dropwizard-request-logging:jar:2.0.4:compile
[INFO] |  |  +- (io.dropwizard:dropwizard-logging:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- ch.qos.logback:logback-access:jar:1.2.3:compile
[INFO] |  |  |  \- (ch.qos.logback:logback-core:jar:1.2.3:compile - omitted for duplicate)
[INFO] |  |  +- (ch.qos.logback:logback-classic:jar:1.2.3:compile - omitted for duplicate)
[INFO] |  |  +- (ch.qos.logback:logback-core:jar:1.2.3:compile - omitted for duplicate)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  |  +- (org.eclipse.jetty:jetty-http:jar:9.4.27.v20200227:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  +- (ch.qos.logback:logback-classic:jar:1.2.3:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  +- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] |  +- net.sourceforge.argparse4j:argparse4j:jar:0.8.1:compile
[INFO] |  +- (org.eclipse.jetty:jetty-security:jar:9.4.27.v20200227:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  +- (org.eclipse.jetty:jetty-servlet:jar:9.4.27.v20200227:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  +- org.eclipse.jetty.toolchain.setuid:jetty-setuid-java:jar:1.0.4:compile
[INFO] |  +- org.glassfish.hk2.external:jakarta.inject:jar:2.6.1:compile
[INFO] |  +- (org.glassfish.jersey.core:jersey-common:jar:2.30.1:compile - omitted for conflict with 2.19)
[INFO] |  +- org.glassfish.jersey.ext:jersey-bean-validation:jar:2.30.1:compile
[INFO] |  |  +- (org.glassfish.hk2.external:jakarta.inject:jar:2.6.1:compile - omitted for duplicate)
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-common:jar:2.30.1:compile - omitted for conflict with 2.19)
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-server:jar:2.30.1:compile - omitted for duplicate)
[INFO] |  |  +- (org.hibernate.validator:hibernate-validator:jar:6.0.17.Final:compile - omitted for conflict with 6.1.2.Final)
[INFO] |  |  \- org.glassfish:jakarta.el:jar:3.0.3:compile
[INFO] |  +- org.hibernate.validator:hibernate-validator:jar:6.1.2.Final:compile
[INFO] |  |  +- (org.jboss.logging:jboss-logging:jar:3.3.2.Final:compile - omitted for duplicate)
[INFO] |  |  \- (com.fasterxml:classmate:jar:1.3.4:compile - omitted for duplicate)
[INFO] |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] +- org.eclipse.jetty:jetty-server:jar:9.4.30.v20200611:compile
[INFO] |  +- javax.servlet:javax.servlet-api:jar:3.1.0:compile
[INFO] |  +- org.eclipse.jetty:jetty-http:jar:9.4.30.v20200611:compile
[INFO] |  |  +- (org.eclipse.jetty:jetty-util:jar:9.4.30.v20200611:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  |  \- (org.eclipse.jetty:jetty-io:jar:9.4.30.v20200611:compile - omitted for duplicate)
[INFO] |  \- org.eclipse.jetty:jetty-io:jar:9.4.30.v20200611:compile
[INFO] |     \- (org.eclipse.jetty:jetty-util:jar:9.4.30.v20200611:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] +- org.eclipse.jetty:jetty-util:jar:9.4.37.v20210219:compile
[INFO] +- io.dropwizard:dropwizard-configuration:jar:2.0.4:compile
[INFO] |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.10.3:compile
[INFO] |  |  +- (org.yaml:snakeyaml:jar:1.24:compile - omitted for conflict with 1.14)
[INFO] |  |  \- (com.fasterxml.jackson.core:jackson-core:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:2.8.1:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.1.0:compile
[INFO] |  |  \- (com.google.errorprone:error_prone_annotations:jar:2.3.4:compile - omitted for conflict with 2.0.18)
[INFO] |  \- (org.apache.commons:commons-text:jar:1.8:compile - omitted for conflict with 1.6)
[INFO] +- io.dropwizard:dropwizard-testing:jar:2.0.4:test (scope not updated to compile)
[INFO] |  +- (io.dropwizard:dropwizard-configuration:jar:2.0.4:test - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-jackson:jar:2.0.4:compile - scope updated from test; omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-jetty:jar:2.0.4:test - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-lifecycle:jar:2.0.4:test - omitted for conflict with 2.0.2)
[INFO] |  +- (io.dropwizard:dropwizard-logging:jar:2.0.4:test - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-servlets:jar:2.0.4:test - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:test - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:test - omitted for duplicate)
[INFO] |  +- (io.dropwizard.metrics:metrics-annotation:jar:4.1.5:compile - scope updated from test; omitted for duplicate)
[INFO] |  +- (io.dropwizard.metrics:metrics-healthchecks:jar:4.1.5:test - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.3:test - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:test - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.10.3:test - omitted for conflict with 2.6.7)
[INFO] |  +- (com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:jar:2.10.3:test - omitted for conflict with ********)
[INFO] |  +- (com.google.code.findbugs:jsr305:jar:3.0.2:test - omitted for duplicate)
[INFO] |  +- (jakarta.servlet:jakarta.servlet-api:jar:4.0.3:compile - scope updated from test; omitted for duplicate)
[INFO] |  +- (jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile - scope updated from test; omitted for duplicate)
[INFO] |  +- (junit:junit:jar:4.13:test - omitted for conflict with 4.11)
[INFO] |  +- (net.sourceforge.argparse4j:argparse4j:jar:0.8.1:test - omitted for duplicate)
[INFO] |  +- (org.junit.jupiter:junit-jupiter:jar:5.6.1:compile - scope updated from test; omitted for duplicate)
[INFO] |  +- (org.junit.jupiter:junit-jupiter-api:jar:5.6.1:compile - scope updated from test; omitted for duplicate)
[INFO] |  +- (org.junit.platform:junit-platform-commons:jar:1.6.1:compile - scope updated from test; omitted for duplicate)
[INFO] |  +- (org.eclipse.jetty:jetty-io:jar:9.4.27.v20200227:test - omitted for conflict with 9.4.30.v20200611)
[INFO] |  +- (org.eclipse.jetty:jetty-server:jar:9.4.27.v20200227:test - omitted for conflict with 9.4.30.v20200611)
[INFO] |  +- (org.glassfish.jersey.containers:jersey-container-servlet-core:jar:2.30.1:compile - scope updated from test; omitted for duplicate)
[INFO] |  +- (org.glassfish.jersey.core:jersey-common:jar:2.30.1:test - omitted for conflict with 2.19)
[INFO] |  +- (org.glassfish.jersey.core:jersey-server:jar:2.30.1:compile - scope updated from test; omitted for duplicate)
[INFO] |  +- org.glassfish.jersey.test-framework:jersey-test-framework-core:jar:2.30.1:test
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-server:jar:2.30.1:test - omitted for duplicate)
[INFO] |  |  +- (org.glassfish.jersey.containers:jersey-container-servlet-core:jar:2.30.1:test - omitted for duplicate)
[INFO] |  |  +- (jakarta.servlet:jakarta.servlet-api:jar:4.0.3:test - omitted for duplicate)
[INFO] |  |  \- (junit:junit:jar:4.12:test - omitted for conflict with 4.13)
[INFO] |  +- org.glassfish.jersey.test-framework.providers:jersey-test-framework-provider-inmemory:jar:2.30.1:test
[INFO] |  |  +- (org.glassfish.jersey.test-framework:jersey-test-framework-core:jar:2.30.1:test - omitted for duplicate)
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-server:jar:2.30.1:test - omitted for duplicate)
[INFO] |  |  \- (junit:junit:jar:4.12:test - omitted for conflict with 4.13)
[INFO] |  +- (jakarta.activation:jakarta.activation-api:jar:1.2.2:runtime - scope updated from test; omitted for duplicate)
[INFO] |  \- (jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:runtime - scope updated from test; omitted for duplicate)
[INFO] +- io.dropwizard:dropwizard-hibernate:jar:2.0.4:compile
[INFO] |  +- io.dropwizard:dropwizard-db:jar:2.0.4:compile
[INFO] |  |  +- (io.dropwizard:dropwizard-configuration:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-jackson:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-lifecycle:jar:2.0.4:compile - omitted for conflict with 2.0.2)
[INFO] |  |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-healthchecks:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.3:compile - omitted for conflict with 2.9.6)
[INFO] |  |  +- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  |  \- (org.apache.tomcat:tomcat-*********************** - omitted for duplicate)
[INFO] |  +- io.dropwizard:dropwizard-jackson:jar:2.0.4:compile
[INFO] |  |  +- (com.google.guava:guava:jar:28.2-jre:compile - omitted for conflict with 23.0)
[INFO] |  |  +- (com.github.ben-manes.caffeine:caffeine:jar:2.8.1:compile - omitted for duplicate)
[INFO] |  |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.3:compile - omitted for conflict with 2.9.6)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.10.3:compile - omitted for conflict with 2.6.7)
[INFO] |  |  +- (com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.10.3:compile - omitted for conflict with 2.7.8)
[INFO] |  |  +- (com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.10.3:compile - omitted for conflict with 2.6.3)
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.10.3:compile
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  |  \- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-afterburner:jar:2.10.3:compile
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  |  \- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-joda:jar:2.10.3:compile
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.3:compile - omitted for conflict with 2.9.6)
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  |  \- (joda-time:joda-time:jar:2.9.9:compile - omitted for duplicate)
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  |  \- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-lifecycle:jar:2.0.4:compile - omitted for conflict with 2.0.2)
[INFO] |  +- (io.dropwizard:dropwizard-logging:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard.metrics:metrics-healthchecks:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.3:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-hibernate5:jar:2.10.3:compile
[INFO] |  |  +- javax.transaction:javax.transaction-api:jar:1.3:compile
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  \- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  +- jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile
[INFO] |  +- (javax.persistence:javax.persistence-api:jar:2.2:compile - omitted for duplicate)
[INFO] |  +- (joda-time:joda-time:jar:2.10.5:compile - omitted for conflict with 2.9.9)
[INFO] |  +- org.apache.tomcat:tomcat-***********************
[INFO] |  |  \- org.apache.tomcat:tomcat-juli:jar:9.0.33:compile
[INFO] |  +- (org.glassfish.jersey.core:jersey-client:jar:2.30.1:compile - omitted for duplicate)
[INFO] |  +- (org.glassfish.jersey.core:jersey-common:jar:2.30.1:compile - omitted for conflict with 2.19)
[INFO] |  +- org.javassist:javassist:jar:3.27.0-GA:compile
[INFO] |  +- org.jadira.usertype:usertype.core:jar:7.0.0.CR1:runtime
[INFO] |  |  \- org.jadira.usertype:usertype.spi:jar:7.0.0.CR1:runtime
[INFO] |  +- (org.hibernate:hibernate-core:jar:5.4.12.Final:compile - omitted for conflict with 5.4.4.Final)
[INFO] |  +- (jakarta.activation:jakarta.activation-api:jar:1.2.2:compile - scope updated from runtime; omitted for duplicate)
[INFO] |  +- (jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:compile - scope updated from runtime; omitted for duplicate)
[INFO] |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] +- com.google.inject:guice:jar:4.0:compile
[INFO] |  +- aopalliance:aopalliance:jar:1.0:compile
[INFO] |  \- (com.google.guava:guava:jar:16.0.1:compile - omitted for conflict with 23.0)
[INFO] +- mysql:mysql-connector-java:jar:8.0.25:compile
[INFO] |  \- com.google.protobuf:protobuf-java:jar:3.11.4:compile
[INFO] +- commons-io:commons-io:jar:2.5:compile
[INFO] +- com.flipkart.flux:client:jar:1.2.0:compile
[INFO] |  +- com.flipkart.flux:api:jar:1.2.0:compile
[INFO] |  +- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] |  +- (org.apache.commons:commons-lang3:jar:3.3.2:compile - omitted for duplicate)
[INFO] |  +- (org.apache.httpcomponents:httpclient:jar:4.3.6:compile - omitted for conflict with 4.5.8)
[INFO] |  +- org.apache.httpcomponents:httpmime:jar:4.3.6:compile
[INFO] |  |  \- (org.apache.httpcomponents:httpclient:jar:4.3.6:compile - omitted for conflict with 4.5.8)
[INFO] |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.0:compile - omitted for conflict with 2.0.1)
[INFO] |  +- com.google.inject.extensions:guice-multibindings:jar:4.0:compile
[INFO] |  |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] |  +- (cglib:cglib-nodep:jar:3.1:compile - omitted for conflict with 2.2)
[INFO] |  \- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] +- com.flipkart.fintech:pinaka-client:jar:2.2.67-SM1-SNAPSHOT:compile
[INFO] |  +- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] |  +- (com.flipkart.fintech:pinaka-api:jar:2.2.67-SM1-SNAPSHOT:compile - omitted for duplicate)
[INFO] |  +- (org.glassfish.jersey.media:jersey-media-multipart:jar:2.30.1:compile - omitted for duplicate)
[INFO] |  +- (com.flipkart.fintech:ardour-api:jar:1.0.83:compile - omitted for duplicate)
[INFO] |  +- (com.flipkart.fintech:pinaka-capacity:jar:2.2.67-SM1-SNAPSHOT:compile - omitted for duplicate)
[INFO] |  +- (org.projectlombok:lombok:jar:1.18.10:compile - omitted for conflict with 1.18.12)
[INFO] |  +- (com.flipkart.fintech:fintech-commons:jar:1.0.122:compile - omitted for duplicate)
[INFO] |  \- (com.flipkart.fintech:winterfell-client:jar:1.0.49:compile - omitted for duplicate)
[INFO] +- com.flipkart.affordability:onboarding-client:jar:1.5.16:compile
[INFO] |  +- com.flipkart.affordability:bnpl-onboarding-model:jar:1.5.16:compile
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.6:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (javax.validation:validation-api:jar:1.1.0.Final:compile - omitted for conflict with 2.0.1.Final)
[INFO] |  |  +- (com.google.guava:guava:jar:27.0-jre:compile - omitted for conflict with 23.0)
[INFO] |  |  \- (com.flipkart.affordability:bnpl-tijori-api:jar:2.0.63:compile - omitted for conflict with 2.0.117)
[INFO] |  +- (com.google.inject:guice:jar:4.1.0:compile - omitted for conflict with 4.0)
[INFO] |  +- (org.glassfish.jersey.core:jersey-client:jar:2.25:compile - omitted for conflict with 2.30.1)
[INFO] |  \- (com.flipkart.fintech:fintech-common-headers:jar:1.0.42:compile - omitted for conflict with ********)
[INFO] +- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:jar:********:compile
[INFO] |  +- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:jar:********:compile
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.2:compile - omitted for duplicate)
[INFO] |  |  \- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.2:compile - omitted for duplicate)
[INFO] |  \- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.10.2:compile
[INFO] |     +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.2:compile - omitted for conflict with 2.9.6)
[INFO] |     +- (com.fasterxml.jackson.core:jackson-core:jar:2.10.2:compile - omitted for duplicate)
[INFO] |     +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.2:compile - omitted for duplicate)
[INFO] |     +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.2:compile
[INFO] |     |  \- (jakarta.activation:jakarta.activation-api:jar:1.2.2:compile - omitted for conflict with 1.2.1)
[INFO] |     \- jakarta.activation:jakarta.activation-api:jar:1.2.1:compile
[INFO] +- org.glassfish.jersey.core:jersey-server:jar:2.30.1:compile
[INFO] |  +- (org.glassfish.jersey.core:jersey-common:jar:2.30.1:compile - omitted for conflict with 2.19)
[INFO] |  +- (org.glassfish.jersey.core:jersey-client:jar:2.30.1:compile - omitted for duplicate)
[INFO] |  +- (jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile - omitted for duplicate)
[INFO] |  +- org.glassfish.jersey.media:jersey-media-jaxb:jar:2.30.1:compile
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-common:jar:2.30.1:compile - omitted for conflict with 2.19)
[INFO] |  |  +- (org.glassfish.hk2.external:jakarta.inject:jar:2.6.1:compile - omitted for duplicate)
[INFO] |  |  \- (org.glassfish.hk2:osgi-resource-locator:jar:1.0.3:compile - omitted for conflict with 1.0.1)
[INFO] |  +- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |  +- (org.glassfish.hk2.external:jakarta.inject:jar:2.6.1:compile - omitted for duplicate)
[INFO] |  \- jakarta.validation:jakarta.validation-api:jar:2.0.2:compile
[INFO] +- org.glassfish.jersey.media:jersey-media-multipart:jar:2.30.1:compile
[INFO] |  +- (org.glassfish.jersey.core:jersey-common:jar:2.30.1:compile - omitted for conflict with 2.19)
[INFO] |  \- org.jvnet.mimepull:mimepull:jar:1.9.11:compile
[INFO] +- io.dropwizard:dropwizard-metrics:jar:2.0.4:compile
[INFO] |  +- (io.dropwizard:dropwizard-lifecycle:jar:2.0.4:compile - omitted for conflict with 2.0.2)
[INFO] |  +- (io.dropwizard:dropwizard-jackson:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.3:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  +- (jakarta.validation:jakarta.validation-api:jar:2.0.2:compile - omitted for duplicate)
[INFO] |  \- (com.github.ben-manes.caffeine:caffeine:jar:2.8.1:compile - omitted for duplicate)
[INFO] +- io.dropwizard:dropwizard-jersey:jar:2.0.2:compile
[INFO] |  +- (io.dropwizard:dropwizard-jackson:jar:2.0.2:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (io.dropwizard:dropwizard-validation:jar:2.0.2:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (io.dropwizard:dropwizard-logging:jar:2.0.2:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (org.glassfish.jersey.core:jersey-server:jar:2.30:compile - omitted for conflict with 2.30.1)
[INFO] |  +- org.glassfish.jersey.ext:jersey-metainf-services:jar:2.30:compile
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-common:jar:2.30:compile - omitted for conflict with 2.19)
[INFO] |  |  \- (jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile - omitted for duplicate)
[INFO] |  +- (org.glassfish.jersey.ext:jersey-bean-validation:jar:2.30:compile - omitted for conflict with 2.30.1)
[INFO] |  +- (org.glassfish.jersey.inject:jersey-hk2:jar:2.30:compile - omitted for conflict with 2.29)
[INFO] |  +- io.dropwizard.metrics:metrics-jersey2:jar:4.1.2:compile
[INFO] |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.2:compile - omitted for conflict with 4.1.5)
[INFO] |  |  +- (io.dropwizard.metrics:metrics-annotation:jar:4.1.2:compile - omitted for conflict with 4.1.5)
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-server:jar:2.29.1:compile - omitted for conflict with 2.30.1)
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.29:compile - omitted for conflict with 1.7.6)
[INFO] |  +- (com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:jar:********:compile - omitted for duplicate)
[INFO] |  +- org.glassfish.jersey.containers:jersey-container-servlet:jar:2.30:compile
[INFO] |  |  +- org.glassfish.jersey.containers:jersey-container-servlet-core:jar:2.30.1:compile
[INFO] |  |  |  +- (org.glassfish.hk2.external:jakarta.inject:jar:2.6.1:compile - omitted for duplicate)
[INFO] |  |  |  +- (org.glassfish.jersey.core:jersey-common:jar:2.30:compile - omitted for conflict with 2.19)
[INFO] |  |  |  +- (org.glassfish.jersey.core:jersey-server:jar:2.30:compile - omitted for conflict with 2.30.1)
[INFO] |  |  |  \- (jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile - omitted for duplicate)
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-common:jar:2.30:compile - omitted for conflict with 2.19)
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-server:jar:2.30:compile - omitted for conflict with 2.30.1)
[INFO] |  |  \- (jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile - omitted for duplicate)
[INFO] |  +- (org.eclipse.jetty:jetty-server:jar:9.4.26.v20200117:compile - omitted for conflict with 9.4.30.v20200611)
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:4.0.3:compile
[INFO] |  +- org.eclipse.jetty:jetty-webapp:jar:9.4.26.v20200117:compile
[INFO] |  |  +- org.eclipse.jetty:jetty-xml:jar:9.4.26.v20200117:compile
[INFO] |  |  |  \- (org.eclipse.jetty:jetty-util:jar:9.4.26.v20200117:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  |  \- (org.eclipse.jetty:jetty-servlet:jar:9.4.26.v20200117:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  +- org.eclipse.jetty:jetty-continuation:jar:9.4.26.v20200117:compile
[INFO] |  +- (org.apache.commons:commons-lang3:jar:3.9:compile - omitted for conflict with 3.3.2)
[INFO] |  +- (com.github.ben-manes.caffeine:caffeine:jar:2.8.1:compile - omitted for duplicate)
[INFO] |  +- (jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.2:compile - scope updated from runtime; omitted for duplicate)
[INFO] |  \- (jakarta.activation:jakarta.activation-api:jar:1.2.1:compile - scope updated from runtime; omitted for duplicate)
[INFO] +- io.dropwizard:dropwizard-client:jar:2.0.4:compile
[INFO] |  +- (io.dropwizard:dropwizard-configuration:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-jackson:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-lifecycle:jar:2.0.4:compile - omitted for conflict with 2.0.2)
[INFO] |  +- (io.dropwizard:dropwizard-util:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-validation:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard.metrics:metrics-healthchecks:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.3:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.3:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  +- (jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile - omitted for duplicate)
[INFO] |  +- (org.eclipse.jetty:jetty-util:jar:9.4.27.v20200227:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |  +- (org.glassfish.jersey.core:jersey-client:jar:2.30.1:compile - omitted for duplicate)
[INFO] |  +- (org.apache.commons:commons-lang3:jar:3.9:compile - omitted for conflict with 3.3.2)
[INFO] |  +- (org.apache.httpcomponents:httpclient:jar:4.5.12:compile - omitted for conflict with 4.5.8)
[INFO] |  +- (org.apache.httpcomponents:httpcore:jar:4.4.13:compile - omitted for conflict with 4.4.11)
[INFO] |  +- io.dropwizard.metrics:metrics-httpclient:jar:4.1.5:compile
[INFO] |  |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.5:compile - omitted for duplicate)
[INFO] |  |  +- (org.apache.httpcomponents:httpclient:jar:4.5.12:compile - omitted for conflict with 4.5.8)
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |  +- org.glassfish.jersey.connectors:jersey-apache-connector:jar:2.30.1:compile
[INFO] |  |  +- (org.apache.httpcomponents:httpclient:jar:4.5.9:compile - omitted for conflict with 4.5.8)
[INFO] |  |  +- (org.glassfish.jersey.core:jersey-client:jar:2.30.1:compile - omitted for duplicate)
[INFO] |  |  \- (jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile - omitted for duplicate)
[INFO] |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] +- org.glassfish.jersey.core:jersey-common:jar:2.30.1:compile
[INFO] |  +- (jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile - omitted for duplicate)
[INFO] |  +- (jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile - omitted for duplicate)
[INFO] |  +- (org.glassfish.hk2.external:jakarta.inject:jar:2.6.1:compile - omitted for duplicate)
[INFO] |  \- org.glassfish.hk2:osgi-resource-locator:jar:1.0.3:compile
[INFO] +- com.flipkart.affordability:robinhood-client:jar:2.1.19:compile
[INFO] |  \- com.flipkart.affordability:robinhood-api:jar:2.1.19:compile
[INFO] |     +- (javax.validation:validation-api:jar:1.1.0.Final:compile - omitted for conflict with 2.0.1.Final)
[INFO] |     +- org.apache.poi:poi-ooxml:jar:5.0.0:compile
[INFO] |     |  +- org.apache.poi:poi:jar:5.0.0:compile
[INFO] |     |  |  +- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |     |  |  +- org.slf4j:jcl-over-slf4j:jar:1.7.30:compile
[INFO] |     |  |  |  \- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |     |  |  +- (commons-codec:commons-codec:jar:1.15:compile - omitted for conflict with 1.11)
[INFO] |     |  |  +- (org.apache.commons:commons-collections4:jar:4.4:compile - omitted for conflict with 4.3)
[INFO] |     |  |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |     |  |  \- com.zaxxer:SparseBitSet:jar:1.2:compile
[INFO] |     |  +- org.apache.poi:poi-ooxml-lite:jar:5.0.0:compile
[INFO] |     |  |  \- org.apache.xmlbeans:xmlbeans:jar:4.0.0:compile
[INFO] |     |  |     \- (xml-apis:xml-apis:jar:1.4.01:compile - omitted for duplicate)
[INFO] |     |  +- org.apache.commons:commons-compress:jar:1.20:compile
[INFO] |     |  +- com.github.virtuald:curvesapi:jar:1.06:compile
[INFO] |     |  +- (org.bouncycastle:bcpkix-jdk15on:jar:1.68:compile - omitted for duplicate)
[INFO] |     |  +- (org.bouncycastle:bcprov-jdk15on:jar:1.68:compile - omitted for duplicate)
[INFO] |     |  +- org.apache.santuario:xmlsec:jar:2.2.1:compile
[INFO] |     |  |  +- (org.slf4j:slf4j-api:jar:1.7.30:compile - omitted for conflict with 1.7.6)
[INFO] |     |  |  +- (commons-codec:commons-codec:jar:1.15:compile - omitted for conflict with 1.11)
[INFO] |     |  |  \- com.fasterxml.woodstox:woodstox-core:jar:5.2.1:runtime
[INFO] |     |  |     \- org.codehaus.woodstox:stax2-api:jar:4.2:runtime
[INFO] |     |  +- org.apache.xmlgraphics:batik-all:jar:1.13:compile
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-anim:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-awt-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-css:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-dom:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-ext:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-parser:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-svg-dom:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- xml-apis:xml-apis-ext:jar:1.3.04:compile
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-awt-util:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- org.apache.xmlgraphics:xmlgraphics-commons:jar:2.4:compile
[INFO] |     |  |  |     \- (commons-io:commons-io:jar:1.3.1:compile - omitted for conflict with 2.5)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-bridge:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-anim:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-awt-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-css:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-dom:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-gvt:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-parser:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-script:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-svg-dom:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-xml:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:xmlgraphics-commons:jar:2.4:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (xml-apis:xml-apis-ext:jar:1.3.04:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-codec:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-awt-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-bridge:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-transcoder:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-constants:jar:1.13:compile
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-css:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:xmlgraphics-commons:jar:2.4:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (xml-apis:xml-apis-ext:jar:1.3.04:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-dom:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-css:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-ext:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-xml:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- xalan:xalan:jar:2.7.2:compile
[INFO] |     |  |  |  |  \- xalan:serializer:jar:2.7.2:compile
[INFO] |     |  |  |  |     \- (xml-apis:xml-apis:jar:1.3.04:compile - omitted for conflict with 1.4.01)
[INFO] |     |  |  |  +- (xml-apis:xml-apis:jar:1.4.01:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (xml-apis:xml-apis-ext:jar:1.3.04:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-ext:jar:1.13:compile
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-extension:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-anim:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-awt-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-bridge:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-css:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-dom:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-ext:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-gvt:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-parser:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-svg-dom:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (xml-apis:xml-apis-ext:jar:1.3.04:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-gui-util:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-xml:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-gvt:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-awt-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-i18n:jar:1.13:compile
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-parser:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-awt-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-xml:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (xml-apis:xml-apis-ext:jar:1.3.04:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-rasterizer-ext:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-svgrasterizer:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-extension:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-rasterizer:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-svgrasterizer:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-script:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-anim:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-slideshow:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-bridge:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-gvt:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (xml-apis:xml-apis-ext:jar:1.3.04:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-squiggle-ext:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-svgbrowser:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-extension:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-squiggle:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-svgbrowser:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-svg-dom:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-awt-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-css:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-dom:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-ext:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-parser:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (xml-apis:xml-apis-ext:jar:1.3.04:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-svgbrowser:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-anim:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-bridge:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-dom:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-gvt:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-swing:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-transcoder:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-xml:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (xml-apis:xml-apis-ext:jar:1.3.04:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-svggen:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-awt-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-svgpp:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-transcoder:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-svgrasterizer:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-parser:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-transcoder:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-codec:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-swing:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-anim:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-awt-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-bridge:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-css:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-dom:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-ext:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-gui-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-gvt:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-script:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (xml-apis:xml-apis-ext:jar:1.3.04:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-transcoder:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-anim:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-awt-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-bridge:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-dom:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-gvt:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-svggen:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-xml:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (xml-apis:xml-apis-ext:jar:1.3.04:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-util:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-constants:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-i18n:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  +- org.apache.xmlgraphics:batik-ttf2svg:jar:1.13:compile
[INFO] |     |  |  |  +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  |  \- (org.apache.xmlgraphics:batik-svggen:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |  \- org.apache.xmlgraphics:batik-xml:jar:1.13:compile
[INFO] |     |  |     +- (org.apache.xmlgraphics:batik-shared-resources:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  |     \- (org.apache.xmlgraphics:batik-util:jar:1.13:compile - omitted for duplicate)
[INFO] |     |  \- de.rototor.pdfbox:graphics2d:jar:0.30:compile
[INFO] |     |     \- org.apache.pdfbox:pdfbox:jar:2.0.22:compile
[INFO] |     |        \- org.apache.pdfbox:fontbox:jar:2.0.22:compile
[INFO] |     +- com.flipkart.payments:commons:jar:3.2.23:compile
[INFO] |     |  \- (commons-lang:commons-lang:jar:2.6:compile - omitted for duplicate)
[INFO] |     \- (org.projectlombok:lombok:jar:1.16.4:compile - omitted for conflict with 1.18.12)
[INFO] +- com.flipkart.cp:caishen-api:jar:0.2.143:compile
[INFO] |  +- (io.dropwizard:dropwizard-validation:jar:1.1.0:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.7:compile - omitted for conflict with 2.10.2)
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-json-org:jar:2.8.7:compile
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.7:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.7:compile - omitted for conflict with 2.10.2)
[INFO] |  |  \- org.apache.geronimo.bundles:json:jar:20090211_1:compile
[INFO] |  +- (joda-time:joda-time:jar:2.7:compile - omitted for conflict with 2.9.9)
[INFO] |  +- (com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.8.7:compile - omitted for conflict with 2.6.7)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.7:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (javax.validation:validation-api:jar:1.1.0.Final:compile - omitted for conflict with 2.0.1.Final)
[INFO] |  +- (com.google.guava:guava:jar:21.0:compile - omitted for conflict with 23.0)
[INFO] |  +- fkpg:pz-checkout-api:jar:0.2.14:compile
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.5.0:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.fasterxml.jackson.datatype:jackson-datatype-json-org:jar:2.5.0:compile - omitted for conflict with 2.8.7)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.5.0:compile - omitted for conflict with 2.9.6)
[INFO] |  |  +- (org.json:json:jar:20141113:compile - omitted for conflict with 20090211)
[INFO] |  |  +- (commons-codec:commons-codec:jar:1.7:compile - omitted for conflict with 1.11)
[INFO] |  |  +- (commons-lang:commons-lang:jar:2.6:compile - omitted for duplicate)
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.7:compile - omitted for conflict with 1.7.6)
[INFO] |  +- com.flipkart.convert:egv-api-entities:jar:0.0.3-SNAPSHOT:compile
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.6.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.6.3:compile - omitted for conflict with 2.9.6)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.6.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  \- (javax.validation:validation-api:jar:1.1.0.Final:compile - omitted for conflict with 2.0.1.Final)
[INFO] |  \- (javax.ws.rs:javax.ws.rs-api:jar:2.0.1:compile - omitted for duplicate)
[INFO] +- org.powermock:powermock-api-mockito:jar:1.5.6:test
[INFO] |  +- (org.mockito:mockito-all:jar:1.9.5:compile - scope updated from test; omitted for duplicate)
[INFO] |  \- org.powermock:powermock-api-support:jar:1.5.6:test
[INFO] |     +- org.powermock:powermock-core:jar:1.5.6:test
[INFO] |     |  +- (org.powermock:powermock-reflect:jar:1.5.6:test - omitted for duplicate)
[INFO] |     |  \- (org.javassist:javassist:jar:3.18.2-GA:test - omitted for conflict with 3.27.0-GA)
[INFO] |     \- org.powermock:powermock-reflect:jar:1.5.6:test
[INFO] |        \- (org.objenesis:objenesis:jar:2.1:compile - scope updated from test; omitted for duplicate)
[INFO] +- org.powermock:powermock-module-junit4:jar:1.5.6:test
[INFO] |  +- (junit:junit:jar:4.11:test - omitted for conflict with 4.13)
[INFO] |  \- org.powermock:powermock-module-junit4-common:jar:1.5.6:test
[INFO] |     +- (junit:junit:jar:4.4:test - omitted for conflict with 4.13)
[INFO] |     +- (org.powermock:powermock-core:jar:1.5.6:test - omitted for duplicate)
[INFO] |     \- (org.powermock:powermock-reflect:jar:1.5.6:test - omitted for duplicate)
[INFO] +- junit:junit:jar:4.11:test (scope not updated to compile)
[INFO] |  \- org.hamcrest:hamcrest-core:jar:1.3:test
[INFO] +- com.flipkart.affordability:fintech-underwriting-client:jar:1.8.23:compile
[INFO] |  +- (com.flipkart.affordability:fintech-underwriting-model:jar:1.8.23:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.5:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.apache.commons:commons-lang3:jar:3.4:compile - omitted for conflict with 3.3.2)
[INFO] |  +- (com.google.inject:guice:jar:3.0:compile - omitted for conflict with 4.0)
[INFO] |  \- (org.glassfish.jersey.core:jersey-client:jar:2.25:compile - omitted for conflict with 2.30.1)
[INFO] +- org.mvel:mvel2:jar:2.2.2.Final:compile
[INFO] +- commons-collections:commons-collections:jar:3.2.2:compile
[INFO] +- commons-codec:commons-codec:jar:1.11:compile
[INFO] +- com.flipkart.fintech:fintech-cryptex-bundle:jar:1.0.57:compile
[INFO] |  +- (com.flipkart.kloud.authn:client:jar:**********:compile - omitted for conflict with **********)
[INFO] |  +- (com.flipkart.kloud.config:client-java:jar:1.5.1:compile - omitted for conflict with 1.5.9)
[INFO] |  +- com.flipkart.security:cryptex-client-shade:jar:1.2.6:compile
[INFO] |  +- (io.dropwizard:dropwizard-core:jar:2.0.2:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech:fintech-common-CT:jar:1.0.73:compile
[INFO] |  +- (org.liquibase:liquibase-core:jar:3.8.5:compile - omitted for duplicate)
[INFO] |  +- (org.testng:testng:jar:6.9.8:compile - omitted for duplicate)
[INFO] |  +- (com.h2database:h2:jar:1.4.195:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech:fintech-outbound-bundle:jar:1.0.56:compile
[INFO] |  +- (io.dropwizard:dropwizard-core:jar:1.0.2:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 4.0)
[INFO] |  +- (com.flipkart.kloud.config:client-java:jar:1.3.10:compile - omitted for conflict with 1.5.9)
[INFO] |  +- (io.dropwizard.metrics:metrics-core:jar:4.1.12:compile - omitted for conflict with 4.1.5)
[INFO] |  +- (com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.6.7:compile - omitted for duplicate)
[INFO] |  \- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] +- com.flipkart.restbus.hibernate:turbo-client:jar:2.3.1-hibernate-5.4.10:compile
[INFO] |  +- (com.google.code.gson:gson:jar:2.2.2:compile - omitted for conflict with 2.8.9)
[INFO] |  +- org.mockito:mockito-all:jar:1.9.5:compile
[INFO] |  +- com.restbus:restbus-client:jar:2.0.8-Turbo:compile
[INFO] |  |  +- (log4j:log4j:jar:1.2.17:compile - omitted for conflict with 1.2.15)
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-hibernate4:jar:2.2.1:compile
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.2.1:compile - omitted for conflict with 2.10.2)
[INFO] |  |  |  \- (com.fasterxml.jackson.core:jackson-core:jar:2.2.1:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- com.restbus:zookeeper-client:jar:0.0.3:compile
[INFO] |  |  |  +- (org.slf4j:slf4j-api:jar:1.7.2:compile - omitted for conflict with 1.7.6)
[INFO] |  |  |  +- org.apache.curator:curator-recipes:jar:2.1.0-incubating:compile
[INFO] |  |  |  |  +- org.apache.curator:curator-framework:jar:2.1.0-incubating:compile
[INFO] |  |  |  |  |  +- (org.apache.curator:curator-client:jar:2.1.0-incubating:compile - omitted for conflict with 2.1.0-incubating.patched)
[INFO] |  |  |  |  |  +- (org.apache.zookeeper:zookeeper:jar:3.4.5:compile - omitted for duplicate)
[INFO] |  |  |  |  |  \- (com.google.guava:guava:jar:14.0.1:compile - omitted for conflict with 23.0)
[INFO] |  |  |  |  +- org.apache.zookeeper:zookeeper:jar:3.4.5:compile
[INFO] |  |  |  |  |  +- (org.slf4j:slf4j-api:jar:1.6.1:compile - omitted for conflict with 1.7.6)
[INFO] |  |  |  |  |  +- (log4j:log4j:jar:1.2.15:compile - omitted for conflict with 1.2.17)
[INFO] |  |  |  |  |  +- jline:jline:jar:0.9.94:compile
[INFO] |  |  |  |  |  \- org.jboss.netty:netty:jar:3.2.2.Final:compile
[INFO] |  |  |  |  \- (com.google.guava:guava:jar:14.0.1:compile - omitted for conflict with 23.0)
[INFO] |  |  |  \- org.apache.curator:curator-client:jar:2.1.0-incubating.patched:compile
[INFO] |  |  |     +- (org.slf4j:slf4j-api:jar:1.6.4:compile - omitted for conflict with 1.7.6)
[INFO] |  |  |     +- (org.apache.zookeeper:zookeeper:jar:3.4.5:compile - omitted for duplicate)
[INFO] |  |  |     \- (com.google.guava:guava:jar:14.0.1:compile - omitted for conflict with 23.0)
[INFO] |  |  \- (org.yaml:snakeyaml:jar:1.14:compile - omitted for conflict with 1.24)
[INFO] |  +- org.yaml:snakeyaml:jar:1.14:compile
[INFO] |  +- org.hibernate.javax.persistence:hibernate-jpa-2.1-api:jar:1.0.2.Final:compile
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.6.4:compile - omitted for conflict with 1.7.6)
[INFO] |  +- org.hibernate:hibernate-c3p0:jar:5.4.12.Final:compile
[INFO] |  |  \- com.mchange:c3p0:jar:0.9.5.3:compile
[INFO] |  |     \- com.mchange:mchange-commons-java:jar:0.2.15:compile
[INFO] |  +- (mysql:mysql-connector-java:jar:5.1.20:compile - omitted for conflict with 8.0.25)
[INFO] |  +- commons-lang:commons-lang:jar:2.6:compile
[INFO] |  \- log4j:log4j:jar:1.2.15:compile
[INFO] +- org.junit.jupiter:junit-jupiter:jar:5.4.2:compile
[INFO] |  +- org.junit.jupiter:junit-jupiter-api:jar:5.6.1:compile
[INFO] |  |  +- org.apiguardian:apiguardian-api:jar:1.0.0:compile
[INFO] |  |  +- org.opentest4j:opentest4j:jar:1.1.1:compile
[INFO] |  |  \- org.junit.platform:junit-platform-commons:jar:1.6.1:compile
[INFO] |  |     \- (org.apiguardian:apiguardian-api:jar:1.0.0:compile - omitted for duplicate)
[INFO] |  +- org.junit.jupiter:junit-jupiter-params:jar:5.4.2:compile
[INFO] |  |  +- (org.apiguardian:apiguardian-api:jar:1.0.0:compile - omitted for duplicate)
[INFO] |  |  \- (org.junit.jupiter:junit-jupiter-api:jar:5.4.2:compile - omitted for conflict with 5.6.1)
[INFO] |  \- org.junit.jupiter:junit-jupiter-engine:jar:5.4.2:runtime
[INFO] |     +- (org.apiguardian:apiguardian-api:jar:1.0.0:runtime - omitted for duplicate)
[INFO] |     +- org.junit.platform:junit-platform-engine:jar:1.4.2:runtime
[INFO] |     |  +- (org.apiguardian:apiguardian-api:jar:1.0.0:runtime - omitted for duplicate)
[INFO] |     |  +- (org.opentest4j:opentest4j:jar:1.1.1:runtime - omitted for duplicate)
[INFO] |     |  \- (org.junit.platform:junit-platform-commons:jar:1.4.2:runtime - omitted for conflict with 1.6.1)
[INFO] |     \- (org.junit.jupiter:junit-jupiter-api:jar:5.4.2:runtime - omitted for conflict with 5.6.1)
[INFO] +- org.apache.commons:commons-text:jar:1.6:compile
[INFO] |  \- (org.apache.commons:commons-lang3:jar:3.8.1:compile - omitted for conflict with 3.3.2)
[INFO] +- com.flipkart.affordability:bnpl-collections-client:jar:1.2.6:compile
[INFO] |  +- com.flipkart.affordability:bnpl-collections-model:jar:1.2.6:compile
[INFO] |  |  +- (com.google.inject:guice:jar:3.0:compile - omitted for conflict with 4.0)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.6.3:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (io.dropwizard:dropwizard-jackson:jar:0.9.0-rc4:compile - omitted for conflict with 2.0.4)
[INFO] |  |  \- (io.dropwizard:dropwizard-hibernate:jar:1.3.5:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (org.apache.httpcomponents:httpclient:jar:4.5.1:compile - omitted for conflict with 4.5.8)
[INFO] |  \- org.json:json:jar:20180813:compile
[INFO] +- com.restbus:dropwizard-hibernate-plugin:jar:1.1.3-fintech:compile
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.2:compile - omitted for conflict with 1.7.6)
[INFO] |  +- (org.apache.commons:commons-lang3:jar:3.1:compile - omitted for conflict with 3.3.2)
[INFO] |  +- (org.projectlombok:lombok:jar:1.12.4:compile - omitted for conflict with 1.18.12)
[INFO] |  +- (io.dropwizard:dropwizard-core:jar:0.7.1:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (io.dropwizard:dropwizard-hibernate:jar:0.7.1:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.0:compile - omitted for conflict with 2.0.1)
[INFO] |  \- io.netty:netty:jar:3.6.3.Final:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.10:compile
[INFO] +- com.flipkart.scheduler:scheduler-client-api:jar:0.10.0:compile
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.5:compile - omitted for conflict with 1.7.6)
[INFO] |  \- flipkart.transact.commons:hystrix-utils:jar:1.4.10:compile
[INFO] |     +- com.flipkart.cp.transact:mini-proxy:jar:1.0-RC4:compile
[INFO] |     |  +- (org.eclipse.jetty:jetty-server:jar:9.2.1.v20140609:compile - omitted for conflict with 9.4.30.v20200611)
[INFO] |     |  +- (org.eclipse.jetty:jetty-servlet:jar:9.2.1.v20140609:compile - omitted for conflict with 9.4.37.v20210219)
[INFO] |     |  +- (com.google.guava:guava:jar:18.0:compile - omitted for conflict with 23.0)
[INFO] |     |  +- (org.slf4j:slf4j-api:jar:1.7.4:compile - omitted for conflict with 1.7.6)
[INFO] |     |  +- (com.netflix.hystrix:hystrix-javanica:jar:1.5-RC:compile - omitted for duplicate)
[INFO] |     |  \- (com.fasterxml.jackson.core:jackson-databind:jar:2.4.2:compile - omitted for conflict with 2.10.2)
[INFO] |     +- (com.netflix.hystrix:hystrix-core:jar:1.4.21:compile - omitted for conflict with 1.5.5)
[INFO] |     +- com.netflix.hystrix:hystrix-metrics-event-stream:jar:1.4.0-RC5:compile
[INFO] |     |  +- (com.netflix.hystrix:hystrix-core:jar:1.4.0-RC5:compile - omitted for conflict with 1.5.5)
[INFO] |     |  \- (org.codehaus.jackson:jackson-core-asl:jar:1.9.2:compile - omitted for conflict with 1.5.0)
[INFO] |     +- (org.eclipse.jetty:jetty-servlets:jar:9.2.1.v20140609:compile - omitted for conflict with 9.4.27.v20200227)
[INFO] |     +- (commons-io:commons-io:jar:2.4:compile - omitted for conflict with 2.5)
[INFO] |     +- (org.apache.httpcomponents:httpclient:jar:4.3.2:compile - omitted for conflict with 4.5.8)
[INFO] |     +- (org.apache.httpcomponents:httpcore:jar:4.3.2:compile - omitted for conflict with 4.4.11)
[INFO] |     \- (junit:junit:jar:4.11:compile - omitted for duplicate)
[INFO] +- com.flipkart.fpg:helios-proxy-lib:jar:1.0.3:compile
[INFO] |  +- com.flipkart.asynchttpclient:AsyncHTTPClient:jar:1.0.0:compile
[INFO] |  |  +- org.asynchttpclient:async-http-client:jar:2.8.1:compile
[INFO] |  |  |  +- org.asynchttpclient:async-http-client-netty-utils:jar:2.8.1:compile
[INFO] |  |  |  |  +- (io.netty:netty-buffer:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  +- (org.slf4j:slf4j-api:jar:1.7.25:compile - omitted for conflict with 1.7.6)
[INFO] |  |  |  |  \- (com.sun.activation:javax.activation:jar:1.2.0:compile - omitted for duplicate)
[INFO] |  |  |  +- (io.netty:netty-handler:jar:4.1.33.Final:compile - omitted for conflict with 4.1.44.Final)
[INFO] |  |  |  +- io.netty:netty-codec-socks:jar:4.1.33.Final:compile
[INFO] |  |  |  |  +- (io.netty:netty-common:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  +- (io.netty:netty-buffer:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  +- (io.netty:netty-transport:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  \- (io.netty:netty-codec:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  +- io.netty:netty-handler-proxy:jar:4.1.33.Final:compile
[INFO] |  |  |  |  +- (io.netty:netty-common:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  +- (io.netty:netty-buffer:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  +- (io.netty:netty-transport:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  +- (io.netty:netty-codec:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  \- (io.netty:netty-codec-socks:jar:4.1.33.Final:compile - omitted for duplicate)
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.33.Final:compile
[INFO] |  |  |  |  +- (io.netty:netty-common:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  +- (io.netty:netty-buffer:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  +- (io.netty:netty-transport:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  \- io.netty:netty-transport-native-unix-common:jar:4.1.33.Final:compile
[INFO] |  |  |  |     +- (io.netty:netty-common:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |     +- (io.netty:netty-buffer:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |     \- (io.netty:netty-transport:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  +- io.netty:netty-resolver-dns:jar:4.1.33.Final:compile
[INFO] |  |  |  |  +- (io.netty:netty-common:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  +- (io.netty:netty-buffer:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  +- (io.netty:netty-resolver:jar:4.1.33.Final:compile - omitted for conflict with 4.1.44.Final)
[INFO] |  |  |  |  +- (io.netty:netty-transport:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  +- (io.netty:netty-codec:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |  \- io.netty:netty-codec-dns:jar:4.1.33.Final:compile
[INFO] |  |  |  |     +- (io.netty:netty-common:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |     +- (io.netty:netty-buffer:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |     +- (io.netty:netty-transport:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  |     \- (io.netty:netty-codec:jar:4.1.33.Final:compile - omitted for conflict with 4.1.36.Final)
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.2:compile
[INFO] |  |  |  +- com.typesafe.netty:netty-reactive-streams:jar:2.0.0:compile
[INFO] |  |  |  |  +- (io.netty:netty-handler:jar:4.1.13.Final:compile - omitted for conflict with 4.1.33.Final)
[INFO] |  |  |  |  \- (org.reactivestreams:reactive-streams:jar:1.0.0:compile - omitted for conflict with 1.0.2)
[INFO] |  |  |  +- (org.slf4j:slf4j-api:jar:1.7.25:compile - omitted for conflict with 1.7.6)
[INFO] |  |  |  \- (com.sun.activation:javax.activation:jar:1.2.0:compile - omitted for duplicate)
[INFO] |  |  +- org.asynchttpclient:async-http-client-extras-rxjava:jar:2.8.1:compile
[INFO] |  |  |  +- (io.reactivex:rxjava:jar:1.3.8:compile - omitted for duplicate)
[INFO] |  |  |  +- (org.asynchttpclient:async-http-client:jar:2.8.1:compile - omitted for duplicate)
[INFO] |  |  |  +- (org.slf4j:slf4j-api:jar:1.7.25:compile - omitted for conflict with 1.7.6)
[INFO] |  |  |  \- (com.sun.activation:javax.activation:jar:1.2.0:compile - omitted for duplicate)
[INFO] |  |  \- (com.netflix.hystrix:hystrix-core:jar:1.5.18:compile - omitted for conflict with 1.5.5)
[INFO] |  +- (javax.servlet:javax.servlet-api:jar:3.1.0:compile - omitted for duplicate)
[INFO] |  +- (io.reactivex:rxjava:jar:1.3.8:compile - omitted for duplicate)
[INFO] |  +- (commons-lang:commons-lang:jar:2.5:compile - omitted for conflict with 2.6)
[INFO] |  \- (commons-io:commons-io:jar:2.5:compile - omitted for duplicate)
[INFO] +- io.netty:netty-codec-http:jar:4.1.44.Final:compile
[INFO] |  +- io.netty:netty-common:jar:4.1.44.Final:compile
[INFO] |  +- io.netty:netty-buffer:jar:4.1.44.Final:compile
[INFO] |  |  \- (io.netty:netty-common:jar:4.1.44.Final:compile - omitted for duplicate)
[INFO] |  +- io.netty:netty-transport:jar:4.1.44.Final:compile
[INFO] |  |  +- (io.netty:netty-common:jar:4.1.44.Final:compile - omitted for duplicate)
[INFO] |  |  +- (io.netty:netty-buffer:jar:4.1.44.Final:compile - omitted for duplicate)
[INFO] |  |  \- io.netty:netty-resolver:jar:4.1.44.Final:compile
[INFO] |  |     \- (io.netty:netty-common:jar:4.1.44.Final:compile - omitted for duplicate)
[INFO] |  \- io.netty:netty-handler:jar:4.1.44.Final:compile
[INFO] |     +- (io.netty:netty-common:jar:4.1.44.Final:compile - omitted for duplicate)
[INFO] |     +- (io.netty:netty-buffer:jar:4.1.44.Final:compile - omitted for duplicate)
[INFO] |     \- (io.netty:netty-transport:jar:4.1.44.Final:compile - omitted for duplicate)
[INFO] +- io.netty:netty-codec:jar:4.1.66.Final:compile
[INFO] |  +- (io.netty:netty-common:jar:4.1.66.Final:compile - omitted for conflict with 4.1.44.Final)
[INFO] |  +- (io.netty:netty-buffer:jar:4.1.66.Final:compile - omitted for conflict with 4.1.44.Final)
[INFO] |  \- (io.netty:netty-transport:jar:4.1.66.Final:compile - omitted for conflict with 4.1.44.Final)
[INFO] +- com.fasterxml.jackson.dataformat:jackson-dataformat-xml:jar:2.7.4:compile
[INFO] +- com.flipkart:ablibrary:jar:3.8.0:compile
[INFO] |  +- com.flipkart.abservice:rulesEngine:jar:1.3.0:compile
[INFO] |  |  \- (com.fasterxml.jackson.core:jackson-annotations:jar:2.7.6:compile - omitted for conflict with 2.9.6)
[INFO] |  +- com.yammer.metrics:metrics-core:jar:2.2.0:compile
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.2:compile - omitted for conflict with 1.7.6)
[INFO] |  +- com.github.statsig-io:java-server-sdk:jar:v0.17.0:compile
[INFO] |  |  +- (com.google.code.gson:gson:jar:2.8.9:runtime - omitted for duplicate)
[INFO] |  |  +- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:jar:1.5.0:runtime
[INFO] |  |  |  +- (org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.5.0:runtime - omitted for conflict with 1.4.10)
[INFO] |  |  |  \- (org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.5.0:runtime - omitted for conflict with 1.4.10)
[INFO] |  |  +- org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:jar:1.5.0:runtime
[INFO] |  |  |  +- (org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:jar:1.5.0:runtime - omitted for duplicate)
[INFO] |  |  |  \- (org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.5.0:runtime - omitted for conflict with 1.4.10)
[INFO] |  |  +- (com.squareup.okhttp3:okhttp:jar:4.9.1:runtime - omitted for duplicate)
[INFO] |  |  +- com.github.statsig-io:ip3country-kotlin:jar:0.1.0:runtime
[INFO] |  |  \- com.github.ua-parser:uap-java:jar:1.5.2:runtime
[INFO] |  |     +- (org.yaml:snakeyaml:jar:1.26:runtime - omitted for conflict with 1.14)
[INFO] |  |     \- (org.apache.commons:commons-collections4:jar:4.1:runtime - omitted for conflict with 4.3)
[INFO] |  \- com.flipkart.cp:config-service-client-wrapper:jar:3.8.1:compile
[INFO] |     +- (com.flipkart.kloud.config:client-java:jar:1.5.4:compile - omitted for conflict with 1.5.9)
[INFO] |     +- com.flipkart.security:cryptex-client:jar:1.2.3:compile
[INFO] |     |  +- (com.flipkart.security:cryptex-client-interface:jar:1.0.3:compile - omitted for duplicate)
[INFO] |     |  +- (com.flipkart.security:cryptex-data-models:jar:1.1.1:compile - omitted for conflict with 1.1.0)
[INFO] |     |  +- (com.flipkart.kloud.authn:client:jar:**********:compile - omitted for conflict with **********)
[INFO] |     |  +- (org.slf4j:slf4j-api:jar:1.7.26:compile - omitted for conflict with 1.7.6)
[INFO] |     |  \- (org.aspectj:aspectjrt:jar:1.8.13:compile - omitted for conflict with 1.7.4)
[INFO] |     +- (org.apache.httpcomponents:httpclient:jar:4.5.10:compile - omitted for conflict with 4.5.8)
[INFO] |     +- (org.apache.httpcomponents:httpcore:jar:4.4.12:compile - omitted for conflict with 4.4.11)
[INFO] |     +- (org.slf4j:slf4j-api:jar:1.7.10:compile - omitted for conflict with 1.7.6)
[INFO] |     \- (com.beust:jcommander:jar:1.58:compile - omitted for conflict with 1.48)
[INFO] +- com.flipkart.cri:alfred-api:jar:4.3.14:compile
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.13.3:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.13.3:compile - omitted for conflict with 2.10.2)
[INFO] |  \- (com.flipkart.sensitive:sensitive-annotation:jar:2.0.4:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech:pinaka-capacity:jar:2.2.67-SM1-SNAPSHOT:compile
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.2:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.10.2:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.10.2:compile - omitted for conflict with 2.7.8)
[INFO] |  +- (javax.validation:validation-api:jar:2.0.1.Final:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-hibernate:jar:2.0.4:compile - omitted for duplicate)
[INFO] |  +- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] |  +- (com.smoketurner:dropwizard-swagger:jar:1.0.0-1:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.dataformat:jackson-dataformat-xml:jar:2.7.4:compile - omitted for duplicate)
[INFO] |  +- (org.eclipse.jetty:jetty-server:jar:9.4.30.v20200611:compile - omitted for duplicate)
[INFO] |  \- (com.google.guava:guava:jar:28.0-jre:compile - omitted for conflict with 23.0)
[INFO] +- com.flipkart.fintech:winterfell-client:jar:1.0.49:compile
[INFO] |  +- (com.flipkart.fintech:winterfell-api:jar:1.0.49:compile - omitted for duplicate)
[INFO] |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 4.0)
[INFO] |  +- (org.glassfish.jersey.core:jersey-client:jar:2.25.1:compile - omitted for conflict with 2.30.1)
[INFO] |  +- (org.apache.httpcomponents:httpclient:jar:4.5.11:compile - omitted for conflict with 4.5.8)
[INFO] |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.1:compile - omitted for conflict with 2.0.1)
[INFO] |  \- (com.flipkart.fintech:fintech-common-headers:jar:1.0.42:compile - omitted for conflict with ********)
[INFO] +- com.flipkart.fintech:citadel-client:jar:1.0.35:compile
[INFO] |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 4.0)
[INFO] |  +- (org.glassfish.jersey.core:jersey-client:jar:2.25.1:compile - omitted for conflict with 2.30.1)
[INFO] |  +- com.flipkart.fintech:citadel-api:jar:1.0.35:compile
[INFO] |  |  +- org.hibernate:hibernate-validator:jar:5.4.2.Final:compile
[INFO] |  |  |  +- (javax.validation:validation-api:jar:1.1.0.Final:compile - omitted for conflict with 2.0.1.Final)
[INFO] |  |  |  +- (org.jboss.logging:jboss-logging:jar:3.3.0.Final:compile - omitted for conflict with 3.3.2.Final)
[INFO] |  |  |  \- (com.fasterxml:classmate:jar:1.3.1:compile - omitted for conflict with 1.3.4)
[INFO] |  |  +- (org.projectlombok:lombok:jar:1.18.12:compile - omitted for conflict with 1.18.10)
[INFO] |  |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 4.0)
[INFO] |  |  +- (io.dropwizard:dropwizard-jackson:jar:1.3.5:compile - omitted for conflict with 2.0.4)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.7.1:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.1:compile - omitted for conflict with 2.0.1)
[INFO] |  |  \- (com.flipkart.fintech:fintech-commons:jar:1.0.94:compile - omitted for conflict with 1.0.122)
[INFO] |  +- (org.apache.commons:commons-lang3:jar:3.9:compile - omitted for conflict with 3.3.2)
[INFO] |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.1:compile - omitted for conflict with 2.0.1)
[INFO] |  \- (com.flipkart.fintech:fintech-common-headers:jar:1.0.42:compile - omitted for conflict with ********)
[INFO] +- com.flipkart.fintech:fintech-source-attribution:jar:1.0.114:compile
[INFO] |  +- (com.flipkart.fintech:fintech-commons:jar:1.0.114:compile - omitted for conflict with 1.0.122)
[INFO] |  +- com.flipkart.fintech:fintech-redis-client:jar:1.0.86:compile
[INFO] |  |  +- org.redisson:redisson:jar:3.11.1:compile
[INFO] |  |  |  +- (io.netty:netty-common:jar:4.1.36.Final:compile - omitted for conflict with 4.1.44.Final)
[INFO] |  |  |  +- (io.netty:netty-codec:jar:4.1.36.Final:compile - omitted for conflict with 4.1.66.Final)
[INFO] |  |  |  +- (io.netty:netty-buffer:jar:4.1.36.Final:compile - omitted for conflict with 4.1.44.Final)
[INFO] |  |  |  +- (io.netty:netty-transport:jar:4.1.36.Final:compile - omitted for conflict with 4.1.44.Final)
[INFO] |  |  |  +- (io.netty:netty-resolver-dns:jar:4.1.36.Final:compile - omitted for conflict with 4.1.33.Final)
[INFO] |  |  |  +- (io.netty:netty-handler:jar:4.1.36.Final:compile - omitted for conflict with 4.1.44.Final)
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.0.0:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.2.6.RELEASE:compile
[INFO] |  |  |  |  \- (org.reactivestreams:reactive-streams:jar:1.0.2:compile - omitted for duplicate)
[INFO] |  |  |  +- io.reactivex.rxjava2:rxjava:jar:2.2.7:compile
[INFO] |  |  |  |  \- (org.reactivestreams:reactive-streams:jar:1.0.2:compile - omitted for duplicate)
[INFO] |  |  |  +- de.ruedigermoeller:fst:jar:2.57:compile
[INFO] |  |  |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.8:compile - omitted for conflict with 2.10.2)
[INFO] |  |  |  |  +- (org.javassist:javassist:jar:3.21.0-GA:compile - omitted for conflict with 3.27.0-GA)
[INFO] |  |  |  |  \- org.objenesis:objenesis:jar:2.5.1:compile
[INFO] |  |  |  +- (org.slf4j:slf4j-api:jar:1.7.25:compile - omitted for conflict with 1.7.6)
[INFO] |  |  |  +- (com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.9.9:compile - omitted for conflict with 2.10.3)
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.9.9:compile - omitted for conflict with 2.10.2)
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.9.9:compile - omitted for conflict with 2.10.2)
[INFO] |  |  |  \- org.jodd:jodd-bean:jar:5.0.10:compile
[INFO] |  |  |     \- org.jodd:jodd-core:jar:5.0.10:compile
[INFO] |  |  +- (com.flipkart.fintech:fintech-commons:jar:1.0.93:compile - omitted for conflict with 1.0.122)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] +- com.palominolabs.metrics:metrics-guice:jar:3.1.3:compile
[INFO] |  +- (io.dropwizard.metrics:metrics-core:jar:3.1.0:compile - omitted for conflict with 4.1.5)
[INFO] |  +- io.dropwizard.metrics:metrics-annotation:jar:3.1.0:compile
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.7:compile - omitted for conflict with 1.7.6)
[INFO] |  +- (io.dropwizard.metrics:metrics-healthchecks:jar:3.1.0:compile - omitted for conflict with 4.1.5)
[INFO] |  +- (com.google.inject:guice:jar:3.0:compile - omitted for conflict with 4.0)
[INFO] |  \- (com.google.code.findbugs:jsr305:jar:3.0.0:compile - omitted for conflict with 3.0.2)
[INFO] +- com.flipkart.fintech:fintech-common-mysql:jar:1.0.59:compile
[INFO] |  +- (org.hibernate:hibernate-envers:jar:5.4.4.Final:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-hibernate:jar:2.0.2:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (com.flipkart.fintech:fintech-common-headers:jar:1.0.59:compile - omitted for conflict with ********)
[INFO] |  +- (com.flipkart.fintech:fintech-commons:jar:1.0.59:compile - omitted for conflict with 1.0.122)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech:fintech-common-rotator:jar:1.0.32:compile
[INFO] |  +- (io.dropwizard:dropwizard-core:jar:1.3.5:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 4.0)
[INFO] |  +- (com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.6.7:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  \- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] +- com.flipkart.fintech:fintech-kafka-producer:jar:1.0.57-SNAPSHOT:compile
[INFO] |  +- org.apache.kafka:kafka-clients:jar:2.0.1:compile
[INFO] |  |  +- org.lz4:lz4-java:jar:1.4.1:compile
[INFO] |  |  +- org.xerial.snappy:snappy-java:jar:*******:compile
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.7.25:compile - omitted for conflict with 1.7.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech:fintech-am:jar:1.0.110:compile
[INFO] |  +- (com.flipkart.fintech:fintech-kafka-producer:jar:1.0.106:compile - omitted for conflict with 1.0.57-SNAPSHOT)
[INFO] |  +- (com.flipkart.fintech:fintech-commons:jar:1.0.76:compile - omitted for conflict with 1.0.122)
[INFO] |  +- (com.flipkart.fintech:fintech-redis-client:jar:1.0.106:compile - omitted for conflict with 1.0.86)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech:stratum-client:jar:1.0.92:compile
[INFO] |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 4.0)
[INFO] |  +- (org.glassfish.jersey.core:jersey-client:jar:2.25.1:compile - omitted for conflict with 2.30.1)
[INFO] |  +- (org.apache.httpcomponents:httpclient:jar:4.5.11:compile - omitted for conflict with 4.5.8)
[INFO] |  +- (org.glassfish.jersey.connectors:jersey-apache-connector:jar:2.25.1:compile - omitted for conflict with 2.30.1)
[INFO] |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.1:compile - omitted for conflict with 2.0.1)
[INFO] |  +- (com.flipkart.fintech:stratum-api:jar:1.0.92:compile - omitted for duplicate)
[INFO] |  +- (com.flipkart.fintech:fintech-common-headers:jar:1.0.42:compile - omitted for conflict with ********)
[INFO] |  +- com.amazonaws:aws-java-sdk-core:jar:1.9.4:compile
[INFO] |  |  +- (commons-logging:commons-logging:jar:1.1.3:compile - omitted for conflict with 1.2)
[INFO] |  |  +- (org.apache.httpcomponents:httpclient:jar:4.3:compile - omitted for conflict with 4.5.8)
[INFO] |  |  \- (joda-time:joda-time:jar:2.9.9:compile - omitted for duplicate)
[INFO] |  \- (com.fasterxml.jackson.core:jackson-databind:jar:2.9.6:compile - omitted for conflict with 2.10.2)
[INFO] +- com.flipkart:lockin-model:jar:2.0.4:compile
[INFO] |  +- (org.apache.commons:commons-collections4:jar:4.1:compile - omitted for conflict with 4.3)
[INFO] |  +- (javax.validation:validation-api:jar:1.1.0.Final:compile - omitted for conflict with 2.0.1.Final)
[INFO] |  +- com.flipkart:lockin-client-model:jar:1.0.4:compile
[INFO] |  |  +- (org.apache.commons:commons-collections4:jar:4.1:compile - omitted for conflict with 4.3)
[INFO] |  |  \- (javax.validation:validation-api:jar:1.1.0.Final:compile - omitted for conflict with 2.0.1.Final)
[INFO] |  +- (org.apache.commons:commons-lang3:jar:3.4:compile - omitted for conflict with 3.3.2)
[INFO] |  \- com.flipkart:coinmanager-model:jar:1.1.1-SNAPSHOT:compile
[INFO] |     +- (javax.validation:validation-api:jar:1.1.0.Final:compile - omitted for conflict with 2.0.1.Final)
[INFO] |     \- (org.apache.commons:commons-lang3:jar:3.8.1:compile - omitted for conflict with 3.3.2)
[INFO] +- com.flipkart.fintech:fintech-kafka-consumers:jar:1.0.126:compile
[INFO] |  +- (org.apache.kafka:kafka-clients:jar:2.0.1:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-lifecycle:jar:1.3.1:compile - omitted for conflict with 2.0.2)
[INFO] |  +- (com.google.inject.extensions:guice-multibindings:jar:4.0:compile - omitted for duplicate)
[INFO] |  +- com.flipkart.yak:sep-models:jar:0.3-RELEASE:compile
[INFO] |  +- (com.flipkart.fintech:fintech-common-yak:jar:1.0.126:compile - omitted for conflict with 1.0.11)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech:fintech-common-yak:jar:1.0.11:compile
[INFO] |  +- com.flipkart.yak:client:jar:1.2.4-fk4:compile
[INFO] |  |  \- org.apache.hbase:hbase-shaded-client:jar:1.2.4:compile
[INFO] |  |     +- (commons-logging:commons-logging:jar:1.2:compile - omitted for duplicate)
[INFO] |  |     +- (org.slf4j:slf4j-api:jar:1.7.7:compile - omitted for conflict with 1.7.6)
[INFO] |  |     +- org.apache.htrace:htrace-core:jar:3.1.0-incubating:compile
[INFO] |  |     +- com.github.stephenc.findbugs:findbugs-annotations:jar:1.3.9-1:compile
[INFO] |  |     \- (log4j:log4j:jar:1.2.17:compile - omitted for conflict with 1.2.15)
[INFO] |  +- (com.flipkart.fintech:fintech-commons:jar:1.0.11:compile - omitted for conflict with 1.0.122)
[INFO] |  +- (io.dropwizard:dropwizard-core:jar:2.0.2:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 4.0)
[INFO] |  +- (org.projectlombok:lombok:jar:1.18.12:compile - omitted for conflict with 1.18.10)
[INFO] |  +- (com.flipkart.kloud.config:client-java:jar:1.5.1:compile - omitted for conflict with 1.5.9)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  +- (io.dropwizard:dropwizard-lifecycle:jar:2.0.2:compile - omitted for duplicate)
[INFO] |  +- (org.apache.httpcomponents:httpclient:jar:4.5.2:compile - omitted for conflict with 4.5.8)
[INFO] |  \- (com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.6.7:compile - omitted for duplicate)
[INFO] +- plutus-service:plutus-client:jar:1.0.15:compile
[INFO] |  +- (javax.validation:validation-api:jar:2.0.1.Final:compile - omitted for duplicate)
[INFO] |  +- plutus-service:plutus-common-models:jar:1.0.1:compile
[INFO] |  |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.7.6:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.7.6:compile
[INFO] |  |  |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.7.6:compile - omitted for conflict with 2.10.2)
[INFO] |  |  |  \- (com.fasterxml.jackson.core:jackson-databind:jar:2.7.6:compile - omitted for conflict with 2.10.2)
[INFO] |  |  +- (com.fasterxml.jackson.datatype:jackson-datatype-joda:jar:2.7.6:compile - omitted for conflict with 2.10.3)
[INFO] |  |  +- (com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.7.6:compile - omitted for conflict with 2.6.7)
[INFO] |  |  +- (com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.7.6:compile - omitted for conflict with 2.7.8)
[INFO] |  |  +- (com.flipkart.kloud.authn:client:jar:**********:compile - omitted for conflict with **********)
[INFO] |  |  +- (org.webjars:swagger-ui:jar:3.23.11:compile - omitted for duplicate)
[INFO] |  |  +- (org.projectlombok:lombok:jar:1.18.12:compile - omitted for conflict with 1.18.10)
[INFO] |  |  +- (com.flipkart.scheduler:scheduler-client-api:jar:0.10.1:compile - omitted for conflict with 0.10.0)
[INFO] |  |  +- (com.flipkart.alm.configmap:java-sdk-shaded:jar:0.1.0:compile - omitted for duplicate)
[INFO] |  |  \- (com.flipkart.cp.usercluser.facadelayer:data-model:jar:1.0.0:compile - omitted for duplicate)
[INFO] |  +- (io.dropwizard:dropwizard-validation:jar:1.3.1:compile - omitted for conflict with 2.0.4)
[INFO] |  +- (com.flipkart.kloud.authn:client:jar:**********:compile - omitted for conflict with **********)
[INFO] |  +- org.webjars:swagger-ui:jar:3.23.11:compile
[INFO] |  +- (org.projectlombok:lombok:jar:1.18.12:compile - omitted for conflict with 1.18.10)
[INFO] |  +- (com.flipkart.scheduler:scheduler-client-api:jar:0.10.1:compile - omitted for conflict with 0.10.0)
[INFO] |  +- com.flipkart.alm.configmap:java-sdk-shaded:jar:0.1.0:compile
[INFO] |  \- com.flipkart.cp.usercluser.facadelayer:data-model:jar:1.0.0:compile
[INFO] |     +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.0:compile - omitted for conflict with 2.9.6)
[INFO] |     +- (org.projectlombok:lombok:jar:1.18.20:compile - omitted for conflict with 1.18.10)
[INFO] |     \- (jakarta.validation:jakarta.validation-api:jar:2.0.2:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech:user-service-client:jar:1.0.24:compile
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.10.2:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.google.inject:guice:jar:4.2.2:compile - omitted for conflict with 4.0)
[INFO] |  +- (org.glassfish.jersey.core:jersey-client:jar:2.30.1:compile - omitted for duplicate)
[INFO] |  +- (com.flipkart.fintech.userservice:api:jar:1.0.24:compile - omitted for duplicate)
[INFO] |  \- (org.apache.commons:commons-lang3:jar:3.9:compile - omitted for conflict with 3.3.2)
[INFO] +- com.flipkart.fintech:fintech-ffb:jar:1.0.60:compile
[INFO] |  +- (com.flipkart.asynchttpclient:AsyncHTTPClient:jar:1.0.0:compile - omitted for duplicate)
[INFO] |  +- (javax.servlet:javax.servlet-api:jar:3.1.0:compile - omitted for duplicate)
[INFO] |  +- (io.reactivex:rxjava:jar:1.3.8:compile - omitted for duplicate)
[INFO] |  +- (commons-lang:commons-lang:jar:2.5:compile - omitted for conflict with 2.6)
[INFO] |  +- (commons-io:commons-io:jar:2.5:compile - omitted for duplicate)
[INFO] |  +- (com.flipkart.fintech:fintech-common-headers:jar:1.0.59:compile - omitted for conflict with ********)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] +- com.netflix.hystrix:hystrix-javanica:jar:1.5-RC:compile
[INFO] |  +- org.aspectj:aspectjweaver:jar:1.7.4:compile
[INFO] |  +- org.aspectj:aspectjrt:jar:1.7.4:compile
[INFO] |  +- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] |  +- (aopalliance:aopalliance:jar:1.0:compile - omitted for duplicate)
[INFO] |  +- (commons-collections:commons-collections:jar:3.2.1:compile - omitted for conflict with 3.2.2)
[INFO] |  +- (org.apache.commons:commons-lang3:jar:3.1:compile - omitted for conflict with 3.3.2)
[INFO] |  +- (com.google.code.findbugs:jsr305:jar:2.0.0:compile - omitted for conflict with 3.0.2)
[INFO] |  \- org.ow2.asm:asm:jar:5.0.4:compile
[INFO] +- com.netflix.hystrix:hystrix-codahale-metrics-publisher:jar:1.5.18:compile
[INFO] |  +- (com.netflix.hystrix:hystrix-core:jar:1.5.18:compile - omitted for conflict with 1.5.9)
[INFO] |  \- (io.dropwizard.metrics:metrics-core:jar:3.2.2:compile - omitted for conflict with 4.1.5)
[INFO] +- com.netflix.hystrix:hystrix-core:jar:1.5.9:compile
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.0:runtime - omitted for conflict with 1.7.6)
[INFO] |  +- com.netflix.archaius:archaius-core:jar:0.4.1:compile
[INFO] |  |  +- commons-configuration:commons-configuration:jar:1.8:compile
[INFO] |  |  |  +- (commons-lang:commons-lang:jar:2.6:compile - omitted for duplicate)
[INFO] |  |  |  \- (commons-logging:commons-logging:jar:1.1.1:compile - omitted for conflict with 1.2)
[INFO] |  |  \- (org.slf4j:slf4j-api:jar:1.6.4:compile - omitted for conflict with 1.7.6)
[INFO] |  +- (io.reactivex:rxjava:jar:1.2.0:compile - omitted for conflict with 1.3.8)
[INFO] |  \- org.hdrhistogram:HdrHistogram:jar:2.1.9:compile
[INFO] +- com.flipkart.affordability:affordability-service-clients:jar:4.0.271:compile
[INFO] |  +- org.jetbrains:annotations-java5:jar:RELEASE:compile
[INFO] |  +- (org.apache.httpcomponents:httpcore:jar:4.4.3:compile - omitted for conflict with 4.4.11)
[INFO] |  +- (com.netflix.hystrix:hystrix-core:jar:1.5.9:compile - omitted for duplicate)
[INFO] |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.0:compile - omitted for conflict with 2.0.1)
[INFO] |  +- (com.google.inject.extensions:guice-multibindings:jar:4.1.0:compile - omitted for conflict with 4.0)
[INFO] |  +- (org.json:json:jar:20090211:compile - omitted for conflict with 20180813)
[INFO] |  +- (org.apache.commons:commons-lang3:jar:3.0:compile - omitted for conflict with 3.3.2)
[INFO] |  +- (commons-codec:commons-codec:jar:1.10:compile - omitted for conflict with 1.11)
[INFO] |  +- (org.glassfish.jersey.core:jersey-client:jar:2.19:compile - omitted for conflict with 2.30.1)
[INFO] |  +- (com.flipkart.restbus.hibernate:turbo-client:jar:2.3.1:compile - omitted for conflict with 2.3.1-hibernate-5.4.10)
[INFO] |  +- (com.flipkart.fintech:skyler-api-models:jar:1.8.11:compile - omitted for conflict with 1.8.14)
[INFO] |  \- com.flipkart.fintech:skyler-billing:jar:1.8.7:compile
[INFO] +- com.flipkart.de:client-jar:jar:1.8.12:compile
[INFO] +- com.flipkart.fintech:fintech-logger:jar:1.0.0:compile
[INFO] |  +- (jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile - omitted for duplicate)
[INFO] |  +- (com.flipkart.fintech:fintech-common-headers:jar:1.0.114:compile - omitted for conflict with ********)
[INFO] |  +- (com.flipkart.cp:config-service-client-wrapper:jar:3.8.5:compile - omitted for conflict with 3.8.1)
[INFO] |  +- com.flipkart.common.flogger:flogger-core:jar:0.1:compile
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.3:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- (com.google.guava:guava:jar:14.0:compile - omitted for conflict with 23.0)
[INFO] |  |  \- (com.google.code.findbugs:jsr305:jar:1.3.9:compile - omitted for conflict with 3.0.2)
[INFO] |  +- com.flipkart.common.flogger:flogger-slf4j:jar:0.1:compile
[INFO] |  |  +- (com.flipkart.common.flogger:flogger-core:jar:0.1:compile - omitted for duplicate)
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.3:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- (com.google.guava:guava:jar:14.0:compile - omitted for conflict with 23.0)
[INFO] |  |  \- (com.google.code.findbugs:jsr305:jar:1.3.9:compile - omitted for conflict with 3.0.2)
[INFO] |  +- (jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile - omitted for duplicate)
[INFO] |  +- (org.glassfish.jersey.core:jersey-common:jar:2.19:compile - omitted for conflict with 2.30.1)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech:fintech-viesti-consumers:jar:1.0.126:compile
[INFO] |  +- org.apache.pulsar:pulsar-client:jar:2.7.2:compile
[INFO] |  |  +- org.apache.pulsar:protobuf-shaded:jar:2.1.0-incubating:compile
[INFO] |  |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  |  +- (com.google.code.findbugs:jsr305:jar:3.0.2:compile - omitted for duplicate)
[INFO] |  |  +- (org.checkerframework:checker-qual:jar:3.5.0:compile - omitted for conflict with 3.1.0)
[INFO] |  |  +- (com.google.errorprone:error_prone_annotations:jar:2.3.4:compile - omitted for conflict with 2.0.18)
[INFO] |  |  +- (com.google.j2objc:j2objc-annotations:jar:1.3:compile - omitted for conflict with 1.1)
[INFO] |  |  +- io.airlift:aircompressor:jar:0.16:compile
[INFO] |  |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.1:compile - omitted for conflict with 2.0.1)
[INFO] |  |  +- org.apache.pulsar:pulsar-transaction-common:jar:2.7.2:compile
[INFO] |  |  |  +- (com.google.protobuf:protobuf-java:jar:3.11.4:compile - omitted for duplicate)
[INFO] |  |  |  \- (com.google.protobuf:protobuf-java-util:jar:3.11.4:compile - omitted for duplicate)
[INFO] |  |  +- (com.google.protobuf:protobuf-java:jar:3.11.4:compile - omitted for duplicate)
[INFO] |  |  +- com.google.protobuf:protobuf-java-util:jar:3.11.4:compile
[INFO] |  |  |  +- (com.google.protobuf:protobuf-java:jar:3.11.4:compile - omitted for duplicate)
[INFO] |  |  |  +- (com.google.guava:guava:jar:28.1-android:compile - omitted for conflict with 23.0)
[INFO] |  |  |  +- (com.google.errorprone:error_prone_annotations:jar:2.3.4:compile - omitted for conflict with 2.0.18)
[INFO] |  |  |  \- (com.google.code.gson:gson:jar:2.8.6:compile - omitted for conflict with 2.8.9)
[INFO] |  |  +- org.apache.pulsar:bouncy-castle-bc:jar:pkg:2.7.2:compile
[INFO] |  |  |  +- (org.bouncycastle:bcpkix-jdk15on:jar:1.68:compile - omitted for duplicate)
[INFO] |  |  |  \- (org.bouncycastle:bcprov-ext-jdk15on:jar:1.68:compile - omitted for duplicate)
[INFO] |  |  +- org.bouncycastle:bcpkix-jdk15on:jar:1.68:compile
[INFO] |  |  |  \- (org.bouncycastle:bcprov-jdk15on:jar:1.68:compile - omitted for duplicate)
[INFO] |  |  +- org.bouncycastle:bcprov-jdk15on:jar:1.68:compile
[INFO] |  |  +- org.bouncycastle:bcprov-ext-jdk15on:jar:1.68:compile
[INFO] |  |  +- com.sun.activation:javax.activation:jar:1.2.0:compile
[INFO] |  |  +- (org.slf4j:slf4j-api:jar:1.7.25:compile - omitted for conflict with 1.7.6)
[INFO] |  |  +- (javax.validation:validation-api:jar:1.1.0.Final:compile - omitted for conflict with 2.0.1.Final)
[INFO] |  |  \- net.jcip:jcip-annotations:jar:1.0:compile
[INFO] |  +- (io.dropwizard:dropwizard-lifecycle:jar:1.3.1:compile - omitted for conflict with 2.0.2)
[INFO] |  +- (com.google.inject.extensions:guice-multibindings:jar:4.0:compile - omitted for duplicate)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-core:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (org.slf4j:slf4j-api:jar:1.7.21:compile - omitted for conflict with 1.7.6)
[INFO] |  \- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] +- com.flipkart.fintech.userservice:api:jar:1.0.24:compile
[INFO] |  +- (com.fasterxml.jackson.core:jackson-annotations:jar:2.8.4:compile - omitted for conflict with 2.9.6)
[INFO] |  +- (com.fasterxml.jackson.core:jackson-databind:jar:2.8.4:compile - omitted for conflict with 2.10.2)
[INFO] |  +- (com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.8.4:compile - omitted for conflict with 2.7.8)
[INFO] |  +- (com.google.inject:guice:jar:4.0:compile - omitted for duplicate)
[INFO] |  +- (com.flipkart.fintech:fintech-commons:jar:1.0.50:compile - omitted for conflict with 1.0.122)
[INFO] |  +- (jakarta.validation:jakarta.validation-api:jar:2.0.2:compile - omitted for duplicate)
[INFO] |  +- (javax.ws.rs:javax.ws.rs-api:jar:2.1:compile - omitted for conflict with 2.0.1)
[INFO] |  +- (org.projectlombok:lombok:jar:1.18.24:compile - omitted for conflict with 1.18.10)
[INFO] |  \- (com.flipkart.fintech:pinaka-api:jar:2.2.27:compile - omitted for conflict with 2.2.67-SM1-SNAPSHOT)
[INFO] \- com.flipkart.primus:primus-core:jar:1.0.6:compile
[INFO]    +- com.bazaarvoice.jolt:jolt-core:jar:0.0.16:compile
[INFO]    +- com.bazaarvoice.jolt:json-utils:jar:0.0.16:compile
[INFO]    \- com.flipkart.primus:primus-models:jar:1.0.6:compile
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  01:07 min
[INFO] Finished at: 2023-08-22T19:10:12+05:30
[INFO] ------------------------------------------------------------------------
