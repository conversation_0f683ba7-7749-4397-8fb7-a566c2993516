package com.flipkart.fintech.pinaka.service.core.v6.document;

import com.amazonaws.services.s3.AmazonS3;
import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.filter.RequestContext;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pinaka.api.request.v6.documents.Document;
import com.flipkart.fintech.pinaka.api.request.v6.documents.SelfieImage;
import com.flipkart.fintech.pinaka.api.request.v6.documents.UploadDocumentRequestV6;
import com.flipkart.fintech.pinaka.service.application.PinakaModule;
import com.flipkart.fintech.stratum.api.config.StratumD42Configuration;
import com.flipkart.fintech.stratum.api.models.common.EncryptionKeyData;
import java.io.InputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

@Slf4j
public class D42DocumentHelperTest {

  D42DocumentHelper documentHelper;

  public StratumD42Configuration providesAmazonS3() {
    StratumD42Configuration configuration = new StratumD42Configuration();
    configuration.setAccessKey("GOOG1E42KDDZR4BDJO4RXCEMLX2RMZTTE7EUXMDNW4ENC2VJSLNFXPYKBLZU6");
    configuration.setSecretKey("9qDg6wG5wW0s/BuznjennwsoxUL0vnpi2qg0ZYow");
    configuration.setEndPoint("https://storage.googleapis.com");
    configuration.setMaxConnections(10);
    configuration.setConnectionTimeout(2000);
    return configuration;
  }

  @Before
  public void setUp() throws Exception {
    AmazonS3 amazonS3 = PinakaModule.getAmazonS3(providesAmazonS3());
    ObjectStore objectStore = new D42ObjectStore(amazonS3);
    documentHelper = new D42DocumentHelper(objectStore);
    RequestContext requestContext = new RequestContext(Tenant.CALM, "", "", "", false);
    RequestContextThreadLocal.setRequestContext(requestContext);
  }

  @Test
  public void uploadDocument() throws Exception {
    EncryptionKeyData keyData = new EncryptionKeyData();
    InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("base64Image.txt");
    byte[] data = IOUtils.toByteArray(inputStream);
    Document document = new SelfieImage(data);
    UploadDocumentRequestV6 uploadDocumentRequest = new UploadDocumentRequestV6(document, keyData);
    try {
      documentHelper.uploadDocument("TEST5", uploadDocumentRequest);
    } catch (Exception e) {
      log.error("Error", e);
    }
  }

    @Test
    public void downloadDocument() throws Exception {
        String documentId = "fk-p-calm-kyc_image-docs/APP2311241300529592197193813674743926646";
        try {
            String base64String = documentHelper.downloadDocument(documentId).getBase64EncodedImage();
            Assert.assertNotNull("The object you enter return null", base64String);
        } catch (Exception e) {
            log.error("Error", e);
        }
    }

}