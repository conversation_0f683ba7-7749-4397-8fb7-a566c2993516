server:
  type: default
  applicationContextPath: /pinaka
  adminContextPath: /admin
  applicationConnectors:
    - type: http
      port: 9090
  adminConnectors:
    - type: http
      port: 9091
  requestLog:
    appenders:
      - type: file
        currentLogFilename: /var/log/pinaka/access.log
        threshold: ALL
        archive: true
        archivedLogFilenamePattern: /var/log/pinaka/access-%d{yyyy-MM-dd-HH}.log.gz
        archivedFileCount: 24
        timeZone: IST

logging:
  level: INFO
  appenders:
    - type: file
      threshold: INFO
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: /var/log/pinaka/pinaka.log
      archivedLogFilenamePattern: /var/log/pinaka/pinaka-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: file
      threshold: DEBUG
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: /var/log/pinaka/pinaka.log
      archivedLogFilenamePattern: /var/log/pinaka/pinaka-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: file
      threshold: ERROR
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: /var/log/pinaka/pinaka.log
      archivedLogFilenamePattern: /var/log/pinaka/pinaka-error-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: console
      threshold: ALL
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message %replace(%exception){'\n',' | '} %n"
      timeZone: IST
      target: stdout

databaseConfig:
  encryptedUrl: jdbc:h2:mem:/target/pinaka;TRACE_LEVEL_FILE=0;TRACE_LEVEL_SYSTEM_OUT=0
  encryptedUser: root
  encryptedPassword: root
  driverClass: org.h2.Driver
  properties:
    charSet: UTF-8
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 2
    # the minimum number of connections to keep open
    minSize: 2
    # the maximum number of connections to keep open
    maxSize: 10
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

databaseSlaveConfig:
  slaveEncryptedUrl: jdbc:h2:mem:/target/pinaka;TRACE_LEVEL_FILE=0;TRACE_LEVEL_SYSTEM_OUT=0
  slaveEncryptedUser: root
  slaveEncryptedPassword: root
  driverClass: org.h2.Driver
  properties:
    charSet: UTF-8
    hibernate.generate_statistics: true
    hibernate.session.events.log: true
    hibernate.show_sql: true
    hibernate.format_sql: false
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 2
    # the minimum number of connections to keep open
    minSize: 2
    # the maximum number of connections to keep open
    maxSize: 10
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

#healthCheckName: PinakaHealth

rateLimitingConfig:
  [
    { "limiterKey": "FETCH_UI", "timeoutInMs":5 },
    { "limiterKey": "PAN_SUBMIT", "timeoutInMs":5 },
    { "limiterKey": "AADHAR_VERIFICATION", "timeoutInMs":5 },
    { "limiterKey": "GENERATE_OTP", "timeoutInMs":5 },
    { "limiterKey": "CREATE_APPLICATION", "timeoutInMs":5 },
    { "limiterKey": "PENNY_DROP_SUBMIT_DETAILS", "timeoutInMs":5 },
    { "limiterKey": "FETCH_LOADER", "timeoutInMs":5 },
    { "limiterKey": "VERIFY_OTP", "timeoutInMs":5 }
  ]

pinakaClientConfig:
  url: http://localhost:9090
  client: pinaka

ardourClientConfig:
  url: http://**********:80
  client: pinaka

pandoraClientConfig:
  url: http://***********:11002/pandora
  client: pinaka

pandoraLiteClientConfig:
  url: http://***********:11002/pandoralite
  client: pinaka

userServiceClientConfig:
  usUrl: http://***********:11002
  usClientId: affordability

oAuthServiceClientConfig:
  oAuthUrl: http://***********:80
  oAuthClientID: c2c95e0075c04b2e9b83e1bc8a09f57e
  oAuthClientSecret: FVMWGlJ22AoWfQ+ithjYn+M0tjr8xgakuHl06J5A+DELtVtc
  cachedAccessTokenTTL: 120

oAuthLockinServiceClientConfig:
  oAuthUrl: http://**********
  oAuthClientID : CBC
  oAuthTargetClientID: wallet_flipkart_plus_preprod
  oAuthClientSecret : VuLMjEJGnlPQGQS2vDvcdS2HSzh6yXdTR6Q7dfOAlL1j46uC
  cachedAccessTokenTTL : 120

#loginServiceClientConfig:
#  loginServiceUrl: http://***********:35100
#  loginServiceClientId: affordability

bigfootConfiguration:
  url: http://***********:28223
  #  url: http://localhost:28223
  poolName: pool
  company: fkint
  org: cp
  namespace: affordability
  exchangeName: advanz_ingestion_queue
  exchangeType: queue
  batchIngestionSize: 21

fluxAsyncClientConfig:
  url: http://***********:9998/api/machines
  clientId: pinaka
  exchangeName: advanz_onboarding_queue

onboardingClientConfig:
  url: http://************:8080
  client: pinaka

connektClientConfig:
  exchangeName: fintech_wintefell_chronos_calback_preprod
  callbackUrl: http://***********/pinaka/communications
  domain: flipkart
  emailUrl: http://***********
  pnUrl: http://***********
  smsUrl: http://***********
  emailApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  pnApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  smsApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  emailAppName: flipkart
  pnAppName: RetailApp
  smsAppName: flipkart
  emailBucket: alerts
  emailSubBucket: pay
  pnBucket: alerts
  pnSubBucket: pay
  smsBucket: alerts
  smsSubBucket: pay
  pnChannelId: fk_channel_order_payments
  transactionalEmail : true
  transactionalPN : true
  transactionalSMS : true

caishenClientConfig:
  url: http://*************
  targetClientId: caishen_flipkart

robinhoodAsyncClientConfig:
  url: http://************
  clientId: pinaka
  exchangeName: "fintech_wintefell_chronos_calback_preprod"

fluxConfiguration:
  fluxRuntimeUrl: http://***********:9998
  connectionTimeout: 10000
  socketTimeout: 10000

creditModelConfig:
  url: http://***********:80
  enabled: true
  testUsers: ["test1", "test2","AC5PD5WPWNURTFQ1D8NA9MP85WUXB2UI","Test_1", "Test_2", "Test_3", "ACC14118057725487501", "ACC14118057728187506", "ACC14118057731001326", "ACC14032583400405772", "ACC798D3CC5AC4447869C1B1DD645A7128AZ", "ACC14215635411148470", "ACCA17D4EA4D70F43388C6EC80E73A69180R"]

gibraltarConfig:
  maxPublicKeyCount: 100
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  trustStorePath: "src/integration-test/resources/client-truststore.jks"
  trustStorePass: password
  keyStorePath: "src/integration-test/resources/client-keystore.jks"
  keyStorePass: password
  certificateAlias: gibraltarSelfSigned
  url: http://*************:80
  generateKeysOnStartUp: false
  connectionTimeoutInMs: 200
  socketTimeoutInMs: 200
  httpRequestRetryCount: 5

cryptexConfiguration:
  cryptexBundleEnabled: false
  authNClientConfig:
    url: http://10.47.0.167
    clientId: pinaka-service
    clientSecret: VdsO8BjanfTADqp3RNaaDm0bkm14iyVGJ3lUuGdaSTS+7Ba0

  cryptexClientConfig:
    endpoint: http://***********
    maxConnections: 5  # default value
    connectTimeOut: 1500 # default value
    readTimeOut: 1500 # default value

  dynamicBucketConfig:
    bucketName: pinaka-local-constants
    enableLocalDynamicBucket: false

bnplOnboardingConfig:
  optionalPermissions:
    - CONTACT
    - LOCATION
  mandatoryPermissions:
    - SMS
    - DEVICE
  tncUrl: https://www.flipkart.com/pages/pay-later-tnc
  privaryPolicyUrl: https://www.flipkart.com/pages/fapl-privacy-policy

efaOnboardingConfig:
  ceEnabled: true
  ceTestUsers: ["ACQ23LU0JFDB2RR6K07NG1IJ7ZL5NQYI","AC5PD5WPWNURTFQ1D8NA9MP85WUXB2UI","AC3AS99QZ0B0WTYTVXWANJ7SOSHYURH5","ACCE36E0BCF906848FC8DA046725F3954F7X","ACC798D3CC5AC4447869C1B1DD645A7128AZ","ACC14215635411148470","ACCA17D4EA4D70F43388C6EC80E73A69180R","ACC05D577873443449FA9E165A662D03B961"]
  testAccountPhones: [{"<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********"}]
  optionalPermissions:
    - CONTACT
    - LOCATION
    - DEVICE
  mandatoryPermissions:
    - SMS
  tncUrl: https://www.flipkart.com/pages/efa-preapproval-tnc
  privaryPolicyUrl: https://www.flipkart.com/m/st/policies
  formFields:
    CITI:
      - fieldType: TEXT
        label: Company Name
        name: Company
      - fieldType: NUMBER
        label: Monthly Income
        name: Income
      - fieldType: DROPDOWN
        label: Employment
        name: Employment
      - fieldType: DROPDOWN
        label: Occupation
        name: Occupation
      - fieldType: DROPDOWN
        label: Qualification
        name: Qualification
      - fieldType: ADDRESS
        label: Current Address
        name: Address
    KISSHT: []
  lenderWiseFormFields:
    KISSHT:
      - PAN_NUMBER
      - DOB
      - GENDER
      - ADDRESS
    INDIA_BULLS:
      - PAN_NUMBER
      - DOB
      - GENDER
      - EMPLOYMENT_STATUS
      - MONTHLY_SALARY
      - ADDRESS
  underwritingCallouts:
    KISSHT: [{"callout":"Repay by 15th of next month at 0% interest","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}},{"callout":"Repay in 6-12 EMI's at 25% per annum","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/dfcd50c7-4c29-404e-91da-dab3c7861872.png?q={@quality}"}},{"callout":"No preclosure charges","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/93ab33b6-f57b-433a-a336-745b63c4d7a3.png?q={@quality}"}},{"callout":"Late fee - 3% of the unpaid bill* (Min ?200)","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/8e307790-3518-446f-b545-9e22243bcfeb.png?q={@quality}"}}]
    INDIA_BULLS: [{"callout":"Repay by 15th of next month at 0% interest","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}},{"callout":"Repay in 3-12 EMIs starting at 20% per annum","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/dfcd50c7-4c29-404e-91da-dab3c7861872.png?q={@quality}"}},{"callout":"No preclosure charges","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/93ab33b6-f57b-433a-a336-745b63c4d7a3.png?q={@quality}"}},{"callout":"Late fee - 3% of the unpaid bill* (Min ?200)","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/8e307790-3518-446f-b545-9e22243bcfeb.png?q={@quality}"}}]
  approvalCallouts:
    KISSHT: [{"callout":"Start shopping, select cardless credit as your payment option.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d388836b-a541-4f36-95df-49818d720bb7.png?q={@quality}"}},{"callout":"Repay by 15th of the month using debit card/netbanking.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d4534a52-a197-4463-9530-90ac5a25b4aa.png?q={@quality}"}}]
    INDIA_BULLS: [{"callout":"Start shopping, select cardless credit as your payment option.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d388836b-a541-4f36-95df-49818d720bb7.png?q={@quality}"}},{"callout":"Repay by 15th of the month using debit card/netbanking.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d4534a52-a197-4463-9530-90ac5a25b4aa.png?q={@quality}"}}]

bigfootEntityConfig:
  borrowerConfig:
    name: fintech_borrowers
    schemaVersion: 1.6
  loanApplicationConfig:
    name: fintech_loan_application
    schemaVersion: 2
  subApplicationConfig:
    name: fintech_loan_subapplication
    schemaVersion: 1.1
  whitelistConfig:
    name: fintech_whitelist
    schemaVersion: 2
  sourceAttributionConfig:
    name: fintech_source_attribution
    schemaVersion: 1
  ebcApplicationConfig:
    name: fintech_ebc_borrower
    schemaVersion : 1

swagger:
  title: Pinaka Service
  description: APIs for Pinaka Service
  contact: <EMAIL>
  resourcePackage: com.flipkart.fintech.pinaka.service.web

cryptoConfig:
  algo: AES
  secretKey: "Cd1/SXipT)So3=19"
  cbcTelesaleSecretKey: "cbc/Tele$@les(=3"

underwritingConfig:
  endPoint: http://***********:11002/fintech-underwriting

citiConfig:
  enabled: false
  productConfig:
    productCode: PC400
    sourceCode: AAFKMNET
    organization: 730
    logo: 400
    embossName: "Matthew Hyden"
  supportedPincodes: ["pincode1", "pincode2"]

lenderConfiguration:
  configurations:
    EFA:
      -
        consentTextKey: key3
        hasAdditionalForm: true
        interestRate: 10
        emiSupported: true
        paylaterSupported: false
        lateFees: 10
        lender: CITI
        postApprovalTncKey: key2
        preApprovalTncKey: key1
        showKycDetails: false
        primaryDataEditable: false
      -
        consentTextKey: key3
        hasAdditionalForm: false
        interestRate: 10
        emiSupported: true
        paylaterSupported: true
        lateFees: 10
        lender: KISSHT
        postApprovalTncKey: key2
        preApprovalTncKey: key1
        showKycDetails: true
        primaryDataEditable: true
      -
        consentTextKey: key3
        hasAdditionalForm: false
        interestRate: 10
        emiSupported: true
        paylaterSupported: true
        lateFees: 10
        lender: INDIA_BULLS
        postApprovalTncKey: key2
        preApprovalTncKey: key1
        showKycDetails: true
        primaryDataEditable: true

    CBC:
      -
        lender: AXIS
    BNPL:
      -
        hasAdditionalForm: false
        lender: IDFC
        primaryDataEditable: true
        nameMatchThreshold: 50
        nameValidationEnabled: true
        panValidationRequired: true
        postApprovalTncKey: key2
        consentTextKey: key3
        preApprovalTncKey: key1
        minimumAgeRequired: 21

    FLIPKART_ADVANZ:
      -
        hasAdditionalForm: false
        lender: IDFC
        primaryDataEditable: true
        nameMatchThreshold: 50
        nameValidationEnabled: true
        panValidationRequired: true
        postApprovalTncKey: key2
        consentTextKey: key3
        preApprovalTncKey: key1
        minimumAgeRequired: 21
        ignoreLenderCEAccountStates: ["APPROVED", "ADOPTED"]


loginServiceClientConfig:
  loginServiceUrl: http://***********:11002
  loginServiceClientId: affordability

upgradationConfig:
  upgradationEnabled: true

tijoriConfig:
  url: http://**********:9090
  clientName: robinhood

tijoriAsyncClientConfig:
  merchant: mp_flipkart
  exchangeName: advanz_onboarding_queue
  tijoriUrl: http://**********:9090

external_client_config:
  page_service_config:
    host: ***********
    port: 80

cbcOnboardingConfig:
  fkpayTriggerOtpUrl: http://localhost:8090/fkpay/api/v3/fintech/otp/send
  fkpayDisplayCardEtbUrl: http://localhost:8090/fkpay/api/v3/fintech/paymentInstrument/CBC_AXIS_APP_SERNO
  fkpayDisplayCardEtccUrl: http://localhost:8090/fkpay/api/v3/fintech/paymentInstrument/CBC_AXIS_CARD_SERNO
  fkpayDisplayCardConsoleUrl: http://localhost:8090/fkpay/api/v3/fintech/paymentInstrument/CBC_AXIS_CARD_CONSOLE
  cbcComingSoonBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/09/2019/9afe3704-98b0-4faf-9f0b-0391069cfde3.jpg?q={@quality}"
  cbcApplyNowBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/09/2019/052efd3d-a219-4350-b1f5-0ce8911e5a1b.jpg?q={@quality}"
  cbcContinueApplicationBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/09/2019/71652a80-3aad-4104-b156-e3f5fd05e51f.jpg?q={@quality}"
  cbcViewCardDetailsBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/10/07/2019/8f863e90-5ebe-49f9-a23f-d2e0fb438f18.jpg?q={@quality}"
  cbcApplicationEtccBuzzBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/09/2019/70b693fa-4fc8-416a-bed7-fb4f7e1c8c77.png?q={@quality}"
  cbcApplicationEtccNonBuzzBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/26/06/2019/8f52e838-3942-458c-ba93-c274c5de9df0.png?q={@quality}"
  cbcApplicationEtccBuzzBannerAspectRatio: "312:44"
  cbcApplicationEtccNonBuzzBannerAspectRatio: "312:80"
  plusWhitelistIdMap:
    phonepe: 112
    offers : 112
    myntra: 136
    wakefit: 154
    mastercard: 154
  whitelistIdMap:
    phonepe: 113
    offers: 113
    myntra: 137
    wakefit: 154
    mastercard: 154
  externalMerchnats: ["phonepe","offers","myntra", "wakefit","mastercard"]
  etbApprovalCallouts:    [
    {
      "callout": "Access your card details",
      "description": "Account Summary - Details of dues, transactions",
      "icon": {
        "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"
      }
    },
    {
      "callout": "Reward Points Summary",
      "description": "Manage/Redeem reward points or Set Goals",
      "icon": {
        "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"
      }
    },
    {
      "callout": "Create Requests",
      "description": "Subscribe for E-statement or duplicate statement, Block Lost/Stolen card, Active international usage...",
      "icon": {
        "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"
      }
    }]
  ntbApprovalCallouts: [
    {
      "callout": "You will need to provide an identity proof from the list of accepted documents",
      "icon": {
        "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"
      }
    }
  ]
  accountsEnabledForCbcNtbSplitForm: ["ACCA17D4EA4D70F43388C6EC80E73A69180R", "ACC5131090496944D149296C47B98F07A99A", "ACC43FCBD35D415408ABF46E228618FEA39B"]
  accountsEnabledForConsoleCug: []
  ntbFormSubTypeWiseFormList:
    PERSONAL_DETAILS:
      - personal_details
    CONTACT_DETAILS:
      - contact_details
      - address_form
    PROFESSIONAL_DETAILS:
      - professional_details
    CREDIT_CARD_FEATURES:
      - credit_card_features
  etsFormSubTypeWiseFormList:
    PERSONAL_DETAILS:
      - personal_details
    CONTACT_DETAILS:
      - contact_details
      - address_form
    PROFESSIONAL_DETAILS:
      - professional_details
    CREDIT_CARD_FEATURES:
      - credit_card_features
  cohortWiseFormList:
    ETB_PRE_APPROVED:
      - personal_details
      - address_form
      - credit_card_features
    ETB_PRE_APPROVED_AUTODEBIT:
      - personal_details
      - autodebit_details
      - address_form
      - credit_card_features
    ETB_NON_PRE_APPROVED_AUTODEBIT:
      - personal_details
      - address_form
      - professional_details
      - autodebit_details
      - credit_card_features
    ETB_NON_PRE_APPROVED:
      - personal_details
      - address_form
      - professional_details
      - credit_card_features
    ETCC:
      - personal_details
      - address_form
    NTB:
      - personal_details
      - contact_details
      - professional_details
      - address_form
      - credit_card_features
    ETS:
      - personal_details
      - contact_details
      - professional_details
      - address_form
      - credit_card_features
  axisThinCremoScoreThreshold: 493
  autoRejectThinEnabled: "N"
  cbcNtbSplitEnabled: true
  whitelistIdsEnabledForCbcNtbSplitForm: [
      "107",
      "108",
      "37",
      "138",
      "13",
      "135",
      "66",
      "110"
  ]
  channelsEnabledForCbcNtbSplitForm: [
      "ANDROID",
      "iOS",
      "MSITE_UCWEB"
  ]
  encryptionKey: "53EACE72CD83D6B60754C2F3959168EA"
  kycSchedulerEnabled: true
  dobSubtextEnabled: "Y"
  kycSchedulerTtl: 30
  accountsEnabledForCheckStatusV3: []
  accountEnabledForKycV2: []
  firstYearFreeStartDate: "17/08/2020"
  firstYearFreeEndDate: "30/09/2020"
  accountsEnabledForStoreMergeExp: ["ACXGN4LIBRS1DYHS6PNEDBHZNDZGJXPU"]
  storeMergeEnabled: true
  cbcVkycEnabled: true
  accountsEnabledForVkyc: ["ACC3BB3DEE46C6B482F9ECD3F33667719CC4","ACC7015C0E29AB547C58C59A7A40CB52172Y","ACXGN4LIBRS1DYHS6PNEDBHZNDZGJXPU","ACCD1FAFCA251E848B48031CB2D42EBD100H", "ACCTEST10010", "ACCTEST10010"]
  emiUserList: [ "ACC14269179088889030","ACC4BBD119E3A394E2D99818EB471201D53A" ]
  emiBillingEnabled: true
  ntbCbcWhitelistIds: [ "66" ]
  etccCbcWhitelistIds: [ "42" ]
  etbpaCbcWhitelistIds: [ "136" ]
  etbnpaCbcWhitelistIds: [ "137" ]
  ntbSupercoinWhitelistIds: [ "142" ]
  etccSupercoinWhitelistIds: [ "143" ]
  etbpaSupercoinWhitelistIds: [ "144" ]
  etbnpaSupercoinWhitelistIds: [ "145" ]
  cbcVkycCugEnabled : true
  whitelistIdForOfflineCustomers: "135"
  accountsEnabledForEtccAlternateCug: []
  accountsEnabledForTransactionSummary: []
  externalWhitelistIds : [111,112,113,136,137]
  incomeBinThreshold: 474
  cbcHyperlinkPhoneEnabled : true
  checkApplyNowVisibilty: true
  cbcNewRejectPageEnabled: true
  accountsEnabledForCbcSplit: [ "ACCTEST12345" ]
  ntbCugUserList: ["ACC39537669976E4CDF95F265AC514076EF6"]
  cbcNtbRevampValue: true
  applicationRetryDuration: 500
  applicationSyncCutoffDate: "2022-12-22 20:00:00"

collectionsClientConfiguration:
  scheme: http
  host: ************
  port: 7980

schedulerClientConfig:
  host: ***********
  port: 11002
  poolSize: 20
  clientId: fintech_cf

contextWiseWhitelistConfig:
  contextMap:
    NUDGING-EFA-INDIA_BULLS:
      isEnabledForEveryone: false
      whitelistedAccountIds: ["ACC798D3CC5AC4447869C1B1DD645A7128AZ"]
    NUDGING-CBC-AXIS:
      isEnabledForEveryone: false
      whitelistedAccountIds: []
    NPS-CBC-AXIS:
      isEnabledForEveryone: false
      whitelistedAccountIds: ["ACC1A75EB1E55584AE89225A4B9CC7232FCT"]
    NUDGING-FLIPKART_ADVANZ-IDFC:
      isEnabledForEveryone: false
      whitelistedAccountIds: ["ACCEF461CC7F5054F6CA400852EB41CC9ABT"]

contextWiseCacheBuilderConfig:
  contextMap:
    PAN_RETRY:
      maximumSize: 10000
      duration: 30
      durationUnit: DAYS

bigfootCallbackConfiguration:
  pinakaBaseUrl: http://***********:9090
  callbackQueue: EXCHANGE_NAME

heliosProxyConfig:
  maxConnections: 1024
  maxConnectionsPerHost: 1024
  pooledConnectionIdleTimeoutInMS: 6000
  executorThreadsCount: 32
  executionIsolationSemaphoreMaxConcurrentRequests: 1024
  executionTimeoutInMS: 10000
  hystrixCommandKey: PinakaServiceHystrixCommand
  hystrixGroupKey: PinakaServiceHystrixGroup
  rewriteProtocal: http
  rewriteHostName: 127.0.0.1
  rewritePortNumber: 80
  enableProxy: false

fldgConfiguration:
  enableFldg: false

abConfiguration:
  endpoint: ***********:80
  layerList: "Affordability"
  clientId: "Mapi.affordability"
  testAccounts: ["ABC123"]

kycConfig:
  xmlKycAndroidAppVersion: "1080100"
  xmlKycChannels: ["ANDROID"]
  activeKycModes: ["EKYC", "AADHAAR_XML_EKYC"]
  createNewKycApplicationForOldUserEnabled: false
  defaultMethodForTestAccount: CKYC

onboardingConfig:

  genericFlowEnabled: true
  whitelistChangeEntityEnabled: true
  genericFlowAccountIds: ["ACC76FCADFC7B3A443F939DFE42003603AAI"]
  plusWhitelistId: 135
  productOnboardingConfig:
    BNPL:
      storePageUrl: "/pages/mw/flipkart-advanz/apply-now"
      queue: bnpl_onboarding_queue
    FLIPKART_ADVANZ:
      storePageUrl: "/pages/mw/flipkart-advanz/apply-now"
      queue: advanz_onboarding_queue


alfredClientConfig:
  url: http://***********
  secretKey: 123rewedrgrvcz


lockinClientConfig:
  url: http://*************
  clientId: Fintech

neoCrmClientConfig:
  exchangeName: "fintech_cbc_production"
  authToken: "LVNKY7IAABXXAAZB"
  clientId: "Fintech"
  url: "http://***********"
  path: "/v1/crm/publish"


uiConfiguration:
  cohortPriorityList: [CASH, EMI, PAY_LATER, FSUP]
  cohorts:
    CASH:
      name: Cash Loan
      full_description: For withdrawal upto ₹%s
    EMI:
      name: EMIs
      full_description: For purchases upto ₹%s
    PAY_LATER:
      name: Pay Later
      full_description: Pay next month for shopping upto ₹%s
      minimized_description: Flipkart Pay Later enabled
      logo_url:
    FSUP:
      name: Smart Upgrade
      full_description: Get your phone by paying just a fraction of amount upfront
      minimized_description: Flipkart Smart Upgrade enabled
      logo_url:
  uiConfigMap:
    APPROVED:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle : "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "knowMoreText"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          -
            description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          -
            description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          -
            description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
    LOAN_CREATION_IN_PROGRESS:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle: "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "knowMoreText"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          - description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          - description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          - description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    POST_PROCESSING_PENDING:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle: "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "knowMoreText"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          - description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          - description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          - description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    CONDITIONALLY_APPROVED:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle: "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "Know more about Flipkart Pay Later"
      knowMoreLandingUrl: ""
      payLaterSubmitButtonText: "Activate Flipkart Pay Later"
      payLaterSubmitButtonUrl: ""
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          - description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          - description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          - description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
    REJECTED:
      title: "Thank you for your interest in %s"
      subtitle: "Due to our financial partner policies, we are not able to provide a credit line at this moment"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      imageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      nonUpgradedSubtitle : "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"


mysqlLockConfig:
  prefix: ""
  timeout: 0

dataPointConfiguration:
  enableDataPoint:
    TS:
      enable: false
      testAccountList: ["ACC8B2427BE8D354EAF9D94046347FDA13ES"]


#redisConfig:
#  masterName: mymaster
#  timeout: 5000
#  sentinelAddresses: ["redis://127.0.0.1:26379", "redis://127.0.0.1:26379", "redis://127.0.0.1:26379"]
#  password:
#  masterConnectionMinimumIdleSize: 3
#  masterConnectionPoolSize: 100
#  lockWaitTime: 10
#  lockReleaseTime: 3

redisConfig:
  masterName: fintech-redis-prod
  timeout: 5000
  sentinelAddresses: ["redis://*************:26379","redis://************:26379","redis://*************:26379"]
  password: ABtr@25
  masterConnectionMinimumIdleSize: 3
  masterConnectionPoolSize: 100
  lockWaitTime: 10
  lockReleaseTime: 3

sourceAttributionConfig:
  enabled: true
  redisTtlInSeconds: 43200

rotation:
  enableRotator: true
  defaultRotationStatus: true

winterfellClientConfig:
  host: http://***********:11002/fintech-winterfell
  clientId: pinaka
  connectionTimeout: 60000
  readTimeout: 60000

robinhoodAsyncCbcConfig:
  url: http://************
  exchangeName: "cbc_robinhood_queue"

skylerClientConfig:
  url: http://localhost
  exchangeName: "skylerQueueName"

robinhoodCfaClientConfig:
  url: http://************
  exchangeName: "cfa_onboarding_queue"

coreLogisticsClientConfig:
  url: http://**********
  referer: "http://www.fintech.com"
  key: "79a271ce-b870-c03b-cdbb-a52861a90ea9"

pandoraAsyncClientConfig:
  url: http://************:8214/pandora
  exchangeName: bnpl_onboarding
  exchangeType: queue

ffbConfig:
  url: "http://localhost:8978"
  endPoint: "/ffb/approved"
  exchangeName : "advanz_onboarding_queue"
  environment: PREPROD
  businessToLenderDocumentTypeMapping:
    BUSINESS_PROOF: BUSINESS_PROOF
    SHOP1: OTHER_1
    SHOP2: OTHER_2
    SHOP3: OTHER_3

encryptionKeys:
  kycKey: "03A1CE01ED4D5FA1ABBDCD728C0BE2A8"
  default: "59AC6C2B95DEFC3EC76C56CF232AF829"
  cbcKey: "59AC6C2B95DEFC3EC76C56CF232AF829"

pinakaAsyncClientConfig:
  url: http://localhost:9090
  exchangeName: advanz_onboarding_queue
  ebcExchangeName: production.pandora.ebc.callback
  changeEntityEventTopic: fintech.change.entity
  cfaExchangeName: cfa_onboarding_queue
  cbcCardSerNoExchangeName: cbc_card_ser_queue
  cbcCardSerNoHostUrl: http://***********:9090
  cbcCardSerNoPath: /5/applications/process-application

plutusClientConfig:
  url: http://***********:80

fintechUserServiceClientConfig:
  uslUrl: "http://***********"
  exchangeName: fintech_usl_ingestion_queue

khaataAsyncClientConfig:
  url: http://0.0.0.0:8980
  exchangeName: cbc_bill_initiation_queue

skylerAsyncClientConfig:
  url: hhtp://localhost:11000
  exchangeName: cbc_bill_initiation_queue

fkPayClientConfig:
  url: http://***********
  clientId: Fintech
  clientSecret: ypg37lwetecaj96pih96
  encryptionKey: FINTECH_AES_KEYS

stratumD42Configuration:
  accessKey: 4YIR77D1R7I87RZKGO2C
  secretKey: l8c2tgwTqeUHFUKrcBPoMr91gCu8Ssa5S1bHE6yq
  endPoint: http://**********
  maxConnections: 10
  connectionTimeout: 2000

outboundBundleConfiguration:
  defaultDaysToKeep: 15
  shardNames: ["default"]
  databaseName: "pinaka"

audienceManagerConfiguration:
  realtimeSegmentConfiguration:
    enabled: true
    testAccounts: ["ACC4C581A9788A842AAA296DD318AAF81A6W"]
    topicName: test
    enabledJourneyList: ["LOAN_APPLICATION_JOURNEY", "WHITELIST_JOURNEY"]
  kafkaProducerConfiguration:
    bootstrapServer: "localhost:9092"
    acks: "1"
    retries: 2
    batchSize: 20
    lingerInMilliSecond: 5
    maxInFlightRequestsPerConnection: 1


uslClientConfig:
  host: http://***********
  clientId: pinaka
  connectionTimeout: 2000
  readTimeout: 2000

financialProviderConfig:
  kycMethodConfig:
    AADHAAR_XML_EKYC:
      metadata:
        IDFC:
          tncUrl:  https://www.flipkart.com/pages/pay-later-tnc-v1
          tollFreeNo: 1860 500 9900
          email: <EMAIL>
          xmlKycTncUrl:  https://www.flipkart.com/pages/pay-later-tnc-v1
          xmlKycDisclaimer: Alternatively, you can submit your KYC documents at the nearest IDFC FIRST Bank for verification.
          reviewAndSubmitDisclaimer: I agree to (1) share the above details with Flipkart & it’s lending partners, (2) allow Flipkart’s lending partners to obtain my credit information from a credit rating agency, (3)
          reviewAndSubmitTncUrl: https://www.flipkart.com/pages/pay-later-tnc-v1
          reviewAndSubmitTncText: Terms & Conditions
          consentContext: KYC-PAGE-CONSENT-V0
          kycType: PARTIAL
      validation:
        IDFC:
          nameMatchThreshold: 50
          nameValidationEnabled: true
          panValidationRequired: true
      validityInYears: 1
      validityExtensionPeriod : 9
      supportedAndroidAppVersion: 1080100
      supportedChannelList: ["ANDROID"]
    EKYC:
      metadata:
        IDFC:
          consentContext: EKYC-PAGE-CONSENT-V0
          kycType: PARTIAL
      validation:
        IDFC:
          nameMatchThreshold: 50
          nameValidationEnabled: true
          panValidationRequired: true
      validityInYears: 1
      validityExtensionPeriod : 0
    CKYC:
      metadata:
        IDFC:
          tncUrl:  https://www.flipkart.com/pages/pay-later-tnc-v1
          tollFreeNo: 1860 500 9900
          email: <EMAIL>
          disclaimer: Alternatively, you can submit your KYC documents at the nearest IDFC FIRST Bank for verification.
          reviewAndSubmitDisclaimer: I agree to (1) share the above details with Flipkart & it’s lending partners, (2) allow Flipkart’s lending partners to obtain my credit information from a credit rating agency, (3)
          reviewAndSubmitTncUrl: https://www.flipkart.com/pages/pay-later-tnc-v1
          reviewAndSubmitTncText: Terms & Conditions
          consentContext: KYC-PAGE-CONSENT-V0
          kycType: PARTIAL
          incorrectDetailsPopupHeading: Restart KYC
          incorrectDetailsPopupText: Do you want to restart KYC using another method
          incorrectDetailsPopupImageUrl: abcdefg
          incorrectDetailsPopupButtonText: Restart KYC
      validation:
        IDFC:
          nameMatchThreshold: 50
          nameValidationEnabled: true
          panValidationRequired: true
      validityInYears: 1
      validityExtensionPeriod : 9
      fallbackMethods : ["AADHAAR_XML_EKYC","EKYC"]

ffbProxyConfiguration:
  maxConnections: 1024
  maxConnectionsPerHost: 1024
  pooledConnectionIdleTimeoutInMS: 6000
  executorThreadsCount: 32
  executionIsolationSemaphoreMaxConcurrentRequests: 1024
  executionTimeoutInMS: 10000
  hystrixCommandKey: PinakaServiceHystrixCommand
  hystrixGroupKey: PinakaServiceHystrixGroup
  rewriteProtocal: http
  rewriteHostName: **********
  rewritePortNumber: 80
  enableProxy: false

orchestratorClientConfig:
  url: http://0.0.0.0:8080

hystrixModuleConfiguration:
  hystrixPropertiesFileName: hystrix.properties
  resourcesFilePath: src/main/resources/

multiTenantOAuthClientConfig:
  cachedAccessTokenTTL: 120
  oAuthUrl: http://***********:80
  oAuthServiceClientConfigMap:
    FK_CONSUMER_CREDIT:
      oAuthClientID: c2c95e0075c04b2e9b83e1bc8a09f57e
      oAuthClientSecret: FVMWGlJ22AoWfQ+ithjYn+M0tjr8xgakuHl06J5A+DELtVtc
      cachedAccessTokenTTL: 120
      oAuthUrl: http://***********:80

multiTenantConnektClientConfig:
  tenantConnektClientConfigMap:
    FK_CONSUMER_CREDIT:
      exchangeName: afford_collections_connekt
      callbackUrl: http://**********:7980/bnpl-collections/1/communications
      domain: flipkart
      emailUrl: http://***********
      pnUrl: http://***********
      smsUrl: http://***********
      emailApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      pnApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      smsApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      emailAppName: flipkart
      pnAppName: RetailApp
      smsAppName: flipkart
      emailBucket: alerts
      emailSubBucket: pay
      pnBucket: alerts
      pnSubBucket: pay
      smsBucket: alerts
      smsSubBucket: pay
      pnChannelId: fk_channel_order_payments
      transactionalEmail: true
      transactionalPN: true
      transactionalSMS: true

turboConfig:
  singleDbWriteEnabled: true
  multiDbWriteEnabled: false
  turboOutboundWithoutTrxEnabled: false
  sharding: true
  appDbType: "mysql"
  queue_shard_strategy:
    default_suffix: default
    clusters:
      queues:
        - advanz_onboarding_queue
        - afford_collections_connekt
        - fintech_cf_scheduler

  mysql:
    hibernate.c3p0.idle_test_period: "60000"
    hibernate.c3p0.max_size: "100"
    hibernate.c3p0.max_statements: "50"
    hibernate.c3p0.min_size: "20"
    hibernate.c3p0.timeout: "300"
    hibernate.connection.driver_class: com.mysql.jdbc.Driver
    hibernate.connection.password: "admin"
    hibernate.connection.url: "jdbc:h2:mem:/target/pinaka;TRACE_LEVEL_FILE=0;TRACE_LEVEL_SYSTEM_OUT=0"
    hibernate.connection.username: "root"
    hibernate.connection.autocommit: true
    hibernate.show_sql: "true"
    hibernate.connection.zeroDateTimeBehavior: convertToNull