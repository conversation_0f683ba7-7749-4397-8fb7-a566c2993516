{"account_id": "ACC7B6632470CA944CB9B5148167B50E642XXL", "insight_sets": [{"name": "jrm", "version": "v2_0", "features": [{"name": "purchase_v135", "value": "-1.921"}, {"name": "purchase_v179", "value": "1.05"}, {"name": "purchase_v256", "value": "6.715"}, {"name": "purchase_v337", "value": "-3.064"}, {"name": "payments_v123", "value": "-1.596"}, {"name": "purchase_v371", "value": "-1.088"}, {"name": "engagement_v145", "value": "-0.109"}, {"name": "engagement_v148", "value": "2.483"}, {"name": "payments_v45", "value": "1.386"}, {"name": "profile_v1", "value": "-0.154"}, {"name": "purchase_v223", "value": "-4.352"}, {"name": "purchase_v49", "value": "-1.099"}, {"name": "purchase_v149", "value": "18.968"}, {"name": "purchase_v348", "value": "27.067"}, {"name": "payments_v178", "value": "-1.698"}, {"name": "location_adddress_v161", "value": "-1.283"}, {"name": "payments_v80", "value": "8.242"}, {"name": "payments_v176", "value": "9.756"}, {"name": "engagement_v206", "value": "-0.547"}, {"name": "location_adddress_v74", "value": "5.111"}, {"name": "location_adddress_v75", "value": "5.603"}, {"name": "payments_v137", "value": "11.193"}, {"name": "engagement_v131", "value": "3.85"}, {"name": "payments_v77", "value": "8.686"}, {"name": "purchase_v231", "value": "-1.333"}, {"name": "purchase_v199", "value": "-4.12"}, {"name": "purchase_v4", "value": "16.066"}, {"name": "payments_v182", "value": "-0.938"}, {"name": "purchase_v313", "value": "-6.491"}, {"name": "purchase_v237", "value": "-1.368"}, {"name": "payments_v180", "value": "-0.959"}, {"name": "purchase_v50", "value": "-2.93"}, {"name": "purchase_v195", "value": "-5.841"}, {"name": "purchase_v230", "value": "0.652"}, {"name": "payments_v62", "value": "-0.316"}, {"name": "payments_v20", "value": "-0.279"}, {"name": "purchase_v306", "value": "-2.643"}, {"name": "purchase_v109", "value": "5.256"}, {"name": "location_adddress_v24", "value": "-0.433"}, {"name": "purchase_v23", "value": "-16.018"}, {"name": "purchase_v286", "value": "-1.491"}, {"name": "purchase_v22", "value": "-2.2"}, {"name": "purchase_v245", "value": "-1.954"}, {"name": "purchase_v24", "value": "-1.323"}, {"name": "purchase_v366", "value": "3.609"}, {"name": "payments_v191", "value": "-3.797"}, {"name": "engagement_v51", "value": "2.644"}, {"name": "location_adddress_v105", "value": "13.251"}, {"name": "engagement_v14", "value": "23.758"}, {"name": "location_adddress_v182", "value": "5.93"}, {"name": "purchase_v284", "value": "0.538"}, {"name": "payments_v61", "value": "-0.573"}, {"name": "engagement_v59", "value": "0.146"}, {"name": "purchase_v20", "value": "-2.547"}, {"name": "payments_v54", "value": "-0.636"}, {"name": "engagement_v197", "value": "-1.276"}, {"name": "engagement_v150", "value": "-1.093"}, {"name": "location_adddress_v56", "value": "1.656"}, {"name": "post_purchase_v9", "value": "0.511"}, {"name": "post_purchase_v7", "value": "23.927"}], "scores": [{"name": "SCORE", "value": "302.099"}, {"name": "BIN", "value": "18"}]}, {"name": "address_confidence", "version": "v1_0", "features": [], "scores": []}, {"name": "jrm", "version": "v1_0", "features": [{"name": "order_window_24m__order_count_num_ratio", "value": "0.696969696969697"}, {"name": "order_window_lt__rto_count_num_ratio", "value": "0.0"}, {"name": "payment_modes_12m__credit_card_emi_num_ratio", "value": "0.6666666666666666"}, {"name": "visit_count__web_num", "value": "-99999"}, {"name": "payment_modes_12m__wallet", "value": "0"}, {"name": "payment_modes_lt__credit_card__count_unique_num", "value": "0"}, {"name": "payment_modes__cod_num_ratio", "value": "0.045454545454545456"}, {"name": "anlt_bu__life_style__order_count_num_norm_ratio", "value": "0.1"}, {"name": "payment_modes_12m__credit_card_num_ratio", "value": "0.3333333333333333"}], "scores": [{"name": "visit_count__web_num_SUBSCORE", "value": "47.2"}, {"name": "SCORE", "value": "537.2"}, {"name": "payment_modes_12m_wallet_SUBSCORE", "value": "49.5"}, {"name": "payment_modes_12m__credit_card_SUBSCORE", "value": "76.6"}, {"name": "anlt_bu__life_style_SUBSCORE", "value": "53.1"}, {"name": "order_window_lt__rto_SUBSCORE", "value": "52.6"}, {"name": "BIN", "value": "18"}, {"name": "order_window_24m__order_SUBSCORE", "value": "47.7"}, {"name": "payment_modes__cod_SUBSCORE", "value": "56.0"}, {"name": "payment_modes_lt__credit_card_SUBSCORE", "value": "43.9"}, {"name": "payment_modes_12m__credit_card_emi_SUBSCORE", "value": "61.7"}, {"name": "city__metro__order_SUBSCORE", "value": "48.9"}]}, {"name": "jrm", "version": "v3_3", "features": [{"name": "WOE_payment_mode_12m__fk_finance_num_ratio", "value": "-0.07734"}, {"name": "WOE_bnpl_dpd5_ever_new", "value": "0.248663"}, {"name": "WOE_account_creation_date_vintage", "value": "-0.26029"}, {"name": "WOE_12m_order_completion_rate", "value": "0.207866"}, {"name": "WOE_order_level_window_12m_max_device_duration", "value": "0.209654"}, {"name": "WOE_order_window_12m__rto_count", "value": "0.186063"}, {"name": "most_preferred_category_count", "value": "bgm"}, {"name": "WOE_city__tier_3_and_others__12m_order_count_to_total_ratio", "value": "-0.02236"}, {"name": "WOE_months_since_bnpl_acquisition", "value": "0.161458"}, {"name": "WOE_payment_failure_12m_failed_txn_value_ratio", "value": "-0.*********"}, {"name": "order_window_12m__order_count", "value": "9"}, {"name": "order_window_12m__order_value", "value": "46654.0"}, {"name": "WOE_order_window_12m_total_adjustment_amount", "value": "-0.216114"}, {"name": "WOE_Saving_payment_modes_amount", "value": "0.459095"}, {"name": "WOE_Carded_Non_Carded", "value": "0.*********"}, {"name": "WOE_payment_mode_12m__cod_num_ratio", "value": "0.548427"}], "scores": [{"name": "SCORE", "value": "362.701"}, {"name": "EQUAL_BIN", "value": "19"}, {"name": "BIN", "value": "15"}]}, {"name": "li", "version": "v6_1", "features": [{"name": "most_used_address_id", "value": "CNTCT22F9842623BA4E089ECB831CE"}], "scores": []}, {"name": "fpg_sm_insights", "version": "v1_0", "features": [{"name": "most_used_address_id_sm_nonaac", "value": "CNTCTAFE4668410094A3594244876A"}, {"name": "most_used_address_pincode", "value": "600081"}], "scores": []}], "cohorts": [{"name": "cremo", "version": "v1_1", "cohort_segment": "A", "matched_expression": "rest"}, {"name": "int_seg", "version": "v1_1", "cohort_segment": "int_seg_1", "matched_expression": "rest"}, {"name": "idfc_int_seg", "version": "v1_1", "cohort_segment": "1", "matched_expression": "rest"}], "suggested_data_points": ["li:v6_0:20240801"], "consumed_data_points": []}