{"application_data": {"code": null, "expiry": null, "namePage": {"lastName": "n1VQgy1bGlz7LCwGm10CuQ==", "firstName": "kFuiInuxRazYRSdDN4FxsQ==", "consentData": {"userIP": "************, ************,*************", "deviceId": "RV-cm3xwvp18zwoy0l2gq4mtym8e1732592567421", "consentId": "001", "consentFor": "BUREAU_PULL", "deviceInfo": "Mozilla/5.0 (Linux; Android 13; motorola edge 20 fusion Build/T2RK33.146-14-1) FKUA/Retail/2190400/Android/Mobile (motorola/motorola edge 20 fusion/68cab1a3fd471054ba36dc5ca6e6e36a)", "consentType": "CHECKBOX", "currentTimeStamp": 1742367665753}, "phoneNumber": "mnpHglE6/pz3c4N8/S/WEg==", "consentListData": []}, "sm_user_id": "SMA08A1F510A1164EE8AE5D6D23060B2E52", "reviewPage1": {"dob": "fmU1CORGU+0FuYnBxCqKlw==", "area": "NwgEtetZP9sgcPHM0kcxcQ==", "email": "ld1pLH47WEQ5ZDajvHVRaJnoD8s0YuQUfro23Mqr2kk=", "gender": "M", "panNumber": "AQSHQE+6JW/yyLSY0z9xRg==", "houseNumber": "tlo8oiz6EoUlBNwvP8lu4Q==", "loanPurpose": "PERSONAL", "employmentType": "Salaried", "pincodeDetails": {"city": "Bangalore", "state": "KARNATAKA", "pincode": "560109"}, "consentListData": []}, "form_details": null, "product_type": "LEAD", "pullExperian": {"statusCode": "OK", "errorString": null}, "user_details": {"dob": null, "pan": null, "area": null, "city": null, "state": null, "gender": null, "pincode": null, "segment": null, "bin_score": "366.443", "subsegment": null, "employer_id": null, "industry_id": null, "house_number": null, "employer_name": null, "industry_name": null, "monthly_income": null, "employment_type": null, "annual_turn_over": null, "shipping_address_id": "CNTCTBE2A8117B7E444D39EE33A83A", "pan_consent_provided": false, "offer_consent_provided": false}, "error_message": null, "lender_details": null, "monthly_income": null, "journey_context": "LEAD_APPLICATION_JOURNEY", "external_user_id": "ACC00E8706DB7F646CEB12B2163EDBC78D0O", "financial_provider": null, "consent_details_list": null, "application_created_at": "2025-03-19 12:30:57"}, "sub_applications": {"sub_application_info_map": {}}, "external_user_id": "ACC00E8706DB7F646CEB12B2163EDBC78D0O", "sm_user_id": "SMA08A1F510A1164EE8AE5D6D23060B2E52", "application_type": "LEAD", "application_id": "APP2503191230574988641264236989793625533", "parent_application_id": null, "tenant": "CALM", "pending_task": [{"process_instance_id": "3AVJKE8lIzoI6oEB", "task_id": "WZTr-hWfs1uw86cG", "task_key": "reviewPage2", "form_key": null}], "application_state": "REVIEW_PAGE_2", "merchant_id": "FLIPKART", "partner_state": null, "partner_sub_state": null, "created_at": 1742367657000, "updated_at": 1742367675000}