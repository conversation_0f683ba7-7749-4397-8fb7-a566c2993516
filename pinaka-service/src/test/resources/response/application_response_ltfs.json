{"application_data": {"email": null, "expiry": null, "lender": "LTFS", "userId": "SMAD2C9B1BF36094AD48A518A3AC8E5BA28", "panPage": {"dob": "Lzr9vxFXhux5W/l0ZJIVbQ==", "Gender": "M"}, "getOffer": {"ciiApp": false, "errors": null, "status": "SUCCESS", "message": null, "breResult": null, "subStageC": "OFFER_AVAILABLE", "applicants": null, "statusCode": "200", "nextActions": null, "deDupeStatus": null, "rejectReason": null, "applicationId": null, "breHLResponse": null, "breMaxTenureC": 48, "breMinTenureC": 12, "lenderMessage": null, "applicationIdC": "APP2506131444404163194432969227671478437", "breMfiResponse": null, "breFARMResponse": null, "breTWBCResponse": null, "breTWSCResponse": null, "genesisAccountC": null, "offerGeneration": *************, "onlyNoHypoPromo": false, "applicationStageC": "SANCTION_GENERATED", "perfiosNotAllowed": false, "breRejectionReasonC": null, "reasonForRejectionC": null, "genesisInterestRateC": 18.0, "breMaxLoanEligibilityC": 400000}, "namePage": {"email": "ZGA6dXGzxZh3ObnWnsBYsuVGtK+/xCpJaw5rLbj4/io=", "lastName": "n9uIdMt4mAtq8bZoQRjqnA==", "firstName": "aYNTWdeLK0hzE7GI3Uz59g=="}, "accountId": "SMAD2C9B1BF36094AD48A518A3AC8E5BA28", "getStatus": {"crn": null, "status": "COMPLETED", "message": null, "statusCode": null, "referenceId": null, "rejectReason": null, "responseType": null, "initialStatus": null, "lenderMessage": null, "statusTimestamp": *************, "loanApplicationNo": null, "applicationDetails": null}, "selectEmi": {"bankOffer": {"emi": {"amount": "32211", "endDate": "2026-07-13", "startDate": "2025-06-13"}, "roi": "18", "tenure": {"unit": "MONTH", "value": "12"}, "charges": {"PROCESSING_FEE": {"gst": "632", "amount": "3513", "totalAmount": "4145"}}, "loanAmount": "351333", "netDisbursalAmount": "347188"}}, "digilocker": {"fetchXmlStatus": true}, "reviewPage": {"": ",", "dob": "Lzr9vxFXhux5W/l0ZJIVbQ==", "city": "EGFFn/XylQP1FUsVuMqW0Q==", "email": "ZGA6dXGzxZh3ObnWnsBYsuVGtK+/xCpJaw5rLbj4/io=", "state": "whLQxN+9n03G20PU1w0x5Q==", "gender": "M", "pincode": "638454", "lastName": "n9uIdMt4mAtq8bZoQRjqnA==", "firstName": "aYNTWdeLK0hzE7GI3Uz59g==", "addressLineOne": "FFzRfooeE5kHB73tsYhzxA==", "addressLineTwo": "w3x/y7ky0wqWaXapmyF507TG7pm4+k1Dh+KgCByRDFc=", "employmentType": "SALARIED", "pincodeDetails": {"city": "Bengaluru", "state": "Karnataka", "pincode": "400052"}}, "cremo_model": {"version": "1", "cremoBand": "1", "cremoScore": "1", "modelVersion": "1", "idfcCremoBand": "1", "ltfsCremoBand": "99", "losV2CremoBand": "5"}, "landingPage": {"haha": "hehe", "consentData": {"userIP": "************, **************,*************", "deviceId": "rvclstxs3545c2g070neo4kewh31708407608298", "consentId": "001", "consentFor": "BUREAU_PULL", "deviceInfo": "Mozilla/5.0 (Linux; Android 14; RMX3741 Build/UKQ1.230924.001) FKUA/Retail/2220100/Android/Mobile (realme/RMX3741/f6f5d6fbeb293fd0afc44888292eab47)", "consentType": "CHECKBOX", "currentTimeStamp": 1745815452353}}, "merchant_id": "FLIPKART", "phoneNumber": "9599220024", "productType": "CREDIT_LINE", "userDetails": null, "aadharScreen": {"consentData": {"userIP": "************, *************,**********,************", "deviceId": "clq3eaffz0wo80pdhywr26hk2-***************", "consentId": "", "consentFor": "", "deviceInfo": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.1/msite/Mobile", "consentType": "CI_CONSENT", "currentTimeStamp": 1749808659033}}, "errorMessage": null, "getStatusKyc": {"crn": null, "status": "SANCTION_GENERATED", "message": null, "statusCode": "200", "referenceId": "4e5034d8-0b1e-44e7-b105-39c27464b824", "rejectReason": null, "responseType": null, "initialStatus": null, "lenderMessage": null, "statusTimestamp": null, "loanApplicationNo": null, "applicationDetails": {"status": null, "message": null, "dataRequired": null, "lenderLoanId": "", "taskStatuses": {"kyc": "success", "rps": "pending", "dedupe": "success", "mandate": "pending", "esigning": "pending", "disbursement": "pending", "document_generation": "pending", "bank_account_validation": "pending", "bank_statement_verification": "pending"}, "lenderApplicationId": ""}}, "product_type": "CREDIT_LINE", "pullExperian": {"statusCode": "INTERNAL_SERVER_ERROR", "errorString": null}, "redirect_url": "https://google.com", "selfieScreen": {"documentId": "fk-p-calm-selfie_image-docs/APP2506131444404163194432969227671478437", "documentMeta": {"status": "auto_approved", "documentType": "SELFIE_IMAGE", "livenessScore": 99, "transactionId": "APP2506131444404163194432969227671478437"}}, "uploadAadhar": {"status": "INITIATED", "statusCode": "200"}, "uploadSelfie": {"status": "SUCCESS", "statusCode": "200"}, "monthlyIncome": null, "addressDetails": {"line1": "FFzRfooeE5kHB73tsYhzxA==", "line2": "w3x/y7ky0wqWaXapmyF507TG7pm4+k1Dh+KgCByRDFc=", "pincodeDetails": {"city": "<PERSON><PERSON><PERSON>", "state": "TAMIL NADU", "pincode": "638454"}}, "externalUserId": null, "getStatusOffer": {"crn": null, "status": "SANCTION_GENERATED", "message": null, "statusCode": "200", "referenceId": "4e5034d8-0b1e-44e7-b105-39c27464b824", "rejectReason": null, "responseType": null, "initialStatus": null, "lenderMessage": null, "statusTimestamp": null, "loanApplicationNo": null, "applicationDetails": {"status": null, "message": null, "dataRequired": null, "lenderLoanId": "", "taskStatuses": {"kyc": "pending", "rps": "pending", "dedupe": "success", "mandate": "pending", "esigning": "pending", "disbursement": "pending", "document_generation": "pending", "bank_account_validation": "pending", "bank_statement_verification": "pending"}, "lenderApplicationId": ""}}, "readyForSelfie": {"consentData": {"userIP": "************, *************,**********,************", "deviceId": "clq3eaffz0wo80pdhywr26hk2-***************", "consentId": "", "consentFor": "", "deviceInfo": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.1/msite/Mobile", "consentType": "CI_CONSENT", "currentTimeStamp": *************}}, "subsequentLoan": true, "fetchDigilocker": {"result": {"url": "https://api.digitallocker.gov.in/public/oauth2/1/authorize?client_id=D374178C&state=SMAD2C9B1BF36094AD48A518A3AC8E5BA21,https://google.com/,mbumwbk1,sv1tsh,no,,,SMAD2C9B1BF36094AD48A518A3AC8E5BA21,no&response_type=code&dl_flow=signup"}, "status": "success", "statusCode": "200"}, "external_user_id": "ACC14163975336595710", "createApplication": {"status": "SUCCESS", "message": "The application creation has started", "statusCode": "200"}, "financialProvider": "LTFS", "getMandate": {"nachId": "NAC78055349854", "redirectUrl": null, "mandateMethods": ["UPI", "NETBANKING", "DEBIT_CARD"], "bankLiveDataList": null, "nachStatus": "COMPLETED", "errorMessage": null, "mandateDetails": {"encryptedBankAccount": "+FN2bV7Z+K5mK7d7gAx7Xg==", "bankName": "HDFC Bank", "bankIfsc": "HDFC0000123", "umrnNo": "UMPS250603162607990M37HXP3HO@ybl"}}, "consentDetailsList": null, "fetchDigilockerXml": {"error": null, "result": {"dob": "14-04-1998", "name": "<PERSON><PERSON><PERSON>", "photo": "/9j/4AAQSkZJRgABAgAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCADIAKADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDthTwKQU/AxWBaGHgVVmPU1aciqc7fKTQNFORuTQn3gKYxBPNU9R1ODSdPlvJzwo+Vc43HsB9aCjenu4LG1M1xKkUSDLM5wBXO3fxF0iDAgEk7YJOFwAfTnv8A4V5bqusXmr3AuL642qP9XDGfljHsPX36+/SoYtqFXwd20jazdPc9h3q1HuQ5Hbal8ULll22VnCgI5LsSenOOR+f6VUs/ipfxZF1aQynJxtJT8O/H+ea4iWfDlGAIzxh8iomjRvmU7WHOMcGnZE3Z6xpnxRtZZRHf2jQhujq+8L9ehxj0B6e/HW2etadrELmyuUl2H5gOCP8AP+e9fPYkzH90EdKsWt9LaSpNbSvHKhyCrdP880OI+ZnvqnD4q+p3KDXlOhfEJ4wkOqfvQOPOQYYD3Hf6jH416RpOqWeqW3m2dxHMgxnaeVz6jqPxqGrF3TNIUxkOeKkUZpSKAICmKNtSkZFJjrTQEJFRutWduajZeKZNxfrRuOKZmjdgGoGMkfrVG5k+U81NK/Wsu+uUhgeSRgqKMkk8Cgor3moW9lbvNcSqiL1JNeUeKPEh1i+zFkQJwg6fU/Wk8V+ITq14I0QLDESF9W965rODk8c1cY9SGy1521NxJyOAAamuLpnjRUJCsOQP5VQ3qzDPTFa1jamUrtGR69qbdhKNyiEAOcMD65qUyEbT/EBwfWtt9KLqOPxqpJpUkWSRuX2qFVRbpsyw5DZTgnqKk81hjIH4VM6IuAsRLVesNIkuFaQqVXpVOaSuyVBvYzFYs4ZWw3Y+tX7PUp9OvILu3by5onDjb90keo/E8VTuoHsrlkYc5yKgMuWz1FUmmhNNOx7z4Q8ZR+IkaGeFLe6QDAD5EvHJXPQ8E4549ecdZ3r5+8GaudP8Q2rEuIzIA4VsZzx2+v8AkV9AIdwB4zUNWZSAimkVJg+lIV5pAMxxTGFTYzTWXirJZS3YpjNwaZvzTWcYrIsglbtXL+J3lOmTpCMsUOc9APXNdLKw5rndc3Npl2EPzGJsdu1AzxR8q5yeaYq7yPellP75gafa8uFIGM5rYgu2mmB3Bk6egrpbOKOIBQvHrWbajpWzax5I61zzdzeCSNCGNMcAYqf7PG/VRSRRYAKnmpwrdefesGbFY6fas2WgVj7irHlqiYAAA7YqQKfTNDRsRzSCxg6ppkV2M4O7+8K5u9057XkDcvriu2mQ85HFY2qRFoGI7c1vTk0ZTgmrnL27mOUFSQRyMHFfRfha8fUPDlhcykmVogHJGMkcZ/HrXzqE2MpNe9fDyRpPBtjuIyvmKB6AO2M/59K6JHOjqgOKCtPzxR3qQYwLzzSMvFS0jVRJzXmUM/FVw3OKVnrI0RHM2FNZF0BKrI4DKwwQe9aM7/Kay5WJakUeReIrMWOt3EK9AQcY6ZAP9ap2YJuAPxrvfGfh9rq0Op24zJCuZV9V9R9P5fSuHtFKrJJ3AwK1T0ItqayXsVs4DDcR19quf8JHDGMLGxPtXP8AlIF3SP1q/aW0L3CW62lxJK6lgI03MQASTjI4wM/Tmo5U+hXOzetvE0TMNyY9K1l1qNkyqFs84zXK3OkPaLG7QyxCVdyCRdpI59yM8fd6juKm02YmUROM+lRKMeiNac29zYm1uUsFjjK81FceJ3t5yCgcEcj0q/cQxCEHbzj0rEk0qa6ulhjRMnqW6CphyvoObfQvjxNBcIVMIVv97IqMXVtextGSAx4KHv8ASsnTIpLmaNYok3u5jRfMAYnGckAEgdBk4BJ4PBxdhWN5GgltzHKp5yMEH3q3BR1sZqbZh3UDWlxLC3ODkH2Ne5+Arf7N4SsueZQZSPTP/wBbFeTaxa/aVtZVIRm/ds75wDnjOMnHWvaNBvbO5slis9wS3UIFZMYAyAR2wcf/AKq0vdGbVmboPSnAdzTE5FSAHPFBLAdcU4r8tLsxinY4pok4dwVaozJ1qxMPlNUJGwahlxGzvlTVAnL9anlfIIqqG+eoNDjfFl5PHqyqHbaijYVJBQ4zkfnXPW8YkicccvngYrqvGlvskt7gAfOpGAOcj/8AWPyrlbF8Bs9d2aqL0KsrJkz2DDbIq7iOx5qdoftjReZEysgxuDYJFaFpIGGDV9IFYZUc1HO0NQuVrmd5rSCKc7o7dQkKdlwMD6n3PNVNOixdCRvWtCe2AUknNVbUFpdwHyjge9LmuWo2Z0rKJbfB44rOe2beCGKOOjCtOKJ2gBwcY9KbHHmbY2eehNZp2ehckmZtvaPBM08MUS3DZzLjLDPcZ6Hk8ipUtWVmeQ73JySetaZtdre1K8aoM45x603NsnksZrW6SvbrIQsaTqzfy/rXW+FohH4guivCvBnj2ZcfpXJ3LcKqjJd1AA+ortPCiY+0yEZ4RA/0ByP5Vau2iG0os7OM5FWBgVTgbI61ZVhXQkcrZPjjNOQbs03I2VLEP3bH0IqrE3OGlyUzWTMcNWs2DHWPcja5FZtFx3KsrcVCnzPTpTz1pkR+YCoNRNd006locsaLmWP94gHcjqPyz+OK8zlj8jD4+bOGwOMV7LbdBXL6n4Ee6uz9juo4baViXDqS0f8AugdR7ZGPemgT6HHW021hzW3BcYXBrm2SSzuZLeYbZYnKOB6g4NXYrnbHuqZxLhI2ZyJYnXdgsuM1g3F3PbOsY27geoPBqKTU5Zsqh2rnGaIYVmwsknJNNQtuDnfY6fT9cujAFcmNMdS3FFprjSXjRStGyg5VlBFUItLtZYyklxKoHJA5yfr2pkmnQW7sVmO3HGTS5UO8jrxdKyg5qvcTDHpXKWeoSxXCoXLx5IJHNbM8xEfXNTyWYc6aJbUfaNUjXn92rMB6kjb/AOzfpXomjwi0s44gMHGT9TXK+DVzHfXAY4eRYgD/ALIJz/4/+ldbC2D15rVRs7mEpXVjXt2w3Wri9eKy4X5BFaCNkCtEZMuKeKnhPyMPWqiHpzU0bbTVCOMGCDn0rIu+pra3KIEkUDdgg/0NYt1zms2XHcypHyaSInzBzTZMhzSxffFZs2Ny0OcZq4rYFUbY4xVkNwKpEnAePNFfzv7XtwSpwsygdCOjf0/L1rj45w67W6HtXq+varbaTpU11cp5q/cWL/nox/h9Mdc+wPXpXj08u9jcIiokhLbF6Lz0HtVJXQXsaESx+V91T6ZFWLaa2Q4kjUD3GRWZbXK4wTWpDHbToOMmpehcZW1RuQzaOVDb4lYDvJwDT5rqx+7borf7vI/OsmPT7MHeRyOgNXA9tEoO0Djmpv2NJVJNaiPGjMHIAYU9nkuporaHDSyMFUe5NZ93qEYGI256cVpaQ76G8erXMfmOjD92eyscH6NgmqjHuYuXY9FsbJNM06C0TBEaYJH8TdWP4nJ/GrkT4NQi4iureO4gffFIoZGwRkEZHB6UkZwap7mZqxNyOcVoxP8AICetY8T9BWjC2U57VRLL6SZFWA+RVENjvUiSEE07ks50vixA+UHn6/561jy4INWy2IySeaoSHis2XEz51+ao4h8/41NKeaZEBuqDU1bY/L+FTlgF+lZEmsWGnr/pV1HGePl6tz/sjmuc1Xx0xjZNPtyuRjzJeT36KPwwST9K0jFvYhuxR+IF4Lm4SzT/AJd1LNgn7zYOCPoB+ZrkbZGltQAM4Jq5cXM15O81wxklkySx9agscplT61pONohB3ZUeBlbIBBpUnniPyk1u+WsgyQKj+yYOdmfoKxU+5o4lEX903Cgk+wqxFHqd2eF2qe7cVpW0IBBA5+lacKkngY/Ck52FyNkGl6QlqRLIfMlHRiMBfp/jVjXcro0yoOTt/Rgf6VdU7Fx3rH1+4xYug71MZNyuW4pKxpeHfFyadpq2t1BJJFvYiRCMpnHAXjvk5z3rorbxZpciK0kksDMcBJYyT9crkAfjXmkBHlNHnK4z6c0eYNhBz7Gu501LU5FJrQ9ptL62ugTbXEUwU4YxuG2n0OK2IZAUHpXgNrcSxTK0UhjdTlXU4IPsa3rLxdrFgEUXrSxocmOcCTf7Fj8361HsX0G2e2BiYwScnFOR8dTXnWmfEmFo1XUbN1kwAXtyCpPc7SRgfiTXWadr+m6qdtlexSvk4jJKvx1O04OPfGKhxktxGTK4CAZqjNMqqSSABzmsDVfF1vBmO2H2hxxuzhR/j/nmuV1DWLu/+aWR9mfuLwo/z70Km5FXsdLf+I7aFykINw467T8o/H/CsC68Q6hcDaknkj0i4P59aygc4C/eNNyQ+1+PXFaRpxQnJsGzu3KTuHNQyMSVye9SFepXO0VE7fMg9KsROCRIrMM+mackR81iB1NG7cQWycdKntmC3C7h8rDFKpG8RwfvEsD4O1qvIvGRTmsVkAdOGqzbWjtwRXnykjsSY2IgelW0kHc0v9nnGcikFsFPWoui0hskpPCn8aw9cJ+z49WArddVjUnA46knpXL6jci5lyozHGeffPf9P1rSkm5aEVNFqNt2KOADglSCPamnduYIW2ngii3O+XzDheMgU/Bbdg8scYFenbQ4B8IHl8Y+vepnQJwxDAjOfSoARArK33vr0NR72ZgflOe1MXUl8zaQyEdackmdyscA9sZBpiRoFO5sEDNLvyQWJIHFA7lclSpOfm7VGJcY3A49KkMf7sPuHXGKazl0VCOB3qQuG8M52KQO1Csu0jGf6UjRmJhg578UwICCckED86AJHRkQEEEHsDUEjAup2gYoO/jqQKUshYfKaQxz3JDfcLfjimxX2ZhHJHtJ4BzmglcnAFRvbGRN4IG09QeaLsEdfY3sACRTtsZlBVyPlbjnn2OR+FbyQBB0rzp7q6WFYoiiADD43fvD6kEkA/QCtKx8UXNpD5csRdUGAo5B6AAZ5X+Ik/MOQAorjqYe+sTqp1+kjsZMgcdKrkdycCks9VtL+MmKVSR95c/7vPODjLAZIGTWdrd/5KfZ4T+8YfMR/CK5lTk5ctjo548vMZ2raoZ2e3hP7pTg/wC0f8Kx4IpJlnaMEqEG89sbh/XFJNIXO1RjjHFOVgoVV6DjNelGmox5UcE6jk7smYFlAGVCDnbxSNOgCCMHeOpzTPLcMwVyQepFSRxxLEfl+fsa2MRgVzJ84BGPWp449qFwR8vahMKRvBwexoPLEqDtH6U0guOdjNJxgE/rTGkIj2HGAc0MVIXYpDd/eonbYGVl5PrSYDCCuNwIz3p77XKiNOcc470zklVYkD+VOYBH+U596Q2CkLINy8dwaULvJKrwKFPmuS7YPvTA2M4PtQA8EbCnGKRoQFVuOfSlymzqd3pQvy4yPwNAEfkrJJ8xxnvUZjKMVDErmrZAkf8Adr+FIrbGOVB7YIosFyuVwmQfm/nTTkkF1P1FWhGTGXGMA4pHYvEEPaiwXIATDIskTYccBh7jB61beeVnd34Z+rY5H+FQRoY5Cz4Ycrj8Of51LjznA4XgKAewHAFCS3BvoRLGxQkKcdzUuVMaoFAYd6AzxqyevBpTGBEHB5zgjFMQ4F7aQEjnHT1pFQuGYFcjnHSmghnG8nHTigj5mCEkfzpgKXZwqk8CnHdBIVDAnGOKTKeV93D/AM6F+R1Lrx6EUAJtHll93IPSog3mTb5MsOn4U5/m3FBx6elKhJRQccUhkalrk8AZIoRgjkOufaqlrMwHBwwq2iB1Zt2CPWkgYhBOWUHH8qA+EK4BJ70B2VSAcZFLsAjDBju9KYxWjMYUnBB6Yp5ZpWUEgcYFQ5yfmJwKlbaGwhJFAhcmOTAPI70qgPuLNgjmmxld/wA4yDTWHJIBxQIXeduM8U8hBGrKx3HqKZ5iGEqVG7PBprBggJU47GgBYz13EkbjU0mwMDGSOM/Q1BHMrq2EAO7+gqch4yrFcZ6ZpoGJEY8MJAenBBpvO3ODjpmnhTPKdgAzzjNIJGVGQdD1oAUkSBVVQG74704eZbSYIIOKPLAhDhgcnGPShSHkBkJx60CGpGXRmBHHvTWcsFVjwO9PfAZghOP50xthi6YfPWgYxxhwqtn3p6lfKxj5geDSIHg2uQQW6fSnBTIGYFc9SKAZjRuEmBI4NXY3BFFFZx3KJiU8rGPnzQFIAPPPrRRVksdKQ+3auDjnFMUmOQFh06g0UUgHDMsrbR+AqRJGjLDoTxRRTAaLYujOrDjtnmmZcx7M8UUULcBkUZVXYEMCRx+FTeY0m1S2FH6UUUIGLyj/ACnt1p8YjKvvJBxxRRVANBOOBwDinSMG2BVAPQ470UUCHIzwv8w9iD3piRmXdjBx70UUr9QGs24AHoOlI/yZAbjFFFAH/9k=", "gender": "M", "address": " S/O <PERSON>,, India, Nellore, 1-1, Polampadu Post, Kaligiri Mandal, 524225, Peddakondur, Andhra Pradesh, Polampadu Village, Kaligiri, Pedakonduru", "aadhaarFile": "https://kyc-hyperverge-co.s3.ap-south-1.amazonaws.com/validation/2025-06-13/sv1tsh/1749808769797-f25a1bae-5c01-4186-aa6b-8b5183c5a187aadhaar.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAXB3KY4F5HT5QQQFD%2F20250613%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250613T095931Z&X-Amz-Expires=900&X-Amz-Security-Token=IQoJb3JpZ2luX2VjECgaCmFwLXNvdXRoLTEiSDBGAiEAgmCiJKnvB%2BUmOsoicXqnudZ4zvFS7UpTBug2th13MeUCIQCKjduDwPq8GUEBKz5KligUcJvX7A5S1GI0ThmDr2wjzSq%2FBQgREAAaDDQ4NTAxODM2MjIzNCIMoIMZAHhcbfvVlB2RKpwFGEstDMQQVWxZ%2BE1h3gfclL2hO27mVyXt4ZNBifKvSblHyI6gWmzdf6%2FQs1SdtrTpcLqtytBozynsUI1wmlR6cljp6c6L9ZlcPxph%2BmlxD3XM%2B97uTgwVFHMN7yiQKIQFuYdU9q6K1NobtjU%2B9ERpAD8l%2BtmYsDz%2Bg9%2Fid0ZN35m%2FviuLgjSAk7LhR4gqMi22w6lr9t4fP7a5R3AKQt4GNdDx8mmxzK5wJKHqufSdb2dTjMlirdXs6O93ko2tgQ6PODCL1u7NZFPGZI7Y97nI%2B5YBF9GtyAYG8Ku8tYmGxn%2BWE4yF3JIleP1x6HN8n1jw%2FyTk4an83dbT4AOw%2FJYuHe88vf4%2B%2FDMwbI5VDG52u8JRcBZYubQLKts15Uh1I8immAYiToyLlU%2B4bPOnXU6OkhTg7Dit6JGhADogjZHisPfvZ2%2BJLDGTWuFUEFKd%2FY9t5T6WOU%2B4cmcGPPNpC4A%2BrZ1DWxZ4L2YF4BcMC%2FkgDajxkT%2B9QAJo3q9pjrFYQCh4871%2BpJZFoVt4LFGTNOGuAaKYNGnrzyc2Xk%2B%2BTLn50dKGYoFTJNa%2BLZDkJJb8t1dTRLvla4VqXTyaX58AVy%2BOo5GMRLO8faRdQwh7%2FwHl%2BCpLKHiw9fNh1KzbAEVFbFW8ypBIl%2B4GcrcTdY8KRZrMHQRCXX5%2Bf%2FPK%2BUbVtVssNc%2BkrEKTG2peLuLPyUZkGH77F7wvph34hzFzoAHwpoTsfYXT6CC2zi9gTCnSHy6BN1UZ7j7G7GpE%2FaJvYoPvLNcYvGK8Fe%2BVNPSv6VeuMY6apr9nAb%2F8FshznvnipP78FIc%2FPkPxqA9ZlJeXpzewMU%2BeCJoK%2FKOmOwDtlzHXrbeUqtZNDkNROsA5zfkM5U%2B5GVF1icILDn9lpd8P9ukwx7mvwgY6sAHDOXcB3aSTKq5W2vEyrhF1sZdsz4Ct49GCK%2FafSs5BHiWfEFoIYUSJ5xxr3TM2Q7q1CyaTtlhMCjkSGVjs180r6PkcTzj7ycgWF2mSEr83kgCbzeIYSfQjX6zENeXiqmIr8Xs%2Borui8KcH8i4ZWyeAKWGvV%2Ff5nPOcSeQlVmIqbr93%2FmZb8sMAdC5iDBEHaYvkZshS2Tq7miS5VlYv7vbNM4%2FQWuNgGP7F4bY5K6gPGQ%3D%3D&X-Amz-Signature=0c7898f752048b523c7d26565719a280d2466ddc45a4ea59389f883501e8e2cd&X-Amz-SignedHeaders=host", "addressFields": {"co": "<PERSON>/O <PERSON><PERSON>", "vtc": "Pedakonduru", "house": "1-1", "state": "Andhra Pradesh", "street": "Polampadu Village", "country": "India", "pincode": "524225", "district": "<PERSON><PERSON>", "landmark": "Polampadu Post", "locality": "Kaligiri Mandal", "postOffice": "Peddakondur", "subDistrict": "Kaligiri"}, "xmlAadhaarFile": "https://kyc-hyperverge-co.s3.ap-south-1.amazonaws.com/validation/2025-06-13/sv1tsh/1749808769797-f25a1bae-5c01-4186-aa6b-8b5183c5a187aadhaarxml.xml?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAXB3KY4F5HT5QQQFD%2F20250613%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250613T095931Z&X-Amz-Expires=900&X-Amz-Security-Token=IQoJb3JpZ2luX2VjECgaCmFwLXNvdXRoLTEiSDBGAiEAgmCiJKnvB%2BUmOsoicXqnudZ4zvFS7UpTBug2th13MeUCIQCKjduDwPq8GUEBKz5KligUcJvX7A5S1GI0ThmDr2wjzSq%2FBQgREAAaDDQ4NTAxODM2MjIzNCIMoIMZAHhcbfvVlB2RKpwFGEstDMQQVWxZ%2BE1h3gfclL2hO27mVyXt4ZNBifKvSblHyI6gWmzdf6%2FQs1SdtrTpcLqtytBozynsUI1wmlR6cljp6c6L9ZlcPxph%2BmlxD3XM%2B97uTgwVFHMN7yiQKIQFuYdU9q6K1NobtjU%2B9ERpAD8l%2BtmYsDz%2Bg9%2Fid0ZN35m%2FviuLgjSAk7LhR4gqMi22w6lr9t4fP7a5R3AKQt4GNdDx8mmxzK5wJKHqufSdb2dTjMlirdXs6O93ko2tgQ6PODCL1u7NZFPGZI7Y97nI%2B5YBF9GtyAYG8Ku8tYmGxn%2BWE4yF3JIleP1x6HN8n1jw%2FyTk4an83dbT4AOw%2FJYuHe88vf4%2B%2FDMwbI5VDG52u8JRcBZYubQLKts15Uh1I8immAYiToyLlU%2B4bPOnXU6OkhTg7Dit6JGhADogjZHisPfvZ2%2BJLDGTWuFUEFKd%2FY9t5T6WOU%2B4cmcGPPNpC4A%2BrZ1DWxZ4L2YF4BcMC%2FkgDajxkT%2B9QAJo3q9pjrFYQCh4871%2BpJZFoVt4LFGTNOGuAaKYNGnrzyc2Xk%2B%2BTLn50dKGYoFTJNa%2BLZDkJJb8t1dTRLvla4VqXTyaX58AVy%2BOo5GMRLO8faRdQwh7%2FwHl%2BCpLKHiw9fNh1KzbAEVFbFW8ypBIl%2B4GcrcTdY8KRZrMHQRCXX5%2Bf%2FPK%2BUbVtVssNc%2BkrEKTG2peLuLPyUZkGH77F7wvph34hzFzoAHwpoTsfYXT6CC2zi9gTCnSHy6BN1UZ7j7G7GpE%2FaJvYoPvLNcYvGK8Fe%2BVNPSv6VeuMY6apr9nAb%2F8FshznvnipP78FIc%2FPkPxqA9ZlJeXpzewMU%2BeCJoK%2FKOmOwDtlzHXrbeUqtZNDkNROsA5zfkM5U%2B5GVF1icILDn9lpd8P9ukwx7mvwgY6sAHDOXcB3aSTKq5W2vEyrhF1sZdsz4Ct49GCK%2FafSs5BHiWfEFoIYUSJ5xxr3TM2Q7q1CyaTtlhMCjkSGVjs180r6PkcTzj7ycgWF2mSEr83kgCbzeIYSfQjX6zENeXiqmIr8Xs%2Borui8KcH8i4ZWyeAKWGvV%2Ff5nPOcSeQlVmIqbr93%2FmZb8sMAdC5iDBEHaYvkZshS2Tq7miS5VlYv7vbNM4%2FQWuNgGP7F4bY5K6gPGQ%3D%3D&X-Amz-Signature=6f4a53143983e8b85af670c840d4fc678e1d1156ffab5cf0a5d8494a88b1b453&X-Amz-SignedHeaders=host", "maskedAadhaarNumber": "xxxxxxxx2332"}, "status": "success", "statusCode": "200"}, "generateSelfieToken": {"result": {"token": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "status": "success"}, "applicationCreatedAt": "2025-06-13 14:44:40"}, "sub_applications": {"sub_application_info_map": {}}, "external_user_id": "ACC14163975336595710", "sm_user_id": "SMAD2C9B1BF36094AD48A518A3AC8E5BA28", "application_type": "CREDIT_LINE_LTFS", "application_id": "APP2506131444404163194432969227671478437", "parent_application_id": null, "tenant": "CALM", "pending_task": [], "application_state": "MANDATE_SETUP", "merchant_id": "FLIPKART", "partner_state": null, "partner_sub_state": null, "created_at": 1749806081000, "updated_at": 1749809291000}