package com.flipkart.fintech.pinaka.service.subscriber;

import com.flipkart.fintech.pinaka.service.data.impl.DisbursalsDao;
import com.flipkart.fintech.pinaka.service.data.model.DisbursalsEntity;
import com.google.cloud.pubsub.v1.AckReplyConsumer;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import com.supermoney.schema.PandoraService.OfferDetail;
import com.supermoney.schema.PandoraService.OfferEventV1;
import lombok.var;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LoanDisbursalDataSubscriberTest {

    @Mock
    private LoanDisbursalDataSubscriberConfig config;
    @Mock
    private DisbursalsDao disbursalsDao;
    @Mock
    private SessionFactory sessionFactory;
    @Mock
    private Session session;
    @Mock
    private Transaction transaction;
    @Mock
    private AckReplyConsumer consumer;

    private LoanDisbursalDataSubscriber loanDisbursalDataSubscriber;

    @Before
    public void setUp() {
        when(config.getProjectId()).thenReturn("test-project");
        when(config.getSubscriptionId()).thenReturn("test-subscription");
        loanDisbursalDataSubscriber = spy(new LoanDisbursalDataSubscriber(config, disbursalsDao, sessionFactory) {
            @Override
            public void startSubscriber() {}
        });
    }

    @Test
    public void testConstructor_InitializesFields() {
        assertNotNull(loanDisbursalDataSubscriber);
    }

    @Test
    public void testStartSubscriber_Success() {
        LoanDisbursalDataSubscriber realSubscriber = new LoanDisbursalDataSubscriber(config, disbursalsDao, sessionFactory);
    }

    @Test
    public void testProcessAndPersist_Success() {
        OfferEventV1 request = mock(OfferEventV1.class, RETURNS_DEEP_STUBS);
        List<OfferDetail> offer = new ArrayList<>();
        OfferDetail offerDetail = new OfferDetail();
        offerDetail.setNetDisbursed(10000L);
        offer.add(offerDetail);
        request.setOfferDetails(offer);
        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("user456");
        when(request.getDate()).thenReturn("2024-06-01T10:00:00");
        when(request.getLender()).thenReturn("LenderX");

        when(sessionFactory.openSession()).thenReturn(session);
        when(session.beginTransaction()).thenReturn(transaction);

        doNothing().when(disbursalsDao).saveOrUpdate(any(), any());

        // Use reflection to call private method
        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("processAndPersist", OfferEventV1.class);
            method.setAccessible(true);
            method.invoke(loanDisbursalDataSubscriber, request);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }

        verify(sessionFactory).openSession();
        verify(session).beginTransaction();
        verify(disbursalsDao).saveOrUpdate(eq(session), any(DisbursalsEntity.class));
        verify(transaction).commit();
        verify(session).close();
    }

    @Test
    public void testProcessAndPersist_ExceptionRollsBack() {
        OfferEventV1 request = mock(OfferEventV1.class, RETURNS_DEEP_STUBS);
        List<OfferDetail> offer = new ArrayList<>();
        OfferDetail offerDetail = new OfferDetail();
        offerDetail.setNetDisbursed(10000L);
        offer.add(offerDetail);
        request.setOfferDetails(offer);
        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("user456");
        when(request.getDate()).thenReturn("2025-07-18T10:00:00");
        when(request.getLender()).thenReturn("Lender");

        when(sessionFactory.openSession()).thenReturn(session);
        when(session.beginTransaction()).thenReturn(transaction);
        doThrow(new RuntimeException("DB error")).when(disbursalsDao).saveOrUpdate(eq(session), any(DisbursalsEntity.class));

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("processAndPersist", OfferEventV1.class);
            method.setAccessible(true);
            method.invoke(loanDisbursalDataSubscriber, request);
        } catch (Exception ignored) {}

        verify(session).close();
    }

    @Test
    public void testHandleMessage_Success() throws Exception {
        String jsonMessage = "{\n" +
                "  \"event_id\": { \"string\": \"TEST_EVENT_ID\" },\n" +
                "  \"date\": { \"string\": \"2025-07-17T17:00:57\" },\n" +
                "  \"additional_data\": null,\n" +
                "  \"lender\": { \"string\": \"LENDERX\" },\n" +
                "  \"account_id\": { \"string\": \"SmUserId\" },\n" +
                "  \"program_id\": null,\n" +
                "  \"validity\": { \"long\": **************** },\n" +
                "  \"application_id\": { \"string\": \"TestApplicationId\" },\n" +
                "  \"offer_details\": [\n" +
                "    {\n" +
                "      \"min_amount\": { \"double\": 50000 },\n" +
                "      \"max_amount\": { \"double\": 50000 },\n" +
                "      \"min_tenure\": { \"double\": 540 },\n" +
                "      \"max_tenure\": { \"double\": 540 },\n" +
                "      \"roi\": { \"double\": 24.75 },\n" +
                "      \"pf_amount\": { \"double\": 3 },\n" +
                "      \"pf_type\": null,\n" +
                "      \"gst\": { \"double\": 18 },\n" +
                "      \"stamp_duty\": null,\n" +
                "      \"net_disbursed\": { \"long\": 49876 },\n" +
                "      \"emi\": null,\n" +
                "      \"first_emi_date\": null,\n" +
                "      \"validity\": null\n" +
                "    }\n" +
                "  ],\n" +
                "  \"lead\": null,\n" +
                "  \"offer_type\": { \"string\": \"INITIAL\" },\n" +
                "  \"offer_id\": { \"string\": \"Offer123\" },\n" +
                "  \"external_user_id\": { \"string\": \"TestUserId\" }\n" +
                "}";
        PubsubMessage pubsubMessage = PubsubMessage.newBuilder().setData(ByteString.copyFromUtf8(jsonMessage)).build();

        // Mock session and transaction
        when(sessionFactory.openSession()).thenReturn(session);
        when(session.beginTransaction()).thenReturn(transaction);
        doNothing().when(disbursalsDao).saveOrUpdate(any(), any());

        // Act
        loanDisbursalDataSubscriber.handleMessage(pubsubMessage, consumer);

        // Assert (as before)
        ArgumentCaptor<DisbursalsEntity> entityCaptor = ArgumentCaptor.forClass(DisbursalsEntity.class);
        verify(disbursalsDao).saveOrUpdate(eq(session), entityCaptor.capture());

        // 5. Verify the captured entity's fields.
        DisbursalsEntity capturedEntity = entityCaptor.getValue();
        assertEquals("TestApplicationId", capturedEntity.getApplicationId());
        assertEquals("SmUserId", capturedEntity.getSmUserId());
        assertEquals("LENDERX", capturedEntity.getLender());

        Date expectedDate = Date.from(LocalDateTime.parse("2025-07-17T17:00:57").atZone(ZoneId.systemDefault()).toInstant());
        assertEquals(expectedDate, capturedEntity.getDisbursalDate());

        // 6. Verify that the transaction was committed.
        verify(transaction).commit();

        // 7. Verify that the message was acknowledged.
        verify(consumer).ack();

        // 8. Ensure nack() was never called.
        verify(consumer, never()).nack();
    }

    @Test
    public void testHandleMessage_Exception_NacksMessage() throws Exception {
        String jsonMessage = "{ \"application_id\": { \"string\": \"TestApp\" }, \"date\": { \"string\": \"2025-07-17T17:00:57\" }, \"lender\": { \"string\": \"LENDERX\" }, \"account_id\": { \"string\": \"SmUserId\" }, \"offer_details\": [ { \"net_disbursed\": { \"long\": 1000 } } ] }";
        PubsubMessage pubsubMessage = PubsubMessage.newBuilder().setData(ByteString.copyFromUtf8(jsonMessage)).build();

        // Spy and mock processAndPersist to throw exception
        LoanDisbursalDataSubscriber spySubscriber = spy(loanDisbursalDataSubscriber);
        doThrow(new RuntimeException("Test Exception")).when(spySubscriber).processAndPersist(any());

        // Act
        spySubscriber.handleMessage(pubsubMessage, consumer);

        // Assert
        verify(consumer).nack();
        verify(consumer, never()).ack();
    }

    @Test
    public void testIsNotValidRequest_ReturnsFalse_WhenAllFieldsValid() {
        OfferEventV1 request = mock(OfferEventV1.class);
        List<OfferDetail> offerDetails = new ArrayList<>();
        OfferDetail offerDetail = new OfferDetail();
        offerDetail.setNetDisbursed(10000L);
        offerDetails.add(offerDetail);

        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("user456");
        when(request.getOfferDetails()).thenReturn(offerDetails);
        when(request.getDate()).thenReturn("2024-06-01T10:00:00");
        when(request.getLender()).thenReturn("LenderX");

        // Use reflection to call private method
        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertFalse(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenRequestIsNull() {
        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, (OfferEventV1) null);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenApplicationIdIsEmpty() {
        OfferEventV1 request = mock(OfferEventV1.class);
        when(request.getApplicationId()).thenReturn("");

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenOfferDetailsIsEmpty() {
        OfferEventV1 request = mock(OfferEventV1.class);
        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("user456");
        when(request.getOfferDetails()).thenReturn(new ArrayList<>());

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenNetDisbursedIsNull() {
        OfferEventV1 request = mock(OfferEventV1.class);
        List<OfferDetail> offerDetails = new ArrayList<>();
        OfferDetail offerDetail = new OfferDetail();
        offerDetail.setNetDisbursed(null);
        offerDetails.add(offerDetail);

        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("user456");
        when(request.getOfferDetails()).thenReturn(offerDetails);

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenAccountIdIsNull() {
        OfferEventV1 request = mock(OfferEventV1.class);
        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn(null);

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenAccountIdIsEmpty() {
        OfferEventV1 request = mock(OfferEventV1.class);
        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("");

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenOfferDetailsIsNull() {
        OfferEventV1 request = mock(OfferEventV1.class);
        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("user456");
        when(request.getOfferDetails()).thenReturn(null);

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenFirstOfferDetailIsNull() {
        OfferEventV1 request = mock(OfferEventV1.class);
        List<OfferDetail> offerDetails = new ArrayList<>();
        offerDetails.add(null);

        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("user456");
        when(request.getOfferDetails()).thenReturn(offerDetails);

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenDateIsNull() {
        OfferEventV1 request = mock(OfferEventV1.class);
        List<OfferDetail> offerDetails = new ArrayList<>();
        OfferDetail offerDetail = new OfferDetail();
        offerDetail.setNetDisbursed(10000L);
        offerDetails.add(offerDetail);

        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("user456");
        when(request.getOfferDetails()).thenReturn(offerDetails);
        when(request.getDate()).thenReturn(null);

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenDateIsEmpty() {
        OfferEventV1 request = mock(OfferEventV1.class);
        List<OfferDetail> offerDetails = new ArrayList<>();
        OfferDetail offerDetail = new OfferDetail();
        offerDetail.setNetDisbursed(10000L);
        offerDetails.add(offerDetail);

        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("user456");
        when(request.getOfferDetails()).thenReturn(offerDetails);
        when(request.getDate()).thenReturn("");

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenLenderIsNull() {
        OfferEventV1 request = mock(OfferEventV1.class);
        List<OfferDetail> offerDetails = new ArrayList<>();
        OfferDetail offerDetail = new OfferDetail();
        offerDetail.setNetDisbursed(10000L);
        offerDetails.add(offerDetail);

        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("user456");
        when(request.getOfferDetails()).thenReturn(offerDetails);
        when(request.getDate()).thenReturn("2024-06-01T10:00:00");
        when(request.getLender()).thenReturn(null);

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }

    @Test
    public void testIsNotValidRequest_ReturnsTrue_WhenLenderIsEmpty() {
        OfferEventV1 request = mock(OfferEventV1.class);
        List<OfferDetail> offerDetails = new ArrayList<>();
        OfferDetail offerDetail = new OfferDetail();
        offerDetail.setNetDisbursed(10000L);
        offerDetails.add(offerDetail);

        when(request.getApplicationId()).thenReturn("app123");
        when(request.getAccountId()).thenReturn("user456");
        when(request.getOfferDetails()).thenReturn(offerDetails);
        when(request.getDate()).thenReturn("2024-06-01T10:00:00");
        when(request.getLender()).thenReturn("");

        try {
            var method = LoanDisbursalDataSubscriber.class.getDeclaredMethod("isNotValidRequest", OfferEventV1.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(loanDisbursalDataSubscriber, request);
            assertTrue(result);
        } catch (Exception e) {
            fail("Exception thrown: " + e.getMessage());
        }
    }
}