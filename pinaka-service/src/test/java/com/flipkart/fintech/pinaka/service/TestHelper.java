package com.flipkart.fintech.pinaka.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.affordability.common.ObjectMapperFactory;
import com.flipkart.fintech.pinaka.api.response.*;
import com.flipkart.fintech.pinaka.common.bigfootIngestor.BigfootEntityConfig;

import java.io.IOException;

import static io.dropwizard.testing.FixtureHelpers.fixture;

/**
 * Created by su<PERSON><PERSON><PERSON>.r on 27/11/17.
 */
public class TestHelper {

    private final        ObjectMapper objectMapper;
    private static final String       RESPONSE_PATH           = "response/";

    public TestHelper() {
        objectMapper = ObjectMapperFactory.OBJECT_MAPPER;
    }


    public SecurityKeyResponse getSecurityKeyResponse() throws IOException {
        String requestStr = fixture(RESPONSE_PATH + "security_key_response.json");
        return objectMapper.readValue(requestStr, SecurityKeyResponse.class);
    }

    public BigfootEntityConfig getBigfootEntityConfig() throws IOException {
        String requestStr = fixture("bigfoot_entity_config.json");
        return objectMapper.readValue(requestStr, BigfootEntityConfig.class);
    }

}
