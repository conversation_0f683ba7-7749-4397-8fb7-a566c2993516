package com.flipkart.fintech.pinaka.service.core.impl;

import com.flipkart.fintech.security.gibraltar.GibraltarService;
import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.service.core.SecurityService;
import com.flipkart.gibraltar.api.response.GibraltarPublicKey;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Base64;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


public class SecurityServiceImplTest {

    @Mock private GibraltarService gibraltarService;
    @Mock private GibraltarPublicKey key;

    private SecurityService service;
    private String keyRef = "test_key_ref";
    private String publicKey = "test_key";

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        service = new SecurityServiceImpl(gibraltarService);
    }

    @After
    public void tearDown() throws Exception {

    }

    @Test
    public void testGetKey() throws Exception {
        when(gibraltarService.getKey()).thenReturn(key);
        when(key.getKeyRef()).thenReturn(keyRef);
        when(key.getKeyValue()).thenReturn(publicKey);
        SecurityKeyResponse actualResponse = service.getKey();
        verify(gibraltarService).getKey();
        assertEquals(keyRef, actualResponse.getKeyRef());
        assertEquals(new String(Base64.getEncoder().encode(Base64.getUrlDecoder().decode(publicKey))),
                actualResponse.getPublicKey());
    }
}