package com.flipkart.fintech.pinaka.service.utils;


import org.junit.Test;

import static org.testng.AssertJUnit.assertEquals;
import static org.testng.AssertJUnit.assertTrue;

public class APRCalculatorTest {

    @Test
    public void testCalculateAPR_typicalCase() {
        double loanAmount = 400000;
        double emi = 36672;
        int tenureMonths = 12;
        double processingFee = 9440;
        double apr = APRCalculator.calculateAPR(loanAmount, emi, tenureMonths, processingFee);
        assertTrue(apr > 0);
    }

    @Test
    public void testCalculateAPR_zeroProcessingFee() {
        double loanAmount = 50000;
        double emi = 4500;
        int tenureMonths = 12;
        double processingFee = 0;
        double apr = APRCalculator.calculateAPR(loanAmount, emi, tenureMonths, processingFee);
        assertTrue(apr > 0);
    }

    @Test
    public void testComputeEMI_zeroRate() {
        double principal = 12000;
        double monthlyRate = 0;
        int months = 12;
        double emi = APRCalculator.computeEMI(principal, monthlyRate, months);
        assertEquals(1000, emi, 1e-6);
    }

    @Test
    public void testComputeEMI_nonZeroRate() {
        double principal = 10000;
        double monthlyRate = 0.01;
        int months = 12;
        double emi = APRCalculator.computeEMI(principal, monthlyRate, months);
        assertTrue(emi > 0);
    }
}