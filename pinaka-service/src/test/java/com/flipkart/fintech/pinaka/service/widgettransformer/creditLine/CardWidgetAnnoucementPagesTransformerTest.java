package com.flipkart.fintech.pinaka.service.widgettransformer.creditLine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.leaf.value.ImageValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardWidgetData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import static org.junit.Assert.*;

public class CardWidgetAnnoucementPagesTransformerTest {

    private CardWidgetAnnoucementPagesTransformer transformer;

    @Before
    public void setUp() {
        transformer = new CardWidgetAnnoucementPagesTransformer();
    }

    @Test
    public void testBuildCardWidgetData_WithAlreadyHaveALoanInteractionKey() throws JsonProcessingException {
        // Execute the method with ALREADY_HAVE_A_LOAN interaction key
        CardWidgetData cardWidgetData = transformer.buildCardWidgetData("ALREADY_HAVE_A_LOAN");

        // Verify the expected values in the CardWidgetData
        assertNotNull(cardWidgetData);
        assertNotNull(cardWidgetData.getCard());
        assertNotNull(cardWidgetData.getCard().getValue());

        // Verify image URL
        ImageValue imageValue = cardWidgetData.getCard().getValue().getIcon();
        assertEquals("https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/05/2025/10062f89-b982-4efe-8ac6-f6396fa4d165.png?q={@quality}", imageValue.getDynamicImageUrl());

        // Verify title
        RichTextValue title = cardWidgetData.getCard().getValue().getTitle().getValue();
        assertEquals("You already have a loan from us ", title.getText());

        // Verify description
        RichTextValue description = cardWidgetData.getCard().getValue().getDescription().getValue();
        assertEquals("Sorry, we cannot credit the cash right now as you already have loan from us ", description.getText());
    }

    @Test
    public void testBuildCardWidgetData_WithWithdrawalBankSuccessInteractionKey() throws JsonProcessingException {
        // Execute the method with WITHDRAWAL_BANK_SUCCESS interaction key
        CardWidgetData cardWidgetData = transformer.buildCardWidgetData("WITHDRAWAL_BANK_SUCCESS");

        // Verify the expected values in the CardWidgetData
        assertNotNull(cardWidgetData);
        assertNotNull(cardWidgetData.getCard());
        assertNotNull(cardWidgetData.getCard().getValue());

        // Verify image URL
        ImageValue imageValue = cardWidgetData.getCard().getValue().getIcon();
        assertEquals("https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/05/2025/10062f89-b982-4efe-8ac6-f6396fa4d165.png?q={@quality}", imageValue.getDynamicImageUrl());

        // Verify title
        RichTextValue title = cardWidgetData.getCard().getValue().getTitle().getValue();
        assertEquals("Super! Withdrawal bank set successfully", title.getText());

        // Verify description
        RichTextValue description = cardWidgetData.getCard().getValue().getDescription().getValue();
        assertEquals("Just a step away to get the cash in your account", description.getText());
    }

    @Test
    public void testBuildCardWidgetData_WithUnknownInteractionKey() throws JsonProcessingException {
        // Execute the method with an unknown interaction key
        CardWidgetData cardWidgetData = transformer.buildCardWidgetData("UNKNOWN_KEY");

        // Verify that the CardWidgetData is not null but no customization was done
        assertNotNull(cardWidgetData);
        assertNotNull(cardWidgetData.getCard());
        assertNotNull(cardWidgetData.getCard().getValue());
    }

    @Test
    public void testBuildSubmitButtonWidgetData() throws JsonProcessingException {
        // Create a mock ApplicationDataResponse
        ApplicationDataResponse applicationDataResponse = Mockito.mock(ApplicationDataResponse.class);
        Mockito.when(applicationDataResponse.getApplicationState()).thenReturn("TEST_STATE");

        // Execute the method
        SubmitButtonWidgetData submitButtonWidgetData = transformer.buildSubmitButtonWidgetData(applicationDataResponse);

        // Verify the SubmitButtonWidgetData structure
        assertNotNull(submitButtonWidgetData);

        // Verify the SubmitButton structure
        SubmitButtonValue submitButtonValue = submitButtonWidgetData.getSubmitButton();
        assertNotNull(submitButtonValue);

        // Verify the URL
        assertNotNull(submitButtonValue.getButton());
        assertNotNull(submitButtonValue.getButton().getAction());
        assertEquals("/api/sm/1/application/form", submitButtonValue.getButton().getAction().getUrl());
    }
}
