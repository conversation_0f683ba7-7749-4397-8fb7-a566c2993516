package com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormGroupDataValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GroupedFormWidgetData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Map;

import static org.testng.AssertJUnit.assertEquals;

@RunWith(PowerMockRunner.class)
public class FormWidgetDataJsonParserTest {

    FormWidgetDataJsonParser jsonParser = new FormWidgetDataJsonParser();

    private final String FINAL_SCREEN_FORM_TEMPLATE = TransformerUtils.readFileasString("template/lead/V3/AllFilledReviewScreen.json");
    private final String ONLY_WORK_DETAILS_FORM_TEMPLATE = TransformerUtils.readFileasString("template/lead/V3/WorkDetails.json");

    @Test
    public void testGetFormFieldValueMapToPrefill() throws JsonProcessingException {
        GroupedFormWidgetData groupedFormWidgetData = ObjectMapperUtil.get().readValue(FINAL_SCREEN_FORM_TEMPLATE, GroupedFormWidgetData.class);
        Map<String, FormFieldValue> formFieldValueMapToPrefill = this.jsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        DropdownFormFieldValue dropdownFormFieldValue = (DropdownFormFieldValue) formFieldValueMapToPrefill.get("incomeSource");
        assertEquals("ONLINE", dropdownFormFieldValue.getSelectedOption().getId());
        assertEquals(15, formFieldValueMapToPrefill.size());
    }

    @Test
    public void testGetFormFieldValueMapToPrefillNullHeader() throws JsonProcessingException {
        GroupedFormWidgetData groupedFormWidgetData = ObjectMapperUtil.get().readValue(FINAL_SCREEN_FORM_TEMPLATE, GroupedFormWidgetData.class);
        groupedFormWidgetData.getFormGroups().get(0).setFormGroupHeader(null);
        groupedFormWidgetData.getFormGroups().get(1).setFormGroupHeader(null);
        groupedFormWidgetData.getFormGroups().get(2).setFormGroupHeader(null);

        Map<String, FormFieldValue> formFieldValueMapToPrefill = this.jsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        assertEquals(0, formFieldValueMapToPrefill.size());
    }

    @Test
    public void testGetFormFieldValueMapToPrefillNullNext() throws JsonProcessingException {
        GroupedFormWidgetData groupedFormWidgetData = ObjectMapperUtil.get().readValue(FINAL_SCREEN_FORM_TEMPLATE, GroupedFormWidgetData.class);
        groupedFormWidgetData.getFormGroups().get(0).getFormGroupHeader().setNext(null);
        groupedFormWidgetData.getFormGroups().get(1).getFormGroupHeader().setNext(null);
        groupedFormWidgetData.getFormGroups().get(2).getFormGroupHeader().setNext(null);

        Map<String, FormFieldValue> formFieldValueMapToPrefill = this.jsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        assertEquals(0, formFieldValueMapToPrefill.size());
    }

    @Test
    public void testGetFormFieldValueMapToPrefillNullNextAction() throws JsonProcessingException {
        GroupedFormWidgetData groupedFormWidgetData = ObjectMapperUtil.get().readValue(FINAL_SCREEN_FORM_TEMPLATE, GroupedFormWidgetData.class);
        groupedFormWidgetData.getFormGroups().get(0).getFormGroupHeader().getNext().setAction(null);
        groupedFormWidgetData.getFormGroups().get(1).getFormGroupHeader().getNext().setAction(null);
        groupedFormWidgetData.getFormGroups().get(2).getFormGroupHeader().getNext().setAction(null);

        Map<String, FormFieldValue> formFieldValueMapToPrefill = this.jsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        assertEquals(0, formFieldValueMapToPrefill.size());
    }

    @Test
    public void testGetGroupFormFieldValueMapToPrefill() throws JsonProcessingException {
        GroupedFormWidgetData groupedFormWidgetData = ObjectMapperUtil.get().readValue(FINAL_SCREEN_FORM_TEMPLATE, GroupedFormWidgetData.class);
        Map<String, FormGroupDataValue> groupFieldValueMapToPrefill = this.jsonParser.getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        assertEquals(13, groupFieldValueMapToPrefill.size());
    }

    @Test
    public void testGetFormFieldValueMapToPrefillForRenderableComponents() throws JsonProcessingException {
        GenericFormWidgetData genericFormWidgetData = ObjectMapperUtil.get().readValue(ONLY_WORK_DETAILS_FORM_TEMPLATE, GenericFormWidgetData.class);
        Map<String, FormFieldValue> formFieldValueMapToPrefillForRenderableComponents = this.jsonParser.getFormFieldValueMapToPrefillForRenderableComponents(genericFormWidgetData.getRenderableComponents());
        assertEquals(9, formFieldValueMapToPrefillForRenderableComponents.size());
    }

    @Test
    public void testUpdateFormFieldPrefillValue() throws JsonProcessingException {
        GroupedFormWidgetData groupedFormWidgetData = ObjectMapperUtil.get().readValue(FINAL_SCREEN_FORM_TEMPLATE, GroupedFormWidgetData.class);
        Map<String, FormFieldValue> formFieldValueMapToPrefill = this.jsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        this.jsonParser.getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        Map<String, SubmitButtonValue> formFieldSubmitButtons = this.jsonParser.getFormFieldSubmitButtons(groupedFormWidgetData.getFormGroups());
        this.jsonParser.updateFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups(), formFieldValueMapToPrefill, formFieldSubmitButtons);
        assertEquals(15, formFieldValueMapToPrefill.size());
    }

    @Test
    public void testupdateFormFieldValueMapToPrefill() throws JsonProcessingException {
        GenericFormWidgetData genericFormWidgetData = ObjectMapperUtil.get().readValue(ONLY_WORK_DETAILS_FORM_TEMPLATE, GenericFormWidgetData.class);
        Map<String, FormFieldValue> formFieldValueMapToPrefillForRenderableComponents = this.jsonParser.getFormFieldValueMapToPrefillForRenderableComponents(genericFormWidgetData.getRenderableComponents());
        jsonParser.updateFormFieldValueMapToPrefill(formFieldValueMapToPrefillForRenderableComponents, genericFormWidgetData.getRenderableComponents());
        assertEquals(9, formFieldValueMapToPrefillForRenderableComponents.size());
    }

}
