package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LeadApprovedAmountCard;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4LandingPageTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4LenderCarouselTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4SubmitButtonWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardCarouselWidgetDataV0;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CardWidgetTransformerTest {

    @Mock
    private LV4LandingPageTransformer lv4LandingPageTransformer;
    
    @Mock
    private LV4LenderCarouselTransformer lv4LenderCarouselTransformer;
    
    @Mock
    private LV4SubmitButtonWidgetTransformer lv4SubmitButtonWidgetTransformer;
    
    @Mock
    private ApplicationDataResponse applicationDataResponse;
    
    @Mock
    private CardSummaryListWidgetData cardSummaryListWidgetData;
    
    @Mock
    private CardCarouselWidgetDataV0 cardCarouselWidgetData;
    
    @Mock
    private GenericFormWidgetData genericFormWidgetData;

    private CardWidgetTransformer cardWidgetTransformer;

    @Mock
    private LeadApprovedAmountCard lV4ApprovedAmountCard;

    @Before
    public void setUp() {
        cardWidgetTransformer = new CardWidgetTransformer(
            lv4LandingPageTransformer,
            lv4SubmitButtonWidgetTransformer,
            lv4LenderCarouselTransformer,
            lV4ApprovedAmountCard
        );
        
        // Setup common mock behavior
        when(applicationDataResponse.getSmUserId()).thenReturn("test-user-123");
    }

    @Test
    public void testConstructor() {
        // Test that constructor properly initializes dependencies
        CardWidgetTransformer transformer = new CardWidgetTransformer(
            lv4LandingPageTransformer,
            lv4SubmitButtonWidgetTransformer,
            lv4LenderCarouselTransformer,
            lV4ApprovedAmountCard
        );
        assertNotNull("CardWidgetTransformer should be created successfully", transformer);
    }

    @Test
    public void testBuildWidgetGroupData_SlotId1_Success() throws Exception {
        // Test slotId = 1 calls lv4LandingPageTransformer.buildWidgetGroupData
        when(lv4LandingPageTransformer.buildWidgetGroupData(applicationDataResponse))
            .thenReturn(cardSummaryListWidgetData);

        WidgetData result = cardWidgetTransformer.buildWidgetGroupData(1, applicationDataResponse);

        assertNotNull("Result should not be null", result);
        assertEquals("Result should be CardSummaryListWidgetData", cardSummaryListWidgetData, result);
        verify(lv4LandingPageTransformer).buildWidgetGroupData(applicationDataResponse);
        verifyNoInteractions(lv4LenderCarouselTransformer, lv4SubmitButtonWidgetTransformer);
    }

    @Test
    public void testBuildWidgetGroupData_SlotId2_Success() throws Exception {
        // Test slotId = 2 calls lv4LenderCarouselTransformer.buildWidgetGroupData
        when(lv4LenderCarouselTransformer.buildWidgetGroupData(applicationDataResponse))
            .thenReturn(cardSummaryListWidgetData);

        WidgetData result = cardWidgetTransformer.buildWidgetGroupData(2, applicationDataResponse);

        assertNotNull("Result should not be null", result);
        assertEquals("Result should be CardSummaryListWidgetData", cardSummaryListWidgetData, result);
        verify(lv4LenderCarouselTransformer).buildWidgetGroupData(applicationDataResponse);
        verifyNoInteractions(lv4LandingPageTransformer, lv4SubmitButtonWidgetTransformer);
    }

    @Test
    public void testBuildWidgetGroupData_SlotId3_Success() throws Exception {
        // Test slotId = 3 calls lv4LenderCarouselTransformer.buildWidgetData
        when(lv4LenderCarouselTransformer.buildWidgetData(applicationDataResponse))
            .thenReturn(cardCarouselWidgetData);

        WidgetData result = cardWidgetTransformer.buildWidgetGroupData(3, applicationDataResponse);

        assertNotNull("Result should not be null", result);
        assertEquals("Result should be CardCarouselWidgetDataV0", cardCarouselWidgetData, result);
        verify(lv4LenderCarouselTransformer).buildWidgetData(applicationDataResponse);
        verifyNoInteractions(lv4LandingPageTransformer, lv4SubmitButtonWidgetTransformer);
    }

    @Test(expected = PinakaException.class)
    public void testBuildWidgetGroupData_InvalidSlotId_ThrowsException() throws Exception {
        // Test invalid slotId throws PinakaException
        cardWidgetTransformer.buildWidgetGroupData(4, applicationDataResponse);
    }

    @Test
    public void testBuildWidgetGroupData_InvalidSlotId_ExceptionMessage() throws Exception {
        // Test invalid slotId exception message
        try {
            cardWidgetTransformer.buildWidgetGroupData(0, applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain error context",
                      e.getMessage().contains("Error building card widget group data") ||
                      e.getMessage().contains("Unknown slotId : 0"));
        }
    }

    @Test
    public void testBuildWidgetGroupData_NullApplicationDataResponse() throws Exception {
        // Test null ApplicationDataResponse - the method should handle null gracefully or throw exception
        // Since the transformers are mocked, they may return null without throwing exception
        when(lv4LandingPageTransformer.buildWidgetGroupData(null)).thenReturn(null);

        WidgetData result = cardWidgetTransformer.buildWidgetGroupData(1, null);
        // If no exception is thrown, result may be null which is acceptable
        // The actual behavior depends on the transformer implementation
        verify(lv4LandingPageTransformer).buildWidgetGroupData(null);
    }

    @Test
    public void testBuildWidgetGroupData_SlotId1_TransformerThrowsException() throws Exception {
        // Test when underlying transformer throws exception
        when(lv4LandingPageTransformer.buildWidgetGroupData(applicationDataResponse))
            .thenThrow(new RuntimeException("Transformer error"));

        try {
            cardWidgetTransformer.buildWidgetGroupData(1, applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain userId", 
                      e.getMessage().contains("test-user-123"));
            assertTrue("Exception should have cause", e.getCause() instanceof RuntimeException);
        }
    }

    @Test
    public void testBuildWidgetGroupData_SlotId2_TransformerThrowsException() throws Exception {
        // Test when lv4LenderCarouselTransformer.buildWidgetGroupData throws exception
        when(lv4LenderCarouselTransformer.buildWidgetGroupData(applicationDataResponse))
            .thenThrow(new RuntimeException("Carousel transformer error"));

        try {
            cardWidgetTransformer.buildWidgetGroupData(2, applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain userId", 
                      e.getMessage().contains("test-user-123"));
            assertTrue("Exception should have cause", e.getCause() instanceof RuntimeException);
        }
    }

    @Test
    public void testBuildWidgetGroupData_SlotId3_TransformerThrowsException() throws Exception {
        // Test when lv4LenderCarouselTransformer.buildWidgetData throws exception
        when(lv4LenderCarouselTransformer.buildWidgetData(applicationDataResponse))
            .thenThrow(new RuntimeException("Carousel widget data error"));

        try {
            cardWidgetTransformer.buildWidgetGroupData(3, applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain userId", 
                      e.getMessage().contains("test-user-123"));
            assertTrue("Exception should have cause", e.getCause() instanceof RuntimeException);
        }
    }

    @Test
    public void testBuildWidgetGroupData_WithWidgetType_Success() throws Exception {
        // Test buildWidgetGroupData with WidgetTypeV4 parameter
        when(lv4SubmitButtonWidgetTransformer.buildWidgetData(applicationDataResponse, WidgetTypeV4.FORM))
            .thenReturn(genericFormWidgetData);

        GenericFormWidgetData result = cardWidgetTransformer.buildWidgetGroupData(applicationDataResponse, WidgetTypeV4.FORM);

        assertNotNull("Result should not be null", result);
        assertEquals("Result should be GenericFormWidgetData", genericFormWidgetData, result);
        verify(lv4SubmitButtonWidgetTransformer).buildWidgetData(applicationDataResponse, WidgetTypeV4.FORM);
        verifyNoInteractions(lv4LandingPageTransformer, lv4LenderCarouselTransformer);
    }

    @Test
    public void testBuildWidgetGroupData_WithWidgetType_NullApplicationDataResponse() throws Exception {
        // Test buildWidgetGroupData with WidgetTypeV4 parameter with null ApplicationDataResponse
        // Since the transformer is mocked, it may return null without throwing exception
        when(lv4SubmitButtonWidgetTransformer.buildWidgetData(null, WidgetTypeV4.FORM)).thenReturn(null);

        GenericFormWidgetData result = cardWidgetTransformer.buildWidgetGroupData(null, WidgetTypeV4.FORM);
        // If no exception is thrown, result may be null which is acceptable
        // The actual behavior depends on the transformer implementation
        verify(lv4SubmitButtonWidgetTransformer).buildWidgetData(null, WidgetTypeV4.FORM);
    }

    @Test
    public void testBuildWidgetGroupData_WithWidgetType_TransformerThrowsException() throws Exception {
        // Test when lv4SubmitButtonWidgetTransformer throws exception
        when(lv4SubmitButtonWidgetTransformer.buildWidgetData(applicationDataResponse, WidgetTypeV4.FORM))
            .thenThrow(new RuntimeException("Submit button transformer error"));

        try {
            cardWidgetTransformer.buildWidgetGroupData(applicationDataResponse, WidgetTypeV4.FORM);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain userId",
                      e.getMessage().contains("test-user-123"));
            assertTrue("Exception should have cause", e.getCause() instanceof RuntimeException);
        }
    }

    @Test
    public void testBuildWidgetGroupData_MultipleSlotIds_Success() throws Exception {
        // Test multiple calls with different slotIds work correctly
        when(lv4LandingPageTransformer.buildWidgetGroupData(applicationDataResponse))
            .thenReturn(cardSummaryListWidgetData);
        when(lv4LenderCarouselTransformer.buildWidgetGroupData(applicationDataResponse))
            .thenReturn(cardSummaryListWidgetData);
        when(lv4LenderCarouselTransformer.buildWidgetData(applicationDataResponse))
            .thenReturn(cardCarouselWidgetData);

        // Test slotId 1
        WidgetData result1 = cardWidgetTransformer.buildWidgetGroupData(1, applicationDataResponse);
        assertNotNull("Result1 should not be null", result1);

        // Test slotId 2
        WidgetData result2 = cardWidgetTransformer.buildWidgetGroupData(2, applicationDataResponse);
        assertNotNull("Result2 should not be null", result2);

        // Test slotId 3
        WidgetData result3 = cardWidgetTransformer.buildWidgetGroupData(3, applicationDataResponse);
        assertNotNull("Result3 should not be null", result3);

        // Verify all transformers were called
        verify(lv4LandingPageTransformer).buildWidgetGroupData(applicationDataResponse);
        verify(lv4LenderCarouselTransformer).buildWidgetGroupData(applicationDataResponse);
        verify(lv4LenderCarouselTransformer).buildWidgetData(applicationDataResponse);
    }

    @Test
    public void testBuildWidgetGroupData_NegativeSlotId_ThrowsException() throws Exception {
        // Test negative slotId throws exception
        try {
            cardWidgetTransformer.buildWidgetGroupData(-1, applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain error context",
                      e.getMessage().contains("Error building card widget group data") ||
                      e.getMessage().contains("Unknown slotId : -1"));
        }
    }

    @Test
    public void testBuildWidgetGroupData_LargeSlotId_ThrowsException() throws Exception {
        // Test large slotId throws exception
        try {
            cardWidgetTransformer.buildWidgetGroupData(999, applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain error context",
                      e.getMessage().contains("Error building card widget group data") ||
                      e.getMessage().contains("Unknown slotId : 999"));
        }
    }

    @Test
    public void testBuildWidgetGroupData_ApplicationDataResponseWithNullUserId() throws Exception {
        // Test when ApplicationDataResponse has null userId
        when(applicationDataResponse.getSmUserId()).thenReturn(null);
        when(lv4LandingPageTransformer.buildWidgetGroupData(applicationDataResponse))
            .thenThrow(new RuntimeException("Test exception"));

        try {
            cardWidgetTransformer.buildWidgetGroupData(1, applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should handle null userId",
                      e.getMessage().contains("null"));
        }
    }

    @Test
    public void testBuildWidgetGroupData_PinakaExceptionFromTransformer() throws Exception {
        // Test when transformer throws PinakaException directly
        PinakaException originalException = new PinakaException("Original transformer error");
        when(lv4LandingPageTransformer.buildWidgetGroupData(applicationDataResponse))
            .thenThrow(originalException);

        try {
            cardWidgetTransformer.buildWidgetGroupData(1, applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain userId",
                      e.getMessage().contains("test-user-123"));
            assertEquals("Exception cause should be original PinakaException",
                        originalException, e.getCause());
        }
    }

    @Test
    public void testBuildWidgetGroupData_NoSlotId_PinakaExceptionFromTransformer() throws Exception {
        // Test when submit button transformer throws PinakaException directly
        PinakaException originalException = new PinakaException("Submit button error");
        when(lv4SubmitButtonWidgetTransformer.buildWidgetData(applicationDataResponse, WidgetTypeV4.FORM))
            .thenThrow(originalException);

        try {
            cardWidgetTransformer.buildWidgetGroupData(applicationDataResponse, WidgetTypeV4.FORM);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain userId",
                      e.getMessage().contains("test-user-123"));
            assertEquals("Exception cause should be original PinakaException",
                        originalException, e.getCause());
        }
    }

    @Test
    public void testBuildWidgetGroupData_EmptyUserId() throws Exception {
        // Test when ApplicationDataResponse has empty userId
        when(applicationDataResponse.getSmUserId()).thenReturn("");
        when(lv4LandingPageTransformer.buildWidgetGroupData(applicationDataResponse))
            .thenThrow(new RuntimeException("Test exception"));

        try {
            cardWidgetTransformer.buildWidgetGroupData(1, applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain empty string",
                      e.getMessage().contains("Error building card widget group data for userId: "));
        }
    }

    @Test
    public void testBuildWidgetGroupData_ConsistentBehaviorAcrossMultipleCalls() throws Exception {
        // Test that multiple calls to same method with same parameters behave consistently
        when(lv4LandingPageTransformer.buildWidgetGroupData(applicationDataResponse))
            .thenReturn(cardSummaryListWidgetData);

        // Call multiple times
        for (int i = 0; i < 3; i++) {
            WidgetData result = cardWidgetTransformer.buildWidgetGroupData(1, applicationDataResponse);
            assertNotNull("Result should not be null on call " + (i + 1), result);
            assertEquals("Result should be consistent on call " + (i + 1),
                        cardSummaryListWidgetData, result);
        }

        // Verify transformer was called 3 times
        verify(lv4LandingPageTransformer, times(3)).buildWidgetGroupData(applicationDataResponse);
    }

    @Test
    public void testBuildWidgetGroupData_AllSlotIdsWithDifferentUserIds() throws Exception {
        // Test all slot IDs with different user IDs
        ApplicationDataResponse app1 = mock(ApplicationDataResponse.class);
        ApplicationDataResponse app2 = mock(ApplicationDataResponse.class);
        ApplicationDataResponse app3 = mock(ApplicationDataResponse.class);

        when(app1.getSmUserId()).thenReturn("user1");
        when(app2.getSmUserId()).thenReturn("user2");
        when(app3.getSmUserId()).thenReturn("user3");

        when(lv4LandingPageTransformer.buildWidgetGroupData(app1))
            .thenReturn(cardSummaryListWidgetData);
        when(lv4LenderCarouselTransformer.buildWidgetGroupData(app2))
            .thenReturn(cardSummaryListWidgetData);
        when(lv4LenderCarouselTransformer.buildWidgetData(app3))
            .thenReturn(cardCarouselWidgetData);

        // Test each slot with different user
        WidgetData result1 = cardWidgetTransformer.buildWidgetGroupData(1, app1);
        WidgetData result2 = cardWidgetTransformer.buildWidgetGroupData(2, app2);
        WidgetData result3 = cardWidgetTransformer.buildWidgetGroupData(3, app3);

        assertNotNull("Result1 should not be null", result1);
        assertNotNull("Result2 should not be null", result2);
        assertNotNull("Result3 should not be null", result3);

        verify(lv4LandingPageTransformer).buildWidgetGroupData(app1);
        verify(lv4LenderCarouselTransformer).buildWidgetGroupData(app2);
        verify(lv4LenderCarouselTransformer).buildWidgetData(app3);
    }
}
