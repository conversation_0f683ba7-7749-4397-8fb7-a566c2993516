package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.model.PaOffer;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.utils.EncryptionUtil;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.TextStyle;
import com.flipkart.rome.datatypes.response.fintech.supermoney.cards.PrimitiveCard;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.supermoney.schema.PinakaService.LeadV3Events;
import org.apache.commons.text.StringSubstitutor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.PA_OFFER;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;
import static org.powermock.api.mockito.PowerMockito.whenNew;

/**
 * Comprehensive test suite for LV4ApprovedAmountCard using PowerMock
 * Tests all public and package-private methods with various scenarios including edge cases and error handling
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({
        LeadApprovedAmountCard.class,
        ObjectMapperUtil.class,
        TransformerUtils.class,
        LV4Util.class,
        LV3Util.class,
        FormConfig.class,
        StringSubstitutor.class,
        EncryptionUtil.class
})
public class LeadApprovedAmountCardTest {

    @Mock
    private DynamicBucket dynamicBucket;

    @Mock
    private BqIngestionHelper bqIngestionHelper;

    @Mock
    private ApplicationDataResponse applicationDataResponse;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private CardSummaryListWidgetData cardSummaryListWidgetData;

    @Mock
    private PaOffer paOffer;

    @Mock
    private LV4Util.ApprovedAmount approvedAmount;

    @Mock
    private RenderableComponent<PrimitiveCard> primitiveCardRenderableComponent;

    @Mock
    private PrimitiveCard primitiveCard;

    @Mock
    private RenderableComponent<RichTextValue> titleComponent;

    @Mock
    private RichTextValue richTextValue;

    @Mock
    private FormConfig formConfig;

    @Mock
    private StringSubstitutor stringSubstitutor;
    @Mock
    private LocationRequestHandler locationRequestHandler;
    @Mock
    private Decrypter decrypter;
    @Mock
    private LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer;
    @Mock
    private FormWidgetDataFetcher formWidgetDataFetcher;

    @Mock
    private RichTextValue superTitleRichTextValue;

    private LeadApprovedAmountCard lv4ApprovedAmountCard;

    private static final String TEMPLATE_CONTENT = "{\"template\": \"content\"}";
    private static final String PROCESSED_JSON = "{\"processed\": \"json\"}";

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        // Mock static classes
        PowerMockito.mockStatic(ObjectMapperUtil.class);
        PowerMockito.mockStatic(TransformerUtils.class);
        PowerMockito.mockStatic(LV4Util.class);
        PowerMockito.mockStatic(LV3Util.class);
        PowerMockito.mockStatic(EncryptionUtil.class);

        // Setup basic mock responses
        when(applicationDataResponse.getSmUserId()).thenReturn("test-user-123");
        when(applicationDataResponse.getApplicationState()).thenReturn("LEAD_V4_PAGE_1");
        when(applicationDataResponse.getMerchantId()).thenReturn("FK");

        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put(PA_OFFER, new HashMap<>());
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);

        // Setup ObjectMapperUtil mock
        when(ObjectMapperUtil.get()).thenReturn(objectMapper);

        // Setup TransformerUtils mock
        when(TransformerUtils.readFileasString("template/lead/V4/ApprovedAmountWidget.json"))
            .thenReturn(TEMPLATE_CONTENT);

        // Setup LV3Util mock
        LeadV3Events mockLeadEvents = LeadV3Events.newBuilder().build();
        when(LV3Util.getLeadEvents(any(ApplicationDataResponse.class), anyString(), anyString(), anyString()))
            .thenReturn(mockLeadEvents);

        // Create instance
        lv4ApprovedAmountCard = new LeadApprovedAmountCard(dynamicBucket, bqIngestionHelper, formWidgetDataFetcher, locationRequestHandler, decrypter, lv3ReviewPage1FormTransformer, null);
    }

    // ========== Constructor Tests ==========

    @Test
    public void testConstructor() {
        LeadApprovedAmountCard transformer = new LeadApprovedAmountCard(dynamicBucket, bqIngestionHelper, formWidgetDataFetcher, locationRequestHandler, decrypter, lv3ReviewPage1FormTransformer, null);
        assertNotNull("Transformer should be created successfully", transformer);
    }

    @Test
    public void testConstructorWithNullDependencies() {
        LeadApprovedAmountCard transformer = new LeadApprovedAmountCard(null, null, null, null, null, null, null);
        assertNotNull("Transformer should be created even with null dependencies", transformer);
    }

    // ========== buildWidgetGroupData Tests ==========

//    @Test
//    public void testBuildWidgetGroupData_Success() throws Exception {
//        // Setup mocks for successful execution
//        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
//            .thenReturn(cardSummaryListWidgetData);
//        when(objectMapper.convertValue(any(), eq(PaOffer.class)))
//            .thenReturn(paOffer);
//        when(LV4Util.getOfferAmount(applicationDataResponse, paOffer))
//            .thenReturn(approvedAmount);
//
//        // Setup CardSummaryListWidgetData structure for extractValueToBeUpdated
//        when(cardSummaryListWidgetData.getRenderableComponents())
//            .thenReturn(java.util.Arrays.asList(primitiveCardRenderableComponent));
//        when(primitiveCardRenderableComponent.getValue()).thenReturn(primitiveCard);
//        when(primitiveCard.getTitle()).thenReturn(titleComponent);
//        when(titleComponent.getValue()).thenReturn(richTextValue);
//
//        // Setup approved amount
//        when(approvedAmount.isAppendUpto()).thenReturn(true);
//        when(approvedAmount.getAmount()).thenReturn(500000L);
//        when(LV4Util.formatNumber(500000.0)).thenReturn("5,00,000");
//
//        // Setup FormConfig mock
//        whenNew(FormConfig.class).withAnyArguments().thenReturn(formConfig);
//        Map<String, Object> formConfigMap = new HashMap<>();
//        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class)))
//            .thenReturn(formConfigMap);
//
//        // Setup StringSubstitutor mock
//        whenNew(StringSubstitutor.class).withAnyArguments().thenReturn(stringSubstitutor);
//        when(stringSubstitutor.replace(TEMPLATE_CONTENT)).thenReturn(PROCESSED_JSON);
//
//        CardSummaryListWidgetData result = lv4ApprovedAmountCard.buildWidgetGroupData(applicationDataResponse);
//
//        assertNotNull("Result should not be null", result);
//        assertEquals("Result should be the mocked CardSummaryListWidgetData",
//                    cardSummaryListWidgetData, result);
//
//        // Verify interactions
//        verify(objectMapper).readValue(PROCESSED_JSON, CardSummaryListWidgetData.class);
//        verify(objectMapper).convertValue(any(), eq(PaOffer.class));
//        PowerMockito.verifyStatic(LV4Util.class);
//        LV4Util.getOfferAmount(applicationDataResponse, paOffer);
//    }

    @Test
    public void testBuildWidgetGroupData_JsonProcessingException() throws Exception {
        // Setup mock to throw exception during JSON processing
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
            .thenThrow(new RuntimeException("JSON processing failed"));

        // Setup FormConfig and StringSubstitutor mocks
        whenNew(FormConfig.class).withAnyArguments().thenReturn(formConfig);
        Map<String, Object> formConfigMap = new HashMap<>();
        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class)))
            .thenReturn(formConfigMap);
        whenNew(StringSubstitutor.class).withAnyArguments().thenReturn(stringSubstitutor);
        when(stringSubstitutor.replace(TEMPLATE_CONTENT)).thenReturn(PROCESSED_JSON);

        try {
            lv4ApprovedAmountCard.buildWidgetGroupData(applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain userId",
                      e.getMessage().contains("test-user-123"));
            assertTrue("Exception message should contain context",
                      e.getMessage().contains("Error while building widget Group Data for LV4 Approved Amount Card"));
            assertNotNull("Exception should have a cause", e.getCause());
        }
    }

    @Test
    public void testBuildWidgetGroupData_PaOfferConversionException() throws Exception {
        // Setup mock to throw exception during PaOffer conversion
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
            .thenReturn(cardSummaryListWidgetData);
        when(objectMapper.convertValue(any(), eq(PaOffer.class)))
            .thenThrow(new IllegalArgumentException("PaOffer conversion failed"));

        // Setup FormConfig and StringSubstitutor mocks
        whenNew(FormConfig.class).withAnyArguments().thenReturn(formConfig);
        Map<String, Object> formConfigMap = new HashMap<>();
        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class)))
            .thenReturn(formConfigMap);
        whenNew(StringSubstitutor.class).withAnyArguments().thenReturn(stringSubstitutor);
        when(stringSubstitutor.replace(TEMPLATE_CONTENT)).thenReturn(PROCESSED_JSON);

        try {
            lv4ApprovedAmountCard.buildWidgetGroupData(applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain userId",
                      e.getMessage().contains("test-user-123"));
            assertNotNull("Exception should have a cause", e.getCause());
            assertTrue("Cause should be IllegalArgumentException",
                      e.getCause() instanceof IllegalArgumentException);
        }

    }

    @Test
    public void testBuildWidgetGroupData_NullApplicationDataResponse() {
        try {
            lv4ApprovedAmountCard.buildWidgetGroupData(null);
            fail("Should have thrown exception for null ApplicationDataResponse");
        } catch (PinakaException e) {
            assertTrue("Exception should be properly handled", true);
        } catch (NullPointerException e) {
            assertTrue("NPE is acceptable for null input", true);
        }
    }

    // ========== updateOfferAmountText Tests ==========

    @Test
    public void testUpdateOfferAmountText_Success() throws Exception {
        // Setup mocks for extractValueToBeUpdated
        when(cardSummaryListWidgetData.getRenderableComponents())
            .thenReturn(java.util.Arrays.asList(primitiveCardRenderableComponent));
        when(primitiveCardRenderableComponent.getValue()).thenReturn(primitiveCard);
        when(primitiveCard.getTitle()).thenReturn(titleComponent);
        when(titleComponent.getValue()).thenReturn(richTextValue);

        // Setup approved amount mock
        when(approvedAmount.isAppendUpto()).thenReturn(true);
        when(approvedAmount.getAmount()).thenReturn(500000L);

        // Setup LV4Util.formatNumber mock
        when(LV4Util.formatNumber(500000.0)).thenReturn("5,00,000");

        lv4ApprovedAmountCard.updateOfferAmountText(cardSummaryListWidgetData, approvedAmount, null, null);

        // Verify text was set
        verify(richTextValue).setText("Review your details to unlock ₹5,00,000 *");
        verify(richTextValue).setSpecialTextsMapper(any(Map.class));

        // LV4Util.formatNumber is called twice: once in getFormattedAmountText and once in updateSpecialTextsMapper
        PowerMockito.verifyStatic(LV4Util.class, times(2));
        LV4Util.formatNumber(500000.0);
    }

    @Test
    public void testUpdateOfferAmountText_WithoutUpto() throws Exception {
        // Setup mocks for extractValueToBeUpdated
        when(cardSummaryListWidgetData.getRenderableComponents())
            .thenReturn(java.util.Arrays.asList(primitiveCardRenderableComponent));
        when(primitiveCardRenderableComponent.getValue()).thenReturn(primitiveCard);
        when(primitiveCard.getTitle()).thenReturn(titleComponent);
        when(titleComponent.getValue()).thenReturn(richTextValue);

        // Setup approved amount mock without "upto"
        when(approvedAmount.isAppendUpto()).thenReturn(false);
        when(approvedAmount.getAmount()).thenReturn(300000L);

        // Setup LV4Util.formatNumber mock
        when(LV4Util.formatNumber(300000.0)).thenReturn("3,00,000");

        lv4ApprovedAmountCard.updateOfferAmountText(cardSummaryListWidgetData, approvedAmount, null, null);

        // Verify text was set without "upto" and "*"
        verify(richTextValue).setText("Review your details to unlock ₹3,00,000");
        verify(richTextValue).setSpecialTextsMapper(any(Map.class));
    }

    // ========== updateSpecialTextsMapper Tests ==========

    @Test
    public void testUpdateSpecialTextsMapper() throws Exception {
        when(approvedAmount.getAmount()).thenReturn(250000L);
        when(LV4Util.formatNumber(250000.0)).thenReturn("2,50,000");

        lv4ApprovedAmountCard.updateSpecialTextsMapper(approvedAmount, richTextValue);

        // Verify that special texts mapper was set with correct color
        verify(richTextValue).setSpecialTextsMapper(argThat(map -> {
            Map<String, TextStyle> specialTextsMap = (Map<String, TextStyle>) map;
            return specialTextsMap.containsKey("₹2,50,000") &&
                   specialTextsMap.get("₹2,50,000").getColor().equals("#4D43FE");
        }));

        PowerMockito.verifyStatic(LV4Util.class);
        LV4Util.formatNumber(250000.0);
    }

    // ========== extractValueToBeUpdated Tests ==========

    @Test
    public void testExtractValueToBeUpdated_Success() {
        // Setup mock chain
        when(cardSummaryListWidgetData.getRenderableComponents())
            .thenReturn(java.util.Arrays.asList(primitiveCardRenderableComponent));
        when(primitiveCardRenderableComponent.getValue()).thenReturn(primitiveCard);
        when(primitiveCard.getTitle()).thenReturn(titleComponent);
        when(titleComponent.getValue()).thenReturn(richTextValue);

        RichTextValue result = lv4ApprovedAmountCard.extractValueToBeUpdated(cardSummaryListWidgetData);

        assertNotNull("Result should not be null", result);
        assertEquals("Result should be the mocked RichTextValue", richTextValue, result);

        verify(cardSummaryListWidgetData).getRenderableComponents();
        verify(primitiveCardRenderableComponent).getValue();
        verify(primitiveCard).getTitle();
        verify(titleComponent).getValue();
    }

    @Test(expected = NullPointerException.class)
    public void testExtractValueToBeUpdated_NullRenderableComponents() {
        when(cardSummaryListWidgetData.getRenderableComponents()).thenReturn(null);

        lv4ApprovedAmountCard.extractValueToBeUpdated(cardSummaryListWidgetData);
    }

    @Test(expected = IndexOutOfBoundsException.class)
    public void testExtractValueToBeUpdated_EmptyRenderableComponents() {
        when(cardSummaryListWidgetData.getRenderableComponents())
            .thenReturn(java.util.Collections.emptyList());

        lv4ApprovedAmountCard.extractValueToBeUpdated(cardSummaryListWidgetData);
    }

    @Test(expected = NullPointerException.class)
    public void testExtractValueToBeUpdated_NullPrimitiveCard() {
        when(cardSummaryListWidgetData.getRenderableComponents())
            .thenReturn(java.util.Arrays.asList(primitiveCardRenderableComponent));
        when(primitiveCardRenderableComponent.getValue()).thenReturn(null);

        lv4ApprovedAmountCard.extractValueToBeUpdated(cardSummaryListWidgetData);
    }

    // ========== getFormattedAmountText Tests ==========

    @Test
    public void testGetFormattedAmountText_WithUpto() throws Exception {
        when(approvedAmount.isAppendUpto()).thenReturn(true);
        when(approvedAmount.getAmount()).thenReturn(750000L);
        when(LV4Util.formatNumber(750000.0)).thenReturn("7,50,000");

        String result = lv4ApprovedAmountCard.getFormattedAmountText(approvedAmount, "LEAD_V4_PAGE_1", null);

        assertEquals("Should format with 'upto' and '*'",
                    "Review your details to unlock ₹7,50,000 *", result);

        PowerMockito.verifyStatic(LV4Util.class);
        LV4Util.formatNumber(750000.0);
    }

    @Test
    public void testGetFormattedAmountText_WithoutUpto() throws Exception {
        when(approvedAmount.isAppendUpto()).thenReturn(false);
        when(approvedAmount.getAmount()).thenReturn(400000L);
        when(LV4Util.formatNumber(400000.0)).thenReturn("4,00,000");

        String result = lv4ApprovedAmountCard.getFormattedAmountText(approvedAmount, null, null);

        assertEquals("Should format without 'upto' and '*'",
                    "Review your details to unlock ₹4,00,000", result);

        PowerMockito.verifyStatic(LV4Util.class);
        LV4Util.formatNumber(400000.0);
    }

    @Test
    public void testGetFormattedAmountText_WithoutUpto_forPersonalDetails() {
        when(approvedAmount.isAppendUpto()).thenReturn(false);
        when(approvedAmount.getAmount()).thenReturn(400000L);
        when(LV4Util.formatNumber(400000.0)).thenReturn("4,00,000");

        String result = lv4ApprovedAmountCard.getFormattedAmountText(approvedAmount, null, "LEAD_V4_NOTHING_PREFILLED_SCREEN");

        assertEquals("Should format without 'upto' and '*'",
                "Enter personal details to unlock ₹4,00,000", result);

        PowerMockito.verifyStatic(LV4Util.class);
        LV4Util.formatNumber(400000.0);
    }

    @Test
    public void testGetFormattedAmountText_WithoutUpto_forWorkDetails() {
        when(approvedAmount.isAppendUpto()).thenReturn(false);
        when(approvedAmount.getAmount()).thenReturn(400000L);
        when(LV4Util.formatNumber(400000.0)).thenReturn("4,00,000");

        String result = lv4ApprovedAmountCard.getFormattedAmountText(approvedAmount, "LEAD_V4_PAGE_2", null);

        assertEquals("Should format without 'upto' and '*'",
                "Enter work details to unlock ₹4,00,000", result);

        PowerMockito.verifyStatic(LV4Util.class);
        LV4Util.formatNumber(400000.0);
    }

    // ========== getINRFormattedAmount Tests ==========

    @Test
    public void testGetINRFormattedAmount() throws Exception {
        when(approvedAmount.getAmount()).thenReturn(123456L);
        when(LV4Util.formatNumber(123456.0)).thenReturn("1,23,456");

        String result = lv4ApprovedAmountCard.getINRFormattedAmount(approvedAmount);

        assertEquals("Should format with INR symbol", "₹1,23,456", result);

        PowerMockito.verifyStatic(LV4Util.class);
        LV4Util.formatNumber(123456.0);
    }

    @Test
    public void testGetINRFormattedAmount_ZeroAmount() throws Exception {
        when(approvedAmount.getAmount()).thenReturn(0L);
        when(LV4Util.formatNumber(0.0)).thenReturn("0");

        String result = lv4ApprovedAmountCard.getINRFormattedAmount(approvedAmount);

        assertEquals("Should format zero amount with INR symbol", "₹0", result);
    }

    // ========== Integration Tests ==========

//    @Test
//    public void testFullWorkflow_WithValidPaOffer() throws Exception {
//        // Setup complete workflow mocks
//        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
//            .thenReturn(cardSummaryListWidgetData);
//        when(objectMapper.convertValue(any(), eq(PaOffer.class)))
//            .thenReturn(paOffer);
//        when(LV4Util.getOfferAmount(applicationDataResponse, paOffer))
//            .thenReturn(approvedAmount);
//
//        // Setup CardSummaryListWidgetData structure
//        when(cardSummaryListWidgetData.getRenderableComponents())
//            .thenReturn(java.util.Arrays.asList(primitiveCardRenderableComponent));
//        when(primitiveCardRenderableComponent.getValue()).thenReturn(primitiveCard);
//        when(primitiveCard.getTitle()).thenReturn(titleComponent);
//        when(titleComponent.getValue()).thenReturn(richTextValue);
//
//        // Setup approved amount
//        when(approvedAmount.isAppendUpto()).thenReturn(false);
//        when(approvedAmount.getAmount()).thenReturn(600000L);
//        when(LV4Util.formatNumber(600000.0)).thenReturn("6,00,000");
//
//        // Setup FormConfig and StringSubstitutor
//        whenNew(FormConfig.class).withAnyArguments().thenReturn(formConfig);
//        Map<String, Object> formConfigMap = new HashMap<>();
//        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class)))
//            .thenReturn(formConfigMap);
//        whenNew(StringSubstitutor.class).withAnyArguments().thenReturn(stringSubstitutor);
//        when(stringSubstitutor.replace(TEMPLATE_CONTENT)).thenReturn(PROCESSED_JSON);
//
//        CardSummaryListWidgetData result = lv4ApprovedAmountCard.buildWidgetGroupData(applicationDataResponse);
//
//        assertNotNull("Result should not be null", result);
//
//        // Verify the complete workflow
//        verify(objectMapper).readValue(PROCESSED_JSON, CardSummaryListWidgetData.class);
//        verify(objectMapper).convertValue(any(), eq(PaOffer.class));
//        verify(richTextValue).setText("Review your details to unlock ₹6,00,000");
//        verify(richTextValue).setSpecialTextsMapper(any(Map.class));
//
//        PowerMockito.verifyStatic(LV4Util.class);
//        LV4Util.getOfferAmount(applicationDataResponse, paOffer);
//        // LV4Util.formatNumber is called twice: once in getFormattedAmountText and once in updateSpecialTextsMapper
//        PowerMockito.verifyStatic(LV4Util.class, times(2));
//        LV4Util.formatNumber(600000.0);
//    }

//    @Test
//    public void testFullWorkflow_WithNullPaOffer() throws Exception {
//        // Setup workflow with null PaOffer
//        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
//            .thenReturn(cardSummaryListWidgetData);
//        when(objectMapper.convertValue(any(), eq(PaOffer.class)))
//            .thenReturn(null);
//        when(LV4Util.getOfferAmount(applicationDataResponse, null))
//            .thenReturn(approvedAmount);
//
//        // Setup CardSummaryListWidgetData structure
//        when(cardSummaryListWidgetData.getRenderableComponents())
//            .thenReturn(java.util.Arrays.asList(primitiveCardRenderableComponent));
//        when(primitiveCardRenderableComponent.getValue()).thenReturn(primitiveCard);
//        when(primitiveCard.getTitle()).thenReturn(titleComponent);
//        when(titleComponent.getValue()).thenReturn(richTextValue);
//
//        // Setup approved amount with upto (default behavior for null PaOffer)
//        when(approvedAmount.isAppendUpto()).thenReturn(true);
//        when(approvedAmount.getAmount()).thenReturn(1000000L);
//        when(LV4Util.formatNumber(1000000.0)).thenReturn("10,00,000");
//
//        // Setup FormConfig and StringSubstitutor
//        whenNew(FormConfig.class).withAnyArguments().thenReturn(formConfig);
//        Map<String, Object> formConfigMap = new HashMap<>();
//        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class)))
//            .thenReturn(formConfigMap);
//        whenNew(StringSubstitutor.class).withAnyArguments().thenReturn(stringSubstitutor);
//        when(stringSubstitutor.replace(TEMPLATE_CONTENT)).thenReturn(PROCESSED_JSON);
//
//        CardSummaryListWidgetData result = lv4ApprovedAmountCard.buildWidgetGroupData(applicationDataResponse);
//
//        assertNotNull("Result should not be null", result);
//        verify(richTextValue).setText("Review your details to unlock upto ₹10,00,000 *");
//
//        PowerMockito.verifyStatic(LV4Util.class);
//        LV4Util.getOfferAmount(applicationDataResponse, null);
//    }

    // ========== Edge Cases and Error Scenarios ==========

    @Test
    public void testBuildWidgetGroupData_FormConfigException() throws Exception {
        // Setup FormConfig to throw exception
        whenNew(FormConfig.class).withAnyArguments().thenThrow(new RuntimeException("FormConfig creation failed"));

        try {
            lv4ApprovedAmountCard.buildWidgetGroupData(applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain userId",
                      e.getMessage().contains("test-user-123"));
            assertNotNull("Exception should have a cause", e.getCause());
        }
    }

    @Test
    public void testBuildWidgetGroupData_StringSubstitutorException() throws Exception {
        // Setup FormConfig to throw exception instead of StringSubstitutor
        whenNew(FormConfig.class).withAnyArguments().thenThrow(new RuntimeException("FormConfig creation failed"));

        try {
            lv4ApprovedAmountCard.buildWidgetGroupData(applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain userId",
                      e.getMessage().contains("test-user-123"));
            assertNotNull("Exception should have a cause", e.getCause());
        }

    }

    @Test
    public void testBuildWidgetGroupData_LV4UtilException() throws Exception {
        // Setup successful JSON processing but LV4Util.getOfferAmount throws exception
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
            .thenReturn(cardSummaryListWidgetData);
        when(objectMapper.convertValue(any(), eq(PaOffer.class)))
            .thenReturn(paOffer);
        when(LV4Util.getOfferAmount(applicationDataResponse, paOffer))
            .thenThrow(new RuntimeException("LV4Util.getOfferAmount failed"));

        // Setup FormConfig and StringSubstitutor
        whenNew(FormConfig.class).withAnyArguments().thenReturn(formConfig);
        Map<String, Object> formConfigMap = new HashMap<>();
        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class)))
            .thenReturn(formConfigMap);
        whenNew(StringSubstitutor.class).withAnyArguments().thenReturn(stringSubstitutor);
        when(stringSubstitutor.replace(TEMPLATE_CONTENT)).thenReturn(PROCESSED_JSON);

        try {
            lv4ApprovedAmountCard.buildWidgetGroupData(applicationDataResponse);
            fail("Should have thrown PinakaException");
        } catch (PinakaException e) {
            assertTrue("Exception message should contain userId",
                      e.getMessage().contains("test-user-123"));
            assertNotNull("Exception should have a cause", e.getCause());
        }

    }

    @Test
    public void testStaticFieldInitialization() throws Exception {
        // Test that static template loading works - this is called during class loading
        // We can't easily verify static field initialization with PowerMock in this context
        // Instead, we verify that the constant is accessible
        assertNotNull("APPROVED_AMOUNT_TEXT_COLOR should be accessible",
                     LeadApprovedAmountCard.APPROVED_AMOUNT_TEXT_COLOR);
    }

    @Test
    public void testConstantValues() {
        // Test that constants have expected values
        assertEquals("Approved amount text color should be correct",
                    "#4D43FE", LeadApprovedAmountCard.APPROVED_AMOUNT_TEXT_COLOR);
    }

    // ========== Boundary Value Tests ==========

    @Test
    public void testGetFormattedAmountText_LargeAmount() throws Exception {
        when(approvedAmount.isAppendUpto()).thenReturn(false);
        when(approvedAmount.getAmount()).thenReturn(99999999L);
        when(LV4Util.formatNumber(99999999.0)).thenReturn("9,99,99,999");

        String result = lv4ApprovedAmountCard.getFormattedAmountText(approvedAmount, null, null);

        assertEquals("Should handle large amounts correctly",
                    "Review your details to unlock ₹9,99,99,999", result);
    }

    @Test
    public void testGetFormattedAmountText_MinimumAmount() throws Exception {
        when(approvedAmount.isAppendUpto()).thenReturn(true);
        when(approvedAmount.getAmount()).thenReturn(1L);
        when(LV4Util.formatNumber(1.0)).thenReturn("1");

        String result = lv4ApprovedAmountCard.getFormattedAmountText(approvedAmount, null, null);

        assertEquals("Should handle minimum amounts correctly",
                    "Review your details to unlock ₹1 *", result);
    }

}
