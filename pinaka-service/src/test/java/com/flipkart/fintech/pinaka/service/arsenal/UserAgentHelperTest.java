package com.flipkart.fintech.pinaka.service.arsenal;

import static org.junit.Assert.*;

import com.flipkart.mobile.parser.UserAgentParserSingleton;
import com.flipkart.mobile.parser.reponseobject.UserAgentResponse;
import org.junit.Test;

public class UserAgentHelperTest {

  private static final String msite = "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.3/msite/Mobile";
  private static final String android = "Mozilla/5.0 (Linux; Android 14; sdk_gphone64_arm64 Build/UPB5.230623.003) FKUA/Retail/1840005/Android/Mobile (Google/sdk_gphone64_arm64/737ba46216808ec45feb161c799fa298)";

  @Test
  public void isMsite() throws Exception {
    UserAgentResponse userAgentResponse = UserAgentParserSingleton.getInstance().parse(msite);
    assertTrue(UserAgentHelper.isMsite(userAgentResponse));
  }

  @Test
  public void isNotAndroid() throws Exception {
    UserAgentResponse userAgentResponse = UserAgentParserSingleton.getInstance().parse(msite);
    assertFalse(UserAgentHelper.isAndroidPlatform(userAgentResponse));
  }

  @Test
  public void isAndroid() throws Exception {
    UserAgentResponse userAgentResponse = UserAgentParserSingleton.getInstance().parse(android);
    assertTrue(UserAgentHelper.isAndroidPlatform(userAgentResponse));
  }
}