package com.flipkart.fintech.pinaka.service.widgettransformer.creditLine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.response.CreditLineFormPageDataSourceResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.fintech.calm.MultiDropdownFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.flipkart.rome.datatypes.response.fintech.insurtech.TextBoxFormFieldValueV0;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DateFormFieldValue;

import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class CreditLinePanFormPageTransformerTest {
    private CreditLinePanPageFormTransformer transformer;
    private CreditLineFormPageDataSourceResponse response;
    private ProfileDetailedResponse profileDetailedResponse;
    private ApplicationDataResponse applicationDataResponse;

    @Before
    public void setup() {
        profileDetailedResponse = mock(ProfileDetailedResponse.class);
        response = mock(CreditLineFormPageDataSourceResponse.class);

        when(response.getProfile()).thenReturn(profileDetailedResponse);
        when(profileDetailedResponse.getPan()).thenReturn("t6jb7KfjCFlSaFoZ3R4ixw==");
        when(profileDetailedResponse.getDob()).thenReturn("SwOh200P48IDYA1tupE/4w==");
        when(profileDetailedResponse.getGender()).thenReturn("M");

        transformer = new CreditLinePanPageFormTransformer(response);
    }

    @Test
    public void testBuildWidgetData_shouldPrefillPanDobGenderFields() throws PinakaClientException, JsonProcessingException {
        applicationDataResponse = new ApplicationDataResponse();

        GenericFormWidgetData widgetData = transformer.buildWidgetData(applicationDataResponse);

        List<RenderableComponent<FormFieldValue>> renderableComponents = widgetData.getRenderableComponents();

        for (RenderableComponent<FormFieldValue> component : renderableComponents) {
            if (component.getValue() instanceof TextBoxFormFieldValueV0) {
                TextBoxFormFieldValueV0 value = (TextBoxFormFieldValueV0) component.getValue();
                if ("pan".equals(value.getName())) {
                    assertEquals("**********", value.getValue());
                }
            }
            if (component.getValue() instanceof DateFormFieldValue) {
                DateFormFieldValue dateValue = (DateFormFieldValue) component.getValue();
                if ("dob".equals(dateValue.getName())) {
                    assertEquals("07/11/1998", dateValue.getValue());
                }
            }
            if (component.getValue() instanceof MultiDropdownFormFieldValue) {
                MultiDropdownFormFieldValue dropdownField = (MultiDropdownFormFieldValue) component.getValue();
                if ("gender".equals(dropdownField.getName())) {
                    assertEquals("M", dropdownField.getOptions().get(0).getId());
                }
            }
        }
    }
}
