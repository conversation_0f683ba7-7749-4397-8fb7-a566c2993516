package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataJsonParser;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.schema.PinakaService.LeadV3Events;
import org.apache.commons.text.StringSubstitutor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doNothing;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import static org.testng.AssertJUnit.assertEquals;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        LV3ReviewPage2FormTransformer.class,
        InitialUserReviewDataSource.class,
        ReviewUserDataSourceResponse.class,
        LeadPageDataSourceResponse.class,
        LeadPageDataSource.class,
        ObjectMapperUtil.class,
        FormConfig.class,
        StringSubstitutor.class,
        LV3Util.class
})
public class LV3ReviewPage2FormTransformerTest {

    @Mock
    private ApplicationDataResponse applicationDataResponse;
    @Mock
    private LocationRequestHandler locationRequestHandler;
    @Mock
    private DynamicBucket dynamicBucket;
    @Mock
    private Decrypter decrypter;

    @Mock
    private InitialUserReviewDataSource initialUserReviewDataSource;
    @Mock
    private LeadPageDataSource leadPageDataSource;
    @Mock
    private ReviewUserDataSourceResponse reviewUserDataSourceResponse;
    @Mock
    private LeadPageDataSourceResponse leadPageDataSourceResponse;

    @Mock
    private FormWidgetDataFetcher formWidgetDataFetcher;
    @Mock
    private FormWidgetDataJsonParser formWidgetDataJsonParser;
    @Mock
    private FormWidgetDataPrefillUtils formWidgetDataPrefillUtils;
    @Mock
    private BqIngestionHelper bqIngestionHelper;

    @Before
    public void setup() throws Exception {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(LV3Util.class);
        PowerMockito.when(LV3Util.class, "getLeadV3Events", any(ApplicationDataResponse.class), any(String.class), any(String.class)).thenReturn(LeadV3Events.newBuilder().build());
    }

    private LV3ReviewPage2FormTransformer getLv3ReviewPage2FormTransformer() throws PinakaException {
        return new LV3ReviewPage2FormTransformer(
                decrypter,
                dynamicBucket,
                formWidgetDataPrefillUtils,
                formWidgetDataFetcher,
                formWidgetDataJsonParser,
                locationRequestHandler,
                bqIngestionHelper,
                null); // Mock BureauDataManager not needed for tests
    }

    @Test
    public void testLV3BannerTransformer() throws PinakaException {
        LV3ReviewPage2FormTransformer.WorkDetailsBannerFormTransformer bannerFormTransformer = new LV3ReviewPage2FormTransformer.WorkDetailsBannerFormTransformer();
        BannerWidgetData bannerWidgetData = bannerFormTransformer.buildBannerWidgetData(applicationDataResponse);
        assertEquals(BannerWidgetData.class, bannerWidgetData.getClass());
    }

    @Test(expected = PinakaException.class)
    public void testExceptionWhileParsingJson() throws JsonProcessingException, PinakaException {
        PowerMockito.mockStatic(ObjectMapperUtil.class);
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        when(objectMapper.readValue(anyString(), eq(BannerWidgetData.class))).thenThrow(new RuntimeException());
        when(ObjectMapperUtil.get()).thenReturn(objectMapper);
        LV3ReviewPage2FormTransformer.WorkDetailsBannerFormTransformer bannerFormTransformer = new LV3ReviewPage2FormTransformer.WorkDetailsBannerFormTransformer();
        BannerWidgetData bannerWidgetData = bannerFormTransformer.buildBannerWidgetData(applicationDataResponse);
        assertEquals(BannerWidgetData.class, bannerWidgetData.getClass());
    }

    @Test
    public void testLV3ReviewPageTransformer() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = new HashMap<>();

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMap(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());

        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(Map.class), any(List.class));

        LV3ReviewPage2FormTransformer lv3ReviewPage2FormTransformer = spy(getLv3ReviewPage2FormTransformer());
        lv3ReviewPage2FormTransformer.buildWidgetData(applicationDataResponse, LeadDetails.LeadState.LEAD_V3_PAGE_2);

        verify(formWidgetDataPrefillUtils, times(1)).prefillFormFieldValues(any(Map.class), any(Map.class));
        verify(formWidgetDataJsonParser, times(1)).updateFormFieldValueMapToPrefill(any(Map.class), any(List.class));
        verify(lv3ReviewPage2FormTransformer, times(1)).getFormJson(any(String.class), any(ApplicationDataResponse.class));
    }

    @Test
    public void testLV4ReviewPageTransformer() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = new HashMap<>();

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMap(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());

        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(Map.class), any(List.class));

        LV3ReviewPage2FormTransformer lv3ReviewPage2FormTransformer = spy(getLv3ReviewPage2FormTransformer());
        lv3ReviewPage2FormTransformer.buildWidgetData(applicationDataResponse, LeadDetails.LeadState.LEAD_V4_PAGE_2);

        verify(formWidgetDataPrefillUtils, times(1)).prefillFormFieldValues(any(Map.class), any(Map.class));
        verify(formWidgetDataJsonParser, times(1)).updateFormFieldValueMapToPrefill(any(Map.class), any(List.class));
        verify(lv3ReviewPage2FormTransformer, times(1)).getFormJson(any(String.class), any(ApplicationDataResponse.class));
    }

    @Test(expected = PinakaException.class)
    public void expectedException() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMap(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());

        PowerMockito.mockStatic(ObjectMapperUtil.class);
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        when(objectMapper.readValue(anyString(), eq(GenericFormWidgetData.class))).thenThrow(new RuntimeException(""));
        when(ObjectMapperUtil.get()).thenReturn(objectMapper);

        LV3ReviewPage2FormTransformer lv3ReviewPage2FormTransformer = spy(getLv3ReviewPage2FormTransformer());
        lv3ReviewPage2FormTransformer.buildWidgetData(applicationDataResponse, LeadDetails.LeadState.LEAD_V3_PAGE_2);

        verify(formWidgetDataPrefillUtils, times(0)).prefillFormFieldValues(any(Map.class), any(Map.class));
        verify(formWidgetDataJsonParser, times(0)).updateFormFieldValueMapToPrefill(any(Map.class), any(List.class));
        verify(lv3ReviewPage2FormTransformer, times(1)).getFormJson(any(String.class), any(ApplicationDataResponse.class));
    }

    @Test(expected = PinakaException.class)
    public void expectedExceptionWithInvalidLeadState() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMap(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());

        PowerMockito.mockStatic(ObjectMapperUtil.class);
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        when(objectMapper.readValue(anyString(), eq(GenericFormWidgetData.class))).thenThrow(new RuntimeException(""));
        when(ObjectMapperUtil.get()).thenReturn(objectMapper);

        LV3ReviewPage2FormTransformer lv3ReviewPage2FormTransformer = spy(getLv3ReviewPage2FormTransformer());
        lv3ReviewPage2FormTransformer.buildWidgetData(applicationDataResponse, LeadDetails.LeadState.LEAD_V3_NAME_PAGE);

        verify(formWidgetDataPrefillUtils, times(0)).prefillFormFieldValues(any(Map.class), any(Map.class));
        verify(formWidgetDataJsonParser, times(0)).updateFormFieldValueMapToPrefill(any(Map.class), any(List.class));
        verify(lv3ReviewPage2FormTransformer, times(1)).getFormJson(any(String.class), any(ApplicationDataResponse.class));
    }
}
