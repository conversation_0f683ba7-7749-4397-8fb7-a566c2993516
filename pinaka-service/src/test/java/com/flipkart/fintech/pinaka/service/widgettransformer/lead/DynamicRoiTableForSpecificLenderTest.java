package com.flipkart.fintech.pinaka.service.widgettransformer.lead;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.*;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.enums.*;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.GetOfferResponse;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.service.response.SandboxOfferScreenResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.SandboxOfferScreenTransformer;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.BankOfferFormFieldV2Data;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.CustomFormFieldData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.CustomFormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.PinakaConstants;
//import org.junit.api.Assertions;
//import org.junit.api.Test;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;

public class DynamicRoiTableForSpecificLenderTest {

    public static String formJson = TransformerUtils.readFileasString("template/sandbox/v2/OfferDetails.json");
    private DynamicBucket dynamicBucket;

    @Test
    public void OfferDetailsDynamicShowROIColTest() throws JsonProcessingException {
        dynamicBucket = Mockito.mock(DynamicBucket.class);
        Mockito.when(dynamicBucket.getStringArray("excludedLenders.ForRoiColumn.OfferDetails")).thenReturn(Arrays.asList("FINNABLE", "RINGoo", "MONEYVIEW", "FIBE"));
        SandboxOfferScreenTransformer sandboxOfferScreenTransformer = new SandboxOfferScreenTransformer(formJson,dynamicBucket);
        GenericFormWidgetData testDate = sandboxOfferScreenTransformer.buildWidgetData(getIncludedSandboxOfferResponse());
        System.out.println(ObjectMapperUtil.get().writeValueAsString(testDate));
        CustomFormFieldData customFormFieldValue = ((CustomFormFieldValue)testDate.getRenderableComponents().get(0).getValue()).getCustomFieldData();
        // Assertions.assertNotNull(((BankOfferFormFieldV2Data)customFormFieldValue).getAmountRangeOffers().get(0).getInterest().getValue());
    }

    @Test
    public void OfferDetailsDynamicDontShowROIColTest() throws JsonProcessingException {
        dynamicBucket = Mockito.mock(DynamicBucket.class);
        Mockito.when(dynamicBucket.getStringArray("excludedLenders.ForRoiColumn.OfferDetails")).thenReturn(Arrays.asList("FINNABLE", "RINGoo", "MONEYVIEW", "FIBE"));
        SandboxOfferScreenResponse sandboxOfferScreenResponse = getIncludedSandboxOfferResponse();
        SandboxOfferScreenTransformer sandboxOfferScreenTransformer = new SandboxOfferScreenTransformer(formJson,dynamicBucket);
        GenericFormWidgetData testDate = sandboxOfferScreenTransformer.buildWidgetData(getExcludedSandboxOfferResponse());
        System.out.println(ObjectMapperUtil.get().writeValueAsString(testDate));
        CustomFormFieldData customFormFieldValue = ((CustomFormFieldValue)testDate.getRenderableComponents().get(0).getValue()).getCustomFieldData();
        // Assertions.assertEquals(-1,((BankOfferFormFieldV2Data)customFormFieldValue).getAmountRangeOffers().get(0).getInterest().getValue());
    }

    @Test
    public void OfferDetailsDynamicDontShowROIColBucketNullTest() throws JsonProcessingException {
        dynamicBucket = Mockito.mock(DynamicBucket.class);
        Mockito.when(dynamicBucket.getStringArray("excludedLenders.ForRoiColumn.OfferDetails")).thenReturn(Arrays.asList());
        SandboxOfferScreenResponse sandboxOfferScreenResponse = getIncludedSandboxOfferResponse();
        SandboxOfferScreenTransformer sandboxOfferScreenTransformer = new SandboxOfferScreenTransformer(formJson,dynamicBucket);
        GenericFormWidgetData testDate = sandboxOfferScreenTransformer.buildWidgetData(getExcludedSandboxOfferResponse());
        System.out.println(ObjectMapperUtil.get().writeValueAsString(testDate));
        CustomFormFieldData customFormFieldValue = ((CustomFormFieldValue)testDate.getRenderableComponents().get(0).getValue()).getCustomFieldData();
        // Assertions.assertEquals(-1,((BankOfferFormFieldV2Data)customFormFieldValue).getAmountRangeOffers().get(0).getInterest().getValue());
    }

    @Test
    public void OfferDetailsDynamicDontShowROIColBucketOneNameNullTest() throws JsonProcessingException {
        dynamicBucket = Mockito.mock(DynamicBucket.class);
        Mockito.when(dynamicBucket.getStringArray("excludedLenders.ForRoiColumn.OfferDetails")).thenReturn(Arrays.asList("FINNABLE", "", "MONEYVIEW", "FIBE"));
        SandboxOfferScreenResponse sandboxOfferScreenResponse = getIncludedSandboxOfferResponse();
        SandboxOfferScreenTransformer sandboxOfferScreenTransformer = new SandboxOfferScreenTransformer(formJson,dynamicBucket);
        GenericFormWidgetData testDate = sandboxOfferScreenTransformer.buildWidgetData(getExcludedSandboxOfferResponse());
        System.out.println(ObjectMapperUtil.get().writeValueAsString(testDate));
        CustomFormFieldData customFormFieldValue = ((CustomFormFieldValue)testDate.getRenderableComponents().get(0).getValue()).getCustomFieldData();
        // Assertions.assertEquals(-1,((BankOfferFormFieldV2Data)customFormFieldValue).getAmountRangeOffers().get(0).getInterest().getValue());
    }

    @Test
    public void OfferDetailsCheckLenderImagesTest() throws JsonProcessingException {
        dynamicBucket = Mockito.mock(DynamicBucket.class);
        Mockito.when(dynamicBucket.getStringArray("excludedLenders.ForRoiColumn.OfferDetails")).thenReturn(Arrays.asList("FINNABLE", "RINGoo", "MONEYVIEW", "FIBE"));
        SandboxOfferScreenTransformer sandboxOfferScreenTransformer = new SandboxOfferScreenTransformer(formJson,dynamicBucket);
        SandboxOfferScreenResponse sandboxOfferScreenResponse = getExcludedSandboxOfferResponse();
        SandboxOfferScreenResponse sandboxOfferScreenResponse1=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse1.setLender(Lender.ABFL);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse1);
        SandboxOfferScreenResponse sandboxOfferScreenResponse2=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse2.setLender(Lender.DMI);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse2);
        SandboxOfferScreenResponse sandboxOfferScreenResponse3=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse3.setLender(Lender.FINNABLE_V2);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse3);
        SandboxOfferScreenResponse sandboxOfferScreenResponse4=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse4.setLender(Lender.FINNABLE);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse4);
        SandboxOfferScreenResponse sandboxOfferScreenResponse5=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse5.setLender(Lender.HDFC);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse5);
        SandboxOfferScreenResponse sandboxOfferScreenResponse6=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse6.setLender(Lender.IDFC);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse6);
        SandboxOfferScreenResponse sandboxOfferScreenResponse7=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse7.setLender(Lender.FINNABLE_V2);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse7);
        SandboxOfferScreenResponse sandboxOfferScreenResponse8=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse8.setLender(Lender.FIBEV2);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse8);
        SandboxOfferScreenResponse sandboxOfferScreenResponse9=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse9.setLender(Lender.KISSHT);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse9);
        SandboxOfferScreenResponse sandboxOfferScreenResponse10=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse10.setLender(Lender.PFL);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse10);
        SandboxOfferScreenResponse sandboxOfferScreenResponse11=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse10.setLender(Lender.RING);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse11);
        SandboxOfferScreenResponse sandboxOfferScreenResponse12=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse10.setLender(Lender.SMARTCOIN);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse12);
        SandboxOfferScreenResponse sandboxOfferScreenResponse13=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse10.setLender(Lender.MONEYVIEW);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse13);
        SandboxOfferScreenResponse sandboxOfferScreenResponse14=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse10.setLender(Lender.MONEYVIEWV2);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse14);
        SandboxOfferScreenResponse sandboxOfferScreenResponse15=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse10.setLender(Lender.MONEYVIEWOPENMKT);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse15);
        SandboxOfferScreenResponse sandboxOfferScreenResponse16=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse10.setLender(Lender.OMNI);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse16);
        SandboxOfferScreenResponse sandboxOfferScreenResponse17=sandboxOfferScreenResponse;
        sandboxOfferScreenResponse10.setLender(Lender.OMNIV2);
        sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenResponse17);
    }

    public SandboxOfferScreenResponse getExcludedSandboxOfferResponse(){
        SandboxOfferScreenResponse sandboxOfferScreenResponse = new SandboxOfferScreenResponse();
        GeneratedOffer generatedOffer = new GeneratedOffer();
        Offer offer = new Offer();
        LoanCharge loanCharge = new LoanCharge(FeeType.FLAT,234.44,234.56);
        SanctionedAmount sanctionedAmount = new SanctionedAmount(234876.00, 500000.00);
        Tenure tenure = new Tenure();
        tenure.setValue(34);
        tenure.setUnit(TenureUnit.MONTH);
        OfferEntity offerEntity1 = new OfferEntity(sanctionedAmount,tenure,null,null,36.5);
        OfferEntity offerEntity2 = new OfferEntity(sanctionedAmount,tenure,null,null,38.5);
        OfferEntity offerEntity3 = new OfferEntity(sanctionedAmount,tenure,loanCharge,null,39.5);
        List<OfferEntity> listOfferEntity = new ArrayList<>();
        listOfferEntity.add(offerEntity1);
        listOfferEntity.add(offerEntity2);
        listOfferEntity.add(offerEntity3);
        offer.setOfferTable(listOfferEntity);
        offer.setId("offerId");
        offer.setStampDuty(200.33);
        offer.setMaxSanctionedAmount(500000.00);
        offer.setStepper(5000.00);

        generatedOffer.setOffer(offer);
        generatedOffer.setOfferType(OfferType.INITIAL);
        List<String> additionalData = Arrays.asList("AA","AA");
        JourneyState journeyState = new JourneyState(ApplicationState.AA_SUCCESS,"succesAA", 234556L,null,additionalData);
        ApplicationStatus applicationStatus = new ApplicationStatus(Status.SUBMITTED,null);
        GetOfferResponse getOfferResponse = new GetOfferResponse("appsjdg11",applicationStatus,journeyState,generatedOffer);
        sandboxOfferScreenResponse.setGetOfferResponse(getOfferResponse);
        sandboxOfferScreenResponse.setLender(Lender.FIBE);
        Map<String, Object> mockQueryParams = new HashMap<>();
        mockQueryParams.put("loanId", "123456");
        mockQueryParams.put("userId", "user_001");
        mockQueryParams.put("status", "PENDING");
        sandboxOfferScreenResponse.setQueryParams(mockQueryParams);
        return sandboxOfferScreenResponse;
    }

    public SandboxOfferScreenResponse getIncludedSandboxOfferResponse(){
        SandboxOfferScreenResponse sandboxOfferScreenResponse = new SandboxOfferScreenResponse();
        GeneratedOffer generatedOffer = new GeneratedOffer();
        Offer offer = new Offer();
        SanctionedAmount sanctionedAmount = new SanctionedAmount(234876.00, 500000.00);
        Tenure tenure = new Tenure();
        tenure.setValue(34);
        tenure.setUnit(TenureUnit.MONTH);
        LoanCharge loanCharge = new LoanCharge(FeeType.FLAT,234.44,234.56);
        OfferEntity offerEntity1 = new OfferEntity(sanctionedAmount,tenure,null,null,36.5);
        OfferEntity offerEntity2 = new OfferEntity(sanctionedAmount,tenure,null,null,38.5);
        OfferEntity offerEntity3 = new OfferEntity(sanctionedAmount,tenure,null,loanCharge,39.5);
        List<OfferEntity> listOfferEntity = new ArrayList<>();
        listOfferEntity.add(offerEntity1);
        listOfferEntity.add(offerEntity2);
        listOfferEntity.add(offerEntity3);
        offer.setOfferTable(listOfferEntity);
        offer.setId("offerId");
        offer.setStampDuty(200.33);
        offer.setMaxSanctionedAmount(500000.00);
        offer.setStepper(5000.00);
        generatedOffer.setOffer(offer);
        generatedOffer.setOfferType(OfferType.INITIAL);
        List<String> additionalData = Arrays.asList("AA_FAILED","AA_SUCCESS");
        JourneyState journeyState = new JourneyState(ApplicationState.AA_SUCCESS,"succesAA", 234556L,null,additionalData);
        ApplicationStatus applicationStatus = new ApplicationStatus(Status.SUBMITTED,null);
        GetOfferResponse getOfferResponse = new GetOfferResponse("appsjdg11",applicationStatus,journeyState,generatedOffer);
        sandboxOfferScreenResponse.setGetOfferResponse(getOfferResponse);
        sandboxOfferScreenResponse.setLender(Lender.AXIS);
        return sandboxOfferScreenResponse;
    }


}
