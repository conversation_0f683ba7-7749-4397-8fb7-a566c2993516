package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.pl.response.AadhaarValidateOtpResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.SearchCkycResponse;
import com.flipkart.fintech.pandora.service.client.hyperverge.responses.DigilockerAadhaarResponse;
import com.flipkart.fintech.pinaka.service.response.KycDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import org.apache.http.NameValuePair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.flipkart.fintech.pinaka.service.application.Constants.CREDIT_LINE_LTFS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ObjectMapperUtil.class, UrlUtil.class, QueryParamUtils.class, ConfigUtils.class})
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
public class KycDetailsPageDataSourceTest {

    private KycDetailsPageDataSource dataSource;
    private ConfigUtils mockConfigUtils;
    private ObjectMapper mockObjectMapper;  // Changed from ObjectMapperUtil to ObjectMapper

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // Create a new instance of KycDetailsPageDataSource
        dataSource = new KycDetailsPageDataSource();

        // Mock static classes
        PowerMockito.mockStatic(ObjectMapperUtil.class);
        PowerMockito.mockStatic(UrlUtil.class);
        PowerMockito.mockStatic(QueryParamUtils.class);

        // Create and set up the mock for ObjectMapperUtil.get()
        mockObjectMapper = mock(ObjectMapper.class);  // Changed to ObjectMapper
        PowerMockito.when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);

        // Mock ConfigUtils and inject it into the static field
        mockConfigUtils = PowerMockito.mock(ConfigUtils.class);
        Whitebox.setInternalState(KycDetailsPageDataSource.class, "configUtils", mockConfigUtils);
    }

    @Test
    public void testGetData_withCreditLineLtfs() {
        // Arrange
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        DigilockerAadhaarResponse mockDigilockerResponse = mock(DigilockerAadhaarResponse.class);
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("fetchDigilockerXml", mockDigilockerResponse);

        // Configure mocks
        when(applicationDataResponse.getApplicationType()).thenReturn(CREDIT_LINE_LTFS);
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);

        when(mockObjectMapper.convertValue(any(), eq(DigilockerAadhaarResponse.class)))
            .thenReturn(mockDigilockerResponse);

        List<NameValuePair> queryParams = new ArrayList<>();
        Map<String, Object> queryParamsMap = new HashMap<>();
        queryParamsMap.put("testKey", "testValue");

        PowerMockito.when(UrlUtil.getQueryParams(any(ApplicationDataResponse.class))).thenReturn(queryParams);
        PowerMockito.when(QueryParamUtils.getQueryParams(any(List.class))).thenReturn(queryParamsMap);

        EncryptionData mockEncryptionData = mock(EncryptionData.class);
        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.of(mockEncryptionData));

        // Act
        KycDetailsPageDataSourceResponse response = dataSource.getData(applicationDataResponse);

        // Assert
        assertNotNull("Response should not be null", response);
        assertSame("DigilockerAadhaarResponse should be set correctly",
                mockDigilockerResponse, response.getDigilockerAadhaarResponse());
        assertNull("SearchCkycResponse should be null", response.getSearchCkycResponse());
        assertNull("AadhaarValidateOtpResponse should be null", response.getAadhaarValidateOtpResponse());
        assertEquals("QueryParams should be set correctly", queryParamsMap, response.getQueryParams());
        assertSame("EncryptionData should be set correctly", mockEncryptionData, response.getEncryptionData());
    }
    @Test
    public void testGetData_withoutCreditLineLtfs() {
        // Arrange
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        AadhaarValidateOtpResponse mockDigilockerResponse = mock(AadhaarValidateOtpResponse.class);
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("fetchDigilockerXml", mockDigilockerResponse);
        dataSource.setKyc(true);

        // Configure mocks
        when(applicationDataResponse.getApplicationType()).thenReturn("CREDIT_LINE_LTFS_1");
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);

        when(mockObjectMapper.convertValue(any(), eq(AadhaarValidateOtpResponse.class)))
            .thenReturn(mockDigilockerResponse);

        List<NameValuePair> queryParams = new ArrayList<>();
        Map<String, Object> queryParamsMap = new HashMap<>();
        queryParamsMap.put("testKey", "testValue");

        PowerMockito.when(UrlUtil.getQueryParams(any(ApplicationDataResponse.class))).thenReturn(queryParams);
        PowerMockito.when(QueryParamUtils.getQueryParams(any(List.class))).thenReturn(queryParamsMap);

        EncryptionData mockEncryptionData = mock(EncryptionData.class);
        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.of(mockEncryptionData));

        // Act
        KycDetailsPageDataSourceResponse response = dataSource.getData(applicationDataResponse);

        // Assert
        assertNotNull("Response should not be null", response);
    }

    @Test
    public void testGetData_withCreditLineLtfs_noEncryptionData() {
        // Arrange
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        DigilockerAadhaarResponse mockDigilockerResponse = mock(DigilockerAadhaarResponse.class);
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("fetchDigilockerXml", mockDigilockerResponse);

        // Configure mocks
        when(applicationDataResponse.getApplicationType()).thenReturn(CREDIT_LINE_LTFS);
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);

        when(mockObjectMapper.convertValue(any(), eq(DigilockerAadhaarResponse.class)))
            .thenReturn(mockDigilockerResponse);

        List<NameValuePair> queryParams = new ArrayList<>();
        Map<String, Object> queryParamsMap = new HashMap<>();

        PowerMockito.when(UrlUtil.getQueryParams(any(ApplicationDataResponse.class))).thenReturn(queryParams);
        PowerMockito.when(QueryParamUtils.getQueryParams(any(List.class))).thenReturn(queryParamsMap);

        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());

        // Act
        KycDetailsPageDataSourceResponse response = dataSource.getData(applicationDataResponse);

        // Assert
        assertNotNull("Response should not be null", response);
        assertSame("DigilockerAadhaarResponse should be set correctly",
                mockDigilockerResponse, response.getDigilockerAadhaarResponse());
        assertNull("EncryptionData should be null when Optional is empty", response.getEncryptionData());
    }

    @Test
    public void testGetData_withCreditLineLtfs_nullApplicationData() {
        // Arrange
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        Map<String, Object> applicationData = new HashMap<>();
        // No data for "fetchDigilockerXml"

        // Configure mocks
        when(applicationDataResponse.getApplicationType()).thenReturn(CREDIT_LINE_LTFS);
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);

        when(mockObjectMapper.convertValue(eq(null), eq(DigilockerAadhaarResponse.class)))
            .thenReturn(null);

        List<NameValuePair> queryParams = new ArrayList<>();
        Map<String, Object> queryParamsMap = new HashMap<>();

        PowerMockito.when(UrlUtil.getQueryParams(any(ApplicationDataResponse.class))).thenReturn(queryParams);
        PowerMockito.when(QueryParamUtils.getQueryParams(any(List.class))).thenReturn(queryParamsMap);

        EncryptionData mockEncryptionData = mock(EncryptionData.class);
        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.of(mockEncryptionData));

        // Act
        KycDetailsPageDataSourceResponse response = dataSource.getData(applicationDataResponse);

        // Assert
        assertNotNull("Response should not be null", response);
        assertNull("DigilockerAadhaarResponse should be null", response.getDigilockerAadhaarResponse());
        assertSame("EncryptionData should be set correctly", mockEncryptionData, response.getEncryptionData());
    }
}
