package com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils;

import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.pinaka.api.response.v6.PincodeDetailsResponse;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.library.entities.CAISHolderAddressDetails;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.profile.model.EmploymentType;
import com.flipkart.fintech.profile.model.IncomeSource;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.codahale.metrics.MetricRegistry;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.ADDRESSES_STRING;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils.PINCODE_DETAILS_STRING;
import static org.mockito.Mockito.*;
import static org.testng.AssertJUnit.*;

@RunWith(MockitoJUnitRunner.class)
public class FormWidgetDataFetcherTest {

    @Mock
    Decrypter decrypter;
    @Mock
    LeadPageDataSourceResponse leadPageDataSourceResponse;
    @Mock
    ReviewUserDataSourceResponse reviewUserDataSourceResponse;
    @Mock
    LocationRequestHandler locationRequestHandler;
    FormWidgetDataFetcher formWidgetDataFetcher = new FormWidgetDataFetcher();
    @Mock
    InitialUserDataResponse initialUserDataResponse;
    @Mock
    MetricRegistry metricRegistry;
    @Before
    public void init() {
        decrypter = mock(Decrypter.class);
        leadPageDataSourceResponse = mock(LeadPageDataSourceResponse.class);
        reviewUserDataSourceResponse = mock(ReviewUserDataSourceResponse.class);
        locationRequestHandler = mock(LocationRequestHandler.class);
        initialUserDataResponse = mock(InitialUserDataResponse.class);
        metricRegistry = mock(MetricRegistry.class);
    }

    @Test
    public void getDataForFieldsJustNameDetails() {
        Set<String> fields = new HashSet<>();
        fields.add("fullName");
        FormWidgetDataFetcher formWidgetDataFetcherSpy = spy(new FormWidgetDataFetcher());
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setFirstName("Prasoon");
        profileDetailedResponse.setLastName("Birla");
        profileDetailedResponse.setPhoneNo("9191919191");
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        Map<String, Object> dataForFields = formWidgetDataFetcherSpy.getDataForFields(fields, leadPageDataSourceResponse,decrypter);
        assertEquals("PRASOON BIRLA", dataForFields.get("fullName"));
        assertEquals("9191919191", dataForFields.get("phoneNumber"));
        assertEquals(2, dataForFields.size());
    }

/*    @Test
    public void getDataForFields() throws PinakaException {
        Set<String> fields = new HashSet<>();
        FormWidgetDataFetcher formWidgetDataFetcherSpy = spy(new FormWidgetDataFetcher());
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setFirstName("Prasoon");
        profileDetailedResponse.setLastName("Birla");
        profileDetailedResponse.setPhoneNo("9191919191");
        profileDetailedResponse.setDob("19/02/1998");
        profileDetailedResponse.setAddressLine1("Line1");
        profileDetailedResponse.setAddressLine2("Line2");
        profileDetailedResponse.setGender("Male");
        profileDetailedResponse.setEmail("email");
//        when(decrypter.decryptString(anyString())).thenReturn("Dummy");
//        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        Map<String, Object> dataForFields = formWidgetDataFetcherSpy.getDataForFields(fields, leadPageDataSourceResponse, reviewUserDataSourceResponse, decrypter, locationRequestHandler);
        assertEquals(17, dataForFields.size());
    }*/

    @Test
    public void testGetFullName() {
        ProfileDetailedResponse profileDetailedResponse = spy(new ProfileDetailedResponse());
        leadPageDataSourceResponse.setIsNameEncrypted(false);
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(profileDetailedResponse.getFirstName()).thenReturn("Prasoon");
        when(profileDetailedResponse.getLastName()).thenReturn("Birla");
        String fullName = formWidgetDataFetcher.getFullName(leadPageDataSourceResponse, decrypter);
        assertEquals("PRASOON BIRLA", fullName);
        verify(decrypter, times(0)).decryptString(anyString());
    }

    @Test
    public void testGetFullNameFirstNameNotFound() {
        ProfileDetailedResponse profileDetailedResponse = spy(new ProfileDetailedResponse());
        leadPageDataSourceResponse.setIsNameEncrypted(false);
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(profileDetailedResponse.getFirstName()).thenReturn("");
        when(profileDetailedResponse.getLastName()).thenReturn("BIRLA");
        String fullName = formWidgetDataFetcher.getFullName(leadPageDataSourceResponse, decrypter);
        assertEquals("BIRLA", fullName);
        verify(decrypter, times(0)).decryptString(anyString());
    }

    @Test
    public void testGetFullNameLastNameNotFound() {
        ProfileDetailedResponse profileDetailedResponse = spy(new ProfileDetailedResponse());
        leadPageDataSourceResponse.setIsNameEncrypted(false);
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(profileDetailedResponse.getFirstName()).thenReturn("Prasoon");
        when(profileDetailedResponse.getLastName()).thenReturn("");
        String fullName = formWidgetDataFetcher.getFullName(leadPageDataSourceResponse, decrypter);
        assertEquals("PRASOON", fullName);
        verify(decrypter, times(0)).decryptString(anyString());
    }

    // Add these test cases to the `FormWidgetDataFetcherTest.java` file

    @Test
    public void testFilterOutAddressesWithoutValidPincodeDetailsWhenAllFieldsAreValid() {
        List<CAISHolderAddressDetails> addressDetails = new ArrayList<>();
        CAISHolderAddressDetails address1 = new CAISHolderAddressDetails();
        address1.setZipPostalCodeOfAddress("123456");
        address1.setCityOfAddress("City1");
        address1.setStateOfAddress("State1");
        address1.setFirstLineOfAddress("Address Line 1");
        addressDetails.add(address1);

        List<CAISHolderAddressDetails> filteredAddresses = formWidgetDataFetcher.filterOutAddressesWithoutValidPincodeDetails(addressDetails);
        assertEquals(1, filteredAddresses.size());
        assertEquals("123456", filteredAddresses.get(0).getZipPostalCodeOfAddress());
    }

    @Test
    public void testFilterOutAddressesWithoutValidPincodeDetailsWhenSomeFieldsAreInvalid() {
        List<CAISHolderAddressDetails> addressDetails = new ArrayList<>();
        CAISHolderAddressDetails address1 = new CAISHolderAddressDetails();
        address1.setZipPostalCodeOfAddress("123456");
        address1.setCityOfAddress("City1");
        address1.setStateOfAddress("State1");
        address1.setFirstLineOfAddress("Address Line 1");
        addressDetails.add(address1);

        CAISHolderAddressDetails address2 = new CAISHolderAddressDetails();
        address2.setZipPostalCodeOfAddress(null);
        address2.setCityOfAddress("City2");
        address2.setStateOfAddress("State2");
        address2.setFirstLineOfAddress("Address Line 2");
        addressDetails.add(address2);

        List<CAISHolderAddressDetails> filteredAddresses = formWidgetDataFetcher.filterOutAddressesWithoutValidPincodeDetails(addressDetails);
        assertEquals(1, filteredAddresses.size());
        assertEquals("123456", filteredAddresses.get(0).getZipPostalCodeOfAddress());
    }

    @Test
    public void testFilterOutAddressesWithoutValidPincodeDetailsWhenAllFieldsAreInvalid() {
        List<CAISHolderAddressDetails> addressDetails = new ArrayList<>();
        CAISHolderAddressDetails address1 = new CAISHolderAddressDetails();
        address1.setZipPostalCodeOfAddress(null);
        address1.setCityOfAddress(null);
        address1.setStateOfAddress(null);
        address1.setFirstLineOfAddress(null);
        addressDetails.add(address1);

        List<CAISHolderAddressDetails> filteredAddresses = formWidgetDataFetcher.filterOutAddressesWithoutValidPincodeDetails(addressDetails);
        assertEquals(0, filteredAddresses.size());
    }

    @Test
    public void testFilterOutAddressesWithoutValidPincodeDetailsWhenInputListIsEmpty() {
        List<CAISHolderAddressDetails> addressDetails = new ArrayList<>();
        List<CAISHolderAddressDetails> filteredAddresses = formWidgetDataFetcher.filterOutAddressesWithoutValidPincodeDetails(addressDetails);
        assertEquals(0, filteredAddresses.size());
    }

    @Test
    public void testFilterOutAddressesWithoutValidPincodeDetailsWhenInputListIsNull() {
        List<CAISHolderAddressDetails> filteredAddresses = formWidgetDataFetcher.filterOutAddressesWithoutValidPincodeDetails(null);
        assertEquals(0, filteredAddresses.size());
    }

    // Add these tests to `FormWidgetDataFetcherTest.java`

    @Test
    public void testUpdatePincodeDetailsInAddressesWithValidPincode() throws PinakaException {
        Map<String, Object> userData = new HashMap<>();
        userData.put(PINCODE_DETAILS_STRING, "123456");
        List<CAISHolderAddressDetails> addressDetails = new ArrayList<>();
        CAISHolderAddressDetails addressDetail = new CAISHolderAddressDetails();
        addressDetail.setZipPostalCodeOfAddress("123456");
        addressDetails.add(addressDetail);
        userData.put(ADDRESSES_STRING, addressDetails);

        PincodeDetailsResponse pincodeDetailsResponse = new PincodeDetailsResponse();
        pincodeDetailsResponse.setPincode("123456");
        pincodeDetailsResponse.setCity("CityName");
        pincodeDetailsResponse.setState("StateName");
        pincodeDetailsResponse.setValid(true);

        when(locationRequestHandler.checkPincodeExistence("123456")).thenReturn(pincodeDetailsResponse);

        formWidgetDataFetcher.updatePincodeDetailsInAddresses(locationRequestHandler, userData);

        assertEquals(pincodeDetailsResponse, userData.get(PINCODE_DETAILS_STRING));
        assertEquals("CityName", addressDetails.get(0).getCityOfAddress());
        assertEquals("StateName", addressDetails.get(0).getStateOfAddress());
    }

    @Test
    public void testUpdatePincodeDetailsInAddressesWithInvalidPincode() throws PinakaException {
        Map<String, Object> userData = new HashMap<>();
        userData.put(PINCODE_DETAILS_STRING, "123456");
        List<CAISHolderAddressDetails> addressDetails = new ArrayList<>();
        CAISHolderAddressDetails addressDetail = new CAISHolderAddressDetails();
        addressDetail.setZipPostalCodeOfAddress("654321");
        addressDetails.add(addressDetail);
        userData.put(ADDRESSES_STRING, addressDetails);

        when(locationRequestHandler.checkPincodeExistence("123456")).thenReturn(null);
        when(locationRequestHandler.checkPincodeExistence("654321")).thenReturn(null);

        formWidgetDataFetcher.updatePincodeDetailsInAddresses(locationRequestHandler, userData);

        assertTrue(userData.get(PINCODE_DETAILS_STRING) instanceof PincodeDetailsResponse);
        assertNull(addressDetails.get(0).getCityOfAddress());
        assertNull(addressDetails.get(0).getStateOfAddress());
    }

    @Test
    public void testUpdatePincodeDetailsInAddressesWithEmptyPincode() {
        Map<String, Object> userData = new HashMap<>();
        userData.put(PINCODE_DETAILS_STRING, "123123");
        List<CAISHolderAddressDetails> addressDetails = new ArrayList<>();
        userData.put(ADDRESSES_STRING, addressDetails);

        formWidgetDataFetcher.updatePincodeDetailsInAddresses(locationRequestHandler, userData);

        assertTrue(((List<?>) userData.get(ADDRESSES_STRING)).isEmpty());
    }

    @Test
    public void testUpdatePincodeDetailsInAddressesWithNullAddresses() {
        Map<String, Object> userData = new HashMap<>();
        userData.put(PINCODE_DETAILS_STRING, "");
        userData.put(ADDRESSES_STRING, null);

        formWidgetDataFetcher.updatePincodeDetailsInAddresses(locationRequestHandler, userData);
        assertTrue(((List<?>) userData.get(ADDRESSES_STRING)).isEmpty());
    }

    @Test
    public void testUpdatePincodeDetailsInAddressesWithFilteredAddresses() throws PinakaException {
        Map<String, Object> userData = new HashMap<>();
        userData.put(PINCODE_DETAILS_STRING, "123456");
        List<CAISHolderAddressDetails> addressDetails = new ArrayList<>();
        CAISHolderAddressDetails validAddress = new CAISHolderAddressDetails();
        validAddress.setZipPostalCodeOfAddress("123456");
        validAddress.setCityOfAddress("CityName");
        validAddress.setStateOfAddress("StateName");
        validAddress.setFirstLineOfAddress("FirstLine");
        addressDetails.add(validAddress);

        CAISHolderAddressDetails invalidAddress = new CAISHolderAddressDetails();
        invalidAddress.setZipPostalCodeOfAddress("");
        addressDetails.add(invalidAddress);

        userData.put(ADDRESSES_STRING, addressDetails);

        PincodeDetailsResponse pincodeDetailsResponse = new PincodeDetailsResponse();
        pincodeDetailsResponse.setCity("CityName");
        pincodeDetailsResponse.setState("StateName");

        when(locationRequestHandler.checkPincodeExistence("123456")).thenReturn(pincodeDetailsResponse);

        formWidgetDataFetcher.updatePincodeDetailsInAddresses(locationRequestHandler, userData);

        List<CAISHolderAddressDetails> filteredAddresses = (List<CAISHolderAddressDetails>) userData.get(ADDRESSES_STRING);
        assertEquals(1, filteredAddresses.size());
        assertEquals("CityName", filteredAddresses.get(0).getCityOfAddress());
        assertEquals("StateName", filteredAddresses.get(0).getStateOfAddress());
    }

    @Test
    public void testGetFullNameEncryptionEnabled() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        when(leadPageDataSourceResponse.getIsNameEncrypted()).thenReturn(true);
        profileDetailedResponse.setFirstName("Prasoon");
        profileDetailedResponse.setLastName("Birla");
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(decrypter.decryptString("Prasoon")).thenReturn("Dece Prasoon");
        when(decrypter.decryptString("Birla")).thenReturn("Dece Birla");
        String fullName = formWidgetDataFetcher.getFullName(leadPageDataSourceResponse, decrypter);
        assertEquals("DECE PRASOON DECE BIRLA", fullName);
        verify(decrypter, times(2)).decryptString(anyString());
    }

    @Test
    public void testNoNameFoundInProfile() {
        when(leadPageDataSourceResponse.getIsNameEncrypted()).thenReturn(false);
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setFirstName("");
        profileDetailedResponse.setLastName("");
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);

        String fullName = formWidgetDataFetcher.getFullName(leadPageDataSourceResponse, decrypter);
        assertEquals("", fullName);
        verify(decrypter, times(0)).decryptString(anyString());
    }

    @Test
    public void testGetPanNumberProfileNullAndUserResponseNull() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String panNumber = formWidgetDataFetcher.getPanNumber(reviewUserDataSourceResponse, decrypter);
        assertEquals("", panNumber);
    }

    @Test
    public void testGetPanNumberOnlyProfileNull() {
        String expectedPan = "abcd";
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(initialUserDataResponse.getPanNumber()).thenReturn(expectedPan);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String panNumber = formWidgetDataFetcher.getPanNumber(reviewUserDataSourceResponse, decrypter);
        assertEquals(expectedPan, panNumber);
    }

    @Test
    public void testGetPanNumberProfileHasPan() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setPan("ABCD");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(decrypter.decryptString(profileDetailedResponse.getPan())).thenReturn(profileDetailedResponse.getPan());
        String panNumber = formWidgetDataFetcher.getPanNumber(reviewUserDataSourceResponse, decrypter);
        assertEquals(profileDetailedResponse.getPan(), panNumber);
        verify(decrypter, times(1)).decryptString(profileDetailedResponse.getPan());
    }

    @Test
    public void testPhoneNumberWhenProfileExists() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setPhoneNo("9191919191");
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String phoneNumber = formWidgetDataFetcher.getPhoneNumber(leadPageDataSourceResponse);
        assertEquals("9191919191", phoneNumber);
    }

    @Test
    public void testPhoneNumberWhenProfileDoesNotExists() {
        when(leadPageDataSourceResponse.getProfile()).thenReturn(null);
        String phoneNumber = formWidgetDataFetcher.getPhoneNumber(leadPageDataSourceResponse);
        assertEquals("", phoneNumber);
    }

    @Test
    public void testBonusIncomeWhenProfileDoesntExist() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        Integer phoneNumber = formWidgetDataFetcher.getBonusIncome(reviewUserDataSourceResponse);
        assertEquals(0, (int) phoneNumber);
    }

    @Test
    public void testBonusIncomeWhenProfileDoesExistButBonusIncomeIsNull() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setBonusIncome(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        Integer phoneNumber = formWidgetDataFetcher.getBonusIncome(reviewUserDataSourceResponse);
        assertEquals(0, (int) phoneNumber);
    }

    @Test
    public void testBonusIncomeWhenProfileDoesExistButBonusIncomeIsValid() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setBonusIncome(1200);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        Integer phoneNumber = formWidgetDataFetcher.getBonusIncome(reviewUserDataSourceResponse);
        assertEquals(1200, (int) phoneNumber);
    }

    @Test
    public void testGetGenderStringWhenProfileNotFound() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String gender = formWidgetDataFetcher.getGenderString(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderStringWhenProfileFoundButGenderIsNull() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setGender(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String gender = formWidgetDataFetcher.getGenderString(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderStringWhenProfileFoundButGenderIsF() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setGender("F");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String gender = formWidgetDataFetcher.getGenderString(reviewUserDataSourceResponse);
        assertEquals("Female", gender);
    }

    @Test
    public void testGetGenderStringWhenProfileFoundButGenderIsM() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setGender("M");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String gender = formWidgetDataFetcher.getGenderString(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderStringWhenProfileFoundButGenderIsO() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setGender("O");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String gender = formWidgetDataFetcher.getGenderString(reviewUserDataSourceResponse);
        assertEquals("Others", gender);
    }

    @Test
    public void testGetGenderString2WhenUserDataResponseNotFound() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        String gender = formWidgetDataFetcher.getGenderString2(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderString2WhenUserDataResponseNotFoundButGenderIsNull() {
        InitialUserDataResponse initialUserDataResponse = new InitialUserDataResponse();
        initialUserDataResponse.setGender(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        String gender = formWidgetDataFetcher.getGenderString2(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderString2WhenProfileFoundButGenderIsF() {
        InitialUserDataResponse initialUserDataResponse = new InitialUserDataResponse();
        initialUserDataResponse.setGender("2");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        String gender = formWidgetDataFetcher.getGenderString2(reviewUserDataSourceResponse);
        assertEquals("Female", gender);
    }

    @Test
    public void testGetGenderString2WhenProfileFoundButGenderIsM() {
        InitialUserDataResponse initialUserDataResponse = new InitialUserDataResponse();
        initialUserDataResponse.setGender("1");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        String gender = formWidgetDataFetcher.getGenderString2(reviewUserDataSourceResponse);
        assertEquals("Male", gender);
    }

    @Test
    public void testGetGenderString2WhenProfileFoundButGenderIsO() {
        InitialUserDataResponse initialUserDataResponse = new InitialUserDataResponse();
        initialUserDataResponse.setGender("3");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        String gender = formWidgetDataFetcher.getGenderString2(reviewUserDataSourceResponse);
        assertEquals("Others", gender);
    }

    @Test
    public void testGetCompanyNameWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String companyName = formWidgetDataFetcher.getCompanyName(reviewUserDataSourceResponse);
        assertEquals("", companyName);
    }

    @Test
    public void testGetCompanyNameWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setCompanyName("ABCD");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String companyName = formWidgetDataFetcher.getCompanyName(reviewUserDataSourceResponse);
        assertEquals("ABCD", companyName);
    }

    @Test
    public void testGetOrganizationIdWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String organizationId = formWidgetDataFetcher.getOrganizationId(reviewUserDataSourceResponse);
        assertEquals("", organizationId);
    }

    @Test
    public void testGetOrganizationIdWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setOrganizationId("ABCD");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String organizationId = formWidgetDataFetcher.getOrganizationId(reviewUserDataSourceResponse);
        assertEquals("ABCD", organizationId);
    }

    @Test
    public void testGetIndustryNameWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String industryName = formWidgetDataFetcher.getOrganizationId(reviewUserDataSourceResponse);
        assertEquals("", industryName);
    }

    @Test
    public void testGetIndustryNameWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setIndustryType("ABCD");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String industryName = formWidgetDataFetcher.getIndustryName(reviewUserDataSourceResponse);
        assertEquals("ABCD", industryName);
    }

    @Test
    public void testGetIndustryIdWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String industryId = formWidgetDataFetcher.getIndustryId(reviewUserDataSourceResponse);
        assertEquals("", industryId);
    }

    @Test
    public void testGetIndustryIdWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setIndustryId("ABCD");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String industryId = formWidgetDataFetcher.getIndustryId(reviewUserDataSourceResponse);
        assertEquals("ABCD", industryId);
    }

    @Test
    public void testGetMonthlyIncomeWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String monthlyIncome = formWidgetDataFetcher.getMonthlyIncome(reviewUserDataSourceResponse);
        assertEquals("", monthlyIncome);
    }

    @Test
    public void testGetMonthlyIncomeWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setMonthlyIncome(123);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String monthlyIncome = formWidgetDataFetcher.getMonthlyIncome(reviewUserDataSourceResponse);
        assertEquals("123", monthlyIncome);
    }

    @Test
    public void testGetIncomeSourceWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String incomeSource = formWidgetDataFetcher.getIncomeSource(reviewUserDataSourceResponse);
        assertEquals("", incomeSource);
    }

    @Test
    public void testGetIncomeSourceWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setIncomeSource(IncomeSource.ONLINE);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String incomeSource = formWidgetDataFetcher.getIncomeSource(reviewUserDataSourceResponse);
        assertEquals(IncomeSource.ONLINE.name(), incomeSource);
    }

    @Test
    public void testGetEmploymentTypeWhenProfileIsNull() {
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String employmentType = formWidgetDataFetcher.getEmploymentType(reviewUserDataSourceResponse);
        assertEquals("", employmentType);
    }

    @Test
    public void testGetEmploymentTypeWhenProfileIsNot() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setEmploymentType(EmploymentType.Salaried);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String employmentType = formWidgetDataFetcher.getEmploymentType(reviewUserDataSourceResponse);
        assertEquals(EmploymentType.Salaried.name(), employmentType);
    }

    @Test
    public void testGetEmailProfileNullAndUserResponseNull() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String email = formWidgetDataFetcher.getEmail(reviewUserDataSourceResponse, decrypter);
        assertEquals("", email);
    }

    @Test
    public void testGetEmailOnlyProfileNull() {
        String expectedEmail = "abcd";
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(initialUserDataResponse.getEmail()).thenReturn(expectedEmail);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String email = formWidgetDataFetcher.getEmail(reviewUserDataSourceResponse, decrypter);
        assertEquals(expectedEmail, email);
    }

    @Test
    public void testGetEmailProfileHasEmail() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setEmail("ABCD");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(decrypter.decryptString(profileDetailedResponse.getEmail())).thenReturn(profileDetailedResponse.getEmail());
        String email = formWidgetDataFetcher.getEmail(reviewUserDataSourceResponse, decrypter);
        assertEquals(profileDetailedResponse.getEmail(), email);
        verify(decrypter, times(1)).decryptString(profileDetailedResponse.getEmail());
    }

    @Test
    public void testGetPinCodeWhenProfileIsNullAndUserDataResponseIsNull(){
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String pincodeDetailsResponse = formWidgetDataFetcher.getPincode(reviewUserDataSourceResponse);
        assertEquals(pincodeDetailsResponse, "");
    }

    @Test
    public void testGetPinCodeWhenProfileIsNull() {
        AddressDetailResponse addressDetailResponse = new AddressDetailResponse();
        addressDetailResponse.setPincode("560102");
        when(initialUserDataResponse.getAddressDetailResponse()).thenReturn(addressDetailResponse);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String pincodeDetailsResponse = formWidgetDataFetcher.getPincode(reviewUserDataSourceResponse);
        assertEquals(pincodeDetailsResponse, "560102");
    }

    @Test
    public void testGetPinCodeWhenProfileIsNotNull() throws PinakaException {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setUserEnteredPincode(560102);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        PincodeDetailsResponse pincodeDetailsResponse2 = new PincodeDetailsResponse();
        pincodeDetailsResponse2.setPincode("560102");
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String pincodeDetailsResponse = formWidgetDataFetcher.getPincode(reviewUserDataSourceResponse);
        assertEquals("560102", pincodeDetailsResponse);
    }

    @Test
    public void testGetAddressLineOneWhenProfileIsNullAndUserDataResponseIsNull(){
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineOne = formWidgetDataFetcher.getAddressLineOne(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineOne);
    }

    @Test
    public void testGetAddressLineOneWhenProfileIsNullAndAddressDetailIsNotPresent(){
        when(initialUserDataResponse.getAddressDetailResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineOne = formWidgetDataFetcher.getAddressLineOne(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineOne);
    }

    @Test
    public void testGetAddressLineOneWhenProfileIsNull(){
        AddressDetailResponse addressDetailResponse = new AddressDetailResponse();
        addressDetailResponse.setAddressLine1("Line1");
        when(initialUserDataResponse.getAddressDetailResponse()).thenReturn(addressDetailResponse);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineOne = formWidgetDataFetcher.getAddressLineOne(reviewUserDataSourceResponse, decrypter);
        assertEquals("Line1", addressLineOne);
    }

    @Test
    public void testGetAddressLineOneWhenProfileIsNotNullAndAddressLine1IsNull(){
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setAddressLine1(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String addressLineOne = formWidgetDataFetcher.getAddressLineOne(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineOne);
    }

    @Test
    public void testGetAddressLineOneWhenProfileIsNotNull(){
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setAddressLine1("Line1");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(decrypter.decryptString("Line1")).thenReturn("Line1");
        String addressLineOne = formWidgetDataFetcher.getAddressLineOne(reviewUserDataSourceResponse, decrypter);
        assertEquals("Line1", addressLineOne);
        verify(decrypter, times(1)).decryptString("Line1");
    }

    @Test
    public void testGetAddressLineTwoWhenProfileIsNullAndUserDataResponseIsNull(){
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineTwo = formWidgetDataFetcher.getAddressLineTwo(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineTwo);
    }

    @Test
    public void testGetAddressLineTwoWhenProfileIsNullAndAddressDetailIsNotPresent(){
        when(initialUserDataResponse.getAddressDetailResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineTwo = formWidgetDataFetcher.getAddressLineTwo(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineTwo);
    }

    @Test
    public void testGetAddressLineTwoWhenProfileIsNull(){
        AddressDetailResponse addressDetailResponse = new AddressDetailResponse();
        addressDetailResponse.setAddressLine2("Line1");
        when(initialUserDataResponse.getAddressDetailResponse()).thenReturn(addressDetailResponse);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String addressLineTwo = formWidgetDataFetcher.getAddressLineTwo(reviewUserDataSourceResponse, decrypter);
        assertEquals("Line1", addressLineTwo);
    }

    @Test
    public void testGetAddressLineTwoWhenProfileIsNotNullAndAddressLine1IsNull(){
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setAddressLine2(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String addressLineTwo = formWidgetDataFetcher.getAddressLineTwo(reviewUserDataSourceResponse, decrypter);
        assertEquals("", addressLineTwo);
    }

    @Test
    public void testGetAddressLineTwoWhenProfileIsNotNull(){
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setAddressLine2("Line1");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        when(decrypter.decryptString("Line1")).thenReturn("Line1");
        String addressLineTwo = formWidgetDataFetcher.getAddressLineTwo(reviewUserDataSourceResponse, decrypter);
        assertEquals("Line1", addressLineTwo);
        verify(decrypter, times(1)).decryptString("Line1");
    }

    @Test
    public void testGetAvailableAddressesWhenInitialUserResponseIsNull() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        List<CAISHolderAddressDetails> availableAddresses = formWidgetDataFetcher.getAvailableAddresses(reviewUserDataSourceResponse);
        assertEquals(0, availableAddresses.size());
    }

    @Test
    public void testGetAvailableAddressesWhenExperianAddressesAreNull() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(initialUserDataResponse.getExperianAddressDetails()).thenReturn(null);
        List<CAISHolderAddressDetails> availableAddresses = formWidgetDataFetcher.getAvailableAddresses(reviewUserDataSourceResponse);
        assertEquals(0, availableAddresses.size());
    }

    @Test
    public void testGetAvailableAddresses() {
        List<CAISHolderAddressDetails> addressDetails = new ArrayList<>();
        addressDetails.add(new CAISHolderAddressDetails());
        addressDetails.add(new CAISHolderAddressDetails());
        addressDetails.add(new CAISHolderAddressDetails());
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(initialUserDataResponse.getExperianAddressDetails()).thenReturn(addressDetails);
        List<CAISHolderAddressDetails> availableAddresses = formWidgetDataFetcher.getAvailableAddresses(reviewUserDataSourceResponse);
        assertEquals(3, availableAddresses.size());
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNullAndUserDataResponseIsNull() {
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNullAndUserDataResponseDateOfBirthIsNull() {
        when(initialUserDataResponse.getDateOfBirth()).thenReturn(null);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNullAndUserDataResponseDateOfBirthIsEmpty() {
        when(initialUserDataResponse.getDateOfBirth()).thenReturn("");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(null);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNullAndUserDataResponseDateOfBirthIsInValid() {
        when(initialUserDataResponse.getDateOfBirth()).thenReturn("INVALID");
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setSmUserId("abdc");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNullAndUserDataResponseDateOfBirthIsValid() {
        String date = "19/05/1990";
        when(initialUserDataResponse.getDateOfBirth()).thenReturn(date);
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setSmUserId("abdc");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(initialUserDataResponse);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals(date, dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNotNullButDOBIsNull() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNotNullButDOBIsInvalid() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        profileDetailedResponse.setDob("INVALID");
        when(decrypter.decryptString("INVALID")).thenReturn("INVALID");
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals("", dateOfBirth);
    }

    @Test
    public void testGetDateOfBirthWhenProfileIsNotNullButDOBValid() {
        ProfileDetailedResponse profileDetailedResponse = new ProfileDetailedResponse();
        String dob = "19/02/1998";
        profileDetailedResponse.setDob(dob);
        when(decrypter.decryptString(dob)).thenReturn(dob);
        when(reviewUserDataSourceResponse.getInitialUserDataResponse()).thenReturn(null);
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profileDetailedResponse);
        String dateOfBirth = formWidgetDataFetcher.getDateOfBirth(reviewUserDataSourceResponse, decrypter);
        assertEquals(dob, dateOfBirth);
    }
}
