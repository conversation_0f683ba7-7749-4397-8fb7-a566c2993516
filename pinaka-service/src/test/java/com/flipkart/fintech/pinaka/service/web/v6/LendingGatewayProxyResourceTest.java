package com.flipkart.fintech.pinaka.service.web.v6;

import com.flipkart.fintech.lending.orchestrator.service.LendingGatewayService;
import com.flipkart.fintech.pinaka.api.request.v6.LandingPageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class LendingGatewayProxyResourceTest {
    @Mock
    private LendingGatewayService lendingGatewayService;

    @InjectMocks
    private LendingGatewayProxyResource resource;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testResumeJourneySuccess() {
        LandingPageRequest request = new LandingPageRequest();
        request.setMerchantId("merchant1");
        PageActionResponse expectedResponse = new PageActionResponse();
        when(lendingGatewayService.getPage(anyString(), any())).thenReturn(expectedResponse);

        PageActionResponse response = resource.resumeJourney(request, "reqId", "merchant1", "userAgent");
        assertEquals(expectedResponse, response);
    }

    @Test
    public void testResumeJourneyException() {
        LandingPageRequest request = new LandingPageRequest();
        request.setMerchantId("merchant1");
        when(lendingGatewayService.getPage(anyString(), any())).thenThrow(new RuntimeException("fail"));

        PageActionResponse response = resource.resumeJourney(request, "reqId", "merchant1", "userAgent");
        assertFalse(response.getActionSuccess());
        assertNotNull(response.getError());
        assertEquals("Error while creating application", response.getError().getMessage());
    }

    @Test
    public void testSubmitSuccess() {
        UserActionRequest submitRequest = new FormSubmitRequest();
        PageActionResponse expectedResponse = new PageActionResponse();
        when(lendingGatewayService.submit(any())).thenReturn(expectedResponse);

        PageActionResponse response = resource.submit(submitRequest, "userAgent", "reqId");
        assertEquals(expectedResponse, response);
    }

    @Test(expected = RuntimeException.class)
    public void testSubmitException() {
        UserActionRequest submitRequest = new FormSubmitRequest();
        when(lendingGatewayService.submit(any())).thenThrow(new RuntimeException("submit error"));

        resource.submit(submitRequest, "userAgent", "reqId");
    }
}
