package com.flipkart.fintech.pinaka.service.utils;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class PlatformDetectionUtilTest {

    @Test
    public void testDetectPlatform_Android() {
        String androidUA = "Mozilla/5.0 (Linux; Android 14; sdk_gphone64_arm64 Build/UPB5.230623.003) FKUA/Retail/1840005/Android/Mobile";
        String result = PlatformDetectionUtil.detectPlatform(androidUA);
        assertEquals("android", result);
    }

    @Test
    public void testDetectPlatform_iOS() {
        String iosUA = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) FKUA/Retail/1840005/iOS/Mobile";
        String result = PlatformDetectionUtil.detectPlatform(iosUA);
        assertEquals("ios", result);
    }

    @Test
    public void testDetectPlatform_Web() {
        String webUA = "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.3/msite/Mobile";
        String result = PlatformDetectionUtil.detectPlatform(webUA);
        assertEquals("web", result);
    }

    @Test
    public void testDetectPlatform_BlankUserAgent() {
        String result = PlatformDetectionUtil.detectPlatform("");
        assertEquals("web", result);
    }

    @Test
    public void testDetectPlatform_NullUserAgent() {
        String result = PlatformDetectionUtil.detectPlatform(null);
        assertEquals("web", result);
    }

    @Test
    public void testDetectPlatform_GenericAndroid() {
        String androidUA = "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36";
        String result = PlatformDetectionUtil.detectPlatform(androidUA);
        assertEquals("android", result);
    }

    @Test
    public void testDetectPlatform_GenericIOS() {
        String iosUA = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15";
        String result = PlatformDetectionUtil.detectPlatform(iosUA);
        assertEquals("ios", result);
    }

    @Test
    public void testDetectPlatform_iPad() {
        String iPadUA = "Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15";
        String result = PlatformDetectionUtil.detectPlatform(iPadUA);
        assertEquals("ios", result);
    }

    @Test
    public void testDetectPlatform_iPod() {
        String iPodUA = "Mozilla/5.0 (iPod touch; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15";
        String result = PlatformDetectionUtil.detectPlatform(iPodUA);
        assertEquals("ios", result);
    }

    @Test
    public void testDetectPlatform_AndroidWithoutFKUA() {
        String androidUA = "Mozilla/5.0 (Linux; Android 11; SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36";
        String result = PlatformDetectionUtil.detectPlatform(androidUA);
        assertEquals("android", result);
    }

    @Test
    public void testDetectPlatform_iOSWithoutFKUA() {
        String iosUA = "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1";
        String result = PlatformDetectionUtil.detectPlatform(iosUA);
        assertEquals("ios", result);
    }

    @Test
    public void testDetectPlatform_DesktopChrome() {
        String desktopUA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
        String result = PlatformDetectionUtil.detectPlatform(desktopUA);
        assertEquals("web", result);
    }

    @Test
    public void testDetectPlatform_MacSafari() {
        String macUA = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15";
        String result = PlatformDetectionUtil.detectPlatform(macUA);
        assertEquals("web", result);
    }

    @Test
    public void testIsAndroid_True() {
        String androidUA = "Mozilla/5.0 (Linux; Android 14; sdk_gphone64_arm64) FKUA/Retail/1840005/Android/Mobile";
        assertTrue(PlatformDetectionUtil.isAndroid(androidUA));
    }

    @Test
    public void testIsAndroid_False() {
        String iosUA = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) FKUA/Retail/1840005/iOS/Mobile";
        assertFalse(PlatformDetectionUtil.isAndroid(iosUA));
    }

    @Test
    public void testIsIOS_True() {
        String iosUA = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) FKUA/Retail/1840005/iOS/Mobile";
        assertTrue(PlatformDetectionUtil.isIOS(iosUA));
    }

    @Test
    public void testIsIOS_False() {
        String androidUA = "Mozilla/5.0 (Linux; Android 14; sdk_gphone64_arm64) FKUA/Retail/1840005/Android/Mobile";
        assertFalse(PlatformDetectionUtil.isIOS(androidUA));
    }

    @Test
    public void testIsWeb_True() {
        String webUA = "Mozilla/5.0 (Linux; Android 6.0; Nexus 5) FKUA/msite/0.0.3/msite/Mobile";
        assertTrue(PlatformDetectionUtil.isWeb(webUA));
    }

    @Test
    public void testIsWeb_False() {
        String androidUA = "Mozilla/5.0 (Linux; Android 14; sdk_gphone64_arm64) FKUA/Retail/1840005/Android/Mobile";
        assertFalse(PlatformDetectionUtil.isWeb(androidUA));
    }

    @Test
    public void testIsWeb_DefaultForUnknown() {
        String unknownUA = "Some unknown user agent string";
        assertTrue(PlatformDetectionUtil.isWeb(unknownUA));
    }

    @Test
    public void testPlatformConstants() {
        assertEquals("android", PlatformDetectionUtil.PLATFORM_ANDROID);
        assertEquals("ios", PlatformDetectionUtil.PLATFORM_IOS);
        assertEquals("web", PlatformDetectionUtil.PLATFORM_WEB);
    }
}
