package com.flipkart.fintech.pinaka.service.widgettransformer.creditLine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.service.response.CreditLineFormPageDataSourceResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.CustomFormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.flipkart.rome.datatypes.response.fintech.insurtech.TextBoxFormFieldValueV0;
import com.flipkart.fintech.pinaka.client.PinakaClientException;

import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class CreditLineAddressFormPageTransformerTest {
    private CreditLineAddressPageFormTransformer transformer;
    private CreditLineFormPageDataSourceResponse response;
    private ProfileDetailedResponse profileDetailedResponse;
    private ApplicationDataResponse applicationDataResponse;

    @Before
    public void setup() throws PinakaClientException {
        profileDetailedResponse = mock(ProfileDetailedResponse.class);
        response = mock(CreditLineFormPageDataSourceResponse.class);

        when(response.getProfile()).thenReturn(profileDetailedResponse);
        when(profileDetailedResponse.getAddressLine1()).thenReturn("Jl7hc+RdjwfbB263JpAOO3VWnphJ+QQjpqu66QyObSo=");
        when(profileDetailedResponse.getAddressLine2()).thenReturn("MkJp5ojl5leq5uOXIMw5cQ==");
        when(profileDetailedResponse.getUserEnteredPincode()).thenReturn(104591);

        transformer = new CreditLineAddressPageFormTransformer(response);
    }

    @Test
    public void testBuildWidgetData_shouldPrefillAddressFields() throws PinakaClientException, JsonProcessingException {
        applicationDataResponse = new ApplicationDataResponse();

        GenericFormWidgetData widgetData = transformer.buildWidgetData(applicationDataResponse);

        List<RenderableComponent<FormFieldValue>> renderableComponents = widgetData.getRenderableComponents();

        int count = 0;
        for (RenderableComponent<FormFieldValue> component : renderableComponents) {
            if (count == 3) {
                continue;
            }

            FormFieldValue value = component.getValue();

            if (value instanceof TextBoxFormFieldValueV0) {
                TextBoxFormFieldValueV0 textBoxValue = (TextBoxFormFieldValueV0) value;

                switch (textBoxValue.getName()) {
                    case "line1":
                        assertEquals("Near hp gas godaun", textBoxValue.getValue());
                        break;
                    case "line2":
                        assertEquals("yeola road", textBoxValue.getValue());
                        break;
                    default:
                        fail("Unexpected TextBox field: " + textBoxValue.getName());
                }

            } else if (value instanceof CustomFormFieldValue) {
                CustomFormFieldValue customValue = (CustomFormFieldValue) value;

                if ("pincodeDetails".equals(customValue.getName())) {
                    assertEquals("104591", customValue.getValue());
                } else {
                    fail("Unexpected CustomForm field: " + customValue.getName());
                }

            } else {
                fail("Unknown field type: " + value.getClass().getName());
            }

            count++;
        }
    }
}
