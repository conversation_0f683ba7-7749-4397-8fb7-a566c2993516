package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.profile.service.BureauDataManager;
import com.flipkart.fintech.profile.service.ProfileService;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.utils.UrlUtil;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
    UrlUtil.class,
    MerchantUserUtils.class,
    QueryParamUtils.class
})
public class InitialUserReviewDataSourceTest {

    @Mock
    private BureauDataManager bureauDataManager;
    
    @Mock
    private ProfileService profileService;
    
    @Mock
    private LocationRequestHandler locationRequestHandler;
    
    @Mock
    private ApplicationDataResponse applicationDataResponse;
    
    @Mock
    private MerchantUser merchantUser;
    
    @Mock
    private InitialUserDataResponse initialUserDataResponse;
    
    @Mock
    private ProfileDetailedResponse profileDetailedResponse;
    
    @Mock
    private AddressDetailResponse addressDetailResponse;

    private InitialUserReviewDataSource initialUserReviewDataSource;

    @Before
    public void setUp() throws Exception {
        // Mock static classes
        mockStatic(UrlUtil.class);
        mockStatic(MerchantUserUtils.class);
        mockStatic(QueryParamUtils.class);
        
        // Create instance and inject mocks using reflection
        initialUserReviewDataSource = new InitialUserReviewDataSource();
        
        // Use reflection to inject static mocks
        java.lang.reflect.Field bureauDataManagerField = InitialUserReviewDataSource.class.getDeclaredField("bureauDataManager");
        bureauDataManagerField.setAccessible(true);
        bureauDataManagerField.set(null, bureauDataManager);
        
        java.lang.reflect.Field profileServiceField = InitialUserReviewDataSource.class.getDeclaredField("profileService");
        profileServiceField.setAccessible(true);
        profileServiceField.set(null, profileService);
        
        java.lang.reflect.Field locationRequestHandlerField = InitialUserReviewDataSource.class.getDeclaredField("locationRequestHandler");
        locationRequestHandlerField.setAccessible(true);
        locationRequestHandlerField.set(null, locationRequestHandler);
    }

    @Test
    public void testGetData_ApplicationDataResponse_Success() throws Exception {
        // Arrange
        String externalUserId = "external123";
        String smUserId = "sm123";
        List<NameValuePair> queryParams = new ArrayList<>();
        queryParams.add(new BasicNameValuePair("param1", "value1"));
        Map<String, Object> queryParamsMap = new HashMap<>();
        queryParamsMap.put("param1", "value1");

        when(applicationDataResponse.getExternalUserId()).thenReturn(externalUserId);
        when(applicationDataResponse.getSmUserId()).thenReturn(smUserId);
        when(UrlUtil.getQueryParams(applicationDataResponse)).thenReturn(queryParams);
        when(MerchantUserUtils.getMerchantUser(externalUserId, smUserId)).thenReturn(merchantUser);
        when(QueryParamUtils.getQueryParams(queryParams)).thenReturn(queryParamsMap);

        setupMocksForSuccessfulResponse();

        // Act
        ReviewUserDataSourceResponse result = initialUserReviewDataSource.getData(applicationDataResponse);

        // Assert
        assertNotNull(result);
        assertEquals(queryParamsMap, result.getQueryParams());
        assertEquals(initialUserDataResponse, result.getInitialUserDataResponse());
        assertEquals(profileDetailedResponse, result.getProfile());
    }

    @Test
    public void testGetData_MerchantUserAndQueryParams_Success() throws Exception {
        // Arrange
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("param1", "value1");
        
        setupMocksForSuccessfulResponse();

        // Act
        ReviewUserDataSourceResponse result = initialUserReviewDataSource.getData(merchantUser, queryParams);

        // Assert
        assertNotNull(result);
        assertEquals(queryParams, result.getQueryParams());
        assertEquals(initialUserDataResponse, result.getInitialUserDataResponse());
        assertEquals(profileDetailedResponse, result.getProfile());
    }

    @Test
    public void testGetData_WithFetchProfileFalse_Success() throws Exception {
        // Arrange
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("param1", "value1");
        
        setupMocksForSuccessfulResponseWithoutProfile();

        // Act
        ReviewUserDataSourceResponse result = initialUserReviewDataSource.getData(merchantUser, queryParams, false);

        // Assert
        assertNotNull(result);
        assertEquals(queryParams, result.getQueryParams());
        assertEquals(initialUserDataResponse, result.getInitialUserDataResponse());
        assertNull(result.getProfile());
        
        // Verify profile service was not called
        verify(profileService, never()).getProfileByUserId(anyString(), anyString(), anyBoolean());
    }

    @Test
    public void testGetData_WithValidAddress_SetsAddressInResponse() throws Exception {
        // Arrange
        Map<String, Object> queryParams = new HashMap<>();
        String merchantUserId = "merchant123";
        String merchantKey = "FLIPKART";
        String smUserId = "sm123";
        
        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getMerchantKey()).thenReturn(merchantKey);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);
        
        // Setup address with all required fields
        when(addressDetailResponse.getAddressLine1()).thenReturn("Address Line 1");
        when(addressDetailResponse.getAddressLine2()).thenReturn("Address Line 2");
        when(addressDetailResponse.getPincode()).thenReturn("123456");
        
        when(locationRequestHandler.getUserAddress(eq(merchantUserId), eq(merchantKey), eq(smUserId), anyString()))
            .thenReturn(addressDetailResponse);
        when(bureauDataManager.initialUserData(smUserId)).thenReturn(initialUserDataResponse);
        when(profileService.getProfileByUserId(merchantUserId, smUserId, false)).thenReturn(profileDetailedResponse);

        // Act
        ReviewUserDataSourceResponse result = initialUserReviewDataSource.getData(merchantUser, queryParams, true);

        // Assert
        assertNotNull(result);
        verify(initialUserDataResponse).setAddressDetailResponse(addressDetailResponse);
    }

    @Test
    public void testGetData_WithIncompleteAddress_DoesNotSetAddress() throws Exception {
        // Arrange
        Map<String, Object> queryParams = new HashMap<>();
        String merchantUserId = "merchant123";
        String merchantKey = "FLIPKART";
        String smUserId = "sm123";
        
        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getMerchantKey()).thenReturn(merchantKey);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);
        
        // Setup address with missing fields
        when(addressDetailResponse.getAddressLine1()).thenReturn("Address Line 1");
        when(addressDetailResponse.getAddressLine2()).thenReturn(null); // Missing address line 2
        when(addressDetailResponse.getPincode()).thenReturn("123456");
        
        when(locationRequestHandler.getUserAddress(eq(merchantUserId), eq(merchantKey), eq(smUserId), anyString()))
            .thenReturn(addressDetailResponse);
        when(bureauDataManager.initialUserData(smUserId)).thenReturn(initialUserDataResponse);
        when(profileService.getProfileByUserId(merchantUserId, smUserId, false)).thenReturn(profileDetailedResponse);

        // Act
        ReviewUserDataSourceResponse result = initialUserReviewDataSource.getData(merchantUser, queryParams, true);

        // Assert
        assertNotNull(result);
        verify(initialUserDataResponse, never()).setAddressDetailResponse(any());
    }

    @Test
    public void testGetData_ProfileServiceThrowsException_ContinuesExecution() throws Exception {
        // Arrange
        Map<String, Object> queryParams = new HashMap<>();
        String merchantUserId = "merchant123";
        String merchantKey = "FLIPKART";
        String smUserId = "sm123";
        
        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getMerchantKey()).thenReturn(merchantKey);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);
        
        when(locationRequestHandler.getUserAddress(eq(merchantUserId), eq(merchantKey), eq(smUserId), anyString()))
            .thenReturn(addressDetailResponse);
        when(bureauDataManager.initialUserData(smUserId)).thenReturn(initialUserDataResponse);
        when(profileService.getProfileByUserId(merchantUserId, smUserId, false))
            .thenThrow(new RuntimeException("Profile service error"));

        // Act
        ReviewUserDataSourceResponse result = initialUserReviewDataSource.getData(merchantUser, queryParams, true);

        // Assert
        assertNotNull(result);
        assertEquals(queryParams, result.getQueryParams());
        assertEquals(initialUserDataResponse, result.getInitialUserDataResponse());
        assertNull(result.getProfile()); // Profile should be null due to exception
    }

    private void setupMocksForSuccessfulResponse() throws Exception {
        String merchantUserId = "merchant123";
        String merchantKey = "FLIPKART";
        String smUserId = "sm123";
        
        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getMerchantKey()).thenReturn(merchantKey);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);
        
        when(locationRequestHandler.getUserAddress(eq(merchantUserId), eq(merchantKey), eq(smUserId), anyString()))
            .thenReturn(addressDetailResponse);
        when(bureauDataManager.initialUserData(smUserId)).thenReturn(initialUserDataResponse);
        when(profileService.getProfileByUserId(merchantUserId, smUserId, false)).thenReturn(profileDetailedResponse);
    }

    private void setupMocksForSuccessfulResponseWithoutProfile() throws Exception {
        String merchantUserId = "merchant123";
        String merchantKey = "FLIPKART";
        String smUserId = "sm123";

        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getMerchantKey()).thenReturn(merchantKey);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);

        when(locationRequestHandler.getUserAddress(eq(merchantUserId), eq(merchantKey), eq(smUserId), anyString()))
            .thenReturn(addressDetailResponse);
        when(bureauDataManager.initialUserData(smUserId)).thenReturn(initialUserDataResponse);
    }

    @Test
    public void testGetData_WithNullAddressLine1_DoesNotSetAddress() throws Exception {
        // Arrange
        Map<String, Object> queryParams = new HashMap<>();
        String merchantUserId = "merchant123";
        String merchantKey = "FLIPKART";
        String smUserId = "sm123";

        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getMerchantKey()).thenReturn(merchantKey);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);

        // Setup address with null address line 1
        when(addressDetailResponse.getAddressLine1()).thenReturn(null);
        when(addressDetailResponse.getAddressLine2()).thenReturn("Address Line 2");
        when(addressDetailResponse.getPincode()).thenReturn("123456");

        when(locationRequestHandler.getUserAddress(eq(merchantUserId), eq(merchantKey), eq(smUserId), anyString()))
            .thenReturn(addressDetailResponse);
        when(bureauDataManager.initialUserData(smUserId)).thenReturn(initialUserDataResponse);
        when(profileService.getProfileByUserId(merchantUserId, smUserId, false)).thenReturn(profileDetailedResponse);

        // Act
        ReviewUserDataSourceResponse result = initialUserReviewDataSource.getData(merchantUser, queryParams, true);

        // Assert
        assertNotNull(result);
        verify(initialUserDataResponse, never()).setAddressDetailResponse(any());
    }

    @Test
    public void testGetData_WithNullPincode_DoesNotSetAddress() throws Exception {
        // Arrange
        Map<String, Object> queryParams = new HashMap<>();
        String merchantUserId = "merchant123";
        String merchantKey = "FLIPKART";
        String smUserId = "sm123";

        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getMerchantKey()).thenReturn(merchantKey);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);

        // Setup address with null pincode
        when(addressDetailResponse.getAddressLine1()).thenReturn("Address Line 1");
        when(addressDetailResponse.getAddressLine2()).thenReturn("Address Line 2");
        when(addressDetailResponse.getPincode()).thenReturn(null);

        when(locationRequestHandler.getUserAddress(eq(merchantUserId), eq(merchantKey), eq(smUserId), anyString()))
            .thenReturn(addressDetailResponse);
        when(bureauDataManager.initialUserData(smUserId)).thenReturn(initialUserDataResponse);
        when(profileService.getProfileByUserId(merchantUserId, smUserId, false)).thenReturn(profileDetailedResponse);

        // Act
        ReviewUserDataSourceResponse result = initialUserReviewDataSource.getData(merchantUser, queryParams, true);

        // Assert
        assertNotNull(result);
        verify(initialUserDataResponse, never()).setAddressDetailResponse(any());
    }

    @Test
    public void testGetData_LocationRequestHandlerReturnsEmptyAddress_ContinuesExecution() throws Exception {
        // Arrange
        Map<String, Object> queryParams = new HashMap<>();
        String merchantUserId = "merchant123";
        String merchantKey = "FLIPKART";
        String smUserId = "sm123";

        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getMerchantKey()).thenReturn(merchantKey);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);

        // LocationRequestHandler returns empty address when there's an error
        AddressDetailResponse emptyAddress = new AddressDetailResponse();
        when(locationRequestHandler.getUserAddress(eq(merchantUserId), eq(merchantKey), eq(smUserId), anyString()))
            .thenReturn(emptyAddress);
        when(bureauDataManager.initialUserData(smUserId)).thenReturn(initialUserDataResponse);
        when(profileService.getProfileByUserId(merchantUserId, smUserId, false)).thenReturn(profileDetailedResponse);

        // Act
        ReviewUserDataSourceResponse result = initialUserReviewDataSource.getData(merchantUser, queryParams, true);

        // Assert
        assertNotNull(result);
        assertEquals(queryParams, result.getQueryParams());
        assertEquals(initialUserDataResponse, result.getInitialUserDataResponse());
        assertEquals(profileDetailedResponse, result.getProfile());
        // Empty address should not be set on initialUserDataResponse
        verify(initialUserDataResponse, never()).setAddressDetailResponse(any());
    }

    @Test
    public void testGetData_BureauDataManagerThrowsException_PropagatesException() throws Exception {
        // Arrange
        Map<String, Object> queryParams = new HashMap<>();
        String merchantUserId = "merchant123";
        String merchantKey = "FLIPKART";
        String smUserId = "sm123";

        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getMerchantKey()).thenReturn(merchantKey);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);

        when(locationRequestHandler.getUserAddress(eq(merchantUserId), eq(merchantKey), eq(smUserId), anyString()))
            .thenReturn(addressDetailResponse);
        when(bureauDataManager.initialUserData(smUserId))
            .thenThrow(new RuntimeException("Bureau data error"));

        // Act & Assert
        try {
            initialUserReviewDataSource.getData(merchantUser, queryParams, true);
            fail("Expected exception to be thrown");
        } catch (RuntimeException e) {
            assertEquals("Bureau data error", e.getMessage());
        }
    }

    @Test
    public void testGetData_WithEmptyQueryParams_Success() throws Exception {
        // Arrange
        Map<String, Object> queryParams = new HashMap<>(); // Empty map

        setupMocksForSuccessfulResponse();

        // Act
        ReviewUserDataSourceResponse result = initialUserReviewDataSource.getData(merchantUser, queryParams, true);

        // Assert
        assertNotNull(result);
        assertEquals(queryParams, result.getQueryParams());
        assertEquals(initialUserDataResponse, result.getInitialUserDataResponse());
        assertEquals(profileDetailedResponse, result.getProfile());
    }

    @Test
    public void testGetData_WithNullQueryParams_Success() throws Exception {
        // Arrange
        Map<String, Object> queryParams = null;

        setupMocksForSuccessfulResponse();

        // Act
        ReviewUserDataSourceResponse result = initialUserReviewDataSource.getData(merchantUser, queryParams, true);

        // Assert
        assertNotNull(result);
        assertEquals(queryParams, result.getQueryParams());
        assertEquals(initialUserDataResponse, result.getInitialUserDataResponse());
        assertEquals(profileDetailedResponse, result.getProfile());
    }
}
