package com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.response.v6.PincodeDetailsResponse;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormGroupDataValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GroupedFormWidgetData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.mockito.Mockito.spy;
import static org.testng.AssertJUnit.assertEquals;
import static org.testng.AssertJUnit.assertNull;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        FormWidgetDataFetcher.class,
        FormWidgetDataJsonParser.class,
        FormWidgetDataPrefillUtils.class
})
public class FormWidgetDataPrefillUtilsTest {

    private static final String PHONE_NUMBER_STRING = "phoneNumber";
    private static final String FULL_NAME_STRING = "fullName";
    public static final String PAN_NUMBER = "panNumber";
    public static final String DOB_STRING = "dob";
    public static final String GENDER_STRING = "gender";
    public static final String GENDER_STRING_2 = "gender_2";
    public static final String COMPANY_NAME_STRING = "companyName";
    public static final String ORGANIZATION_ID_STRING = "organizationId";
    public static final String INDUSTRY_NAME_STRING = "industryName";
    public static final String INDUSTRY_ID_STRING = "industryId";
    public static final String HOUSE_NUMBER_STRING = "houseNumber";
    public static final String AREA_STRING = "area";
    public static final String PINCODE_DETAILS_STRING = "pincodeDetails";
    public static final String MONTHLY_INCOME_STRING = "monthlyIncome";
    public static final String INCOME_SOURCE_STRING = "incomeSource";
    public static final String EMPLOYMENT_TYPE_STRING = "employmentType";
    public static final String EMAIL_STRING = "email";
    public static final String EMPTY_PINCODE = "0";
    public static final String FEMALE_GENDER_STRING = "Female";
    public static final String OTHERS_GENDER_STRING = "Others";
    public static final String MALE_GENDER_STRING = "Male";
    public static final String ADDRESSES_STRING = "addresses";
    public static final String BONUS_INCOME_STRING = "bonusIncome";


    FormWidgetDataFetcher formWidgetDataFetcher;
    String FINAL_SCREEN_FORM_TEMPLATE = TransformerUtils.readFileasString("template/lead/V3/AllFilledReviewScreen.json");
    String NAME_PAGE_FORM_TEMPLATE = TransformerUtils.readFileasString("template/lead/V3/NamePage.json");
    String PERSONAL_DETAILS_PREFILLED_ONLY_TEMPLATE = TransformerUtils.readFileasString("template/lead/V3/Personal-Details-Prefilled-Without-Work-Address-Details.json");
    String NOTHING_PREFILLED_REVIEW_PAGE_2_TEMPLATE = TransformerUtils.readFileasString("template/lead/V3/Without-Personal-Without-Address.json");
    FormWidgetDataJsonParser formWidgetDataJsonParser = new FormWidgetDataJsonParser();

    @Before
    public void setup() throws JsonProcessingException {
        formWidgetDataFetcher = spy(new FormWidgetDataFetcher());
    }

    @Test
    public void testDataForAllPrefilledFields() throws JsonProcessingException {
        GroupedFormWidgetData groupedFormWidgetData = ObjectMapperUtil.get().readValue(FINAL_SCREEN_FORM_TEMPLATE, GroupedFormWidgetData.class);
        Map<String, FormFieldValue> formFieldValueMapToPrefill = this.formWidgetDataJsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        Map<String, FormGroupDataValue> groupFieldValueMapToPrefill = this.formWidgetDataJsonParser.getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        Map<String, Object> userData = getDataForFieldsAllPrefilled(formFieldValueMapToPrefill.keySet());
        FormWidgetDataPrefillUtils formWidgetDataPrefillUtils = new FormWidgetDataPrefillUtils();;
        formWidgetDataPrefillUtils.prefillGroupedFormFieldValues(groupFieldValueMapToPrefill, userData);
        formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);
        assertEquals("panNumber", groupedFormWidgetData.getFormGroups().get(0).getFormGroupData().get(0).getData().getName());
        assertNull(groupedFormWidgetData.getFormGroups().get(0).getFormGroupData().get(0).getData().getValue());
    }

    @Test
    public void testDataForAllPrefilledFieldsSelfEmployed() throws JsonProcessingException {
        GroupedFormWidgetData groupedFormWidgetData = ObjectMapperUtil.get().readValue(FINAL_SCREEN_FORM_TEMPLATE, GroupedFormWidgetData.class);
        Map<String, FormFieldValue> formFieldValueMapToPrefill = this.formWidgetDataJsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        Map<String, FormGroupDataValue> groupFieldValueMapToPrefill = this.formWidgetDataJsonParser.getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        Map<String, Object> userData = getDataForFieldsAllPrefilled(formFieldValueMapToPrefill.keySet());
        userData.put("employmentType", "SelfEmployed");
        FormWidgetDataPrefillUtils formWidgetDataPrefillUtils = new FormWidgetDataPrefillUtils();;
        formWidgetDataPrefillUtils.prefillGroupedFormFieldValues(groupFieldValueMapToPrefill, userData);
        formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);
        assertEquals("panNumber", groupedFormWidgetData.getFormGroups().get(0).getFormGroupData().get(0).getData().getName());
        assertEquals(null, groupedFormWidgetData.getFormGroups().get(0).getFormGroupData().get(0).getData().getValue());
    }

    @Test
    public void testDataForNothingPrefilledFieldsSelfEmployed() throws JsonProcessingException {
        GroupedFormWidgetData groupedFormWidgetData = ObjectMapperUtil.get().readValue(NOTHING_PREFILLED_REVIEW_PAGE_2_TEMPLATE, GroupedFormWidgetData.class);
        Map<String, FormFieldValue> formFieldValueMapToPrefill = this.formWidgetDataJsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        Map<String, FormGroupDataValue> groupFieldValueMapToPrefill = this.formWidgetDataJsonParser.getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        Map<String, Object> userData = getDataForFieldsNoPrefilled(formFieldValueMapToPrefill.keySet());
        userData.put("employmentType", "SelfEmployed");
        FormWidgetDataPrefillUtils formWidgetDataPrefillUtils = new FormWidgetDataPrefillUtils();;
        formWidgetDataPrefillUtils.prefillGroupedFormFieldValues(groupFieldValueMapToPrefill, userData);
        formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);
        assertEquals("panNumber", groupedFormWidgetData.getFormGroups().get(0).getFormGroupData().get(0).getData().getName());
        assertNull(groupedFormWidgetData.getFormGroups().get(0).getFormGroupData().get(0).getData().getValue());
    }

    @Test
    public void testDataForNoPrefilledFieldsSelfEmployed() throws JsonProcessingException {
        GroupedFormWidgetData groupedFormWidgetData = ObjectMapperUtil.get().readValue(PERSONAL_DETAILS_PREFILLED_ONLY_TEMPLATE, GroupedFormWidgetData.class);
        Map<String, FormFieldValue> formFieldValueMapToPrefill = this.formWidgetDataJsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        Map<String, FormGroupDataValue> groupFieldValueMapToPrefill = this.formWidgetDataJsonParser.getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        Map<String, Object> userData = getDataForFieldsNoPrefilled(formFieldValueMapToPrefill.keySet());
        userData.put("employmentType", "SelfEmployed");
        FormWidgetDataPrefillUtils formWidgetDataPrefillUtils = new FormWidgetDataPrefillUtils();;
        formWidgetDataPrefillUtils.prefillGroupedFormFieldValues(groupFieldValueMapToPrefill, userData);
        formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);
        assertEquals("panNumber", groupedFormWidgetData.getFormGroups().get(0).getFormGroupData().get(0).getData().getName());
        assertNull(groupedFormWidgetData.getFormGroups().get(0).getFormGroupData().get(0).getData().getValue());
    }

    @Test
    public void testDataForNamePageFields() throws JsonProcessingException {
        GroupedFormWidgetData groupedFormWidgetData = ObjectMapperUtil.get().readValue(NAME_PAGE_FORM_TEMPLATE, GroupedFormWidgetData.class);
        Map<String, FormFieldValue> formFieldValueMapToPrefill = this.formWidgetDataJsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        Map<String, FormGroupDataValue> groupFieldValueMapToPrefill = this.formWidgetDataJsonParser.getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
        Map<String, Object> userData = getDataForFieldsAllPrefilled(formFieldValueMapToPrefill.keySet());
        FormWidgetDataPrefillUtils formWidgetDataPrefillUtils = new FormWidgetDataPrefillUtils();;
        formWidgetDataPrefillUtils.prefillGroupedFormFieldValues(groupFieldValueMapToPrefill, userData);
        formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);
        assertEquals("fullName", groupedFormWidgetData.getFormGroups().get(0).getFormGroupData().get(0).getData().getName());
        assertNull(groupedFormWidgetData.getFormGroups().get(0).getFormGroupData().get(0).getData().getValue());
    }

    public Map<String, Object> getDataForFieldsAllPrefilled(Set<String> fields) {
        Map<String, Object> userData = new HashMap<>();
        // leadPageDataSourceResponse
        if (fields.contains(FULL_NAME_STRING)) {
            userData.put(FULL_NAME_STRING, "Prasoon Birla");
            userData.put(PHONE_NUMBER_STRING, "9191919191");
        } else {
            // ReviewUserDataSourceResponse
            userData.put(PAN_NUMBER, "123123BCPAN");
            userData.put(DOB_STRING, "19/02/1998");
            userData.put(GENDER_STRING, "Male");
            userData.put(GENDER_STRING_2, "Male");
            userData.put(COMPANY_NAME_STRING, "Supermoney");
            userData.put(ORGANIZATION_ID_STRING, "1233");
            userData.put(INDUSTRY_NAME_STRING, "CAFE");
            userData.put(INDUSTRY_ID_STRING, "123123");
            userData.put(HOUSE_NUMBER_STRING, "Line 1");
            userData.put(AREA_STRING, "Line 2");
            userData.put(ADDRESSES_STRING, Collections.emptyList());
            userData.put(PINCODE_DETAILS_STRING, new PincodeDetailsResponse("123123", true, "Bangalore", "Karnataka"));
            userData.put(MONTHLY_INCOME_STRING, "100000");
            userData.put(BONUS_INCOME_STRING, 123);
            userData.put(INCOME_SOURCE_STRING, "ONLINE");
            userData.put(EMPLOYMENT_TYPE_STRING, "Salaried");
            userData.put(EMAIL_STRING, "<EMAIL>");
        }
        return userData;
    }

    public Map<String, Object> getDataForFieldsNoPrefilled(Set<String> fields) {
        Map<String, Object> userData = new HashMap<>();
        // leadPageDataSourceResponse
        if (fields.contains(FULL_NAME_STRING)) {
            userData.put(FULL_NAME_STRING, "");
            userData.put(PHONE_NUMBER_STRING, "9191919191");
        } else {
            // ReviewUserDataSourceResponse
            userData.put(PAN_NUMBER, "");
            userData.put(DOB_STRING, "");
            userData.put(GENDER_STRING, "");
            userData.put(GENDER_STRING_2, "");
            userData.put(COMPANY_NAME_STRING, "");
            userData.put(ORGANIZATION_ID_STRING, "");
            userData.put(INDUSTRY_NAME_STRING, "");
            userData.put(INDUSTRY_ID_STRING, "");
            userData.put(HOUSE_NUMBER_STRING, "");
            userData.put(AREA_STRING, "");
            userData.put(ADDRESSES_STRING, Collections.emptyList());
            userData.put(PINCODE_DETAILS_STRING, new PincodeDetailsResponse());
            userData.put(MONTHLY_INCOME_STRING, "");
            userData.put(BONUS_INCOME_STRING, 0);
            userData.put(INCOME_SOURCE_STRING, "");
            userData.put(EMPLOYMENT_TYPE_STRING, "");
            userData.put(EMAIL_STRING, "");
        }
        return userData;
    }

}
