package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.fintech.pinaka.api.request.v6.LandingPageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.LgSubmitPayload;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.client.LendingGatewayClient;
import com.flipkart.fintech.pinaka.service.configuration.requests.ApiRequest;
import com.flipkart.fintech.pinaka.service.configuration.requests.Payload;
import com.sumo.bff.models.runtime_get_api.request.RuntimeGetApiResponse;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class LendingGatewayImplTest {

    private LendingGatewayClient lendingGatewayClient;
    private LendingGatewayImpl lendingGateway;

    // Stub class to simulate the response with getData()
    private static class DataResponseStub {
        private final PageActionResponse data;
        DataResponseStub(PageActionResponse data) { this.data = data; }
        public PageActionResponse getData() { return data; }
    }

    @Before
    public void setUp() {
        lendingGatewayClient = mock(LendingGatewayClient.class);
        lendingGateway = new LendingGatewayImpl(lendingGatewayClient);
    }

    @Test
    public void testGetPage_ReturnsPageActionResponse() {
        LandingPageRequest landingPageRequest = mock(LandingPageRequest.class);
        PageActionResponse pageActionResponse = mock(PageActionResponse.class);

        when(landingPageRequest.getMerchantId()).thenReturn("merchant1");
        when(landingPageRequest.getSmUserId()).thenReturn("user1");
        when(landingPageRequest.getSource()).thenReturn("source1");

        RuntimeGetApiResponse<PageActionResponse> stubResponse = new RuntimeGetApiResponse<PageActionResponse>(pageActionResponse);
        when(lendingGatewayClient.getData(any(ApiRequest.class))).thenReturn(stubResponse);

        PageActionResponse result = lendingGateway.getPage("merchant1", landingPageRequest);
        assertEquals(pageActionResponse, result);
    }

    @Test
    public void testSubmit_ReturnsPageActionResponse() {
        UserActionRequest userActionRequest = mock(UserActionRequest.class);
        PageActionResponse pageActionResponse = mock(PageActionResponse.class);

        RuntimeGetApiResponse<PageActionResponse> stubResponse = new RuntimeGetApiResponse<PageActionResponse>(pageActionResponse);
        when(lendingGatewayClient.postSubmit(any(ApiRequest.class))).thenReturn(stubResponse);

        PageActionResponse result = lendingGateway.submit(userActionRequest);
        assertEquals(pageActionResponse, result);
    }

    @Test
    public void testGetPageLgApiRequest_ConstructsCorrectly() {
        LandingPageRequest landingPageRequest = mock(LandingPageRequest.class);
        when(landingPageRequest.getMerchantId()).thenReturn("merchant1");
        when(landingPageRequest.getSmUserId()).thenReturn("user1");
        when(landingPageRequest.getSource()).thenReturn("source1");

        ApiRequest apiRequest = LendingGatewayImpl.getPageLgApiRequest("merchant1", landingPageRequest);

        assertEquals(LendingGatewayImpl.APPLY_NOW, apiRequest.getDataApiName());
        assertEquals("merchant1", apiRequest.getUserRequestContext().getMerchant());
        assertEquals("user1", apiRequest.getUserRequestContext().getAccountId());
        Payload payload = (Payload) apiRequest.getPayload();
        assertEquals("merchant1", payload.getMerchantId());
        assertEquals("user1", payload.getUserId());
        assertEquals("source1", payload.getSource());
    }

    @Test
    public void testGetLgSubmitApiRequest_ConstructsCorrectly() {
        UserActionRequest userActionRequest = mock(UserActionRequest.class);
        when(userActionRequest.getSmUserId()).thenReturn("user1");

        ApiRequest apiRequest = invokeGetLgSubmitApiRequest(userActionRequest);

        assertEquals(LendingGatewayImpl.SUBMIT, apiRequest.getDataApiName());
        assertEquals("user1", apiRequest.getUserRequestContext().getAccountId());
        assertTrue(apiRequest.getPayload() instanceof LgSubmitPayload);
        assertEquals(userActionRequest, ((LgSubmitPayload) apiRequest.getPayload()).getUserActionRequest());
    }

    // Helper to access private static method for testing
    private ApiRequest invokeGetLgSubmitApiRequest(UserActionRequest req) {
        try {
            java.lang.reflect.Method m = LendingGatewayImpl.class.getDeclaredMethod("getLgSubmitApiRequest", UserActionRequest.class);
            m.setAccessible(true);
            return (ApiRequest) m.invoke(null, req);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}