package com.flipkart.fintech.lending.orchestrator.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.service.ruleengine.models.Filter;
import com.flipkart.fintech.pinaka.service.ruleengine.models.PlLandingPageStates;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.LinkedHashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class PlLandingPageOrchestratorTest {

    @Test
    public void testFilterRuleEngineSerialization() {
        try {
        ObjectMapperUtil.get().readValue(TransformerUtils.readFileasString("filterRules/landingPageFilterRules.json"),
                new TypeReference<LinkedHashMap<PlLandingPageStates, List<Filter>>>(){});
        } catch (JsonProcessingException e){
            throw new RuntimeException();
        }
    }
}