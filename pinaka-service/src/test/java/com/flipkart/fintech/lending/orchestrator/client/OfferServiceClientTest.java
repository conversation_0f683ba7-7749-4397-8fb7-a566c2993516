package com.flipkart.fintech.lending.orchestrator.client;

import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import org.junit.Before;
import org.junit.Test;

import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.Response;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class OfferServiceClientTest {
    private WebTarget webTarget;
    private OfferServiceClient offerServiceClient;
    private MerchantUser merchantUser;
    private WebTarget target;
    private Invocation.Builder builder;
    private Response response;

    @Before
    public void setUp() {
        webTarget = mock(WebTarget.class);
        target = mock(WebTarget.class);
        builder = mock(Invocation.Builder.class);
        response = mock(Response.class);
        merchantUser = mock(MerchantUser.class);
        offerServiceClient = new OfferServiceClient(webTarget);
    }

    @Test
    public void testGetPreApprovedOffer_OK() {
        when(webTarget.path(anyString())).thenReturn(target);
        when(target.queryParam(anyString(), anyString())).thenReturn(target);
        when(target.request()).thenReturn(builder);
        when(builder.header(anyString(), any())).thenReturn(builder);
        when(builder.get()).thenReturn(response);
        when(response.getStatusInfo()).thenReturn(Response.Status.OK);
        LenderOfferEntity entity = new LenderOfferEntity();
        when(response.readEntity(LenderOfferEntity.class)).thenReturn(entity);
        when(merchantUser.getMerchantUserId()).thenReturn("userId");
        when(merchantUser.getSmUserId()).thenReturn("smUserId");
        when(merchantUser.getMerchantKey()).thenReturn("merchantKey");

        Optional<LenderOfferEntity> result = offerServiceClient.getPreApprovedOffer(merchantUser, Optional.of("productType"));
        assertTrue(result.isPresent());
        assertEquals(entity, result.get());
    }

    @Test
    public void testGetPreApprovedOffer_NoContent() {
        when(webTarget.path(anyString())).thenReturn(target);
        when(target.queryParam(anyString(), anyString())).thenReturn(target);
        when(target.request()).thenReturn(builder);
        when(builder.header(anyString(), any())).thenReturn(builder);
        when(builder.get()).thenReturn(response);
        when(response.getStatusInfo()).thenReturn(Response.Status.NO_CONTENT);
        when(merchantUser.getMerchantUserId()).thenReturn("userId");
        when(merchantUser.getSmUserId()).thenReturn("smUserId");
        when(merchantUser.getMerchantKey()).thenReturn("merchantKey");

        Optional<LenderOfferEntity> result = offerServiceClient.getPreApprovedOffer(merchantUser, Optional.of("productType"));
        assertFalse(result.isPresent());
    }

    @Test
    public void testGetPreApprovedOffer_Error() {
        when(webTarget.path(anyString())).thenReturn(target);
        when(target.queryParam(anyString(), anyString())).thenReturn(target);
        when(target.request()).thenReturn(builder);
        when(builder.header(anyString(), any())).thenReturn(builder);
        when(builder.get()).thenReturn(response);
        when(response.getStatusInfo()).thenReturn(Response.Status.BAD_REQUEST);
        when(response.readEntity(String.class)).thenReturn("error");
        when(merchantUser.getMerchantUserId()).thenReturn("userId");
        when(merchantUser.getSmUserId()).thenReturn("smUserId");
        when(merchantUser.getMerchantKey()).thenReturn("merchantKey");

        try {
            offerServiceClient.getPreApprovedOffer(merchantUser, Optional.of("productType"));
            fail("Expected RuntimeException");
        } catch (RuntimeException e) {
            // expected
        }
    }
}
