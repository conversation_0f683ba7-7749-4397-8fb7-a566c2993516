package com.flipkart.fintech.lending.orchestrator.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.annotations.JsonFile;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.de.entity.decision.recommendation.business.PlAction.Value;
import com.flipkart.extensions.JsonFileParameterResolver;
import com.flipkart.fintech.lending.orchestrator.client.OfferServiceClient;
import com.flipkart.fintech.pandora.client.UserClient;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScores;
import com.flipkart.fintech.pinaka.service.arsenal.ArsenalService;
import com.flipkart.fintech.pinaka.service.core.LmsService;
import com.flipkart.fintech.pinaka.service.core.v7.CreateApplicationRequestFactory;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.flags.FeatureFlag;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.kloud.config.DynamicBucket;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ExecutorService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@ExtendWith(JsonFileParameterResolver.class)
class BasicOfferOrchestratorTest {

  @Mock
  private CreateApplicationRequestFactory applicationRequestFactory;

  @Mock
  private DynamicBucket dynamicBucket;


  @Mock
  private UserClient userClient;

  @Mock
  private UserProfileScores userProfileScores;

  @Mock
  private OfferComparator offerComparator;

  @Mock
  private OfferServiceClient offerServiceClient;

  @Mock
  private FetchUserProfileResponse dexterResponse;
  @Mock
  private AddressDetailResponse addressDetailResponse;

  @Mock
  private LmsService lmsService;

  private BasicOfferOrchestrator basicOfferOrchestrator;

  private static final String ORCHESTRATOR_MODE = "LIVE";

  @BeforeEach
  void setUp() {
    basicOfferOrchestrator = new BasicOfferOrchestrator(applicationRequestFactory, dynamicBucket, userClient, userProfileScores, offerComparator, offerServiceClient, lmsService);
  }

  @AfterEach
  void tearDown() {
    basicOfferOrchestrator = null;
  }

  @Test
  void getOfferForTnsAccepted(
      @JsonFile("orchestrator-test/lead-details.json")LeadDetails leadDetails,
      @JsonFile("orchestrator-test/alfred-response.json")UserProfileResponseV3 alfredResponse
//      @JsonFile("orchestrator-test/address-details.json") AddressDetailResponse addressDetailResponse
  ) throws PinakaException, IOException {
    InputStream inputStream = BasicOfferOrchestratorTest.class.getClassLoader().getResourceAsStream("orchestrator-test/dexterResponse.json");
    dexterResponse = ObjectMapperUtil.get().readValue(inputStream, FetchUserProfileResponse.class);
    inputStream = BasicOfferOrchestratorTest.class.getClassLoader().getResourceAsStream("orchestrator-test/address-details.json");
    addressDetailResponse = ObjectMapperUtil.get().readValue(inputStream, AddressDetailResponse.class);
    MerchantUser merchantUser = MerchantUser.getMerchantUser("FLIPKART", "acc132", "sm123");
    String requestId = "request123";
    when(userProfileScores.getUserProfile(merchantUser))
        .thenReturn(alfredResponse);
    when(userClient.getAddressDetailResponse("CNTCT22F9842623BA4E089ECB831CE", merchantUser.getMerchantUserId(), merchantUser.getSmUserId(), merchantUser.getMerchantKey()))
        .thenReturn(addressDetailResponse);
    when(dynamicBucket.getString("offerOrchestratorMode"))
        .thenReturn(ORCHESTRATOR_MODE);
    when(dynamicBucket.getBoolean("breEnabled"))
        .thenReturn(true);
    when(dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG))
            .thenReturn(Boolean.TRUE);
    when(userProfileScores.getUserProfileByDexter(requestId, merchantUser))
            .thenReturn(dexterResponse);

    CreateApplicationRequest lenderApplication = basicOfferOrchestrator.getLenderApplication(leadDetails, merchantUser, requestId, Value.ACCEPT, leadDetails.getApplicationData());
    assertNotNull(lenderApplication);
  }



}