package com.flipkart.fintech.robinhoodUpdate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.client.VaradhiClentConfig;
import com.flipkart.fintech.pinaka.service.kafka.RobinHoodEventHandler;
import com.flipkart.fintech.pinaka.service.kafka.RobinHoodEventHandlerImpl;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import org.glassfish.jersey.client.ClientProperties;
import org.junit.Before;
import org.mockito.Mock;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import com.flipkart.fintech.kafka.models.amsEvents.ApplicationEvent;

import java.io.IOException;
import java.io.InputStream;



public class robinhoodUpdateTest {

    @Mock
    RobinHoodEventHandler robinHoodEventHandler;

    @Mock
    VaradhiClentConfig varadhiClentConfig;

    @Before
    public void setUp() throws Exception {
        Client client = ClientBuilder.newClient();
        client.property(ClientProperties.CONNECT_TIMEOUT, 60000);
        client.property(ClientProperties.READ_TIMEOUT, 60000);
        varadhiClentConfig = new VaradhiClentConfig();
        varadhiClentConfig.setUrl("http://************/");
        varadhiClentConfig.setTopicName("sm_pl_application_state");
        robinHoodEventHandler = new RobinHoodEventHandlerImpl(new ObjectMapper(),client,varadhiClentConfig);
    }

    private ApplicationEvent getApplicationEvent() throws IOException {
        InputStream inputStream = robinhoodUpdateTest.class.getClassLoader().getResourceAsStream("robinhoodMock.json");
        ApplicationEvent applicationEvent =  ObjectMapperUtil.get().readValue(inputStream, ApplicationEvent.class);
        return applicationEvent;
    }

    // @Test
    // public void updateEvent() throws IOException {
    //     robinHoodEventHandler.updateApplicationState(getApplicationEvent());

    // }

}
