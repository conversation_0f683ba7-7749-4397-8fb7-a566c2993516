package com.flipkart.extensions;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.flipkart.annotations.JsonFile;
import com.flipkart.test.helpers.StringDeserializer;
import java.io.IOException;
import java.io.InputStream;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.ParameterContext;
import org.junit.jupiter.api.extension.ParameterResolver;

public class JsonFileParameterResolver implements ParameterResolver {

  private static final ObjectMapper objectMapper = createObjectMapper();

  private static ObjectMapper createObjectMapper() {
    ObjectMapper mapper = new ObjectMapper();
    SimpleModule module = new SimpleModule();
    module.addDeserializer(String.class, new StringDeserializer());
    mapper.registerModule(module);
    return mapper;
  }

  @Override
  public boolean supportsParameter(ParameterContext parameterContext, ExtensionContext extensionContext) {
    return parameterContext.isAnnotated(JsonFile.class);
  }

  @Override
  public Object resolveParameter(ParameterContext parameterContext, ExtensionContext extensionContext) {
    JsonFile annotation = parameterContext.getParameter().getAnnotation(JsonFile.class);
    String path = annotation.value();
    Class<?> type = parameterContext.getParameter().getType();

    try (InputStream is = getClass().getClassLoader().getResourceAsStream(path)) {
      if (is == null) {
        throw new RuntimeException("Resource not found: " + path);
      }
      return objectMapper.readValue(is, type);
    } catch (IOException e) {
      throw new RuntimeException("Failed to read JSON file: " + path + " for parameter of type: " + type.getName(), e);
    }
  }
}
