package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.de.entity.decision.recommendation.business.PlAction;
import com.flipkart.fintech.offer.orchestrator.model.PreApprovedOfferDetails;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;

import java.util.Map;
import java.util.Optional;

public interface OfferService {

    CreateApplicationRequest getLenderApplication(LeadDetails leadDetails, MerchantUser merchantUser, String requestId, PlAction.Value tnsScore, Map<String, Object> applicationData) throws PinakaException;
    Optional<PreApprovedOfferDetails> getActivePreApprovedOffersForLead(MerchantUser merchantUser);
}
