package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.fintech.pinaka.api.request.v6.LandingPageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.LgSubmitPayload;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.client.LendingGatewayClient;
import com.flipkart.fintech.pinaka.service.configuration.requests.ApiRequest;
import com.flipkart.fintech.pinaka.service.configuration.requests.Payload;
import com.sumo.bff.models.common.UserRequestContext;
import org.jetbrains.annotations.NotNull;

import javax.inject.Inject;

public class LendingGatewayImpl implements LendingGatewayService {


    public static final String APPLY_NOW = "APPLY_NOW";
    public static final String SUBMIT = "SUBMIT";
    private final LendingGatewayClient lendingGatewayClient;

    @Inject
    public LendingGatewayImpl(LendingGatewayClient lendingGatewayClient) {
        this.lendingGatewayClient = lendingGatewayClient;
    }

    @Override
    public PageActionResponse getPage(String merchantId, LandingPageRequest landingPageRequest) {
        ApiRequest apiRequest = getPageLgApiRequest(merchantId, landingPageRequest);
        return lendingGatewayClient.getData(apiRequest).getData();
    }

    @Override
    public PageActionResponse submit(UserActionRequest submitRequest) {
        return lendingGatewayClient.postSubmit(getLgSubmitApiRequest(submitRequest)).getData();
    }


    @NotNull
    static ApiRequest getPageLgApiRequest(String merchantId,LandingPageRequest pageRequest) {
        Payload payload = Payload.builder().merchantId(pageRequest.getMerchantId()).userId(pageRequest.getSmUserId()).source(pageRequest.getSource())
                .build();
        UserRequestContext userRequestContext = UserRequestContext.builder().accountId(
                pageRequest.getSmUserId()).merchant(merchantId).build();
        return ApiRequest.builder().dataApiName(APPLY_NOW).userRequestContext(userRequestContext)
                .payload(payload).build();
    }

    private static ApiRequest getLgSubmitApiRequest(UserActionRequest pageRequest) {
        UserRequestContext userRequestContext = UserRequestContext.builder().accountId(
                pageRequest.getSmUserId()).build();
        LgSubmitPayload lgSubmitPayload = new LgSubmitPayload();
        lgSubmitPayload.setUserActionRequest(pageRequest);
        return ApiRequest.builder().dataApiName(SUBMIT).userRequestContext(userRequestContext)
                .payload(lgSubmitPayload).build();
    }


}
