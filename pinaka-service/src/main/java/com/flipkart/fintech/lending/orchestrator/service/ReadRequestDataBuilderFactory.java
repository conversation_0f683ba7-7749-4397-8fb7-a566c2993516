package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.inject.Inject;
import com.supermoney.ams.bridge.handlers.InteractionHandlerFactory;
import com.supermoney.ams.bridge.interaction.InteractionResolver;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.ams.bridge.models.InteractionConfig;
import com.supermoney.ams.bridge.models.interaction.MandateInteractionConfig;
import com.supermoney.ams.bridge.request.builder.ResumeApplicationRequestBuilder;


public class ReadRequestDataBuilderFactory {

    private final InteractionResolver interactionResolver;

    private final ResumeApplicationRequestBuilder resumeApplicationRequestBuilder;
    @Inject
    public ReadRequestDataBuilderFactory(InteractionHandlerFactory interactionHandlerFactory, InteractionResolver interactionResolver, ResumeApplicationRequestBuilder resumeApplicationRequestBuilder) {
        this.interactionResolver = interactionResolver;
        this.resumeApplicationRequestBuilder = resumeApplicationRequestBuilder;
    }

    public ReadRepairDataRequestBuilder get(ApplicationDataResponse applicationDataResponse) {
        InteractionConfig interactionConfig = getInteractionConfig(applicationDataResponse);
        if(interactionConfig instanceof MandateInteractionConfig){
            return new MandateReadRepairRequestBuilder(resumeApplicationRequestBuilder);
        }
        return null;
    }

    private InteractionConfig getInteractionConfig(ApplicationDataResponse applicationDataResponse) {
        ApplicationState applicationState = ApplicationState.create(applicationDataResponse);
        InteractionConfig interactionConfig = interactionResolver.getInteraction(applicationState);
        return interactionConfig;
    }
}
