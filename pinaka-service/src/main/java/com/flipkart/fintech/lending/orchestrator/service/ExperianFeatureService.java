package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.fintech.pinaka.library.*;
import com.flipkart.fintech.profile.bureau.models.BureauDataForInsight;
import com.flipkart.fintech.profile.bureau.models.Consent;
import com.flipkart.fintech.profile.response.BureauReport;
import com.flipkart.fintech.profile.service.BureauDataManager;
import com.google.inject.Inject;
import lombok.CustomLog;
import org.apache.commons.text.StringEscapeUtils;

import java.util.Collections;
import java.util.Objects;


@CustomLog
public class ExperianFeatureService {
    private final BureauDataManager bureauDataManager;
    private final ReportFeatureCalculator reportFeatureCalculator;

    @Inject
    public ExperianFeatureService(BureauDataManager bureauDataManager, ReportFeatureCalculator reportFeatureCalculator) {
        this.bureauDataManager = bureauDataManager;
        this.reportFeatureCalculator = reportFeatureCalculator;
    }

    public FeatureReport getFeatureReport(String merchantUserId, String smUserId , Long profileId, String leadId, Consent consent, Integer monthlyIncome ) {
        try {
            BureauDataForInsight bureauDataForInsight =  bureauDataManager.getBureauReport(merchantUserId,smUserId , profileId, leadId,consent, monthlyIncome);
            return getCalculatedFeatureScore(bureauDataForInsight.getReport(), bureauDataForInsight.getErrorString(),merchantUserId,smUserId, monthlyIncome);
        } catch (Exception e) {
            log.error("getting error while fetching and creating analysis for merchantUserId {} smuserId {} error {}", merchantUserId,smUserId , e.getMessage());
        }
        return new FeatureReport();
    }

    private FeatureReport getCalculatedFeatureScore(String reportHtmlDecoded, String bureauErrorMsg, String merchantUserId ,String smUserId, Integer monthlyIncome) {
        if (Objects.nonNull(reportHtmlDecoded)) {
            ReportContext reportContext = new ReportContext();
            String deCodedXmlReport = StringEscapeUtils.unescapeHtml4(reportHtmlDecoded);
            reportContext.setReport(deCodedXmlReport);
            reportContext.setUserDetails(new UserDetails());
            reportContext.getUserDetails().setSmUserId(smUserId);
            reportContext.getUserDetails().setAccountId(merchantUserId);
            reportContext.getUserDetails().setMonthlyIncome(monthlyIncome);
            reportContext.setReportType(ReportType.EXPERIAN_REPORT);
            return reportFeatureCalculator.calculateFeatureScore(reportContext,
                    Feature.getAllFeatures(), bureauErrorMsg);
        } else {
            ReportContext reportContext = new ReportContext();
            reportContext.setReport(null);
            reportContext.setUserDetails(new UserDetails());
            reportContext.getUserDetails().setSmUserId(smUserId);
            reportContext.getUserDetails().setAccountId(merchantUserId);
            reportContext.getUserDetails().setMonthlyIncome(monthlyIncome);
            reportContext.setReportType(ReportType.EXPERIAN_REPORT);
            return reportFeatureCalculator.calculateFeatureScore(reportContext,
                    Collections.singletonList(Feature.NEW_TO_CREDIT), bureauErrorMsg);
        }
    }
}
