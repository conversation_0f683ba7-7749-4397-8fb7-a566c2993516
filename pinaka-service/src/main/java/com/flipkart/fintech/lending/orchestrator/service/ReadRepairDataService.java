package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;

public interface ReadRepairDataService {

    //TODO: Deprecate this method in favor of persistDataAndReturn
    boolean persistData(MerchantUser merchantUser, ApplicationDataResponse applicationDataResponse);

    /**
     * Enhanced version of persistData that returns the updated application data
     * This eliminates the need for a separate fetch after data repair
     *
     * @param merchantUser The merchant user
     * @param applicationDataResponse The application data response
     * @return The updated application data response
     */
    ApplicationDataResponse persistDataAndReturn(MerchantUser merchantUser, ApplicationDataResponse applicationDataResponse);
}
