package com.flipkart.fintech.lending.orchestrator.model;

import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import lombok.Builder;
import lombok.Getter;

import java.util.List;


@Getter
@Builder
public class ApplicationsStateDetailsV2 {
    private ApplicationDataResponse leadApplicationDataResponse;
    private List<ApplicationDataResponse> plApplicationDataResponseList;
}
