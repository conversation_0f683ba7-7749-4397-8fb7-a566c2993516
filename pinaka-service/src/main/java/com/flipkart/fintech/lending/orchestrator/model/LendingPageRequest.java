package com.flipkart.fintech.lending.orchestrator.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pinaka.api.request.v6.LandingPageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.Operation;
import com.flipkart.fintech.pinaka.api.request.v6.ResumePageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LendingPageRequest {
    @NotNull
    private String accountId;

    @NotNull
    private String smUserId;

    @NotNull
    private String source;

    @NotNull
    private Operation operation;

    @NotNull
    private String applicationId;

    private UserActionRequest userRequest;

    private Map<String,String> additionalParams;

    public static LendingPageRequest fromResumePageRequest(ResumePageRequest resumePageRequest) {
        LendingPageRequest lendingPageRequest = new LendingPageRequest();
        lendingPageRequest.setAccountId(resumePageRequest.getAccountId());
        lendingPageRequest.setSmUserId(resumePageRequest.getSmUserId());
        lendingPageRequest.setSource(resumePageRequest.getSource());
        lendingPageRequest.setOperation(resumePageRequest.getOperation());
        lendingPageRequest.setApplicationId(resumePageRequest.getApplicationId());
        return lendingPageRequest;
    }

    public static ResumePageRequest toResumePageRequest(LendingPageRequest lendingPageRequest) {
        ResumePageRequest resumePageRequest = new ResumePageRequest();
        resumePageRequest.setAccountId(lendingPageRequest.getAccountId());
        resumePageRequest.setSmUserId(lendingPageRequest.getSmUserId());
        resumePageRequest.setSource(lendingPageRequest.getSource());
        resumePageRequest.setOperation(lendingPageRequest.getOperation());
        resumePageRequest.setApplicationId(lendingPageRequest.getApplicationId());
        return resumePageRequest;
    }

    public static LendingPageRequest fromLandingPageRequest(LandingPageRequest landingPageRequest) {
        LendingPageRequest lendingPageRequest = new LendingPageRequest();
        lendingPageRequest.setAccountId(landingPageRequest.getAccountId());
        lendingPageRequest.setSmUserId(landingPageRequest.getSmUserId());
        lendingPageRequest.setSource(landingPageRequest.getSource());
        lendingPageRequest.setOperation(landingPageRequest.getOperation());
        lendingPageRequest.setAdditionalParams(landingPageRequest.getAdditionalParameters());
        return lendingPageRequest;
    }

    public LandingPageRequest toLandingPageRequest() {
        LandingPageRequest landingPageRequest = new LandingPageRequest();
        landingPageRequest.setAccountId(accountId);
        landingPageRequest.setSmUserId(smUserId);
        landingPageRequest.setOperation(operation);
        landingPageRequest.setSource(source);
        return landingPageRequest;
    }
}
