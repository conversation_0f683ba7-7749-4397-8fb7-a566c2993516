package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.MandateSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserRequestActionType;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.inject.Inject;
import com.supermoney.ams.bridge.request.builder.ResumeApplicationRequestBuilder;
public class MandateReadRepairRequestBuilder implements ReadRepairDataRequestBuilder{

    private final ResumeApplicationRequestBuilder resumeApplicationRequestBuilder;

    @Inject
    public MandateReadRepairRequestBuilder(ResumeApplicationRequestBuilder resumeApplicationRequestBuilder) {
        this.resumeApplicationRequestBuilder = resumeApplicationRequestBuilder;
    }

    @Override
    public ResumeApplicationRequest build(ApplicationDataResponse applicationDataResponse) {
        MandateSubmitRequest mandateSubmitRequest = new MandateSubmitRequest();
        mandateSubmitRequest.setType(UserRequestActionType.MANDATE);
        ResumeApplicationRequest resumeApplicationRequest = resumeApplicationRequestBuilder.buildReadRepairData(mandateSubmitRequest,applicationDataResponse);
        return resumeApplicationRequest;
    }
}
