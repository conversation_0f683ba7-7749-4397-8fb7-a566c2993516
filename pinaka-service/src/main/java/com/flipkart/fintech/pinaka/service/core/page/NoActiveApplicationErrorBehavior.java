package com.flipkart.fintech.pinaka.service.core.page;

import com.flipkart.fintech.pinaka.api.response.v6.ErrorOperation;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import lombok.CustomLog;

@CustomLog
public class NoActiveApplicationErrorBehavior {

  public static PageActionResponse getPageActionResponse() {
      ErrorOperation errorOperation = new ErrorOperation();
      errorOperation.setMessage("No active application found");
      return new PageActionResponse(null, false, errorOperation, null);
  }
}
