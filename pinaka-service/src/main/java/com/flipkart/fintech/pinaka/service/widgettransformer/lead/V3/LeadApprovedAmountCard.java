package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.model.PaOffer;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.ListFormWidgetTransformer;
import com.flipkart.fintech.profile.service.BureauDataManager;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.TextStyle;
import com.flipkart.rome.datatypes.response.fintech.supermoney.cards.PrimitiveCard;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.supermoney.schema.PinakaService.LeadV3Events;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.jetbrains.annotations.NotNull;

import java.util.*;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.*;
import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util.getLeadEvents;
import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util.getOfferAmount;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.*;

public class LeadApprovedAmountCard implements ListFormWidgetTransformer {

    private static final String APPROVED_AMOUNT_CARD_TEMPLATE;
    private static final String APPROVED_AMOUNT_BANNER_TEXT_TEMPLATE_REVIEW_SCREEN = "Review your details to unlock %s";
    private static final String APPROVED_AMOUNT_BANNER_TEXT_TEMPLATE_WORK_DETAILS_SCREEN = "Enter work details to unlock %s";
    private static final String APPROVED_AMOUNT_BANNER_TEXT_TEMPLATE_REVIEW_SCREEN_NOTHING_PREFILLED = "Enter personal details to unlock %s";
    public static final String APPROVED_AMOUNT_TEXT_COLOR = "#4D43FE";
    public static final String ASTRIK_COLOR = "#C7CDFF";
    public static final String ASTRIK = "*";

    static {
        APPROVED_AMOUNT_CARD_TEMPLATE = TransformerUtils.readFileasString("template/lead/V4/ApprovedAmountWidget.json");
    }

    private final DynamicBucket dynamicBucket;
    private final BqIngestionHelper bqIngestionHelper;
    private final FormWidgetDataFetcher formWidgetDataFetcher;
    private final LocationRequestHandler locationRequestHandler;
    private final LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer;
    private final Decrypter decrypter;
    private final BureauDataManager bureauDataManager;

    private final Set<String> FIELD_NAMES = new HashSet<>();

    public LeadApprovedAmountCard(DynamicBucket dynamicBucket, BqIngestionHelper bqIngestionHelper, FormWidgetDataFetcher formWidgetDataFetcher,
                                  LocationRequestHandler locationRequestHandler, Decrypter decrypter, LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer,
                                  BureauDataManager bureauDataManager) {
        this.dynamicBucket = dynamicBucket;
        this.bqIngestionHelper = bqIngestionHelper;
        this.formWidgetDataFetcher = formWidgetDataFetcher;
        this.locationRequestHandler = locationRequestHandler;
        this.lv3ReviewPage1FormTransformer = lv3ReviewPage1FormTransformer;
        this.decrypter = decrypter;
        this.bureauDataManager = bureauDataManager;
        FIELD_NAMES.add(PAN_NUMBER);
        FIELD_NAMES.add(DOB_STRING);
        FIELD_NAMES.add(GENDER_STRING);
        FIELD_NAMES.add(GENDER_STRING_2);
        FIELD_NAMES.add(COMPANY_NAME_STRING);
        FIELD_NAMES.add(ORGANIZATION_ID_STRING);
        FIELD_NAMES.add(INDUSTRY_NAME_STRING);
        FIELD_NAMES.add(INDUSTRY_ID_STRING);
        FIELD_NAMES.add(HOUSE_NUMBER_STRING);
        FIELD_NAMES.add(AREA_STRING);
        FIELD_NAMES.add(ADDRESSES_STRING);
        FIELD_NAMES.add(PINCODE_DETAILS_STRING);
        FIELD_NAMES.add(MONTHLY_INCOME_STRING);
        FIELD_NAMES.add(BONUS_INCOME_STRING);
        FIELD_NAMES.add(INCOME_SOURCE_STRING);
        FIELD_NAMES.add(EMPLOYMENT_TYPE_STRING);
        FIELD_NAMES.add(EMAIL_STRING);
    }

    @Override
    public CardSummaryListWidgetData buildWidgetGroupData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        CardSummaryListWidgetData cardSummaryListWidgetData;
        String pageState = applicationDataResponse.getApplicationState();
        try {
            cardSummaryListWidgetData = ObjectMapperUtil.get().readValue(getFormJson(APPROVED_AMOUNT_CARD_TEMPLATE, applicationDataResponse), CardSummaryListWidgetData.class);
            PaOffer paOffer = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get(PA_OFFER), PaOffer.class);
            LV4Util.ApprovedAmount offerAmount = getOfferAmount(applicationDataResponse, paOffer);
            // Try to get cached user data first, fallback to external API calls ONLY if cache miss
            Map<String, Object> dataForFields = LV4Util.getCachedUserDataOrFallback(
                    applicationDataResponse, decrypter, locationRequestHandler, this.formWidgetDataFetcher, this.bureauDataManager);
            String screen = lv3ReviewPage1FormTransformer.whichScreenToLoad(dataForFields);
            updateOfferAmountText(cardSummaryListWidgetData, offerAmount, pageState, screen);
            updateStepNumberText(cardSummaryListWidgetData, pageState, screen);
            this.bqIngestionHelper.insertLeadEvents(getLeadEvents(applicationDataResponse, pageState, applicationDataResponse.getApplicationState(), LV3Util.getVersion(LeadDetails.LeadState.LEAD_V4_LANDING_PAGE)));
        } catch (Exception e) {
            LeadV3Events errorWhileBuildingPage = getLeadEvents(applicationDataResponse, "ERROR_WHILE_BUILDING_PAGE", applicationDataResponse.getApplicationState(), LV3Util.getVersion(LeadDetails.LeadState.LEAD_V4_LANDING_PAGE));
            this.bqIngestionHelper.insertLeadEvents(errorWhileBuildingPage);
            throw new PinakaException("Error while building widget Group Data for LV4 Approved Amount Card, userId: " + applicationDataResponse.getSmUserId(), e);
        }
        return cardSummaryListWidgetData;
    }

    void updateOfferAmountText(CardSummaryListWidgetData cardSummaryListWidgetData, LV4Util.ApprovedAmount approvedAmount, String pageState, String screen) {
        RichTextValue value = extractValueToBeUpdated(cardSummaryListWidgetData);
        value.setText(getFormattedAmountText(approvedAmount, pageState, screen));
        updateSpecialTextsMapper(approvedAmount, value);
    }

    void updateStepNumberText(CardSummaryListWidgetData cardSummaryListWidgetData, String pageState, String SCREEN) throws PinakaException {
        RenderableComponent<PrimitiveCard> primitiveCardRenderableComponent = Objects.requireNonNull(cardSummaryListWidgetData.getRenderableComponents()).get(0);
        Boolean enableLeadV4StepsText = dynamicBucket.getBoolean("ENABLE_LEAD_V4_STEPS_TEXT");
        if(enableLeadV4StepsText != null && enableLeadV4StepsText) {
            RichTextValue value = Objects.requireNonNull(primitiveCardRenderableComponent.getValue()).getSuperTitle();
            String stepNumber = getStepNumber(SCREEN, pageState);
            if(StringUtils.isNotBlank(stepNumber)) {
                Objects.requireNonNull(value).setText(stepNumber);
            } else {
                primitiveCardRenderableComponent.getValue().setSuperTitle(null);
            }
        } else {
            primitiveCardRenderableComponent.getValue().setSuperTitle(null);
        }
    }

    String getStepNumber(String screen, String pageState) {
        if("LEAD_V4_PAGE_2".equals(pageState)) {
            return "Step 2/2";
        }
        if(LEAD_V4_HALF_PREFILLED_SCREEN.equals(screen) || LEAD_V4_NOTHING_PREFILLED_SCREEN.equals(screen)) {
            return "Step 1/2";
        }
        // if its ALL FILLED, not steps needs to be displayed.
        return "";
    }

    void updateSpecialTextsMapper(LV4Util.ApprovedAmount approvedAmount, RichTextValue value) {
        Map<String, TextStyle> specialTextMapperData = new HashMap<>();
        TextStyle textStyle = new TextStyle();
        textStyle.setColor(APPROVED_AMOUNT_TEXT_COLOR);
        specialTextMapperData.put(getINRFormattedAmount(approvedAmount), textStyle);
        if(approvedAmount.isAppendUpto()) {
            TextStyle astricTextStyle = new TextStyle();
            astricTextStyle.setColor(ASTRIK_COLOR);
            astricTextStyle.setFontSize(20);
            specialTextMapperData.put(ASTRIK, astricTextStyle);
        }
        value.setSpecialTextsMapper(specialTextMapperData);
    }

    @NotNull
    RichTextValue extractValueToBeUpdated(CardSummaryListWidgetData cardSummaryListWidgetData) {
        RenderableComponent<PrimitiveCard> primitiveCardRenderableComponent = Objects.requireNonNull(cardSummaryListWidgetData.getRenderableComponents()).get(0);
        RenderableComponent<RichTextValue> title = Objects.requireNonNull(primitiveCardRenderableComponent.getValue()).getTitle();
        return Objects.requireNonNull(title.getValue());
    }

    @NotNull
    String getFormattedAmountText(LV4Util.ApprovedAmount approvedAmount, String pageState, String screen) {
        String formattedAmount;
        String template = APPROVED_AMOUNT_BANNER_TEXT_TEMPLATE_REVIEW_SCREEN;
        if("LEAD_V4_PAGE_2".equals(pageState)) {
            template = APPROVED_AMOUNT_BANNER_TEXT_TEMPLATE_WORK_DETAILS_SCREEN;
        } else if(LEAD_V4_NOTHING_PREFILLED_SCREEN.equals(screen)) {
            template = APPROVED_AMOUNT_BANNER_TEXT_TEMPLATE_REVIEW_SCREEN_NOTHING_PREFILLED;
        }
        if(approvedAmount.isAppendUpto()) {
            formattedAmount = getINRFormattedAmount(approvedAmount) + " *";
        } else {
            formattedAmount = getINRFormattedAmount(approvedAmount);
        }
        return String.format(template, formattedAmount);
    }

    @NotNull
    String getINRFormattedAmount(LV4Util.ApprovedAmount approvedAmount) {
        return "₹" + LV4Util.formatNumber(approvedAmount.getAmount());
    }

    private String getFormJson(String template, ApplicationDataResponse applicationDataResponse) {
        FormConfig formConfig = new FormConfig("18", "90", PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);
        Map<String, Object> formConfigMap = formConfig.getFormConfigMapForPage3(applicationDataResponse.getMerchantId(), dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(template);
    }
}
