package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.EligibleLoanOfferRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.IdentifiyCustomerRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.Lender;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.SubmitLoanOfferRequest;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.Offer;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.*;
import com.flipkart.fintech.pandora.client.PlOnboardingClient;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.*;
import com.flipkart.fintech.pinaka.service.adaptor.v6.PandoraClientRequestEnricher;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLWorkflowVariable;
import com.flipkart.fintech.pinaka.service.core.v6.WinterfellResourceHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.profile.service.ProfileService;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.fintech.winterfell.api.response.http.WinterfellNodeResponse;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import lombok.CustomLog;

import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.util.*;


@CustomLog
public class WinterfellResourceHandlerImpl implements WinterfellResourceHandler {
    private final PlOnboardingClient plOnboardingClient;
    private final PandoraClientRequestEnricher pandoraClientRequestEnricher;
    private final ApplicationService applicationService;
    private final ObjectMapper objectMapper;

    @Inject
    public WinterfellResourceHandlerImpl(PlOnboardingClient plOnboardingClient, PandoraClientRequestEnricher pandoraClientRequestEnricher, ApplicationService applicationService, ObjectMapper objectMapper, ProfileService profileService) {
        this.plOnboardingClient = plOnboardingClient;
        this.pandoraClientRequestEnricher = pandoraClientRequestEnricher;
        this.applicationService = applicationService;
        this.objectMapper = objectMapper;
    }

    @Override
    @HystrixCommand(threadPoolKey = "PL_PANDORA_API_POOL", groupKey = "PLPandoraClient", commandKey = "IDENTIFY_CUSTOMER")
    public WinterfellNodeResponse identifyCustomer(IdentifyCustomerRequest identifyCustomerRequest, String merchant) throws PinakaException {
        String requestId = identifyCustomerRequest.getRequestId();
        IdentifyCustomerResponse identifiyCustomerResponse = null;
        WinterfellNodeResponse winterfellNodeResponse = new WinterfellNodeResponse();
        Map<String, Object> plApplicationData = new HashMap<>();
        Map<String, VariableData> plWorkflowData = new HashMap<>();
        MerchantUser merchantUser = MerchantUser.getMerchantUser(merchant, identifyCustomerRequest.getAccountId(), identifyCustomerRequest.getSmUserId());
        try {
            IdentifiyCustomerRequest identifiyCustomerRequest = pandoraClientRequestEnricher.enrichCIRequest(identifyCustomerRequest);
            identifiyCustomerResponse = plOnboardingClient.identifyCustomerByLenders(identifiyCustomerRequest, requestId, merchant);
        } catch (ServiceException ex) {
            log.error("Error while calling pandora for customer identification for accountId: {}, applicationId: {}: {}",
                    identifyCustomerRequest.getAccountId(), identifyCustomerRequest.getApplicationId(), ex.getMessage());
            if (Response.Status.Family.CLIENT_ERROR.equals(Response.Status.Family.familyOf(ex.getServiceErrorResponse().getStatusCode().getStatusCode()))) {
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, "Exception while calling pandora CI");
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.RETRY_WITH_EDIT);
                plWorkflowData.put(PinakaConstants.PLWorkflowVariable.CI_STATUS, new VariableData(false, Status.RETRY_WITH_EDIT));
            } else {
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, "Exception while enriching/calling pandora CI");
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.RETRY_WITHOUT_EDIT);
                plWorkflowData.put(PinakaConstants.PLWorkflowVariable.CI_STATUS, new VariableData(false, Status.RETRY_WITHOUT_EDIT));
            }

            winterfellNodeResponse.setApplicationData(plApplicationData);
            winterfellNodeResponse.setWorkflowData(plWorkflowData);

            return winterfellNodeResponse;
        } catch (Exception ex) {
            log.error("Error in pandora client for customer identification for accountId: {}, applicationId: {}: {}",
                    identifyCustomerRequest.getAccountId(), identifyCustomerRequest.getApplicationId(), ex.getMessage());
            plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, "Exception while calling pandora CI");
            plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.RETRY_WITH_EDIT);
            plWorkflowData.put(PinakaConstants.PLWorkflowVariable.CI_STATUS, new VariableData(false, Status.RETRY_WITH_EDIT));
            winterfellNodeResponse.setApplicationData(plApplicationData);
            winterfellNodeResponse.setWorkflowData(plWorkflowData);

            winterfellNodeResponse.setApplicationData(plApplicationData);
            winterfellNodeResponse.setWorkflowData(plWorkflowData);
            return winterfellNodeResponse;
        }

        if (Status.SUCCESS.equals(identifiyCustomerResponse.getStatus())) {
            ApplicationDataResponse applicationDataResponse = applicationService.fetchApplicationData(merchantUser,
                    identifyCustomerRequest.getApplicationId());
            LoanApplication loanApplication = objectMapper.convertValue(applicationDataResponse.getApplicationData(), LoanApplication.class);

            LenderDetails lenderDetails = Objects.isNull(loanApplication.getLenderDetails()) ? new LenderDetails() : loanApplication.getLenderDetails();
            lenderDetails.setLenderApplicationId(identifiyCustomerResponse.getLenderApplicationId());
            lenderDetails.setApplicationValidTill(identifiyCustomerResponse.getApplicationValidTill());

            UserDetails userDetails = Objects.isNull(loanApplication.getUserDetails()) ? new UserDetails() : loanApplication.getUserDetails();
            userDetails.setSegment(identifiyCustomerResponse.getCustomerSegment().getSegment().name());
            userDetails.setSubsegment(identifiyCustomerResponse.getCustomerSegment().getSubsegment());

            plApplicationData.put(PinakaConstants.PLWorkflowVariable.LENDER_DETAILS, lenderDetails);
            plApplicationData.put(PinakaConstants.PLWorkflowVariable.USER_DETAILS, userDetails);
        }
        plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, identifiyCustomerResponse.getErrorMessage());
        plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.valueOf(identifiyCustomerResponse.getStatus().name()));

        plWorkflowData.put(PinakaConstants.PLWorkflowVariable.CI_STATUS, new VariableData(false, identifiyCustomerResponse.getStatus()));

        winterfellNodeResponse.setApplicationData(plApplicationData);
        winterfellNodeResponse.setWorkflowData(plWorkflowData);

        return winterfellNodeResponse;
    }

    @Override
    @HystrixCommand(threadPoolKey = "PL_PANDORA_EO_POOL", groupKey = "PLPandoraClient", commandKey = "ELIGIBLE_OFFER")
    public WinterfellNodeResponse eligibleOffer(EligibleOfferRequest eligibleOfferRequest, String merchant, String requestId) throws PinakaException {
        EligibleLoanOfferResponse eligibleLoanOfferResponse = null;
        WinterfellNodeResponse winterfellNodeResponse = new WinterfellNodeResponse();
        Map<String, Object> plApplicationData = new HashMap<>();
        Map<String, VariableData> plWorkflowData = new HashMap<>();
        MerchantUser merchantUser = MerchantUser.getMerchantUser(merchant, eligibleOfferRequest.getAccountId(), eligibleOfferRequest.getSmUserId());
        try {
            EligibleLoanOfferRequest eligibleLoanOfferRequest = pandoraClientRequestEnricher.enrichEORequest(eligibleOfferRequest, merchantUser, requestId);
            eligibleLoanOfferResponse = plOnboardingClient.fetchOffersFromLenders(eligibleLoanOfferRequest, eligibleOfferRequest.getRequestId(), merchant);
        } catch (ServiceException ex) {
            log.error("Error while calling pandora for eligible offer for accountId: {}, applicationId: {}: {}",
                    eligibleOfferRequest.getAccountId(), eligibleOfferRequest.getApplicationId(), ex.getMessage());
            if (Response.Status.Family.CLIENT_ERROR.equals(Response.Status.Family.familyOf(ex.getServiceErrorResponse().getStatusCode().getStatusCode()))) {
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, "Exception while calling pandora EO, client error");
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.RETRY_WITH_EDIT);
                plWorkflowData.put(PinakaConstants.PLWorkflowVariable.EO_STATUS, new VariableData(false, Status.RETRY_WITH_EDIT));
            } else {
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, "Exception while calling pandora EO");
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.RETRY_WITHOUT_EDIT);
                plWorkflowData.put(PinakaConstants.PLWorkflowVariable.EO_STATUS, new VariableData(false, Status.RETRY_WITHOUT_EDIT));
            }

            winterfellNodeResponse.setApplicationData(plApplicationData);

            plWorkflowData.put(PinakaConstants.PLWorkflowVariable.EO_STATUS, new VariableData(false, Status.RETRY_WITHOUT_EDIT));
            winterfellNodeResponse.setWorkflowData(plWorkflowData);

            return winterfellNodeResponse;
        } catch (DataEnrichmentException ex) {
            log.error("Error in enriching pandora request for eligible offer for accountId: {}, applicationId: {}: {}",
                    eligibleOfferRequest.getAccountId(), eligibleOfferRequest.getApplicationId(), ex.getMessage());
            plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, "Error in enriching pandora request for EO");
            plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.RETRY_WITH_EDIT);
            plWorkflowData.put(PinakaConstants.PLWorkflowVariable.EO_STATUS, new VariableData(false, Status.RETRY_WITH_EDIT));
            winterfellNodeResponse.setApplicationData(plApplicationData);
            winterfellNodeResponse.setWorkflowData(plWorkflowData);

            return winterfellNodeResponse;
        } catch (Exception ex) {
            log.error("Error in pandora client for eligible offer for accountId: {}, applicationId: {}: {}",
                    eligibleOfferRequest.getAccountId(), eligibleOfferRequest.getApplicationId(), ex.getMessage());
            plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, "Exception while enriching/calling pandora EO");
            plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.RETRY_WITH_EDIT);
            plWorkflowData.put(PinakaConstants.PLWorkflowVariable.EO_STATUS, new VariableData(false, Status.RETRY_WITH_EDIT));
            winterfellNodeResponse.setApplicationData(plApplicationData);
            winterfellNodeResponse.setWorkflowData(plWorkflowData);

            return winterfellNodeResponse;
        }

        if (Status.SUCCESS.equals(eligibleLoanOfferResponse.getStatus())) {
            ApplicationDataResponse applicationDataResponse = applicationService.fetchApplicationData(merchantUser, eligibleOfferRequest.getApplicationId());
            LoanApplication loanApplication = objectMapper.convertValue(applicationDataResponse.getApplicationData(), LoanApplication.class);

            LenderDetails lenderDetails = Objects.isNull(loanApplication.getLenderDetails()) ? new LenderDetails() : loanApplication.getLenderDetails();
            List<Offer> generatedOffer = new ArrayList<>();
            com.flipkart.fintech.pandora.api.model.response.plOnboarding.Offer offer = eligibleLoanOfferResponse.getOffer();
            generatedOffer.add(offer);
            lenderDetails.setGeneratedOffer(generatedOffer);
            lenderDetails.setLenderApplicationValidity(eligibleLoanOfferResponse.getLenderApplicationValidity());

            plApplicationData.put(PinakaConstants.PLWorkflowVariable.LENDER_DETAILS, lenderDetails);
        }
        plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, eligibleLoanOfferResponse.getErrorMessage());
        plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.valueOf(eligibleLoanOfferResponse.getStatus().name()));

        plWorkflowData.put(PinakaConstants.PLWorkflowVariable.EO_STATUS, new VariableData(false, eligibleLoanOfferResponse.getStatus()));

        winterfellNodeResponse.setApplicationData(plApplicationData);
        winterfellNodeResponse.setWorkflowData(plWorkflowData);

        return winterfellNodeResponse;
    }

    @HystrixCommand(
        threadPoolKey = "PL_PANDORA_API_POOL",
        groupKey = "PLPandoraClient",
        commandKey = "STATUS_PL"
    )
    @Override
    public WinterfellNodeResponse getStatusFromLender(String applicationId, String requestId,
        String merchantId) {
        com.flipkart.fintech.pandora.api.model.request.plOnboarding.StatusRequest statusRequest = new com.flipkart.fintech.pandora.api.model.request.plOnboarding.StatusRequest();
        statusRequest.setLspApplicationId(applicationId);
        statusRequest.setLenders(Lender.AXIS);

        StatusResponse statusResponse = null;
        try {
            statusResponse = plOnboardingClient.getStatusFromLender(statusRequest, requestId, merchantId);
        } catch (Exception ex) {
            log.error("Error while calling get status Request for applicationId: {}: {}", applicationId, ex.getMessage());
            statusResponse = new StatusResponse();
            EmploymentVerification ev=new EmploymentVerification();
            ev.setIsEVRequired(true);
            statusResponse.setEmploymentVerification(ev);
        }
        WinterfellNodeResponse winterfellNodeResponse = new WinterfellNodeResponse();
        Map<String, Object> plApplicationData = new HashMap<>();
        Map<String, VariableData> plWorkflowData = new HashMap<>();
        plApplicationData.put(PLWorkflowVariable.APP_STATUS, statusResponse);
        plWorkflowData.put(PLWorkflowVariable.APP_STATUS, new VariableData(false, statusResponse));
        winterfellNodeResponse.setApplicationData(plApplicationData);
        winterfellNodeResponse.setWorkflowData(plWorkflowData);

        return winterfellNodeResponse;
    }

    @Override
    @HystrixCommand(threadPoolKey = "PL_PANDORA_API_POOL", groupKey = "PLPandoraClient", commandKey = "SUBMIT_OFFER")
    public WinterfellNodeResponse submitOffer(SubmitOfferRequest submitOfferRequest, String merchant) throws PinakaException {
        SubmitLoanOfferResponse submitLoanOfferResponse = null;
        WinterfellNodeResponse winterfellNodeResponse = new WinterfellNodeResponse();
        Map<String, Object> plApplicationData = new HashMap<>();
        Map<String, VariableData> plWorkflowData = new HashMap<>();
        try {
            SubmitLoanOfferRequest submitLoanOfferRequest = pandoraClientRequestEnricher.enrichSORequest(submitOfferRequest);
            submitLoanOfferResponse = plOnboardingClient.submitLoanOfferToLender(submitLoanOfferRequest, submitOfferRequest.getRequestId(), merchant);
        } catch (ServiceException ex) {
            log.error("Error while calling pandora for submit offer for accountId: {}, applicationId: {}: {}",
                    submitOfferRequest.getAccountId(), submitOfferRequest.getApplicationId(), ex.getMessage());
            if (Response.Status.Family.CLIENT_ERROR.equals(Response.Status.Family.familyOf(ex.getServiceErrorResponse().getStatusCode().getStatusCode()))) {
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, "Exception while calling pandora SO, client error");
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.RETRY_WITH_EDIT);
                plWorkflowData.put(PinakaConstants.PLWorkflowVariable.SO_STATUS, new VariableData(false, Status.RETRY_WITH_EDIT));
            } else {
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, "Exception while calling pandora SO");
                plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.RETRY_WITHOUT_EDIT);
                plWorkflowData.put(PinakaConstants.PLWorkflowVariable.SO_STATUS, new VariableData(false, Status.RETRY_WITHOUT_EDIT));
            }

            winterfellNodeResponse.setApplicationData(plApplicationData);
            winterfellNodeResponse.setWorkflowData(plWorkflowData);

            return winterfellNodeResponse;
        } catch (Exception ex) {
            log.error("Error in pandora client for submit offer for accountId: {}, applicationId: {}: {}",
                    submitOfferRequest.getAccountId(), submitOfferRequest.getApplicationId(), ex.getMessage());
            plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, "Exception while enriching/calling pandora SO");
            plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.RETRY_WITH_EDIT);
            plWorkflowData.put(PinakaConstants.PLWorkflowVariable.SO_STATUS, new VariableData(false, Status.RETRY_WITH_EDIT));

            winterfellNodeResponse.setApplicationData(plApplicationData);
            winterfellNodeResponse.setWorkflowData(plWorkflowData);

            return winterfellNodeResponse;
        }

        if (Status.SUCCESS.equals(submitLoanOfferResponse.getStatus())) {
            MerchantUser merchantUser = MerchantUser.getMerchantUser(merchant, submitOfferRequest.getAccountId(), submitOfferRequest.getSmUserId());
            ApplicationDataResponse applicationDataResponse = applicationService.fetchApplicationData(merchantUser, submitOfferRequest.getApplicationId());
            LoanApplication loanApplication = objectMapper.convertValue(applicationDataResponse.getApplicationData(), LoanApplication.class);

            LenderDetails lenderDetails = Objects.isNull(loanApplication.getLenderDetails()) ? new LenderDetails() : loanApplication.getLenderDetails();
            lenderDetails.setLenderApplicationValidity(submitLoanOfferResponse.getLenderApplicationValidity());

            FormDetails formDetails = Objects.isNull(loanApplication.getFormDetails()) ? new FormDetails() : loanApplication.getFormDetails();

            Redirection redirection = submitLoanOfferResponse.getRedirectionDetails();
            formDetails.setRedirectionUrl(redirection.getRedirectionUrl());
            formDetails.setRedirectionUrlValidity(redirection.getRedirectionUrlValidity());
            formDetails.setHttpMethod(redirection.getHttpMethod());

            plApplicationData.put(PinakaConstants.PLWorkflowVariable.LENDER_DETAILS, lenderDetails);
            plApplicationData.put(PinakaConstants.PLWorkflowVariable.FORM_DETAILS, formDetails);
        }
        plApplicationData.put(PinakaConstants.PLWorkflowVariable.ERROR_MESSAGE, submitLoanOfferResponse.getErrorMessage());
        plApplicationData.put(PinakaConstants.PLWorkflowVariable.CODE, com.flipkart.fintech.pinaka.api.response.v6.Status.valueOf(submitLoanOfferResponse.getStatus().name()));

        plWorkflowData.put(PinakaConstants.PLWorkflowVariable.SO_STATUS, new VariableData(false, submitLoanOfferResponse.getStatus()));

        winterfellNodeResponse.setApplicationData(plApplicationData);
        winterfellNodeResponse.setWorkflowData(plWorkflowData);

        return winterfellNodeResponse;
    }

    @Override
    public WinterfellNodeResponse rejectApplication(RejectApplicationRequest rejectApplicationRequest, String merchant) {
        Map<String, Object> plApplicationData = new HashMap<>();
        Map<String, VariableData> plWorkflowData = new HashMap<>();
        WinterfellNodeResponse winterfellNodeResponse = WinterfellNodeResponse.builder()
                .applicationData(plApplicationData)
                .workflowData(plWorkflowData).build();

        return winterfellNodeResponse;
    }
}
