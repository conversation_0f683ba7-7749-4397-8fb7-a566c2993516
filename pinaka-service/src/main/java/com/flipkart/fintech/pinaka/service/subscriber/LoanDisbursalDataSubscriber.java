package com.flipkart.fintech.pinaka.service.subscriber;

import com.flipkart.fintech.pinaka.service.data.impl.DisbursalsDao;
import com.flipkart.fintech.pinaka.service.data.model.DisbursalsEntity;
import com.google.cloud.pubsub.v1.AckReplyConsumer;
import com.google.cloud.pubsub.v1.MessageReceiver;
import com.google.cloud.pubsub.v1.Subscriber;
import com.google.inject.Inject;
import com.google.pubsub.v1.ProjectSubscriptionName;
import com.google.pubsub.v1.PubsubMessage;
import com.supermoney.schema.PandoraService.OfferEventV1;
import lombok.CustomLog;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@CustomLog
public class LoanDisbursalDataSubscriber {

    private final String projectId;
    private final String subscriptionId;
    private final LoanDisbursalDataSubscriberConfig loanDisbursalDataSubscriberConfig;
    private final DisbursalsDao disbursalsDao;
    private final SessionFactory sessionFactory;

    @Inject
    public LoanDisbursalDataSubscriber(LoanDisbursalDataSubscriberConfig loanDisbursalDataSubscriberConfig,
                                       DisbursalsDao disbursalsDao,
                                       SessionFactory sessionFactory) {
        this.loanDisbursalDataSubscriberConfig = loanDisbursalDataSubscriberConfig;
        this.projectId = loanDisbursalDataSubscriberConfig.getProjectId();
        this.subscriptionId = loanDisbursalDataSubscriberConfig.getSubscriptionId();
        this.disbursalsDao = disbursalsDao;
        this.sessionFactory = sessionFactory;
        startSubscriber();
    }

    public void startSubscriber() {
        ProjectSubscriptionName subscriptionName = ProjectSubscriptionName.of(projectId, subscriptionId);
        MessageReceiver receiver = this::handleMessage;

        Subscriber subscriber = null;
        try {
            subscriber = Subscriber.newBuilder(subscriptionName, receiver).build();
            subscriber.startAsync().awaitRunning();
            log.info("LoanDisbursalDataSubscriber started and listening on subscription: {}", subscriptionName);
        } catch (Exception e) {
            log.error("Could not start LoanDisbursalDataSubscriber", e);
            if (subscriber != null) {
                subscriber.stopAsync();
            }
        }
    }

    void handleMessage(PubsubMessage message, AckReplyConsumer consumer) {
        try {
            String jsonMessage = message.getData().toStringUtf8();
            OfferEventV1 request = LoanDisbursalDataSubscriberUtils.parseOfferEventV1(jsonMessage);
            log.info("Received loan disbursal data for application ID: {}", request.getApplicationId());
            if(isNotValidRequest(request)){
                log.info("Invalid request received, skipping processing");
                consumer.ack();
                return;
            }
            processAndPersist(request);

            log.info("Successfully persisted disbursal data for application ID: {}", request.getApplicationId());
            consumer.ack();
        } catch (Exception e) {
            log.error("Error processing message for loan disbursal data", e);
            consumer.nack();
        }
    }

    void processAndPersist(OfferEventV1 request) {
        Transaction transaction = null;
        Session session = null;
        DisbursalsEntity disbursalsEntity = mapToDisbursalsEntity(request);
        try {
            session = sessionFactory.openSession();
            transaction = session.beginTransaction();
            disbursalsDao.saveOrUpdate(session, disbursalsEntity);
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            log.error("Error persisting disbursal data", e);
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    private DisbursalsEntity mapToDisbursalsEntity(OfferEventV1 request) {
        DisbursalsEntity entity = new DisbursalsEntity();
        entity.setApplicationId(request.getApplicationId());
        entity.setSmUserId(request.getAccountId());
        entity.setLoanAmount(request.getOfferDetails().get(0).getNetDisbursed().intValue());
        entity.setDisbursalDate(Date.from(LocalDateTime.parse(request.getDate()).atZone(ZoneId.systemDefault()).toInstant()));
        entity.setLender(request.getLender());
        return entity;
    }

    private boolean isNotValidRequest(OfferEventV1 request) {
        return  ObjectUtils.isEmpty(request) || ObjectUtils.isEmpty(request.getApplicationId()) ||
                ObjectUtils.isEmpty(request.getAccountId()) ||
                ObjectUtils.isEmpty(request.getOfferDetails()) ||
                ObjectUtils.isEmpty(request.getOfferDetails().get(0)) ||
                ObjectUtils.isEmpty(request.getOfferDetails().get(0).getNetDisbursed()) ||
                ObjectUtils.isEmpty(request.getDate()) ||
                ObjectUtils.isEmpty(request.getLender());
    }
}