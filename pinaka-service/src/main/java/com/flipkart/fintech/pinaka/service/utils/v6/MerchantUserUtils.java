package com.flipkart.fintech.pinaka.service.utils.v6;

import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.constants.HeaderConstants;
import lombok.CustomLog;

@CustomLog
public class MerchantUserUtils {
    public static MerchantUser getMerchantUserFromRequestContext() {
        String smUserId = RequestContextThreadLocal.REQUEST_CONTEXT.get().getHeader(HeaderConstants.X_SM_USER_ID);
        String merchantUserId = RequestContextThreadLocal.REQUEST_CONTEXT.get().getHeader(HeaderConstants.X_MERCHANT_USER_ID);
        String merchant = RequestContextThreadLocal.REQUEST_CONTEXT.get().getHeader(HeaderConstants.X_MERCHANT_ID);

        return MerchantUser.getMerchantUser(merchant, merchantUserId, smUserId);
    }

    /**
     * Get merchant user id given account id coming in the request (can be merchant account id or sm account id based on flag)
     *
     * @param merchantUserId
     * @param smUserId
     * @return
     */
    public static MerchantUser getMerchantUser(String merchantUserId, String smUserId) {
        String merchant = RequestContextThreadLocal.REQUEST_CONTEXT.get().getHeader(HeaderConstants.X_MERCHANT_ID);
        return MerchantUser.getMerchantUser(merchant, merchantUserId, smUserId);
    }

    public static MerchantUser getMerchantUser(String merchantUserId, String smUserId, String merchant) {
        return MerchantUser.getMerchantUser(merchant, merchantUserId, smUserId);
    }
}
