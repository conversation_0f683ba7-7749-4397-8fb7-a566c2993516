package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.profile.service.BureauDataManager;
import com.flipkart.fintech.profile.service.ProfileService;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.utils.UrlUtil;
import lombok.CustomLog;
import org.apache.http.NameValuePair;

import javax.inject.Inject;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@CustomLog
public class InitialUserReviewDataSource implements PageDataSource<ReviewUserDataSourceResponse> {

    @Inject
    private static BureauDataManager bureauDataManager;
    @Inject
    private static ProfileService profileService;
    @Inject
    private static LocationRequestHandler locationRequestHandler;

    @Override
    public ReviewUserDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        MerchantUser merchantUser = MerchantUserUtils.getMerchantUser(applicationDataResponse.getExternalUserId(), applicationDataResponse.getSmUserId());
        return getData(merchantUser, QueryParamUtils.getQueryParams(queryParams));
    }

    public ReviewUserDataSourceResponse getData(MerchantUser merchantUser, Map<String, Object> queryParams) throws PinakaException {
        return getData(merchantUser, queryParams, true);
    }

    public ReviewUserDataSourceResponse getData(MerchantUser merchantUser, Map<String, Object> queryParams, boolean fetchProfile) throws PinakaException {
        ReviewUserDataSourceResponse response = new ReviewUserDataSourceResponse();
        AddressDetailResponse addressDetailResponse = locationRequestHandler.getUserAddress(merchantUser.getMerchantUserId(), merchantUser.getMerchantKey(),
                merchantUser.getSmUserId(), UUID.randomUUID().toString());
        response.setQueryParams(queryParams);
        response.setInitialUserDataResponse(bureauDataManager.initialUserData(merchantUser.getSmUserId()));
        if(Objects.nonNull(addressDetailResponse.getAddressLine1()) && Objects.nonNull(addressDetailResponse.getAddressLine2()) && Objects.nonNull(addressDetailResponse.getPincode())){
            response.getInitialUserDataResponse().setAddressDetailResponse(addressDetailResponse);
        }
        if (fetchProfile) {
            try {
                response.setProfile(profileService.getProfileByUserId(merchantUser.getMerchantUserId(),merchantUser.getSmUserId(), false));
            } catch (Exception exp) {
                log.error("Error while fetching the profile for user: {}", merchantUser, exp);
            }
        }
        return response;
    }
}
