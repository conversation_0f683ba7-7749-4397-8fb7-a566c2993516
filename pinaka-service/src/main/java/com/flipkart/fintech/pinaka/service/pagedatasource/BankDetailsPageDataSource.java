package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.service.response.BankDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.util.List;
import javax.inject.Inject;
import org.apache.http.NameValuePair;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import java.util.Optional;
public class BankDetailsPageDataSource implements PageDataSource {

    @Inject
    private static ConfigUtils configUtils;
    @Override
    public BankDetailsPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        BankDetailsPageDataSourceResponse bankDetailsPageDataSourceResponse = new BankDetailsPageDataSourceResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        bankDetailsPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        bankDetailsPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        return bankDetailsPageDataSourceResponse;
    }
}