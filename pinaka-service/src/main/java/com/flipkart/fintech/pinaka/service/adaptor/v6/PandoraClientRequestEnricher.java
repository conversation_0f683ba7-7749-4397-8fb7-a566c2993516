package com.flipkart.fintech.pinaka.service.adaptor.v6;

import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.affordability.underwriting.model.dexter.DataPoint;
import com.flipkart.affordability.underwriting.model.dexter.FetchCohort;
import com.flipkart.affordability.underwriting.model.dexter.FetchInsightSet;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.cri.alfred.api.model.v3.Cohort;
import com.flipkart.cri.alfred.api.model.v3.insight.Feature;
import com.flipkart.cri.alfred.api.model.v3.insight.MLModelScore;
import com.flipkart.cri.alfred.api.model.v3.userprofile.UserCBCProfileV3;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.ChargeType;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.EmploymentType;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.KfsRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.Lender;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.Tenure;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.*;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.LoanDetails;
import com.flipkart.fintech.pinaka.api.request.v6.*;
import com.flipkart.fintech.pinaka.api.response.v6.Charge;
import com.flipkart.fintech.pinaka.api.response.v6.TenureUnit;
import com.flipkart.fintech.pinaka.common.constants.AlfredConstants;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScores;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.pinaka.service.utils.EncryptionUtil;
import com.flipkart.fintech.pinaka.service.utils.ValidationUtils;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import liquibase.util.StringUtils;
import lombok.CustomLog;

import java.util.*;
import java.util.stream.Collectors;

import static com.flipkart.fintech.pinaka.service.application.Constants.INDIA;

@CustomLog
public class PandoraClientRequestEnricher {
    private final UserProfileScores userProfileScores;
    private final DynamicBucket dynamicBucket;

    @Inject
    public PandoraClientRequestEnricher(UserProfileScores userProfileScores, DynamicBucket dynamicBucket) {
        this.userProfileScores = userProfileScores;
        this.dynamicBucket = dynamicBucket;
    }

    public IdentifiyCustomerRequest enrichCIRequest(IdentifyCustomerRequest identifyCustomerRequest) {
        IdentifiyCustomerRequest identifiyCustomerRequest = new IdentifiyCustomerRequest();
        identifiyCustomerRequest.setLspApplicationId(identifyCustomerRequest.getApplicationId());

        Identification identification = new Identification();
        String pan = EncryptionUtil.decryptAesCbc(identifyCustomerRequest.getPanNumber(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY);
        identification.setPan(pan);
        String dob = identifyCustomerRequest.getDob();
        identification.setDob(dob);
        identification.setGender(fetchGenderDetails(identifyCustomerRequest.getGender()));
        String firstNameDecrypted = EncryptionUtil.decryptAesCbc(identifyCustomerRequest.getFirstName(), PLConstants.PL_DATA_ENCRYPTION_KEY);
        identification.setFirstName(firstNameDecrypted);
        String lastNameDecrypted = EncryptionUtil.decryptAesCbc(identifyCustomerRequest.getLastName(), PLConstants.PL_DATA_ENCRYPTION_KEY);
        identification.setLastName(lastNameDecrypted);
        log.info("Got name : first name : {}, last name : {}", firstNameDecrypted, lastNameDecrypted);
        log.info("Setting identification : {}", identification);
        identifiyCustomerRequest.setIdentificationDetails(identification);

        identifiyCustomerRequest.setPlScore(makePlScore(identifyCustomerRequest.getBinScore()));

        ContactDetails contactDetails = new ContactDetails();
        contactDetails.setAccountId(identifyCustomerRequest.getAccountId());
        contactDetails.setSmUserId(identifyCustomerRequest.getSmUserId());
        contactDetails.setShippingAddressId(identifyCustomerRequest.getShippingAddressId());
        if (StringUtils.isEmpty(identifyCustomerRequest.getShippingAddressId())) {
            AddressDetailResponse addressDetailResponse = new AddressDetailResponse();
            addressDetailResponse.setAddressLine1(EncryptionUtil.decryptAesCbc(identifyCustomerRequest.getHouseNumber(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
            addressDetailResponse.setAddressLine2(EncryptionUtil.decryptAesCbc(identifyCustomerRequest.getArea(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
            addressDetailResponse.setCity(EncryptionUtil.decryptAesCbc(identifyCustomerRequest.getCity(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
            addressDetailResponse.setState(EncryptionUtil.decryptAesCbc(identifyCustomerRequest.getState(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
            addressDetailResponse.setCountry(INDIA);
            addressDetailResponse.setPincode(identifyCustomerRequest.getPincode());
            if (!ValidationUtils.isCustomerIdAddrEmpty(addressDetailResponse)) {
                contactDetails.setAddressDetailResponse(addressDetailResponse);
            }
        }

        identifiyCustomerRequest.setContactDetails(contactDetails);

        Consent consent = new Consent();
        consent.setProvided(identifyCustomerRequest.isPanConsentProvided());
        consent.setConsentFor(ConsentFor.BUREAU_PULL);
        List<Consent> consentList = new ArrayList<>();
        consentList.add(consent);
        identifiyCustomerRequest.setConsent(consentList);

        identifiyCustomerRequest.setProductDetails(new ProductDetails(identifyCustomerRequest.getCategoryType()));
        List<Lender> lenders = new ArrayList<>();
        lenders.add(Lender.AXIS);
        identifiyCustomerRequest.setLenders(lenders);
        identifiyCustomerRequest.setLoanPurpose(identifyCustomerRequest.getLoanPurpose());

        identifiyCustomerRequest.setConsentMetadata(mapConsentData(identifyCustomerRequest.getConsentMetaData()));

        return identifiyCustomerRequest;
    }

    private ConsentMetadata mapConsentData(ConsentMetaData consentMetaData) {
        if(Objects.nonNull(consentMetaData)) {
            return ConsentMetadata.builder()
                    .consentType(consentMetaData.getConsentType())
                    .currentTimeStamp(consentMetaData.getCurrentTimeStamp())
                    .deviceId(consentMetaData.getDeviceId())
                    .deviceInfo(consentMetaData.getDeviceInfo())
                    .deviceParams(consentMetaData.getDeviceParams())
                    .userIP(consentMetaData.getUserIP()).build();
        }
        return null;
    }

    private com.flipkart.fintech.pandora.api.model.common.Gender fetchGenderDetails(Gender gender) {
        if (Gender.M.equals(gender) || Gender.Male.equals(gender)) {
            return com.flipkart.fintech.pandora.api.model.common.Gender.M;
        } else if (Gender.F.equals(gender) || Gender.Female.equals(gender)) {
            return com.flipkart.fintech.pandora.api.model.common.Gender.F;
        } else if (Gender.T.equals(gender) || Gender.Transgender.equals(gender)) {
            return com.flipkart.fintech.pandora.api.model.common.Gender.T;
        }
        return com.flipkart.fintech.pandora.api.model.common.Gender.valueOf(String.valueOf(gender));
    }

    public EligibleLoanOfferRequest enrichEORequest(EligibleOfferRequest eligibleOfferRequest, MerchantUser merchantUser, String requestId) throws DataEnrichmentException {
        EligibleLoanOfferRequest eligibleLoanOfferRequest = new EligibleLoanOfferRequest();
        eligibleLoanOfferRequest.setLspApplicationId(eligibleOfferRequest.getApplicationId());
        eligibleLoanOfferRequest.setPlScore(makePlScore(eligibleOfferRequest.getBinScore()));

        if(Objects.isNull(eligibleOfferRequest.getConsentMetaDataList())){
            eligibleLoanOfferRequest.setConsentMetadataList(null);
        }else {
            eligibleLoanOfferRequest.setConsentMetadataList(updateConsentMetaDataPandora(eligibleOfferRequest.getConsentMetaDataList()));
        }

        Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
        if(dexterFlag){
            FetchUserProfileResponse fetchUserProfileResponse = userProfileScores.getUserProfileByDexter(requestId, merchantUser);
            List<FetchCohort> cohortsList = new ArrayList<>();
            if(Objects.nonNull(fetchUserProfileResponse) && Objects.nonNull(fetchUserProfileResponse.getCohorts())){
                cohortsList = fetchUserProfileResponse.getCohorts();
            }

            List<FetchInsightSet> insightSetsList = new ArrayList<>();
            if(Objects.nonNull(fetchUserProfileResponse) && Objects.nonNull(fetchUserProfileResponse.getInsightSets())){
                insightSetsList = fetchUserProfileResponse.getInsightSets();
            }

            Optional<FetchCohort> optionalCohortModel = fetchCremoCohortModel(cohortsList);
            if(optionalCohortModel.isPresent()){
                FetchCohort cremoCohortModel = optionalCohortModel.get();
                log.info("Value of freeField7 in Dexter block of code:{}", cremoCohortModel.getCohortSegment());
                eligibleLoanOfferRequest.setFreeField7(cremoCohortModel.getCohortSegment());
            }

            Optional<FetchInsightSet> optionalFetchInsightSetJRMV1 = fetchInsightSetJRMV1(insightSetsList);
            if(optionalFetchInsightSetJRMV1.isPresent()){
                FetchInsightSet fetchInsightSetJRMV1 = optionalFetchInsightSetJRMV1.get();
                eligibleLoanOfferRequest.setFreeField6(fetchJRMV1ScoreVariable(null, fetchInsightSetJRMV1.getFeatures()));
                eligibleLoanOfferRequest.setFreeField16(fetchJRMV1Score(null, fetchInsightSetJRMV1.getScores()));
            }

            Optional<FetchInsightSet> optionalFetchInsightSetJRMV3 = fetchInsightSetJRMV3(insightSetsList);
            if(optionalFetchInsightSetJRMV3.isPresent()){
                FetchInsightSet fetchInsightSetJRMV3 = optionalFetchInsightSetJRMV3.get();
                eligibleLoanOfferRequest = fetchJRMV3ScoreVariable(eligibleLoanOfferRequest, new ArrayList<>(), fetchInsightSetJRMV3.getFeatures());
                eligibleLoanOfferRequest.setFreeField17(fetchJRMV3Score(null, fetchInsightSetJRMV3.getScores()));
            }

        } else {
            UserProfileResponseV3 userProfileResponseV3 = userProfileResponseV3 = userProfileScores.getUserProfile(merchantUser);
            List<UserCBCProfileV3> userCBCProfileV3List = new ArrayList<>();
            if (!Objects.isNull(userProfileResponseV3)) {
                userCBCProfileV3List = userProfileResponseV3.getUserProfileV3();
            }
            Optional<UserCBCProfileV3> optionalV1 = fetchUserProfileJRMV1(userCBCProfileV3List);
            if (optionalV1.isPresent()) {
                UserCBCProfileV3 userCBCProfileV3 = optionalV1.get();
                eligibleLoanOfferRequest.setFreeField6(fetchJRMV1ScoreVariable(userCBCProfileV3.getFeatures(), null));
                eligibleLoanOfferRequest.setFreeField7(fetchCremoScoreBand(userCBCProfileV3.getCohorts()));
                eligibleLoanOfferRequest.setFreeField16(fetchJRMV1Score(userCBCProfileV3.getScores(), null));
            }

            Optional<UserCBCProfileV3> optionalV3 = fetchUserProfileJRMV3(userCBCProfileV3List);
            if (optionalV3.isPresent()) {
                UserCBCProfileV3 userCBCProfile = optionalV3.get();
                eligibleLoanOfferRequest = fetchJRMV3ScoreVariable(eligibleLoanOfferRequest, userCBCProfile.getFeatures(), null);
                eligibleLoanOfferRequest.setFreeField17(fetchJRMV3Score(userCBCProfile.getScores(), null));
            }

        }

        List<Lender> lenders = new ArrayList<>();
        lenders.add(Lender.AXIS);
        eligibleLoanOfferRequest.setLenders(lenders);

        Consent consent = new Consent();
        consent.setProvided(eligibleOfferRequest.isPanConsentProvided());
        consent.setConsentFor(ConsentFor.BUREAU_PULL);
        eligibleLoanOfferRequest.setConsent(consent);

        OccupationaDetails occupationDetails = new OccupationaDetails();
        occupationDetails.setEmploymentType(fetchEmploymentType(eligibleOfferRequest.getEmploymentType()));

        EmploymentDetails employmentDetails = new EmploymentDetails();
        employmentDetails.setEmployerId(eligibleOfferRequest.getEmployerId());
        employmentDetails.setEmployerName(eligibleOfferRequest.getEmployerName());
        employmentDetails.setMonthlyIncome(eligibleOfferRequest.getMonthlyIncomeWithBonus());
        employmentDetails.setIndustryId(eligibleOfferRequest.getIndustryId());
        employmentDetails.setIndustryName(eligibleOfferRequest.getIndustryName());
        employmentDetails.setAnnualTurnOver(eligibleOfferRequest.getAnnualTurnOver());
        occupationDetails.setEmploymentDetails(employmentDetails);
        eligibleLoanOfferRequest.setOccupationaDetails(occupationDetails);

        ContactDetails contactDetails = new ContactDetails();
        contactDetails.setAccountId(eligibleOfferRequest.getAccountId());
        contactDetails.setSmUserId(eligibleOfferRequest.getSmUserId());
        contactDetails.setShippingAddressId(eligibleOfferRequest.getShippingAddressId());
        if (StringUtils.isEmpty(eligibleOfferRequest.getShippingAddressId())) {
            AddressDetailResponse addressDetailResponse = new AddressDetailResponse();
            addressDetailResponse.setAddressLine1(EncryptionUtil.decryptAesCbc(eligibleOfferRequest.getHouseNumber(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
            addressDetailResponse.setAddressLine2(EncryptionUtil.decryptAesCbc(eligibleOfferRequest.getArea(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
            addressDetailResponse.setCity(EncryptionUtil.decryptAesCbc(eligibleOfferRequest.getCity(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
            addressDetailResponse.setState(EncryptionUtil.decryptAesCbc(eligibleOfferRequest.getState(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
            addressDetailResponse.setCountry(INDIA);
            addressDetailResponse.setPincode(eligibleOfferRequest.getPincode());
            if (!ValidationUtils.isCustomerIdAddrEmpty(addressDetailResponse)) {
                contactDetails.setAddressDetailResponse(addressDetailResponse);
            }
        }

        eligibleLoanOfferRequest.setLoanPurpose(eligibleOfferRequest.getLoanPurpose());
        eligibleLoanOfferRequest.setConsentMetadata(mapConsentData(eligibleOfferRequest.getConsentMetaData()));
        eligibleLoanOfferRequest.setContactDetails(contactDetails);
        eligibleLoanOfferRequest.setFirstName(eligibleOfferRequest.getFirstName());
        eligibleLoanOfferRequest.setLastName(eligibleOfferRequest.getLastName());
        eligibleLoanOfferRequest.setVersion(eligibleOfferRequest.getVersion());

        return eligibleLoanOfferRequest;
    }

    private List<ConsentMetadata> updateConsentMetaDataPandora(List<ConsentMetaData> consentMetaDataList){
        List<ConsentMetadata> consentMetadataListPandora = new ArrayList<>();

        for(ConsentMetaData consentMetaData: consentMetaDataList){

            consentMetadataListPandora.add(com.flipkart.fintech.pandora.api.model.request.plOnboarding.ConsentMetadata.builder()
                    .consentType(consentMetaData.getConsentType())
                    .userIP(consentMetaData.getConsentType())
                    .deviceId(consentMetaData.getDeviceId())
                    .deviceInfo(consentMetaData.getDeviceInfo())
                    .deviceParams(consentMetaData.getDeviceParams())
                    .currentTimeStamp(consentMetaData.getCurrentTimeStamp())
                    .consentId(consentMetaData.getConsentId())
                    .consentFor(consentMetaData.getConsentFor())
                    .build());
        }

        return consentMetadataListPandora;
    }

    // TODO : remove this method as its duplicating in pandora
    private String makePlScore(String plScore) {
        return (plScore == null || plScore.equals("null") || plScore.trim().isEmpty() ) ? "300" : plScore;
    }

    private String fetchJRMV1ScoreVariable(List<Feature> features, List<DataPoint> featuresList) {
        String freeField6 = PinakaConstants.EMPTY_STRING;
        Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
        if(dexterFlag){
            if (Objects.nonNull(featuresList)){
                List<String> featureValueList = featuresList.stream().map(feature -> feature.getValue()).collect(Collectors.toList());
                log.info("Value of freeField6 list in Dexter block of code:{}", featureValueList);

                return freeField6.join(PinakaConstants.PIPE_SEPARATOR, featureValueList);
            }
        } else {
            if (!features.isEmpty()) {
                List<String> featureList = features.stream().map(feature -> feature.getFeatureValue()).collect(Collectors.toList());
                log.info("Value of freeField6 list in Alfred block of code:{}", featureList);

                return freeField6.join(PinakaConstants.PIPE_SEPARATOR, featureList);
            }
        }
        return freeField6;
    }

    private String fetchCremoScoreBand(List<Cohort> cohortList) {
        String freeField7 = PinakaConstants.EMPTY_STRING;

        Optional<Cohort> optional = Optional.empty();
        if (!Objects.isNull(cohortList)) {
            optional = cohortList.stream().filter(cohort ->
                    cohort.getName().equals(AlfredConstants.CREMO_BAND)).findFirst();
        }
        if (optional.isPresent()) {
            Cohort cohort = optional.get();
            freeField7 = cohort.getValue();
        }

        return freeField7;
    }

    private EligibleLoanOfferRequest fetchJRMV3ScoreVariable(EligibleLoanOfferRequest eligibleLoanOfferRequest, List<Feature> features, List<DataPoint> featuresList) {
        Map<String, String> freeFieldMap = new HashMap<>();
        String freeField10 = PinakaConstants.EMPTY_STRING;
        String freeField11 = PinakaConstants.EMPTY_STRING;
        String freeField12 = PinakaConstants.EMPTY_STRING;
        String freeField13 = PinakaConstants.EMPTY_STRING;
        String freeField14 = PinakaConstants.EMPTY_STRING;
        String freeField15 = PinakaConstants.EMPTY_STRING;
        if (!features.isEmpty()) {
            Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
            if(dexterFlag){
                for (DataPoint feature : featuresList){
                    freeFieldMap.put(feature.getName(), feature.getValue());
                }
                log.info("Value of freeFieldMap in Dexter block of code:{}", freeFieldMap);
            } else {
                for (Feature feature : features) {
                    freeFieldMap.put(feature.getFeatureName(), feature.getFeatureValue());
                }
                log.info("Value of freeFieldMap in Alfred block of code:{}", freeFieldMap);
            }

            freeField10 = freeFieldMap.get("purchase_v231") + "|" +
                    freeFieldMap.get("purchase_v337") + "|" +
                    freeFieldMap.get("location_adddress_v105") + "|" +
                    freeFieldMap.get("engagement_v145") + "|" +
                    freeFieldMap.get("profile_v1") + "|" +
                    freeFieldMap.get("purchase_v109") + "|" +
                    freeFieldMap.get("payments_v176") + "|" +
                    freeFieldMap.get("payments_v45") + "|" +
                    freeFieldMap.get("payments_v178") + "|" +
                    freeFieldMap.get("purchase_v313");

            freeField11 = freeFieldMap.get("payments_v62") + "|" +
                    freeFieldMap.get("payments_v182") + "|" +
                    freeFieldMap.get("location_adddress_v74") + "|" +
                    freeFieldMap.get("payments_v54") + "|" +
                    freeFieldMap.get("engagement_v206") + "|" +
                    freeFieldMap.get("payments_v61") + "|" +
                    freeFieldMap.get("location_adddress_v56") + "|" +
                    freeFieldMap.get("purchase_v22") + "|" +
                    freeFieldMap.get("post_purchase_v7") + "|" +
                    freeFieldMap.get("purchase_v50");

            freeField12 = freeFieldMap.get("payments_v137") + "|" +
                    freeFieldMap.get("payments_v77") + "|" +
                    freeFieldMap.get("purchase_v4") + "|" +
                    freeFieldMap.get("purchase_v199") + "|" +
                    freeFieldMap.get("purchase_v149") + "|" +
                    freeFieldMap.get("payments_v191") + "|" +
                    freeFieldMap.get("purchase_v284") + "|" +
                    freeFieldMap.get("purchase_v20") + "|" +
                    freeFieldMap.get("purchase_v348") + "|" +
                    freeFieldMap.get("engagement_v197");

            freeField13 = freeFieldMap.get("purchase_v179") + "|" +
                    freeFieldMap.get("engagement_v150") + "|" +
                    freeFieldMap.get("payments_v180") + "|" +
                    freeFieldMap.get("post_purchase_v9") + "|" +
                    freeFieldMap.get("purchase_v245") + "|" +
                    freeFieldMap.get("location_adddress_v182") + "|" +
                    freeFieldMap.get("engagement_v131") + "|" +
                    freeFieldMap.get("purchase_v49") + "|" +
                    freeFieldMap.get("engagement_v59") + "|" +
                    freeFieldMap.get("location_adddress_v24");

            freeField14 = freeFieldMap.get("purchase_v256") + "|" +
                    freeFieldMap.get("purchase_v286") + "|" +
                    freeFieldMap.get("purchase_v23") + "|" +
                    freeFieldMap.get("purchase_v135") + "|" +
                    freeFieldMap.get("purchase_v230") + "|" +
                    freeFieldMap.get("purchase_v195") + "|" +
                    freeFieldMap.get("engagement_v148") + "|" +
                    freeFieldMap.get("payments_v80") + "|" +
                    freeFieldMap.get("payments_v20") + "|" +
                    freeFieldMap.get("purchase_v223");

            freeField15 = freeFieldMap.get("purchase_v237") + "|" +
                    freeFieldMap.get("purchase_v24") + "|" +
                    freeFieldMap.get("location_adddress_v75") + "|" +
                    freeFieldMap.get("engagement_v51") + "|" +
                    freeFieldMap.get("purchase_v371") + "|" +
                    freeFieldMap.get("payments_v123") + "|" +
                    freeFieldMap.get("purchase_v306") + "|" +
                    freeFieldMap.get("purchase_v366") + "|" +
                    freeFieldMap.get("location_adddress_v161") + "|" +
                    freeFieldMap.get("engagement_v14");
        }
        eligibleLoanOfferRequest.setFreeField10(freeField10);
        eligibleLoanOfferRequest.setFreeField11(freeField11);
        eligibleLoanOfferRequest.setFreeField12(freeField12);
        eligibleLoanOfferRequest.setFreeField13(freeField13);
        eligibleLoanOfferRequest.setFreeField14(freeField14);
        eligibleLoanOfferRequest.setFreeField15(freeField15);

        return eligibleLoanOfferRequest;
    }

    private String fetchJRMV1Score(List<MLModelScore> modelScoreList, List<DataPoint> scoresList) {
        String freeField16 = PinakaConstants.EMPTY_STRING;

        Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
        if(dexterFlag){
            Optional<DataPoint> optional = Optional.empty();
            if(Objects.nonNull(scoresList)){
                optional = scoresList.stream().filter(score ->
                        score.getName().equals(DexterConstants.SCORE)).findFirst();
            }
            if(optional.isPresent()){
                DataPoint score = optional.get();
                freeField16 = score.getValue();
            }
            log.info("Value of freeField16 in Dexter block of code:{}", freeField16);
        }else{
            Optional<MLModelScore> optional = Optional.empty();
            if (!Objects.isNull(modelScoreList)) {
                optional = modelScoreList.stream().filter(score ->
                        score.getScoreName().equals(PinakaConstants.PLAlfredConstants.SCORE)).findFirst();
            }
            if (optional.isPresent()) {
                MLModelScore mlModelScore = optional.get();
                freeField16 = mlModelScore.getScoreValue();
            }
            log.info("Value of freeField16 in Alfred block of code:{}", freeField16);
        }
        log.info("Value of freeField16 :{}", freeField16);
        return freeField16;
    }

    private String fetchJRMV3Score(List<MLModelScore> modelScoreList, List<DataPoint> scoresList) {
        String freeField17 = PinakaConstants.EMPTY_STRING;

        Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
        if(dexterFlag){
            Optional<DataPoint> optional = Optional.empty();
            if(Objects.nonNull(scoresList)){
                optional = scoresList.stream().filter(score -> score.getName().equals(DexterConstants.SCORE))
                        .findFirst();
            }
            if(optional.isPresent()){
                DataPoint score = optional.get();
                freeField17 = score.getValue();
            }
            log.info("Value of freeField17 list in Dexter block of code:{}", freeField17);
        } else {
            Optional<MLModelScore> optional = Optional.empty();
            if (!Objects.isNull(modelScoreList)) {
                optional = modelScoreList.stream().filter(score ->
                        score.getScoreName().equals(PinakaConstants.PLAlfredConstants.SCORE)).findFirst();
            }
            if (optional.isPresent()) {
                MLModelScore mlModelScore = optional.get();
                freeField17 = mlModelScore.getScoreValue();
            }
            log.info("Value of freeField17 list in Alfred block of code:{}", freeField17);
        }

        return freeField17;
    }

    private Optional<UserCBCProfileV3> fetchUserProfileJRMV1(List<UserCBCProfileV3> userCBCProfileV3List) {
        Optional<UserCBCProfileV3> optional = Optional.empty();
        if (!Objects.isNull(userCBCProfileV3List)) {
            optional = userCBCProfileV3List.stream().filter(userCBCProfileV3 ->
                    userCBCProfileV3.getVersion().equals(PinakaConstants.PLAlfredConstants.CBC_JRM_V1)).findFirst();
        }

        return optional;
    }
    private Optional<FetchInsightSet> fetchInsightSetJRMV1(List<FetchInsightSet> insightSetsList){
        Optional<FetchInsightSet> optional = Optional.empty();
        if(Objects.nonNull(insightSetsList)){
            optional = insightSetsList.stream().filter(insightSet->
                    insightSet.getName().equals(DexterConstants.CBC_JRM) && insightSet.getVersion().equals(DexterConstants.CBC_JRM_V1_VERSION))
                    .findFirst();
        }
        return optional;
    }
    private Optional<FetchCohort> fetchCremoCohortModel(List<FetchCohort> cohortsList){
        Optional<FetchCohort> optional = Optional.empty();
        if(Objects.nonNull(cohortsList)){
            optional = cohortsList.stream().filter(cohort ->
                    cohort.getName().equals(DexterConstants.CREMO)).findFirst();
        }
        return optional;
    }

    private Optional<UserCBCProfileV3> fetchUserProfileJRMV3(List<UserCBCProfileV3> userCBCProfileV3List) {
        Optional<UserCBCProfileV3> optional = Optional.empty();
        if (!Objects.isNull(userCBCProfileV3List)) {
            optional = userCBCProfileV3List.stream().filter(userCBCProfileV3 ->
                    userCBCProfileV3.getVersion().equals(PinakaConstants.PLAlfredConstants.CBC_JRM_V3)).findFirst();
        }

        return optional;
    }

    private Optional<FetchInsightSet> fetchInsightSetJRMV3(List<FetchInsightSet> insightSetsList){
        Optional<FetchInsightSet> optional = Optional.empty();
        if (Objects.nonNull(insightSetsList)) {
            optional = insightSetsList.stream().filter(insightSet ->
                    insightSet.getName().equals(DexterConstants.CBC_JRM) && insightSet.getVersion().equals(DexterConstants.CBC_JRM_V3_VERSION))
                    .findFirst();
        }

        return optional;
    }

    private EmploymentType fetchEmploymentType(com.flipkart.fintech.pinaka.api.request.v6.EmploymentType employmentType) {
        if (com.flipkart.fintech.pinaka.api.request.v6.EmploymentType.SALARIED.equals(employmentType)) {
            return EmploymentType.SALARIED;
        } else {
            return EmploymentType.SELF_EMPLOYED;
        }
    }

    public SubmitLoanOfferRequest enrichSORequest(SubmitOfferRequest submitOfferRequest) {
        SubmitLoanOfferRequest submitLoanOfferRequest = new SubmitLoanOfferRequest();
        submitLoanOfferRequest.setLspApplicationId(submitOfferRequest.getApplicationId());

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 1);
        long currentDate = cal.getTimeInMillis();
        submitLoanOfferRequest.setRequestTime(currentDate);

        submitLoanOfferRequest.setCallBackURL(submitOfferRequest.getCallbackUrl());
        submitLoanOfferRequest.setLender(Lender.AXIS);

        Consent consent = new Consent();
        consent.setProvided(submitOfferRequest.isOfferConsentProvided());
        consent.setConsentFor(ConsentFor.SUBMIT_OFFER);
        submitLoanOfferRequest.setConsent(consent);

        LoanParameter loanParameter = new LoanParameter();
        loanParameter.setLoanAmount(submitOfferRequest.getLoanAmount());
        loanParameter.setEmi(submitOfferRequest.getEmi());
        loanParameter.setRoi(submitOfferRequest.getRoi());
        loanParameter.setNetDisbursalAmount(submitOfferRequest.getNetDisbursalAmount());

        Tenure tenure = new Tenure();
        tenure.setValue(submitOfferRequest.getTenure().getValue());
        tenure.setUnit(fetchTenureUnit(submitOfferRequest.getTenure().getUnit()));
        loanParameter.setTenure(tenure);

        List<ChargeBreakUp> chargeBreakUpList = new ArrayList<>();
        for (Map.Entry<Charge, ChargeDetails> charge : submitOfferRequest.getCharges().entrySet()) {
            ChargeBreakUp chargeBreakUp = new ChargeBreakUp();
            chargeBreakUp.setAmount((double) charge.getValue().getAmount());
            chargeBreakUp.setGstAmount(charge.getValue().getGstAmount());
            chargeBreakUp.setTotalAmount(charge.getValue().getTotalAmount());
            chargeBreakUp.setChargeType(fetchChargeType(charge.getKey()));
            chargeBreakUpList.add(chargeBreakUp);
        }
        loanParameter.setCharges(chargeBreakUpList);

        submitLoanOfferRequest.setConsentMetadata(mapConsentData(submitOfferRequest.getConsentMetaData()));
        submitLoanOfferRequest.setLoanDetails(loanParameter);

        return submitLoanOfferRequest;
    }

    public KfsRequest enrichKfsRequest(com.flipkart.fintech.pinaka.api.request.v6.KfsRequest kfsRequest) {
        com.flipkart.fintech.pandora.api.model.request.plOnboarding.KfsRequest kfsRequestPandora =
                new com.flipkart.fintech.pandora.api.model.request.plOnboarding.KfsRequest();

        if (com.flipkart.fintech.pinaka.api.request.v6.Lender.AXIS.equals(kfsRequest.getLender())) {
            kfsRequestPandora.setLender(Lender.AXIS);
        }

        kfsRequestPandora.setLspApplicationId(kfsRequest.getLspApplicationId());
        com.flipkart.fintech.pandora.api.model.request.plOnboarding.LoanDetails loanDetailsPandora =
                new com.flipkart.fintech.pandora.api.model.request.plOnboarding.LoanDetails();

        LoanDetails loanDetails = kfsRequest.getLoanDetails();

        loanDetailsPandora.setLoanAmount(loanDetails.getLoanAmount());
        loanDetailsPandora.setRoi(loanDetails.getRoi());

        Tenure tenurePandora = new Tenure();
        tenurePandora.setValue(loanDetails.getTenure().getValue());
        tenurePandora.setUnit(fetchTenureUnit(loanDetails.getTenure().getUnit()));
        loanDetailsPandora.setTenure(tenurePandora);
        kfsRequestPandora.setLoanDetails(loanDetailsPandora);

        return kfsRequestPandora;
    }

    private Unit fetchTenureUnit(TenureUnit unit) {
        if (TenureUnit.MONTH.equals(unit)) {
            return Unit.MONTH;
        } else {
            return Unit.YEAR;
        }
    }

    private ChargeType fetchChargeType(Charge charge) {
        if (Charge.PROCESSING_FEE.equals(charge)) {
            return ChargeType.PROCESSING_FEE;
        } else {
            return ChargeType.STAMP_DUTY;
        }
    }
}
