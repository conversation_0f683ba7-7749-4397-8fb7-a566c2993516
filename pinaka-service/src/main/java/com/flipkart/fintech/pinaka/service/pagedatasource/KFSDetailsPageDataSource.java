package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pandora.api.model.pl.response.AutoDisbursalResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.LoanOnboardingResponse;
import com.flipkart.fintech.pinaka.service.response.KFSDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.http.NameValuePair;

public class KFSDetailsPageDataSource implements PageDataSource<KFSDetailsPageDataSourceResponse> {



    @Inject
    private static ConfigUtils configUtils;
    @Override
    public KFSDetailsPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        KFSDetailsPageDataSourceResponse kfsDetailsPageDataSourceResponse = new KFSDetailsPageDataSourceResponse();
        LoanOnboardingResponse loanOnboardingResponse = ObjectMapperUtil.get()
            .convertValue(applicationDataResponse.getApplicationData().get("onboardLoan"),
                LoanOnboardingResponse.class);
        kfsDetailsPageDataSourceResponse.setLoanOnboardingResponse(loanOnboardingResponse);
        if (applicationDataResponse.getApplicationData().containsKey("refreshAutoDisbursal")) {
            AutoDisbursalResponse refreshedKfsDetail = ObjectMapperUtil.get()
                .convertValue(applicationDataResponse.getApplicationData().get("refreshAutoDisbursal"),
                    AutoDisbursalResponse.class);
            kfsDetailsPageDataSourceResponse.getLoanOnboardingResponse().getResult().getResponse()
                .setKfsDetails(refreshedKfsDetail.getResourceData().get(0).getKfsDetails());

        }
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        kfsDetailsPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        kfsDetailsPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        return kfsDetailsPageDataSourceResponse;
    }
}