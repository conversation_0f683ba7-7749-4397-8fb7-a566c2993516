package com.flipkart.fintech.pinaka.service.core.v7;


import com.google.inject.Inject;
import java.text.DecimalFormat;

public class ApplicationHelper {

	public static final String UNDER_THOUSAND_DECIMAL_PATTERN = "###.00";
	public static final String OVER_THOUSAND_DECIMAL_FLOW = "000.00";
	public static final String UNDER_THOUSAND_NON_DECIMAL_PATTERN = "###";
	public static final String OVER_THOUSAND_NON_DECIMAL_FLOW = "000";

	@Inject
	private ApplicationHelper() {
	}

	public static String format(double value) {
		if (value < 1000) {
			return format(UNDER_THOUSAND_DECIMAL_PATTERN, value);
		} else {
			double hundreds = value % 1000;
			int other = (int) (value / 1000);
			return format(",##", other) + ',' + format(OVER_THOUSAND_DECIMAL_FLOW, hundreds);
		}
	}

	public static String format(double value, boolean isDecimal) {
		if (isDecimal) {
			return format(value);
		}
		if (value < 1000) {
			return format(UNDER_THOUSAND_NON_DECIMAL_PATTERN, value);
		} else {
			double hundreds = value % 1000;
			int other = (int) (value / 1000);
			return format(",##", other) + ',' + format(OVER_THOUSAND_NON_DECIMAL_FLOW, hundreds);
		}
	}

	private static String format(String pattern, Object value) {
		return new DecimalFormat(pattern).format(value);
	}


}
