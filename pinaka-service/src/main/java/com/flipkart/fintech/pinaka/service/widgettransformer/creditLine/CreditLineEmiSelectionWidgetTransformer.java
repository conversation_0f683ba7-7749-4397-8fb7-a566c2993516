package com.flipkart.fintech.pinaka.service.widgettransformer.creditLine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.service.response.CreditLineEmiSelectionPageDataResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.request.fintech.calm.Tenure;
import com.flipkart.rome.datatypes.request.fintech.calm.enums.TenureUnit;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.calm.RangeFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.AmountRangeOffer;
import com.flipkart.rome.datatypes.response.fintech.supermoney.InterestData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.BankOfferFormFieldV2Data;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.CustomFormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static com.flipkart.fintech.pinaka.service.utils.APRCalculator.calculateAPR;
import static com.flipkart.fintech.pinaka.service.utils.APRCalculator.computeEMI;

public class CreditLineEmiSelectionWidgetTransformer {
    private static String emiJson;

    static {
        emiJson = TransformerUtils.readFileasString("template/ltfs/CreditLineChooseEmiPage.json");
    }

    public GenericFormWidgetData buildWidgetData(CreditLineEmiSelectionPageDataResponse response) throws JsonProcessingException {
        try {
            GenericFormWidgetData genericFormWidgetData = ObjectMapperUtil.get().readValue(emiJson, GenericFormWidgetData.class);
            CustomFormFieldValue customFormFieldValue = (CustomFormFieldValue) genericFormWidgetData.getRenderableComponents().get(0).getValue();
            BankOfferFormFieldV2Data bankOfferFormFieldData = (BankOfferFormFieldV2Data) customFormFieldValue.getCustomFieldData();
            updateAmountSliderDetails(response, bankOfferFormFieldData);
            updateAmountRangeOffer(bankOfferFormFieldData, response);
            updateSubmitButton(genericFormWidgetData.getSubmitButton(), response);
            return genericFormWidgetData;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void updateAmountSliderDetails(CreditLineEmiSelectionPageDataResponse response, BankOfferFormFieldV2Data bankOfferFormFieldData) {
        RangeFormFieldValue amountSliderValue = bankOfferFormFieldData.getAmountSlider().getValue();
        amountSliderValue.setMaxValue(Long.parseLong(response.getEmisResponse().getMaxLoanEligibility()));
        amountSliderValue.setDefaultValue(Long.parseLong(response.getEmisResponse().getMaxLoanEligibility()));
        // TODO This could throw NPE. Either we should add not-null in POJO or use Optional.nullable.
        amountSliderValue.setMinValue(Long.parseLong(response.getEmisResponse().getMinLoanEligibility()));
    }

    private void updateAmountRangeOffer(BankOfferFormFieldV2Data bankOfferFormFieldData, CreditLineEmiSelectionPageDataResponse response) {
        AmountRangeOffer originalOffer = (AmountRangeOffer) bankOfferFormFieldData.getAmountRangeOffers().get(0);

        List<AmountRangeOffer> newOffers = new ArrayList<>();
        Integer minTenure = Integer.valueOf(response.getEmisResponse().getMinTenure());
        Integer maxTenure = Integer.valueOf(response.getEmisResponse().getMaxTenure());

        for (int tenureValue = minTenure; tenureValue <= maxTenure; tenureValue += 6) {
            AmountRangeOffer offerCopy = deepCopy(originalOffer);

            offerCopy.setMin(Integer.parseInt(response.getEmisResponse().getMinLoanEligibility()));
            offerCopy.setMax(Integer.parseInt(response.getEmisResponse().getMaxLoanEligibility()));

            InterestData interestData = offerCopy.getInterest();
            interestData.setValue(Double.parseDouble(response.getEmisResponse().getRoi()));
            offerCopy.setInterest(interestData);

            if (offerCopy.getRepaymentDetails() != null) {
                Tenure tenure = new Tenure();
                tenure.setValue(tenureValue);
                tenure.setUnit(TenureUnit.MONTH);

                LocalDate today = LocalDate.now();

                LocalDate endDate = today.plusMonths(tenureValue + 1L);

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String startDateFormatted = today.format(formatter);
                String endDateFormatted = endDate.format(formatter);

                offerCopy.getRepaymentDetails().setTenure(tenure);
                offerCopy.getRepaymentDetails().setStartDate(startDateFormatted);
                offerCopy.getRepaymentDetails().setEndDate(endDateFormatted);
            }

            if (tenureValue > minTenure) {
                offerCopy.setTag(null);
            }
            double emi = computeEMI(offerCopy.getMax(), interestData.getValue() / 1200, tenureValue);
            double apr = calculateAPR(offerCopy.getMax(), emi, tenureValue, offerCopy.getMax() * 0.0236);// Assuming processing fee is 2.36% of the max amount
            offerCopy.getInterest().setValue(apr);
            newOffers.add(offerCopy);
        }

        bankOfferFormFieldData.setAmountRangeOffers(newOffers);
    }

    private AmountRangeOffer deepCopy(AmountRangeOffer originalOffer) {
        return ObjectMapperUtil.get().convertValue(originalOffer, AmountRangeOffer.class);
    }


    private void updateSubmitButton(SubmitButtonValue submitButton, CreditLineEmiSelectionPageDataResponse response) {
        Action action = submitButton.getButton().getAction();
        action.setParams(response.getQueryParams());
        action.setEncryption(response.getEncryptionData());

    }
}
