package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.Lender;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.StatusRequest;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.Unit;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.v1.JourneyState;
import com.flipkart.fintech.pandora.api.model.response.sandbox.v2.GetApplicationStatusResponse;
import com.flipkart.fintech.pandora.client.PlOnboardingClient;
import com.flipkart.fintech.pandora.client.PandoraClientImpl;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.*;
import com.flipkart.fintech.pinaka.api.response.v6.*;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.service.adaptor.v6.PandoraClientRequestEnricher;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.core.v6.BulkDataRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.utils.EncryptionUtil;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantCheckUtils;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.supermoney.ams.bridge.exceptions.InvalidMerchantException;
import com.supermoney.ams.bridge.helpers.URLHelper;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import static com.flipkart.fintech.pinaka.service.utils.TransformerUtils.GetAppStateEvent.getApplicationStatusToStateEvent;
import javax.inject.Inject;
import java.net.URISyntaxException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import static com.flipkart.fintech.pinaka.api.enums.Lender.*;

@CustomLog
public class BulkDataRequestHandlerImpl implements BulkDataRequestHandler {
    private final PlOnboardingClient plOnboardingClient;
    private final PandoraClientRequestEnricher pandoraClientRequestEnricher;
    private final URLHelper plURLHelper;
    private final ApplicationService applicationService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final PandoraClientImpl pandoraClient;
    private final BqIngestionHelper bqIngestionHelper;
    private final Map<String, List<com.flipkart.fintech.pinaka.api.enums.Lender>> sanboxLendersVersionMap = new HashMap<String,List<com.flipkart.fintech.pinaka.api.enums.Lender>>();
    @Inject
    public BulkDataRequestHandlerImpl(PlOnboardingClient plOnboardingClient,
                                      PandoraClientRequestEnricher pandoraClientRequestEnricher,
                                      ApplicationService applicationService, URLHelper plURLHelper, PandoraClientImpl pandoraClient, BqIngestionHelper bqIngestionHelper) {
        this.plOnboardingClient = plOnboardingClient;
        this.pandoraClientRequestEnricher = pandoraClientRequestEnricher;
        this.applicationService = applicationService;
        this.plURLHelper = plURLHelper;
        this.pandoraClient = pandoraClient;
        this.bqIngestionHelper = bqIngestionHelper;
        sanboxLendersVersionMap.put("v1", new ArrayList<com.flipkart.fintech.pinaka.api.enums.Lender>(){{
            add(MONEYVIEW);
            add(FIBE);
            add(DMI);
            add(MONEYVIEWOPENMKT);
        }});
        sanboxLendersVersionMap.put("v2", new ArrayList<com.flipkart.fintech.pinaka.api.enums.Lender>(){{
            add(SMARTCOIN);
            add(RING);
            add(FINNABLE);
            add(FIBEV2);
            add(MONEYVIEWV2);
        }});
    }

    @Override
    @HystrixCommand(
            threadPoolKey = "PL_PANDORA_API_POOL",
            groupKey = "PLPandoraClient",
            commandKey = "KFS_PL"
    )
    public KfsResponse getKfsDetails(KfsRequest kfsRequest, String merchantId, String requestId) throws PinakaException {
        log.info("kfs request is {} merchant {} requestId {}", kfsRequest, merchantId, requestId);
        com.flipkart.fintech.pandora.api.model.request.plOnboarding.KfsRequest kfsRequestPandora =
                pandoraClientRequestEnricher.enrichKfsRequest(kfsRequest);
        com.flipkart.fintech.pandora.api.model.response.plOnboarding.KfsResponse kfsResponsePandora;
        try {
            kfsResponsePandora = plOnboardingClient.fetchKfsByLoanAndLenders(kfsRequestPandora, requestId, merchantId);
            log.info("response from pandora is {} for request {}", objectMapper.writeValueAsString(kfsResponsePandora), kfsRequest);
            KfsResponse kfsResponse = new KfsResponse();
            kfsResponse.setAddress(kfsResponsePandora.getAddress());
            kfsResponse.setStatus(fetchStatus(kfsResponsePandora.getStatus()));
            kfsResponse.setBounceCharge(kfsResponsePandora.getBounceCharge());
            kfsResponse.setDesignation(kfsResponsePandora.getDesignation());
            kfsResponse.setEapr(kfsResponsePandora.getEapr());
            kfsResponse.setAmountOfEachInstallment(kfsResponsePandora.getAmountOfEachInstallment());
            kfsResponse.setBounceChargeTax(kfsResponsePandora.getBounceChargeTax());
            kfsResponse.setChargesAgreementPhotocopy(kfsResponsePandora.getChargesAgreementPhotocopy());
            kfsResponse.setCibilIssuanceCharges(kfsResponsePandora.getCibilIssuanceCharges());
            kfsResponse.setDuplicateAmortizationSchedule(kfsResponsePandora.getDuplicateAmortizationSchedule());
            kfsResponse.setDuplicateInterestCertificate(kfsResponsePandora.getDuplicateInterestCertificate());
            kfsResponse.setDuplicateStatementIssuance(kfsResponsePandora.getDuplicateStatementIssuance());
            kfsResponse.setSwapCharges(kfsResponsePandora.getSwapCharges());

            List<com.flipkart.fintech.pandora.api.model.response.plOnboarding.EriDetailDTOS> eriDetailDTOSList =
                    kfsResponsePandora.getEriDetailDTOS();
            kfsResponse.setEriDetailDTOS(fetcheriDetailDTOSList(eriDetailDTOSList));

        kfsResponse.setForeClosureCharges(kfsResponsePandora.getForeClosureCharges());
        kfsResponse.setForeclosureCoolingOffPerValue(kfsResponsePandora.getForeclosureCoolingOffPerValue());
        kfsResponse.setInsurance(kfsResponsePandora.getRoi());
        //TODO move this to roi field @honey.gupta
        kfsResponse.setRoi(kfsResponsePandora.getRoi());
        kfsResponse.setInterestCharge(kfsResponsePandora.getInterestCharge());
        kfsResponse.setLoanAmount(kfsResponsePandora.getLoanAmount());
        kfsResponse.setTotalAmount(kfsResponsePandora.getTotalAmount());
        kfsResponse.setTotalBounceCharge(kfsResponsePandora.getTotalBounceCharge());
        kfsResponse.setBounceChargeTax(kfsResponsePandora.getBounceChargeTax());
        kfsResponse.setValue(kfsResponsePandora.getValue());
        kfsResponse.setUnit(CoolingOffUnit.valueOf(kfsResponsePandora.getUnit().name()));
        kfsResponse.setLspdetails(kfsResponsePandora.getLspDetails());
        kfsResponse.setAmountOfEachInstallment(kfsResponsePandora.getAmountOfEachInstallment());
        kfsResponse.setDesignation(kfsResponsePandora.getDesignation());
        kfsResponse.setName(kfsResponsePandora.getName());
        kfsResponse.setNetDisbursedAmount(kfsResponsePandora.getNetDisbursedAmount());
        kfsResponse.setNoOfInstallments(kfsResponsePandora.getNoOfInstallments());
        kfsResponse.setPhoneNumber(kfsResponsePandora.getPhoneNumber());
        kfsResponse.setProcessingFeecharge(kfsResponsePandora.getProcessingFeeCharge());
        kfsResponse.setProcessingFeeTax(kfsResponsePandora.getProcessingFeeTax());
        kfsResponse.setRepaymentFrequency(fetchRepaymentUnit(kfsResponsePandora.getRepaymentFrequency()));
        kfsResponse.setTenureOfLoan(kfsResponsePandora.getTenureOfLoan());
        kfsResponse.setTnc(PinakaConstants.PLConstants.PL_TNC_RELATIVE_PATH);
        kfsResponse.setTotalCharges(kfsResponsePandora.getTotalCharges());
        kfsResponse.setTotalProcessingFee(kfsResponsePandora.getTotalProcessingFee());
        kfsResponse.setTotalStampDuty(kfsResponsePandora.getTotalStampDuty());
        kfsResponse.setLatePaymentInterestDescription(kfsResponsePandora.getLatePaymentInterestDescription());
        kfsResponse.setRedirectionUrl(kfsResponsePandora.getRedirectionURL());

            return kfsResponse;
        } catch (Exception ex) {
            log.error("Error while calling pandora for kfs {}: {}", kfsRequest, ex.getMessage());
            throw new PinakaException("Error while calling pandora for kfs Request");
        }
    }

    @Override
    public StatusResponse getStatus(String applicationId, String requestId, MerchantUser merchantUser) throws PinakaException {
        ApplicationDataResponse applicationDataResponse = applicationService.fetchApplicationData(merchantUser, applicationId);

        if (Objects.isNull(applicationDataResponse)) {
            log.error("No active application for accountId {}", merchantUser.getMerchantUserId());
            throw new PinakaException("No active application");
        }

        isUserAllowedForJourney(merchantUser, applicationDataResponse);

        if (!MerchantCheckUtils.isMerchantAllowedForJourney(merchantUser.getMerchantKey(), applicationDataResponse.getMerchantId())) {
            throw new PinakaException("AccountId not whitelisted");
        }

        StatusResponse getStatusResponse = new StatusResponse();
        getStatusResponse.setApplicationId(applicationId);
        getStatusResponse.setContactDetails(PageConstant.CONTACT_DETAILS);
        String applicationState = applicationDataResponse.getApplicationState();

        if (PinakaConstants.PLConstants.LENDER_PLATFORM.equals(applicationState) || Status.APPLICATION_COMPLETED.name().equals(applicationState)) {
            Status applicationStatus = getStatusFromLender(applicationId, requestId, merchantUser);
            getStatusResponse.setStatus(applicationStatus);

            if (PinakaConstants.PLConstants.PL_TERMINAL_STATES.contains(applicationStatus.name())) {
                LoanApplication loanApplication = objectMapper.convertValue(applicationDataResponse.getApplicationData(), LoanApplication.class);
                LenderDetails lenderDetails = Objects.isNull(loanApplication.getLenderDetails()) ? new LenderDetails() : loanApplication.getLenderDetails();
                lenderDetails.setApplicationSubmittedTimestamp(String.valueOf(System.currentTimeMillis()));
                resumeWorkflow(applicationStatus, applicationId, merchantUser, lenderDetails, applicationDataResponse);

                if (Status.SUCCESS.equals(applicationStatus) || Status.DISBURSAL_IN_PROGRESS.equals(applicationStatus)) {
                    getStatusResponse.setStatus(applicationStatus);
                    String timestamp = StringUtils.isEmpty(loanApplication.getLenderDetails().getApplicationSubmittedTimestamp()) ?
                            String.valueOf(System.currentTimeMillis()) : loanApplication.getLenderDetails().getApplicationSubmittedTimestamp();
                    Date date = new Date(Long.parseLong(timestamp));
                    DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String formatted = format.format(date);
                    Timestamp timeStamp = Timestamp.valueOf(formatted);
                    getStatusResponse.setSubmittedTimestamp(timeStamp);
                    Offer submittedOffer = loanApplication.getLenderDetails().getSubmittedOffer().get(0);
                    getStatusResponse.setLoanDetails(getLoanDetails(submittedOffer));
                } else if (Status.REJECTED.equals(applicationStatus)) {
                    getStatusResponse.setStatus(Status.REJECTED);
                } else {
                    updateInProgressDetails(getStatusResponse, applicationId, applicationDataResponse.getMerchantId());
                }
            } else {
                updateInProgressDetails(getStatusResponse, applicationId, applicationDataResponse.getMerchantId());
            }
        } else if (Status.REJECTED.name().equals(applicationState)) {
            getStatusResponse.setStatus(Status.REJECTED);
        } else if (Status.SUCCESS.name().equals(applicationState)) {
            updateSuccessStatusDetails(getStatusResponse, applicationDataResponse);
        } else {
            updateInProgressDetails(getStatusResponse, applicationId, applicationDataResponse.getMerchantId());
        }

        return getStatusResponse;
    }

    @Override
    public ApplicationStatusResponse getApplicationStatus(String applicationId, String requestId, MerchantUser merchantUser) throws PinakaException {
        ApplicationDataResponse applicationDataResponse = applicationService.fetchApplicationData(merchantUser, applicationId);
        String lenderApplicationId = (String) ((Map<String, Object>) applicationDataResponse.getApplicationData().get("createApplication")).get("lenderApplicationId");
        if (Objects.isNull(applicationDataResponse)) {
            log.error("No active application for accountId {}", merchantUser.getMerchantUserId());
            throw new PinakaException("No active application");
        }
        com.flipkart.fintech.pandora.api.model.enums.lender.Lender lender = com.flipkart.fintech.pandora.api.model.enums.lender.Lender.valueOf((String) applicationDataResponse.getApplicationData().get("financial_provider"));
        ApplicationStatusResponse applicationStatusResponse = new ApplicationStatusResponse();
        applicationStatusResponse.setApplicationId(applicationId);
        applicationStatusResponse.setLenderApplicationId(lenderApplicationId);
        if (sanboxLendersVersionMap.get("v1").contains(lender)) {
            com.flipkart.fintech.pandora.api.model.response.sandbox.v1.GetApplicationStatusResponse applicationStatus = getStatusFromSandboxLenderV1(lenderApplicationId, requestId, merchantUser, lender);
            applicationStatusResponse.setStatus(String.valueOf(applicationStatus.getApplicationStatus()));
            applicationStatusResponse.setLenderStatus(Optional.ofNullable(applicationStatus.getJourneyState()).map(s->String.valueOf(s.getStatus())).orElse(null));
            applicationStatusResponse.setLenderSubStatus(Optional.ofNullable(applicationStatus.getJourneyState()).map(JourneyState::getSubStatus).orElse(null));
        }
        if (sanboxLendersVersionMap.get("v2").contains(lender)) {
            com.flipkart.fintech.pandora.api.model.response.sandbox.v2.GetApplicationStatusResponse applicationStatus = getStatusFromSandboxLenderV2(lenderApplicationId, requestId, merchantUser, lender, applicationId);
            applicationStatusResponse.setStatus(String.valueOf(applicationStatus.getApplicationStatus().getStatus()));
            applicationStatusResponse.setLenderStatus(applicationStatus.getApplicationStatus().toString());
            applicationStatusResponse.setLenderSubStatus(Optional.ofNullable(applicationStatus.getJourneyState()).map(com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.JourneyState::getSubStatus).orElse(null));
        }
        try {
            bqIngestionHelper.insertApplicationEvents(Objects.requireNonNull(getApplicationStatusToStateEvent(applicationDataResponse, String.valueOf(lender), lenderApplicationId,applicationStatusResponse)));
        } catch (Exception e) {
            log.error("BulkDataRequestHandler: Error in publishing the event to BQ: {}", e.getMessage());
        }
        return applicationStatusResponse;
    }

    @HystrixCommand(
            threadPoolKey = "PL_PANDORA_API_POOL",
            groupKey = "PLPandoraClient",
            commandKey = "STATUS_PL"
    )
    public Status getStatusFromLender(String applicationId, String requestId, MerchantUser merchantUser) {
        StatusRequest statusRequest = new StatusRequest();
        statusRequest.setLspApplicationId(applicationId);
        statusRequest.setLenders(Lender.AXIS);

        com.flipkart.fintech.pandora.api.model.response.plOnboarding.StatusResponse statusResponse =
                new com.flipkart.fintech.pandora.api.model.response.plOnboarding.StatusResponse();
        try {
            statusResponse = plOnboardingClient.getStatusFromLender(statusRequest, requestId, merchantUser.getMerchantKey());
        } catch (Exception ex) {
            log.error("Error while calling pandora to get status for applicationId {}: {}", applicationId, ex.getMessage());
        }

        return fetchStatus(statusResponse.getStatus());
    }

    public com.flipkart.fintech.pandora.api.model.response.sandbox.v1.GetApplicationStatusResponse getStatusFromSandboxLenderV1(String lenderApplicationId, String requestId, MerchantUser merchantUser, com.flipkart.fintech.pandora.api.model.enums.lender.Lender lender) {
        com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v1.GetApplicationStatusRequest getApplicationStatusRequest = new com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v1.GetApplicationStatusRequest();
        getApplicationStatusRequest.setLender(String.valueOf(lender));
        getApplicationStatusRequest.setLenderApplicationId(lenderApplicationId);
        getApplicationStatusRequest.setLspId("SuperMoney");
        return pandoraClient.getApplicationStatusResponseV1(getApplicationStatusRequest);
    }

    public GetApplicationStatusResponse getStatusFromSandboxLenderV2(String lenderApplicationId, String requestId, MerchantUser merchantUser, com.flipkart.fintech.pandora.api.model.enums.lender.Lender lender, String lspId) {
        com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.GetApplicationStatusRequest getApplicationStatusRequest = new com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.GetApplicationStatusRequest();
        getApplicationStatusRequest.setLender(String.valueOf(lender));
        getApplicationStatusRequest.setLenderApplicationId(lenderApplicationId);
        getApplicationStatusRequest.setLspId("SuperMoney");
        return pandoraClient.getApplicationStatusResponseV2(getApplicationStatusRequest);
    }
    private void updateSuccessStatusDetails(StatusResponse getStatusResponse, ApplicationDataResponse applicationDataResponse) {
        getStatusResponse.setStatus(Status.SUCCESS);
        LoanApplication loanApplication = objectMapper.convertValue(applicationDataResponse.getApplicationData(), LoanApplication.class);
        String timestamp = loanApplication.getLenderDetails().getApplicationSubmittedTimestamp();
        Date date = new Date(Long.parseLong(timestamp));
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formatted = format.format(date);
        Timestamp timeStamp = Timestamp.valueOf(formatted);
        getStatusResponse.setSubmittedTimestamp(timeStamp);

        Offer submittedOffer = loanApplication.getLenderDetails().getSubmittedOffer().get(0);
        getStatusResponse.setLoanDetails(getLoanDetails(submittedOffer));
    }

    private void updateInProgressDetails(StatusResponse getStatusResponse, String applicationId, String merchantId) throws PinakaException {
        getStatusResponse.setStatus(Status.IN_PROGRESS);
        Token token = Token.builder().applicationId(applicationId).operation(Operation.RESUME).build();
        try {
            String plResumeUri = plURLHelper.getPLResumeUrl(merchantId);
            String url = new URIBuilder(plResumeUri)
                    .addParameter(PinakaConstants.PLConstants.TOKEN, EncryptionUtil.encryptWithAes(objectMapper.writeValueAsString(token), PinakaConstants.PLConstants.PL_ENCRYPTION_KEY))
                    .toString();
            getStatusResponse.setUrl(url);
        } catch (URISyntaxException | JsonProcessingException e) {
            throw new PinakaException(e);
        } catch (InvalidMerchantException e) {
            throw new PinakaException(e);
        }
    }

    private void resumeWorkflow(Status applicationStatus, String applicationId, MerchantUser merchantUser, LenderDetails lenderDetails,
                                ApplicationDataResponse applicationDataResponse) throws PinakaException {
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("lender_details", lenderDetails);
        Map<String, VariableData> workflowData = new HashMap<>();

        if (Status.DISBURSAL_IN_PROGRESS.equals(applicationStatus)) {
            workflowData.put(PinakaConstants.PLWorkflowVariable.LENDER_STATUS, new VariableData(false, Status.APPLICATION_COMPLETED.name()));
        } else {
            workflowData.put(PinakaConstants.PLWorkflowVariable.LENDER_STATUS, new VariableData(false, applicationStatus.name()));
        }

        ResumeApplicationRequest resumeApplicationRequest = ResumeApplicationRequest.builder()
                .applicationData(applicationData)
                .workflowData(workflowData)
                .smUserId(merchantUser.getSmUserId())
                .pendingTask(applicationDataResponse.getPendingTask().get(0))
                .build();

        applicationService.resumeApplication(merchantUser, applicationId, resumeApplicationRequest);
    }

    List<EriDetailDTOS> fetcheriDetailDTOSList(List<com.flipkart.fintech.pandora.api.model.response.plOnboarding.EriDetailDTOS> eriDetailDTOSList) {
        List<EriDetailDTOS> eriDetailDTOSList1 = new ArrayList<>();
        for (com.flipkart.fintech.pandora.api.model.response.plOnboarding.EriDetailDTOS detailDTOS : eriDetailDTOSList) {
            EriDetailDTOS eriDetailDTOS = new EriDetailDTOS();
            eriDetailDTOS.setEriAmount(detailDTOS.getEriAmount());
            eriDetailDTOS.setDueDate(detailDTOS.getDueDate());
            eriDetailDTOS.setClosingPrincipal(detailDTOS.getClosingPrincipal());
            eriDetailDTOS.setInterestContribution(detailDTOS.getInterestContribution());
            eriDetailDTOS.setPrincipalContribution(detailDTOS.getPrincipalContribution());
            eriDetailDTOS.setOpeningPrincipal(detailDTOS.getOpeningPrincipal());

            eriDetailDTOSList1.add(eriDetailDTOS);
        }

        return eriDetailDTOSList1;
    }

    private LoanDetails getLoanDetails(Offer submittedOffer) {

        LoanDetails loanDetails = new LoanDetails();
        Map<Charge, ChargeDetails> chargeDetailsMap = new HashMap<>();
        for (ChargeBreakup chargeBreakUp : submittedOffer.getCharges()) {
            ChargeDetails chargeDetails = new ChargeDetails();
            if (ChargeType.STAMP_DUTY.equals(chargeBreakUp.getChargeType())) {
                chargeDetails.setAmount(chargeBreakUp.getAmount());
                chargeDetails.setGstAmount(chargeBreakUp.getGstAmount());
                chargeDetails.setTotalAmount(chargeBreakUp.getTotalAmount());
                chargeDetailsMap.put(Charge.STAMP_DUTY, chargeDetails);
            } else {
                chargeDetails.setAmount(chargeBreakUp.getAmount());
                chargeDetails.setGstAmount(chargeBreakUp.getGstAmount());
                chargeDetails.setTotalAmount(chargeBreakUp.getTotalAmount());
                chargeDetailsMap.put(Charge.PROCESSING_FEE, chargeDetails);
            }
        }
        loanDetails.setCharges(chargeDetailsMap);
        loanDetails.setFlipkartCharges(PageConstant.PL_OFFER_SCREEN_FORM.flipkartChargesValue);
        loanDetails.setLoanAmount(submittedOffer.getLoanAmount());
        loanDetails.setRoi(submittedOffer.getRoi());
        loanDetails.setNetDisbursalAmount(submittedOffer.getNetDisbursalAmount());
        loanDetails.setTenure(submittedOffer.getTenure());

        Emi emi = new Emi();
        emi.setAmount(submittedOffer.getEmi());
        loanDetails.setEmi(emi);

        return loanDetails;
    }

    private Status fetchStatus(com.flipkart.fintech.pandora.api.model.response.plOnboarding.Status status) {
        switch (status) {
            case LOAN_DISBURSAL_SUCCESS:
            case SUCCESS:
                return Status.SUCCESS;
            case RETRY_WITH_EDIT:
                return Status.RETRY_WITH_EDIT;
            case REJECTED:
                return Status.REJECTED;
            case APPLICATION_COMPLETED:
                return Status.DISBURSAL_IN_PROGRESS;
            case RETRY_WITHOUT_EDIT:
                return Status.RETRY_WITHOUT_EDIT;
            case INPROGRESS:
                return Status.IN_PROGRESS;
            default:
                return Status.FAILED;
        }
    }

    private com.flipkart.fintech.pinaka.api.request.v6.Value fetchRepaymentUnit(Unit unit) {
        if (unit.equals(Unit.MONTHLY)) {
            return com.flipkart.fintech.pinaka.api.request.v6.Value.MONTHLY;
        } else {
            log.error("Invalid Repayment Frequency from pandora {}", unit.name());
        }

        return null;
    }

    private void isUserAllowedForJourney(MerchantUser merchantUser, ApplicationDataResponse applicationDataResponse) throws PinakaException {
        if (!merchantUser.getMerchantUserId().equals(applicationDataResponse.getExternalUserId())) {
            throw new PinakaException("AccountId doesn't match with account id linked with application");
        }

        if (StringUtils.isNotEmpty(applicationDataResponse.getSmUserId()) && StringUtils.isNotEmpty(merchantUser.getSmUserId())
                && !merchantUser.getSmUserId().equals(applicationDataResponse.getSmUserId())) {
            throw new PinakaException("AccountId doesn't match with account id linked with application");
        }
    }
}
