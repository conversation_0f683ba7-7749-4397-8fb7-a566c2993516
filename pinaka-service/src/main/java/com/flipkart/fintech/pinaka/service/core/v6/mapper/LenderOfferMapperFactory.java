package com.flipkart.fintech.pinaka.service.core.v6.mapper;

import com.flipkart.fintech.pinaka.service.core.v6.mapper.impl.AxisOfferMapper;
import com.flipkart.fintech.pinaka.service.core.v6.mapper.impl.SandboxV2OfferMapper;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import lombok.CustomLog;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@CustomLog
public class LenderOfferMapperFactory {
    private final AxisOfferMapper axisOfferMapper;
    private final SandboxV2OfferMapper sandboxV2OfferMapper;
    private final Set<String> sandboxV2Lenders;

    @Inject
    public LenderOfferMapperFactory(AxisOfferMapper axisOfferMapper, SandboxV2OfferMapper sandboxV2OfferMapper) {
        this.axisOfferMapper = axisOfferMapper;
        this.sandboxV2OfferMapper = sandboxV2OfferMapper;
        this.sandboxV2Lenders = new HashSet<>(Arrays.asList(
                "MONEYVIEW", "MONEYVIEWOPENMKT", "FIBE", "MONEYVIEWV2",
                "FINNABLE", "RING", "SMARTCOIN", "FIBEV2", "OMNIV2", "ABFL", "PFL"
        ));
    }

    public LenderOfferMapper getMapper(String lender) {
        if (lender == null) {
            return null;
        }

        if (sandboxV2Lenders.contains(lender)) {
            return sandboxV2OfferMapper;
        } else if ("AXIS".equals(lender)) {
            return axisOfferMapper;
        }
        log.warn("Lender offer mapper not found for lender: {}", lender);
        return null;
    }
}