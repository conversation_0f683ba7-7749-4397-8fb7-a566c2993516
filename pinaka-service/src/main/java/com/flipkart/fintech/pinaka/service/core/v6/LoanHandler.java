package com.flipkart.fintech.pinaka.service.core.v6;

import com.flipkart.fintech.pinaka.api.request.v6.LandingPageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.ResumePageRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.core.v6.impl.LoanHandlerImpl;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.inject.ImplementedBy;


@ImplementedBy(LoanHandlerImpl.class)
public interface LoanHandler {

    PageActionResponse getPageActionResponse(ApplicationDataResponse applicationDataResponse, String requestId, MerchantUser merchantUser) throws PinakaException;
}
