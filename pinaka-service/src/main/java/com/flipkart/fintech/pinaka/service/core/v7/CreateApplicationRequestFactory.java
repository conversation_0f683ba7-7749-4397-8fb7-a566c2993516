package com.flipkart.fintech.pinaka.service.core.v7;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.ams.ApplicationTypeUtils;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pinaka.api.enums.JourneyContext;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.ApplicationUserData;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.LoanApplication;
import com.flipkart.fintech.pinaka.api.request.v6.UserDetails;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileCremoScore;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScores;
import com.flipkart.fintech.pinaka.common.utils.UserProfileInsightsUtils;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.data.model.WhitelistEntity;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.pinaka.service.helper.ApplicationUserInputData;
import com.flipkart.fintech.pinaka.service.utils.EncryptionUtil;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util;
import com.flipkart.fintech.profile.common.DynamicConfigHelper;
import com.flipkart.fintech.security.aes.AESService;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import java.nio.charset.StandardCharsets;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.sumo.crisys.api.CremoScores;
import com.sumo.crisys.client.CrisysClientException;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections.MapUtils;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.SMONEY_PL_DATA_ENCRYPTION_KEY;

@CustomLog
public class CreateApplicationRequestFactory {
    private final UserProfileScores userProfileScores;
    private final ObjectMapper objectMapper;
    private final UserProfileCremoScore userProfileCremoScore;
    private final DynamicBucket dynamicBucket;
    @Inject
    public CreateApplicationRequestFactory(ObjectMapper objectMapper, UserProfileCremoScore userProfileCremoScore, UserProfileScores userProfileScores, DynamicBucket dynamicBucket) {
        this.userProfileScores = userProfileScores;
        this.objectMapper = objectMapper;
        this.userProfileCremoScore = userProfileCremoScore;
        this.dynamicBucket = dynamicBucket;
    }

    public CreateApplicationRequest create(MerchantUser merchantUser, String requestId,
                                           WhitelistEntity whitelistEntity, String offer_id, Map<String, Object> leadData) throws DataEnrichmentException {
        String lender = whitelistEntity.getLender();
        ProductType productType = whitelistEntity.getProductType();
        String applicationType = ApplicationTypeUtils.getApplicationType(productType, lender);
        FetchUserProfileResponse fetchUserProfileResponse = new FetchUserProfileResponse();
        UserProfileResponseV3 userProfileResponseV3 = new UserProfileResponseV3();
        Double partnerScore;

        Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
        if(dexterFlag){
            fetchUserProfileResponse = userProfileScores.getUserProfileByDexter(requestId,merchantUser);

        }else{
            userProfileResponseV3 = userProfileScores.getUserProfile(merchantUser);
        }
        partnerScore = getPartnerScore(userProfileResponseV3, fetchUserProfileResponse, lender, merchantUser, requestId);
        String shippingAddressId = UserProfileInsightsUtils.getShippingAddressId(userProfileResponseV3, fetchUserProfileResponse, dynamicBucket);

        CremoScores cremoScore = new CremoScores(null,null,null,"5", "5", null,null,"5");
        try {
            cremoScore = userProfileScores.getCremoScore(merchantUser);
        } catch (CrisysClientException e) {
            log.error("Error while fetching cremo score for user : ", merchantUser.getSmUserId());
            log.error(e.getMessage(), e);
        }

        Map<String, String> cremoModel = new HashMap<>();
        if(cremoScore!=null && StringUtils.isEmpty(cremoScore.losV2CremoBand())){
            cremoModel.put("cremo_band", "5");
        } else{
            cremoModel.put("cremo_band", cremoScore.losV2CremoBand());
            cremoModel.put("version", cremoScore.modelVersion());
            cremoModel.put("cremo_score", cremoScore.cremoScore());
        }

        Map<String, String> granularCremoModel = new HashMap<>();
        if(cremoScore!=null && StringUtils.isEmpty(cremoScore.granularCremoBand())){
            granularCremoModel.put("cremo_band", "5");
        } else{
            granularCremoModel.put("cremo_band", cremoScore.granularCremoBand());
            granularCremoModel.put("version", cremoScore.modelVersion());
            granularCremoModel.put("cremo_score", cremoScore.cremoScore());
        }

        Map<String, VariableData> workflowData = new HashMap<>();

        Boolean useAddressDetailsAxis = DynamicConfigHelper.getBoolean(dynamicBucket, "useAddressDetailsAxis", false);

        if(useAddressDetailsAxis && lender.equals("AXIS"))
            workflowData.put("shipping_address_id", new VariableData(false, null));
        else
            workflowData.put("shipping_address_id", new VariableData(false, shippingAddressId));

        workflowData.put("partner_score", new VariableData(false, String.valueOf(partnerScore)));
        workflowData.put("bin_score", new VariableData(false, partnerScore));
        workflowData.put("offer_id", new VariableData(false, offer_id));
        workflowData.put("lender", new VariableData(false, lender));
        workflowData.put("merchant_id", new VariableData(false, merchantUser.getMerchantKey()));
        workflowData.put("cremo_model", new VariableData(false, cremoModel));
        workflowData.put("granular_cremo_model", new VariableData(false, granularCremoModel));
        if(lender.equals("PREFR")){
            workflowData.put("workFlowVersion", new VariableData(false,"Sandbox_V1.5"));
        } else if (lender.equals("DMI")) {
            workflowData.put("workFlowVersion", new VariableData(false,"Sandbox_V1.0"));
        } else if (dynamicBucket.getStringArray("Sandbox_V2_lenders").contains(lender)) {
            workflowData.put("workFlowVersion", new VariableData(false,"Sandbox_V2.0"));
        }
        UserDetails userDetails = new UserDetails();
        userDetails.setShippingAddressId(shippingAddressId);
        userDetails.setBinScore(String.valueOf(partnerScore));
        Map<String, Object> basicDetails = new HashMap<>();
        Map<String, String> verifyPincode = new HashMap<>();
        Map<String, Object> verifyPanName = null;
        Map<String, Object> createProfile = null;
        Map<String, Object> addressDetails = new HashMap<>();
        Map<String, Object> workDetails = new HashMap<>();
        if(leadData != null) {
            try {
                log.info("log lead data: {}",leadData);
                if (LV4Util.isLv4Application(leadData)) {
                    log.info("Inside extractBasicDetailsFromLeadV4  for userId: {}", merchantUser.getSmUserId());
                    workflowData = extractBasicDetailsFromLeadV3(leadData, workflowData, basicDetails, addressDetails, workDetails, verifyPincode);
                } else if (LV3Util.isLv3Application(leadData)) {
                    log.info("Inside extractBasicDetailsFromLeadV3  for userId: {}", merchantUser.getSmUserId());
                    workflowData = extractBasicDetailsFromLeadV3(leadData, workflowData, basicDetails, addressDetails, workDetails, verifyPincode);
                } else {
                    log.info("Inside extractBasicDetailsFromLead for userId: {}", merchantUser.getSmUserId());
                    workflowData = extractBasicDetailsFromLead(leadData, workflowData, basicDetails, addressDetails, workDetails, verifyPincode);
                }
                if(leadData.containsKey("verifyPincode")){
                    verifyPincode = (Map<String, String>) leadData.get("verifyPincode");
                }
                if(leadData.containsKey("verifyPan")){
                    verifyPanName = (Map<String, Object>) leadData.get("verifyPan");
                }
                createProfile = (Map<String, Object>) leadData.get("createProfile");
                String pan = (String) basicDetails.get("pan");
                String decryptedPan = decryptFormData(pan);
                String dob = (String) basicDetails.get("dob");
                String employmentType;
                if(LV4Util.isLv4Application(leadData)) {
                    log.info("Fetching employmentType for LV4  for userId: {}", merchantUser.getSmUserId());
                    employmentType = String.valueOf(basicDetails.get("employmentType"));
                } else if(LV3Util.isLv3Application(leadData)) {
                    log.info("Fetching employmentType for LV3  for userId: {}", merchantUser.getSmUserId());
                   employmentType = String.valueOf(basicDetails.get("employmentType"));
                } else {
                    log.info("Fetching employmentType for LV2  for userId: {}", merchantUser.getSmUserId());
                    employmentType = (String) basicDetails.get("employmentType");
                }
                String decryptedDob = decryptFormData(dob);
                FinalName finalName = FinalName.from(verifyPanName, basicDetails);
                basicDetails.put("firstName", finalName.getEncryptedFirstName());
                basicDetails.put("lastName", finalName.getEncryptedLastName());
                DateTimeFormatter outputDateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                decryptedDob = LocalDate.parse(decryptedDob, DateTimeFormatter.ofPattern("dd/MM/yyyy")).format(outputDateFormat);
                workflowData.put("basicDetails", new VariableData(false, basicDetails));
                workflowData.put("verifyPanName", new VariableData(false, verifyPanName));
                workflowData.put("los_journey", new VariableData(false, true));
                workflowData.put("pan_consent_provided", new VariableData(false, true));
                workflowData.put("pan_number", new VariableData(false, EncryptionUtil.encryptWithAes(decryptedPan, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY)));
                workflowData.put("dob", new VariableData(false, decryptedDob));
                workflowData.put("firstName", new VariableData(false, EncryptionUtil.encryptWithAes(finalName.getFirstName(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY)));
                workflowData.put("lastName", new VariableData(false, EncryptionUtil.encryptWithAes(finalName.getLastName(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY)));
                // add leadId here
                if(leadData.containsKey("leadId")){
                    workflowData.put("leadId", new VariableData(false, leadData.get("leadId")));
                }
                userDetails.setPan(EncryptionUtil.encryptWithAes(decryptedPan, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
                userDetails.setDob(decryptedDob);
                workflowData.put("employmentType", new VariableData(false, employmentType));
                workflowData.put("gender", new VariableData(false, basicDetails.get("gender")));
                userDetails.setGender((String) basicDetails.get("gender"));
                workflowData.put("category_type", new VariableData(false, whitelistEntity.getProductType().toString()));
                workflowData.put("request_id", new VariableData(false, whitelistEntity.getProductType().toString()));
            } catch (Exception e) {
                log.error("Error while fetching leadDetails", e);
                throw new RuntimeException("Error while fetching leadDetails", e);
            }
        }
        Map<String, Object> applicationData = createApplicationData(merchantUser, whitelistEntity, userDetails);
        applicationData.put("offer_id", offer_id);
        applicationData.put("basicDetails", basicDetails);
        applicationData.put("verifyPincode", verifyPincode);
        applicationData.put("addressDetails", addressDetails);
        applicationData.put("workDetails", workDetails);
        applicationData.put("createProfile", createProfile);
        applicationData.put("verifyPan", verifyPanName);
        CreateApplicationRequest createApplicationRequest = new CreateApplicationRequest();
        if (MapUtils.isNotEmpty(leadData) && leadData.containsKey("leadId")) {
            applicationData.put("leadId", leadData.get("leadId"));
            createApplicationRequest.setLeadId((String) leadData.get("leadId"));
        }

        createApplicationRequest.setExternalUserId(merchantUser.getMerchantUserId());
        createApplicationRequest.setSmUserId(merchantUser.getSmUserId());
        createApplicationRequest.setApplicationType(applicationType);
        createApplicationRequest.setWorkflowData(workflowData);
        createApplicationRequest.setApplicationData(applicationData);
        createApplicationRequest.setFinancialProvider(lender);
        createApplicationRequest.setMerchantId(merchantUser.getMerchantKey());
        return createApplicationRequest;
    }

    private Map<String, VariableData> extractBasicDetailsFromLead(Map<String, Object> leadData, Map<String, VariableData> workflowData,
                                                                  Map<String, Object> basicDetails, Map<String, Object> addressDetails,
                                                                  Map<String, Object> workDetails, Map<String, String> verifyPincode) {
        if(leadData.containsKey("basicDetails")){
            Map<String, Object> basicDetailsData = (Map<String, Object>) leadData.get("basicDetails");
            basicDetails.putAll(new HashMap<>(basicDetailsData));
            workflowData.put("basicDetails", new VariableData(false, basicDetails));
            return workflowData;
        }
        if(leadData.containsKey("namePage")){
            Map<String, Object> namePageData = (Map<String, Object>) leadData.get("namePage");
            basicDetails.putAll(new HashMap<>(namePageData));
            basicDetails.put("phone", namePageData.get("phoneNumber"));
        }
        if(leadData.containsKey("reviewPage1")){
            Map<String, Object> reviewPage1Data = (Map<String, Object>) leadData.get("reviewPage1");
            basicDetails.put("pan",reviewPage1Data.get("panNumber"));
            basicDetails.put("dob", reviewPage1Data.get("dob"));
            basicDetails.put("email", reviewPage1Data.get("email"));
            basicDetails.put("gender", reviewPage1Data.get("gender"));
            basicDetails.put("employmentType", reviewPage1Data.get("employmentType"));
            basicDetails.put("loanPurpose", reviewPage1Data.get("loanPurpose"));
            addressDetails.put("houseNumber", reviewPage1Data.get("houseNumber"));
            addressDetails.put("area", reviewPage1Data.get("area"));
            verifyPincode = (Map<String, String>) reviewPage1Data.get("pincodeDetails");
            addressDetails.put("city", encryptFormData(verifyPincode.get("city")));
            addressDetails.put("state", encryptFormData(verifyPincode.get("state")));
            addressDetails.put("pincode", verifyPincode.get("pincode"));
            basicDetails.put("pincode", verifyPincode.get("pincode"));
        }
        if(leadData.containsKey("reviewPage2")){
            Map<String, Object> reviewPage2Data = (Map<String, Object>) leadData.get("reviewPage2");
            workDetails.put("organization", reviewPage2Data.get("organization"));
            workDetails.put("income", reviewPage2Data.get("income"));
            workDetails.put("incomeSource", reviewPage2Data.get("incomeSource"));
            if(reviewPage2Data.containsKey("industryName")){
                workDetails.put("industryName", reviewPage2Data.get("industryName"));
            }
            if(reviewPage2Data.containsKey("annualTurnOver")){
                workDetails.put("annualTurnOver", reviewPage2Data.get("annualTurnOver"));
            }
            if(reviewPage2Data.containsKey("bonusIncome") && reviewPage2Data.get("bonusIncome") != null){
                workDetails.put("bonusIncome", reviewPage2Data.get("bonusIncome"));
            }
            if(reviewPage2Data.containsKey("consentListData")){
                workDetails.put("consentListData", reviewPage2Data.get("consentListData"));
            }
        }
        workflowData.put("basicDetails", new VariableData(false, basicDetails));
        workflowData.put("addressDetails", new VariableData(false, addressDetails));
        workflowData.put("workDetails", new VariableData(false, workDetails));
        workflowData.put("verifyPincode", new VariableData(false, verifyPincode));

        return workflowData;
    }

    private Map<String, VariableData> extractBasicDetailsFromLeadV3(Map<String, Object> leadData, Map<String, VariableData> workflowData,
                                                                  Map<String, Object> basicDetails, Map<String, Object> addressDetails,
                                                                  Map<String, Object> workDetails, Map<String, String> verifyPincode) throws PinakaException {
        ApplicationUserData applicationUserInputData = ApplicationUserInputData.getApplicationResponseData(leadData);
        basicDetails.put("pan", applicationUserInputData.getPan());
        basicDetails.put("dob", applicationUserInputData.getDob());
        basicDetails.put("email", applicationUserInputData.getEmail());
        basicDetails.put("gender", applicationUserInputData.getGender());
        basicDetails.put("employmentType", applicationUserInputData.getEmploymentType());
        basicDetails.put("loanPurpose", applicationUserInputData.getLoanPurpose());
        basicDetails.put("firstName", applicationUserInputData.getFirstName());
        basicDetails.put("lastName", applicationUserInputData.getLastName());
        basicDetails.put("phone", applicationUserInputData.getPhoneNumber());
        if(Objects.nonNull(applicationUserInputData.getConsentData())) {
            basicDetails.put("consentData", applicationUserInputData.getConsentData());
        }

        addressDetails.put("houseNumber", applicationUserInputData.getHouseNumber());
        addressDetails.put("area", applicationUserInputData.getArea());
        addressDetails.put("city", encryptFormData(applicationUserInputData.getCity()));
        addressDetails.put("state", encryptFormData(applicationUserInputData.getState()));
        addressDetails.put("pincode", applicationUserInputData.getPincode());
        verifyPincode.put("city", applicationUserInputData.getCity());
        verifyPincode.put("state", applicationUserInputData.getState());
        verifyPincode.put("pincode", String.valueOf(applicationUserInputData.getPincode()));
        basicDetails.put("pincode", applicationUserInputData.getPincode());

        workDetails.put("organization", applicationUserInputData.getOrganization());
        workDetails.put("income", applicationUserInputData.getIncome());
        workDetails.put("incomeSource", applicationUserInputData.getIncomeSource());
        if(Objects.nonNull(applicationUserInputData.getIndustryName())){
            workDetails.put("industryName", applicationUserInputData.getIndustryName());
        }
        if(StringUtils.isNotEmpty(applicationUserInputData.getAnnualTurnOver())){
            workDetails.put("annualTurnOver", applicationUserInputData.getAnnualTurnOver());
        }
        if(Objects.nonNull(applicationUserInputData.getConsentListData())){
            workDetails.put("consentListData", applicationUserInputData.getConsentListData());
        }
        if(StringUtils.isNotBlank(applicationUserInputData.getBonusIncome())) {
            workDetails.put("bonusIncome", applicationUserInputData.getBonusIncome());
        }
        workflowData.put("basicDetails", new VariableData(false, basicDetails));
        workflowData.put("addressDetails", new VariableData(false, addressDetails));
        workflowData.put("workDetails", new VariableData(false, workDetails));
        workflowData.put("verifyPincode", new VariableData(false, verifyPincode));
        return workflowData;
    }

    //TODO : convert this into a strategy pattern get partner score on the basis of lender
    private Double getPartnerScore(UserProfileResponseV3 userProfileResponseV3, FetchUserProfileResponse fetchUserProfileResponse, String lender, MerchantUser merchantUser, String requestId) {
        if ("IDFC".equals(lender)) {
            log.info("Getting cremo score for merchantId:{}", merchantUser.getMerchantUserId());
            return userProfileCremoScore.getCremoScoreByUserProfile(userProfileResponseV3, fetchUserProfileResponse);

        }
        return userProfileScores.getUserBinScore(merchantUser, requestId);
    }

    private String decryptFormData(String data) {
        byte[] plaintextBytes = AESService.decrypt(SMONEY_PL_DATA_ENCRYPTION_KEY,
                Base64.getDecoder().decode(data.getBytes(StandardCharsets.UTF_8)));
        return new String(plaintextBytes, StandardCharsets.UTF_8);
    }

    private String encryptFormData(String data) {
        byte[] ciphertextBytes = AESService.encrypt(SMONEY_PL_DATA_ENCRYPTION_KEY, data.getBytes(StandardCharsets.UTF_8));
        return new String(Base64.getEncoder().encode(ciphertextBytes), StandardCharsets.UTF_8);
    }

    private Map<String, Object> createApplicationData(MerchantUser user, WhitelistEntity whitelistEntity, UserDetails userDetails) {
        Format formatter = new SimpleDateFormat(PinakaConstants.DATE_FORMAT_PATTERN);
        String applicationCreatedAt = formatter.format(new Date());

        LoanApplication loanApplicationData = LoanApplication.builder()
                .externalUserId(user.getMerchantUserId())
                .smUserId(user.getSmUserId())
                .financialProvider(whitelistEntity.getLender())
                .userDetails(userDetails)
                .journeyContext(JourneyContext.PERSONAL_LOAN_APPLICATION_JOURNEY.name())
                .productType(String.valueOf(whitelistEntity.getProductType()))
                .applicationCreatedAt(applicationCreatedAt)
                .build();

        Map<String, Object> applicationData = objectMapper.convertValue(loanApplicationData, Map.class);
        return applicationData;
    }

}
