package com.flipkart.fintech.pinaka.service.core.v7;

import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.*;
import com.flipkart.fintech.pinaka.api.response.*;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.flipkart.fintech.pinaka.service.exception.WhiteListException;

import java.util.List;

public interface BorrowerServiceV7 {

  List<BorrowerEntity> getActiveBorrowers(MerchantUser user, List<ProductType> productTypeList);
}
