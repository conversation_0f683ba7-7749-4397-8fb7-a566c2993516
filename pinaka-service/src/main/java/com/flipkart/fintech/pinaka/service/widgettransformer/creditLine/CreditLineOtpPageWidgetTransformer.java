package com.flipkart.fintech.pinaka.service.widgettransformer.creditLine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.service.response.CreditLineFormPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import org.apache.http.NameValuePair;

import java.util.List;

public class CreditLineOtpPageWidgetTransformer {
    private static String otpScreenJson;

    static {
        otpScreenJson = TransformerUtils.readFileasString("template/OtpPageJson.json");
    }

    public GenericFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse) throws JsonProcessingException {
        GenericFormWidgetData widgetData = ObjectMapperUtil.get().readValue(otpScreenJson, GenericFormWidgetData.class);
        String phoneNumber = (String) applicationDataResponse.getApplicationData().get("phoneNumber");
        String lastFourDigits = "";
//TODO add unit test case for this
        if (phoneNumber != null && phoneNumber.length() >= 4) {
            lastFourDigits = phoneNumber.substring(phoneNumber.length() - 4);
        }
        String text = "Please enter the OTP sent to +91 xxxxxx"+ lastFourDigits  +" to process your amount";
        widgetData.getSubTitle().setText(text);
        updateSubmitButton(widgetData.getSubmitButton(), applicationDataResponse);
        return widgetData;
    }
    private void updateSubmitButton(SubmitButtonValue submitButton, ApplicationDataResponse applicationDataResponse) {
        Action action = submitButton.getButton().getAction();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        action.setParams(QueryParamUtils.getQueryParams(queryParams));

    }
}
