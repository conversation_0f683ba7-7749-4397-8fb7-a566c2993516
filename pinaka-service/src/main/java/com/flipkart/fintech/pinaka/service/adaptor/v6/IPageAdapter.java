package com.flipkart.fintech.pinaka.service.adaptor.v6;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.DataEnum;
import com.flipkart.fintech.pinaka.api.request.v6.FormType;
import com.flipkart.fintech.pinaka.api.request.v6.LoanApplication;
import com.flipkart.fintech.pinaka.api.response.v6.DataEnumResponse;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.supermoney.ams.bridge.exceptions.InvalidMerchantException;
import com.flipkart.fintech.winterfell.api.request.VariableData;

import java.util.List;
import java.util.Map;

public interface IPageAdapter {
    Map<String, VariableData> constructWorkflowData(LoanApplication loanApplication, Map<String,Object> formData, MerchantUser merchantUser, String applicationId, String requestId) throws DataEnrichmentException, InvalidMerchantException;
    Map<String, Object> constructApplicationData(LoanApplication loanApplication, Map<String,Object> formData, MerchantUser merchantUser, String applicationId) throws DataEnrichmentException;

    List<DataEnumResponse> constructPageResponse(List<DataEnum> dataEnumList, LoanApplication loanApplication, MerchantUser merchantUser, FormType formType) throws PinakaException;
}
