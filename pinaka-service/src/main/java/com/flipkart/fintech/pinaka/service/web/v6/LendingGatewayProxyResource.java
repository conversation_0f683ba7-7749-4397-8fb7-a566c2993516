package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.lending.orchestrator.service.LendingGatewayService;
import com.flipkart.fintech.pinaka.api.request.v6.LandingPageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.v6.ErrorOperation;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import de.client.shade.javax.validation.Valid;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;

import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("1/lg")
public class LendingGatewayProxyResource {

  private final LendingGatewayService lendingGatewayService;


  @Inject
  public LendingGatewayProxyResource(LendingGatewayService lendingGateway) {
    this.lendingGatewayService = lendingGateway;
  }

  @POST
  @Timed
  @ExceptionMetered
  @ApiOperation("Personal Loan resume journey")
  @Path("/resume")
  public PageActionResponse resumeJourney(@javax.validation.Valid LandingPageRequest landingPageRequest,
      @NotNull @HeaderParam("X-Request-Id") String requestId,
      @NotNull @HeaderParam("X-Merchant-Id") String merchantId,
      @NotNull @HeaderParam("X-User-Agent") String userAgent) {
    PageActionResponse pageActionResponse = new PageActionResponse();
    try {
      pageActionResponse = lendingGatewayService.getPage(landingPageRequest.getMerchantId(),landingPageRequest);
    } catch (Exception ex) {
      log.error("resume call failed: {}", ex.getMessage());
      // TODO return a proper error screen
      pageActionResponse.setActionSuccess(false);
      ErrorOperation error = ErrorOperation.builder().message("Error while creating application").build();
      pageActionResponse.setError(error);
    }
    return pageActionResponse;
  }


  @POST
  @Timed
  @ExceptionMetered
  @ApiOperation("UserAction Submit")
  @Path("/submit")
  public PageActionResponse submit(@Valid UserActionRequest submitRequest,
                                   @HeaderParam("X-User-Agent") String userAgent,
                                   @NotNull @HeaderParam("X-Request-Id") String requestId) {
    PageActionResponse pageActionResponse = new PageActionResponse();
    try {
      pageActionResponse = lendingGatewayService.submit(submitRequest);
    } catch (Exception e) {
      log.error("Error in submit for applicationId: {}, accountId: {}: {}", submitRequest.getApplicationId(),
              submitRequest.getAccountId(), e.getMessage(), e);
      throw e;
    }
    log.info("returning following response: {} for app_id:{}",pageActionResponse,submitRequest.getApplicationId());

    return pageActionResponse;
  }
}
