package com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4;

import com.flipkart.fintech.lead.model.Name;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.helper.UserActionSubmitRequestHelper;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.LV4FormWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.responseContext.consent.BaseConsentResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.responseContext.consent.CheckboxConsentResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.responseContext.consent.ActionTextConsentResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.CheckBoxFormFieldValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GroupedFormWidgetData;
import com.google.inject.Inject;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.apache.commons.text.WordUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.LEAD_V4_LANDING_PAGE;
import static com.flipkart.fintech.pinaka.service.helper.UserActionSubmitRequestHelper.FULL_NAME;
import static com.flipkart.fintech.pinaka.service.helper.UserActionSubmitRequestHelper.PHONE_NUMBER;

@CustomLog
public class LV4SubmitButtonWidgetTransformer implements LV4FormWidgetTransformer {

    // Constants for JSON field names and structure navigation
    private static final String JSON_KEY_PAGE_RESPONSE = "pageResponse";
    private static final String JSON_KEY_PAGE_DATA = "pageData";
    private static final String JSON_KEY_TRACKING_CONTEXT = "trackingContext";
    private static final String JSON_KEY_TRACKING = "tracking";
    private static final String JSON_KEY_SLOTS = "slots";
    private static final String JSON_KEY_WIDGET = "widget";
    private static final String JSON_KEY_DATA = "data";
    private static final String JSON_KEY_RENDERABLE_COMPONENTS = "renderableComponents";
    private static final String JSON_KEY_VALUE = "value";
    private static final String JSON_KEY_NAME = "name";
    public static final String JSON_KEY_SUBMIT_BUTTON = "submitButton";
    public static final String JSON_KEY_BUTTON = "button";
    public static final String JSON_KEY_ACTION = "action";
    public static final String JSON_KEY_PARAMS = "params";
    private static final String JSON_KEY_ACCOUNT_ID = "accountId";
    private static final String JSON_KEY_APPLICATION_ID = "applicationId";
    private static final String JSON_KEY_ENCRYPTION = "encryption";

    /**
     * Enum for LV4 form fields - similar to LV3 approach for structured prefilling
     */
    public enum LV4FormFields {
        NAME(FULL_NAME) {
            @Override
            public void prefill(Map<String, Object> valueMap, LeadPageDataSourceResponse leadPageDataSourceResponse, Decrypter decrypter) {
                // Note: This method is kept for compatibility but should use the new approach in transform methods
                ProfileDetailedResponse profile = leadPageDataSourceResponse.getProfile();
                if (profile != null) {
                    String fullName = LV4Util.getFullNameFromProfile(profile, leadPageDataSourceResponse.getIsNameEncrypted(), decrypter);
                    if (StringUtils.isNotBlank(fullName)) {
                        valueMap.put(JSON_KEY_VALUE, fullName);
                        log.info("Prefilled Name field with value: {}", fullName);
                    }
                }
            }
        },
        PHONE_NUMBER(UserActionSubmitRequestHelper.PHONE_NUMBER) {
            @Override
            public void prefill(Map<String, Object> valueMap, LeadPageDataSourceResponse leadPageDataSourceResponse, Decrypter decrypter) {
                ProfileDetailedResponse profile = leadPageDataSourceResponse.getProfile();
                if (profile != null && StringUtils.isNotBlank(profile.getPhoneNo())) {
                    String phoneNumber = profile.getPhoneNo();
                    valueMap.put(JSON_KEY_VALUE, phoneNumber);
                    log.info("Prefilled Phone Number field with value: {}", phoneNumber);
                }
            }
        };

        private final String fieldName;

        LV4FormFields(String fieldName) {
            this.fieldName = fieldName;
        }

        public String getFieldName() {
            return fieldName;
        }

        public abstract void prefill(Map<String, Object> valueMap, LeadPageDataSourceResponse leadPageDataSourceResponse, Decrypter decrypter);

        public static LV4FormFields fromFieldName(String fieldName) {
            for (LV4FormFields field : values()) {
                if (field.getFieldName().equals(fieldName)) {
                    return field;
                }
            }
            return null;
        }
    }

    private static final String BOTTOM_SHEET_BUTTON_TEMPLATE;
    private static final String SUBMITT_BUTTON_TEMPLATE;
    private final Decrypter decrypter;
    private final BqIngestionHelper bqIngestionHelper;
    private final FormConfig formConfig = new FormConfig("18", "90", PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);
    private final DynamicBucket dynamicBucket;
    private final FormWidgetDataFetcher formWidgetDataFetcher;
    private final FormWidgetDataPrefillUtils formWidgetDataPrefillUtils;

    static {
        BOTTOM_SHEET_BUTTON_TEMPLATE = TransformerUtils.readFileasString("template/lead/V4/LandingPageBottomSheet.json");
        SUBMITT_BUTTON_TEMPLATE = TransformerUtils.readFileasString("template/lead/V4/LandingPageHiddenForm.json");
    }

    @Inject
    public LV4SubmitButtonWidgetTransformer(Decrypter decrypter,
                                           BqIngestionHelper bqIngestionHelper,
                                           DynamicBucket dynamicBucket,
                                           FormWidgetDataFetcher formWidgetDataFetcher,
                                           FormWidgetDataPrefillUtils formWidgetDataPrefillUtils) {
        this.decrypter = decrypter;
        this.bqIngestionHelper = bqIngestionHelper;
        this.dynamicBucket = dynamicBucket;
        this.formWidgetDataFetcher = formWidgetDataFetcher;
        this.formWidgetDataPrefillUtils = formWidgetDataPrefillUtils;
    }

    @Override
    public GenericFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse, WidgetTypeV4 widgetType) throws PinakaException {
        GroupedFormWidgetData groupedFormWidgetData;
        try {
            PageDataSource<ReviewUserDataSourceResponse> reviewDataSource = new InitialUserReviewDataSource();
            ReviewUserDataSourceResponse reviewUserDataSourceResponse = reviewDataSource.getData(applicationDataResponse);

            // Check if name and phone number are available in application data
            boolean hasNameInAppData = LV4Util.hasNameInApplicationData(applicationDataResponse);
            boolean hasPhoneInAppData = LV4Util.hasPhoneNumberInApplicationData(applicationDataResponse);

            // Only fetch leadPageDataSourceResponse if we need fallback data
            LeadPageDataSourceResponse leadPageDataSourceResponse = null;
            if (!hasNameInAppData || !hasPhoneInAppData) {
                PageDataSource<LeadPageDataSourceResponse> leadPageDataSource = new LeadPageDataSource();
                leadPageDataSourceResponse = leadPageDataSource.getData(applicationDataResponse);
                log.debug("Fetched leadPageDataSourceResponse for fallback - hasName: {}, hasPhone: {}", hasNameInAppData, hasPhoneInAppData);
            } else {
                log.debug("Skipping leadPageDataSourceResponse fetch - both name and phone available in application data");
            }

            // Determine template based on full name availability with fallback
            String templateToUse = determineTemplate(applicationDataResponse, leadPageDataSourceResponse, widgetType);
            if (templateToUse == null) return null;
            groupedFormWidgetData = ObjectMapperUtil.get().readValue(getFormJson(applicationDataResponse, templateToUse), GroupedFormWidgetData.class);

            // Extract form field maps for prefilling using LV3 utilities
            Map<String, Map<String, Object>> formFieldValueMapRaw = extractLV4FormFieldValueMap(groupedFormWidgetData);

            // Convert raw map to FormFieldValue map for LV3 utilities
            Map<String, FormFieldValue> formFieldValueMapToPrefill = convertToFormFieldValueMap(formFieldValueMapRaw);

            // Get form field submit buttons
            Map<String, SubmitButtonValue> formFieldSubmitButtons = extractSubmitButtonsFromLV4(groupedFormWidgetData);

            // Get user data for prefilling using new approach with fallback
            Map<String, Object> userData = new HashMap<>();
            // Add name data with fallback
            String fullName = LV4Util.getDecryptedFullNameWithFallback(applicationDataResponse, leadPageDataSourceResponse, decrypter);
            if (StringUtils.isNotBlank(fullName)) {
                userData.put(FULL_NAME, fullName);
            }
            // Add phone number data with fallback
            String phoneNumber = LV4Util.getPhoneNumberWithFallback(applicationDataResponse, leadPageDataSourceResponse);
            if (StringUtils.isNotBlank(phoneNumber)) {
                userData.put(PHONE_NUMBER, phoneNumber);
            }

            // Use LV3 utilities to prefill form fields
            this.formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);

            // Update the form fields in the GroupedFormWidgetData with prefilled values
            updateLV4FormFieldsWithPrefilled(groupedFormWidgetData, formFieldValueMapToPrefill);

            // Set up tracking data for form fields
            setupTrackingDataForFormFields(groupedFormWidgetData, applicationDataResponse, leadPageDataSourceResponse, reviewUserDataSourceResponse);

            // Update tracking data with correct accountId and applicationId
            updateTrackingData(groupedFormWidgetData, applicationDataResponse);

            // Update submit button with user data (V4 has different structure than V3)
            updateV4SubmitButton(groupedFormWidgetData, reviewUserDataSourceResponse);

            // Update consent
            addNameToConsent(groupedFormWidgetData, applicationDataResponse, leadPageDataSourceResponse);

            this.bqIngestionHelper.insertLeadEvents(LV3Util.getLeadEvents(applicationDataResponse, LEAD_V4_LANDING_PAGE, applicationDataResponse.getApplicationState(), "V4"));
        } catch (Exception e) {
            this.bqIngestionHelper.insertLeadEvents(LV3Util.getLeadEvents(applicationDataResponse, "ERROR_WHILE_BUILDING_PAGE", applicationDataResponse.getApplicationState(), "V4"));
            throw new PinakaException("Error while building widget Group Data for LV4 Landing Page for userId: " + applicationDataResponse.getSmUserId(), e);
        }
        return groupedFormWidgetData;
    }

    private void addNameToConsent(GroupedFormWidgetData groupedFormWidgetData, ApplicationDataResponse applicationDataResponse, LeadPageDataSourceResponse leadPageDataSourceResponse) {
        String name = LV4Util.getDecryptedFullNameWithFallback(applicationDataResponse, leadPageDataSourceResponse, decrypter);
        if (StringUtils.isNotBlank(name)) {
            name = WordUtils.capitalizeFully(name.toLowerCase());
            try {
                // Only update consent in direct submitButton structure (SUBMIT_BUTTON_TEMPLATE)
                // Do not update consent for BOTTOM_SHEET_BUTTON_TEMPLATE
                if (updateConsentInDirectSubmitButton(groupedFormWidgetData, name)) {
                    log.info("Successfully updated consent with name in SUBMIT_BUTTON_TEMPLATE: {}", name);
                } else {
                    log.debug("Consent not updated - not using SUBMIT_BUTTON_TEMPLATE structure");
                }
            } catch (Exception e) {
                log.error("Error updating consent with name: {}", name, e);
            }
        } else {
            log.debug("Name is blank, skipping consent update");
        }
    }

    /**
     * Updates consent text in direct submit button structure (SUBMIT_BUTTON_TEMPLATE)
     */
    private boolean updateConsentInDirectSubmitButton(GroupedFormWidgetData groupedFormWidgetData, String name) {
        try {
            if (groupedFormWidgetData.getSubmitButton() != null) {
                BaseConsentResponse consent = groupedFormWidgetData.getSubmitButton().getConsent();
                if (updateConsentTextInBaseConsentResponse(consent, name)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.debug("Could not update consent in direct structure: {}", e.getMessage());
        }
        return false;
    }

    /**
     * Updates consent text in BaseConsentResponse object
     * Returns true if consent was successfully updated, false otherwise
     */
    private boolean updateConsentTextInBaseConsentResponse(BaseConsentResponse consentResponse, String name) {
        if (consentResponse == null) {
            return false;
        }

        try {
            // Handle CheckboxConsentResponse
            if (consentResponse instanceof CheckboxConsentResponse) {
                CheckboxConsentResponse checkboxConsent = (CheckboxConsentResponse) consentResponse;
                RenderableComponent<CheckBoxFormFieldValue> checkboxField = checkboxConsent.getCheckboxField();
                if (checkboxField.getValue() != null) {
                    RenderableComponent<RichTextValue> actionText = checkboxField.getValue().getActionText();
                    if (addName(name, actionText)) return true;
                }
            }

            // Handle ActionTextConsentResponse
            if (consentResponse instanceof ActionTextConsentResponse) {
                ActionTextConsentResponse actionTextConsent = (ActionTextConsentResponse) consentResponse;
                List<RenderableComponent<RichTextValue>> actionTexts = actionTextConsent.getActionTexts();
                for (RenderableComponent<RichTextValue> actionTextComponent : actionTexts) {
                    if (addName(name, actionTextComponent)) return true;
                }
            }

        } catch (Exception e) {
            log.debug("Could not update consent text in BaseConsentResponse: {}", e.getMessage());
        }

        return false;
    }

    private boolean addName(String name, RenderableComponent<RichTextValue> actionTextComponent) {
        if (actionTextComponent != null && actionTextComponent.getValue() != null) {
            RichTextValue richTextValue = actionTextComponent.getValue();
            String currentText = richTextValue.getText();
            if (StringUtils.isNotBlank(currentText)) {
                // Replace "I hereby" with "I, <fullName>, hereby"
                String updatedText = currentText.replaceFirst("I hereby", "I, " + name + ", hereby");
                richTextValue.setText(updatedText);
                log.debug("Updated consent text from 'I hereby' to 'I, {}, hereby'", name);
                return true;
            }
        }
        return false;
    }

    /**
     * Updates consent text within the submit button structure (legacy method for raw JSON)
     * Returns true if consent was successfully updated, false otherwise
     */
    private boolean updateConsentTextInSubmitButton(Map<String, Object> submitButton, String name) {
        Map<String, Object> consent = (Map<String, Object>) submitButton.get("consent");
        if (consent != null) {
            Map<String, Object> checkboxField = (Map<String, Object>) consent.get("checkboxField");
            if (checkboxField != null) {
                Map<String, Object> value = (Map<String, Object>) checkboxField.get(JSON_KEY_VALUE);
                if (value != null) {
                    Map<String, Object> actionText = (Map<String, Object>) value.get("actionText");
                    if (actionText != null) {
                        Map<String, Object> actionTextValue = (Map<String, Object>) actionText.get(JSON_KEY_VALUE);
                        if (actionTextValue != null) {
                            String currentText = (String) actionTextValue.get("text");
                            if (StringUtils.isNotBlank(currentText)) {
                                // Replace "I hereby" with "I, <fullName>, hereby"
                                String updatedText = currentText.replaceFirst("I hereby", "I, " + name + ", hereby");
                                actionTextValue.put("text", updatedText);
                                log.debug("Updated consent text from 'I hereby' to 'I, {}, hereby'", name);
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * Converts raw form field map to FormFieldValue map for LV3 utilities
     */
    private Map<String, FormFieldValue> convertToFormFieldValueMap(Map<String, Map<String, Object>> formFieldValueMapRaw) {
        Map<String, FormFieldValue> formFieldValueMap = new HashMap<>();

        for (Map.Entry<String, Map<String, Object>> entry : formFieldValueMapRaw.entrySet()) {
            String fieldName = entry.getKey();
            Map<String, Object> valueMap = entry.getValue();

            try {
                // Create a FormFieldValue from the raw map
                FormFieldValue formFieldValue = ObjectMapperUtil.get().convertValue(valueMap, FormFieldValue.class);
                formFieldValueMap.put(fieldName, formFieldValue);
            } catch (Exception e) {
                log.warn("Failed to convert field {} to FormFieldValue: {}", fieldName, e.getMessage());
            }
        }

        return formFieldValueMap;
    }

    /**
     * Extracts submit buttons from LV4 structure for LV3 utilities
     */
    private Map<String, SubmitButtonValue> extractSubmitButtonsFromLV4(GroupedFormWidgetData groupedFormWidgetData) {
        Map<String, SubmitButtonValue> submitButtons = new HashMap<>();

        // LV4 structure has submit buttons in different locations, extract them if needed
        // For now, return empty map as LV4 structure is different
        return submitButtons;
    }

    /**
     * Updates LV4 form fields with prefilled values from LV3 utilities
     */
    private void updateLV4FormFieldsWithPrefilled(GroupedFormWidgetData groupedFormWidgetData,
                                                 Map<String, FormFieldValue> formFieldValueMapToPrefill) {
        try {
            // Handle both template structures for updating prefilled values
            if (groupedFormWidgetData.getRenderableComponents() != null && !groupedFormWidgetData.getRenderableComponents().isEmpty()) {
                // Direct renderableComponents structure (SUBMIT_BUTTON_TEMPLATE)
                updateDirectRenderableComponentsWithPrefilled(groupedFormWidgetData, formFieldValueMapToPrefill);
            } else if (groupedFormWidgetData.getSubmitButton() != null &&
                       groupedFormWidgetData.getSubmitButton().getButton().getAction() != null) {
                // Nested pageResponse structure (BOTTOM_SHEET_BUTTON_TEMPLATE)
                updateNestedPageResponseWithPrefilled(groupedFormWidgetData, formFieldValueMapToPrefill);
            }

            log.info("Successfully updated LV4 form fields with prefilled values using LV3 utilities");
        } catch (Exception e) {
            log.error("Error updating LV4 form fields with prefilled values", e);
        }
    }

    /**
     * Updates direct renderableComponents with prefilled values (SUBMIT_BUTTON_TEMPLATE)
     */
    private void updateDirectRenderableComponentsWithPrefilled(GroupedFormWidgetData groupedFormWidgetData,
                                                             Map<String, FormFieldValue> formFieldValueMapToPrefill) {
        try {
            for (RenderableComponent<FormFieldValue> renderableComponent : groupedFormWidgetData.getRenderableComponents()) {
                FormFieldValue formFieldValue = renderableComponent.getValue();
                if (formFieldValue != null && formFieldValue.getName() != null) {
                    String fieldName = formFieldValue.getName();

                    if (formFieldValueMapToPrefill.containsKey(fieldName)) {
                        FormFieldValue prefilledValue = formFieldValueMapToPrefill.get(fieldName);
                        if (prefilledValue.getValue() != null) {
                            // Update the value directly in the original object
                            formFieldValue.setValue(prefilledValue.getValue());

                            // Update tracking data for prefilled fields
                            updateTrackingDataForDirectRenderableComponent(formFieldValue, fieldName, prefilledValue.getValue().toString());

                            log.debug("Updated direct renderableComponent field {} with prefilled value: {}", fieldName, prefilledValue.getValue());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error updating direct renderableComponents with prefilled values", e);
        }
    }

    /**
     * Updates tracking data for direct renderableComponent fields
     */
    private void updateTrackingDataForDirectRenderableComponent(FormFieldValue formFieldValue, String fieldName, String prefilledValue) {
        try {
            // Get existing tracking data or create new one
            Map<String, Object> trackingData = formFieldValue.getTracking();
            if (trackingData == null) {
                trackingData = new HashMap<>();
                formFieldValue.setTracking(trackingData);
            }

            // Update tracking data with prefilled information
            trackingData.put("prefill", true);
            trackingData.put("prefilledValue", prefilledValue);
            trackingData.put("fieldName", fieldName);

            // Set other tracking properties if not already present
            if (!trackingData.containsKey("sendImpression")) {
                trackingData.put("sendImpression", true);
            }
            if (!trackingData.containsKey("sendEdits")) {
                trackingData.put("sendEdits", true);
            }
            if (!trackingData.containsKey("sendInlineError")) {
                trackingData.put("sendInlineError", true);
            }

            log.debug("Updated tracking data for direct renderableComponent field: {} with prefilled value: {}", fieldName, prefilledValue);
        } catch (Exception e) {
            log.error("Error updating tracking data for direct renderableComponent field: {}", fieldName, e);
        }
    }

    /**
     * Updates nested pageResponse structure with prefilled values (BOTTOM_SHEET_BUTTON_TEMPLATE)
     */
    private void updateNestedPageResponseWithPrefilled(GroupedFormWidgetData groupedFormWidgetData,
                                                     Map<String, FormFieldValue> formFieldValueMapToPrefill) {
        try {
            // Get the raw form field map from nested structure
            Map<String, Map<String, Object>> formFieldValueMapRaw = extractLV4FormFieldValueMap(groupedFormWidgetData);

            // Update the raw map with prefilled values
            for (Map.Entry<String, FormFieldValue> entry : formFieldValueMapToPrefill.entrySet()) {
                String fieldName = entry.getKey();
                FormFieldValue prefilledValue = entry.getValue();

                if (formFieldValueMapRaw.containsKey(fieldName) && prefilledValue.getValue() != null) {
                    Map<String, Object> rawValueMap = formFieldValueMapRaw.get(fieldName);
                    rawValueMap.put(JSON_KEY_VALUE, prefilledValue.getValue());
                    log.debug("Updated nested pageResponse field {} with prefilled value: {}", fieldName, prefilledValue.getValue());
                }
            }
        } catch (Exception e) {
            log.error("Error updating nested pageResponse with prefilled values", e);
        }
    }




    /**
     * Set up tracking data for form fields
     * Similar to setupPrefillValueAndTrackingDataForGroupedFormField but adapted for LV4 structure
     */
    private void setupTrackingDataForFormFields(GroupedFormWidgetData groupedFormWidgetData,
                                               ApplicationDataResponse applicationDataResponse,
                                               LeadPageDataSourceResponse leadPageDataSourceResponse,
                                               ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        try {
            // Extract form field map from the V4 structure
            Map<String, Map<String, Object>> formFieldValueMap = extractLV4FormFieldValueMap(groupedFormWidgetData);

            // Set tracking data for each form field
            for (Map.Entry<String, Map<String, Object>> entry : formFieldValueMap.entrySet()) {
                String fieldName = entry.getKey();
                Map<String, Object> valueMap = entry.getValue();

                // Get prefilled value for tracking
                String prefilledValue = getPrefilledValueForField(fieldName, applicationDataResponse, leadPageDataSourceResponse, reviewUserDataSourceResponse);

                // Set tracking data
                setupTrackingDataForFormField(valueMap, fieldName, prefilledValue);
            }

            log.info("Successfully set up tracking data for form fields");
        } catch (Exception e) {
            log.error("Error setting up tracking data for form fields", e);
        }
    }

    /**
     * Get prefilled value for a specific field for tracking purposes
     */
    private String getPrefilledValueForField(String fieldName, ApplicationDataResponse applicationDataResponse,
                                           LeadPageDataSourceResponse leadPageDataSourceResponse,
                                           ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        LV4FormFields formField = LV4FormFields.fromFieldName(fieldName);
        if (formField == null) {
            return "";
        }

        switch (formField) {
            case NAME:
                // Use the new approach with fallback to get name
                return LV4Util.getDecryptedFullNameWithFallback(applicationDataResponse, leadPageDataSourceResponse, decrypter);
            case PHONE_NUMBER:
                // Use the new approach with fallback to get phone number
                return LV4Util.getPhoneNumberWithFallback(applicationDataResponse, leadPageDataSourceResponse);
        }
        return "";
    }

    /**
     * Set up tracking data for a single form field
     */
    private void setupTrackingDataForFormField(Map<String, Object> valueMap, String fieldName, String prefilledValue) {
        try {
            // Set tracking data using WidgetTransformerUtils
            Map<String, Object> trackingData = WidgetTransformerUtils.getTrackingMetadata(fieldName, prefilledValue);
            valueMap.put("tracking", trackingData);

            log.debug("Set tracking data for field: {} with prefilled value: {}", fieldName, prefilledValue);
        } catch (Exception e) {
            log.error("Error setting tracking data for field: {}", fieldName, e);
        }
    }

    /**
     * Extracts form field value map from LV4 structure (handles both template structures)
     */
    private Map<String, Map<String, Object>> extractLV4FormFieldValueMap(GroupedFormWidgetData groupedFormWidgetData) {
        Map<String, Map<String, Object>> formFieldValueMap = new HashMap<>();

        // First try to extract from direct renderableComponents (LandingPageHiddenForm.json structure)
        if (groupedFormWidgetData.getRenderableComponents() != null && !groupedFormWidgetData.getRenderableComponents().isEmpty()) {
            log.debug("Extracting form fields from direct renderableComponents (SUBMIT_BUTTON_TEMPLATE structure)");
            extractRenderableComponentFields(groupedFormWidgetData, formFieldValueMap);
        }
        // Then try to extract from nested pageResponse structure (LandingPageBottomSheet.json structure)
        else if (groupedFormWidgetData.getSubmitButton() != null &&
                 groupedFormWidgetData.getSubmitButton().getButton().getAction() != null) {

            log.debug("Extracting form fields from nested pageResponse structure (BOTTOM_SHEET_BUTTON_TEMPLATE structure)");
            Map<String, Object> params = groupedFormWidgetData.getSubmitButton().getButton().getAction().getParams();
            Map<String, Object> pageResponse = (Map<String, Object>) params.get(JSON_KEY_PAGE_RESPONSE);

            if (pageResponse != null) {
                extractFormFieldsFromPageResponse(pageResponse, formFieldValueMap);
            }
        }

        return formFieldValueMap;
    }

    /**
     * Extracts form fields from page response structure
     */
    private void extractFormFieldsFromPageResponse(Map<String, Object> pageResponse,
                                                 Map<String, Map<String, Object>> formFieldValueMap) {
        List<Map<String, Object>> slots = (List<Map<String, Object>>) pageResponse.get(JSON_KEY_SLOTS);

        if (slots != null) {
            for (Map<String, Object> slot : slots) {
                Map<String, Object> widget = (Map<String, Object>) slot.get(JSON_KEY_WIDGET);
                if (widget != null) {
                    Map<String, Object> data = (Map<String, Object>) widget.get(JSON_KEY_DATA);
                    if (data != null) {
                        extractRenderableComponentFields(data, formFieldValueMap);
                    }
                }
            }
        }
    }

    /**
     * Extracts form fields from direct renderableComponents (SUBMIT_BUTTON_TEMPLATE structure)
     */
    private void extractRenderableComponentFields(GroupedFormWidgetData groupedFormWidgetData,
                                                Map<String, Map<String, Object>> formFieldValueMap) {
        try {
            // Convert the typed renderableComponents to raw Map structure for processing
            if (groupedFormWidgetData.getRenderableComponents() != null && !groupedFormWidgetData.getRenderableComponents().isEmpty()) {
                // Convert GroupedFormWidgetData to Map to access renderableComponents as raw structure
                Map<String, Object> rawGroupedData = ObjectMapperUtil.get().convertValue(groupedFormWidgetData, Map.class);
                List<Map<String, Object>> renderableComponents = (List<Map<String, Object>>) rawGroupedData.get(JSON_KEY_RENDERABLE_COMPONENTS);

                if (renderableComponents != null && !renderableComponents.isEmpty()) {
                    for (Map<String, Object> renderableComponent : renderableComponents) {
                        Map<String, Object> value = (Map<String, Object>) renderableComponent.get(JSON_KEY_VALUE);

                        if (value != null && value.get(JSON_KEY_NAME) != null) {
                            String fieldName = (String) value.get(JSON_KEY_NAME);
                            formFieldValueMap.put(fieldName, value);
                            log.debug("Extracted form field: {} from direct renderableComponents", fieldName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error extracting form fields from direct renderableComponents", e);
        }
    }

    /**
     * Extracts renderable component fields from nested data structure (BOTTOM_SHEET_BUTTON_TEMPLATE structure)
     */
    private void extractRenderableComponentFields(Map<String, Object> data,
                                                Map<String, Map<String, Object>> formFieldValueMap) {
        List<Map<String, Object>> renderableComponents = (List<Map<String, Object>>) data.get(JSON_KEY_RENDERABLE_COMPONENTS);

        if (renderableComponents != null && !renderableComponents.isEmpty()) {
            for (Map<String, Object> renderableComponent : renderableComponents) {
                Map<String, Object> value = (Map<String, Object>) renderableComponent.get(JSON_KEY_VALUE);

                if (value != null && value.get(JSON_KEY_NAME) != null) {
                    String fieldName = (String) value.get(JSON_KEY_NAME);
                    formFieldValueMap.put(fieldName, value);
                    log.debug("Extracted form field: {} from nested renderableComponents", fieldName);
                }
            }
        }
    }



    /**
     * Updates tracking data with correct accountId and applicationId
     */
    private void updateTrackingData(GroupedFormWidgetData groupedFormWidgetData,
                                   ApplicationDataResponse applicationDataResponse) {
        // Update tracking data in the main submit button action
        if (groupedFormWidgetData.getSubmitButton() != null &&
            groupedFormWidgetData.getSubmitButton().getButton().getAction() != null) {

            Map<String, Object> params = groupedFormWidgetData.getSubmitButton().getButton().getAction().getParams();

            // Update tracking data in the main action
            updateTrackingInParams(params, applicationDataResponse);

            // Update tracking data in pageResponse if it exists
            Map<String, Object> pageResponse = (Map<String, Object>) params.get(JSON_KEY_PAGE_RESPONSE);
            if (pageResponse != null) {
                updateTrackingInPageResponse(pageResponse, applicationDataResponse);
            }
        }
    }

    /**
     * Updates tracking data in action params
     */
    private void updateTrackingInParams(Map<String, Object> params, ApplicationDataResponse applicationDataResponse) {
        // Update applicationId in params
        params.put(JSON_KEY_APPLICATION_ID, applicationDataResponse.getApplicationId());
        log.debug("Updated applicationId in params: {}", applicationDataResponse.getApplicationId());
    }

    /**
     * Updates tracking data in page response structure
     */
    private void updateTrackingInPageResponse(Map<String, Object> pageResponse, ApplicationDataResponse applicationDataResponse) {
        // Update tracking data in pageResponse.pageData.trackingContext.tracking
        Map<String, Object> pageData = (Map<String, Object>) pageResponse.get(JSON_KEY_PAGE_DATA);
        if (pageData != null) {
            Map<String, Object> trackingContext = (Map<String, Object>) pageData.get(JSON_KEY_TRACKING_CONTEXT);
            if (trackingContext != null) {
                Map<String, Object> tracking = (Map<String, Object>) trackingContext.get(JSON_KEY_TRACKING);
                if (tracking != null) {
                    // Update accountId and applicationId in tracking
                    tracking.put(JSON_KEY_ACCOUNT_ID, applicationDataResponse.getExternalUserId());
                    tracking.put(JSON_KEY_APPLICATION_ID, applicationDataResponse.getApplicationId());
                    log.info("Updated tracking data - accountId: {}, applicationId: {}",
                            applicationDataResponse.getExternalUserId(),
                            applicationDataResponse.getApplicationId());
                } else {
                    log.warn("Tracking object not found in trackingContext");
                }
            } else {
                log.warn("TrackingContext not found in pageData");
            }
        } else {
            log.warn("PageData not found in pageResponse");
        }
    }

    /**
     * Updates submit buttons in V4 structure which is different from V3
     * V4 has submit buttons nested within the CLIENT__INLINE_NAVIGATION action params
     */
    private void updateV4SubmitButton(GroupedFormWidgetData groupedFormWidgetData,
                                     ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        // Update the main submit button (top level)
        if (groupedFormWidgetData.getSubmitButton() != null &&
            groupedFormWidgetData.getSubmitButton().getButton().getAction() != null) {

            updateSubmitButtonAction(groupedFormWidgetData.getSubmitButton().getButton().getAction(),
                                   reviewUserDataSourceResponse);

            // Also update nested submit buttons within the page response
            updateNestedSubmitButtons(groupedFormWidgetData, reviewUserDataSourceResponse);
        }
    }

    /**
     * Updates submit button action with query params and encryption data
     * Merges query params with existing params to preserve pageResponse data
     */
    private void updateSubmitButtonAction(com.flipkart.rome.datatypes.response.common.Action action,
                                        ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        if (action != null) {
            // Merge query params with existing params instead of replacing
            Map<String, Object> existingParams = action.getParams();

            Map<String, Object> queryParams = reviewUserDataSourceResponse.getQueryParams();
            if (queryParams != null) {
                // Add query params to existing params without overwriting pageResponse
                existingParams.putAll(queryParams);
            }

            action.setParams(existingParams);
            action.setEncryption(reviewUserDataSourceResponse.getEncryptionData());
            log.debug("Updated submit button action with merged query params and encryption data");
        }
    }

    /**
     * Updates nested submit buttons within the page response structure
     */
    private void updateNestedSubmitButtons(GroupedFormWidgetData groupedFormWidgetData,
                                         ReviewUserDataSourceResponse reviewUserDataSourceResponse) {

        if (groupedFormWidgetData.getSubmitButton() != null &&
            groupedFormWidgetData.getSubmitButton().getButton().getAction() != null) {

            Map<String, Object> params = groupedFormWidgetData.getSubmitButton().getButton().getAction().getParams();
            Map<String, Object> pageResponse = (Map<String, Object>) params.get(JSON_KEY_PAGE_RESPONSE);

            if (pageResponse != null) {
                updateSubmitButtonsInPageResponse(pageResponse, reviewUserDataSourceResponse);
            }
        }
    }

    /**
     * Updates submit buttons found within the page response slots
     */
    private void updateSubmitButtonsInPageResponse(Map<String, Object> pageResponse,
                                                 ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        List<Map<String, Object>> slots = (List<Map<String, Object>>) pageResponse.get(JSON_KEY_SLOTS);

        if (slots != null) {
            for (Map<String, Object> slot : slots) {
                Map<String, Object> widget = (Map<String, Object>) slot.get(JSON_KEY_WIDGET);
                if (widget != null) {
                    Map<String, Object> data = (Map<String, Object>) widget.get(JSON_KEY_DATA);
                    if (data != null) {
                        // Check if this widget has a submit button
                        Map<String, Object> submitButton = (Map<String, Object>) data.get(JSON_KEY_SUBMIT_BUTTON);
                        if (submitButton != null) {
                            updateSubmitButtonInData(submitButton, reviewUserDataSourceResponse);
                        }
                    }
                }
            }
        }
    }

    /**
     * Updates a submit button found within widget data
     * Merges query params with existing params to preserve form data
     */
    private void updateSubmitButtonInData(Map<String, Object> submitButton,
                                        ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        Map<String, Object> button = (Map<String, Object>) submitButton.get(JSON_KEY_BUTTON);
        if (button != null) {
            Map<String, Object> action = (Map<String, Object>) button.get(JSON_KEY_ACTION);
            if (action != null) {
                // Merge query params with existing params instead of replacing
                Map<String, Object> existingParams = (Map<String, Object>) action.get(JSON_KEY_PARAMS);
                if (existingParams == null) {
                    existingParams = new HashMap<>();
                }

                Map<String, Object> queryParams = reviewUserDataSourceResponse.getQueryParams();
                if (queryParams != null) {
                    // Add query params to existing params without overwriting form data
                    existingParams.putAll(queryParams);
                }

                action.put(JSON_KEY_PARAMS, existingParams);
                action.put(JSON_KEY_ENCRYPTION, reviewUserDataSourceResponse.getEncryptionData());
                log.debug("Updated nested submit button with merged query params and encryption data");
            }
        }
    }
    /**
     * Determine which template to use based on full name availability from applicationDataResponse
     */
    private String determineTemplate(ApplicationDataResponse applicationDataResponse, LeadPageDataSourceResponse leadPageDataSourceResponse, WidgetTypeV4 widgetType) {
        String fullName = LV4Util.getDecryptedFullNameWithFallback(applicationDataResponse, leadPageDataSourceResponse, decrypter);

        if (StringUtils.isNotBlank(fullName) && widgetType.equals(WidgetTypeV4.FORM_V4)) {
            log.info("Full name available, using SUBMIT_BUTTON_TEMPLATE");
            return SUBMITT_BUTTON_TEMPLATE;
        } else if (StringUtils.isBlank(fullName) && widgetType.equals(WidgetTypeV4.SUBMIT_BUTTON_WIDGET)) {
            log.info("Full name not available, using BOTTOM_SHEET_BUTTON_TEMPLATE");
            return BOTTOM_SHEET_BUTTON_TEMPLATE;
        }
        return null;
    }

    /**
     * Get form configuration for template substitution
     * Uses the same FormConfig as LV3 for consistency
     */
    private String getFormJson(ApplicationDataResponse applicationDataResponse, String template) {
        Map<String, Object> formConfigMap = formConfig.getFormConfigMap(applicationDataResponse.getMerchantId(), dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(template);
    }

}
