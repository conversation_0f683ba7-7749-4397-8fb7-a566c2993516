package com.flipkart.fintech.pinaka.service.adaptor.v6;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.UserDetails;
import com.flipkart.fintech.pinaka.api.request.v6.*;
import com.flipkart.fintech.pinaka.api.response.v6.*;
import com.flipkart.fintech.pinaka.service.adaptor.v6.formFields.FormFieldContent;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.pinaka.service.utils.v6.FormUtils;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.google.inject.Inject;

import java.math.BigDecimal;
import java.util.*;

public class ProfessionalDetailsAdapter implements IPageAdapter {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Inject
    public ProfessionalDetailsAdapter() {
    }

    @Override
    public Map<String, VariableData> constructWorkflowData(LoanApplication loanApplication, Map<String, Object> formData,
                                                           MerchantUser merchantUser, String applicationId, String requestId) throws DataEnrichmentException {
        Map<String, VariableData> variableDataMap = new HashMap<>();

        FormField employmentTypeFieldValue = FormUtils.getFormField(FormFieldContent.EMPLOYMENT_TYPE);
        FormField annualTurnOverValue = FormUtils.getFormField(FormFieldContent.ANNUAL_TURN_OVER);
        FormField monthlyIncomeValue = FormUtils.getFormField(FormFieldContent.MONTHLY_INCOME);
        FormField companyValue = FormUtils.getFormField(FormFieldContent.COMPANY);
        FormField industryTypeValue = FormUtils.getFormField(FormFieldContent.INDUSTRY_TYPE);
        String empType = (String) formData.get(employmentTypeFieldValue.getName());

        variableDataMap.put("employment_type", new VariableData(false, EmploymentType.valueOf(empType.toUpperCase())));
        if (EmploymentType.SALARIED.equals(EmploymentType.valueOf(empType.toUpperCase()))) {
            BigDecimal monthlyIncome = BigDecimal.valueOf((Integer) formData.get(monthlyIncomeValue.getName()));
            validateIncome(monthlyIncome);
            Map<String, String> companyMap = (Map<String, String>) formData.get(companyValue.getName());
            variableDataMap.put("employer_id", new VariableData(false, companyMap.get("id")));
            variableDataMap.put("employer_name", new VariableData(false, companyMap.get("title")));
            variableDataMap.put("monthly_income", new VariableData(false, formData.get(monthlyIncomeValue.getName())));
            variableDataMap.put("industry_id", new VariableData(false, null));
            variableDataMap.put("industry_name", new VariableData(false, null));
            variableDataMap.put("annual_turn_over", new VariableData(false, null));
        } else {
            BigDecimal annualTurnOver = BigDecimal.valueOf((Integer) formData.get(annualTurnOverValue.getName()));
            validateIncome(annualTurnOver);
            Map<String, String> industryMap = (Map<String, String>) formData.get(industryTypeValue.getName());
            variableDataMap.put("employer_id", new VariableData(false, null));
            variableDataMap.put("employer_name", new VariableData(false, null));
            variableDataMap.put("monthly_income", new VariableData(false, null));
            variableDataMap.put("industry_name", new VariableData(false, industryMap.get("title")));
            variableDataMap.put("industry_id", new VariableData(false, industryMap.get("id")));
            variableDataMap.put("annual_turn_over", new VariableData(false, formData.get(annualTurnOverValue.getName())));
        }

        return variableDataMap;
    }

    @Override
    public Map<String, Object> constructApplicationData(LoanApplication loanApplication, Map<String, Object> formData, MerchantUser merchantUser, String applicationId) throws DataEnrichmentException {
        UserDetails userDetails = Objects.isNull(loanApplication.getUserDetails()) ? new UserDetails() : loanApplication.getUserDetails();

        FormField employmentTypeFieldValue = FormUtils.getFormField(FormFieldContent.EMPLOYMENT_TYPE);
        FormField annualTurnOverValue = FormUtils.getFormField(FormFieldContent.ANNUAL_TURN_OVER);
        FormField monthlyIncomeValue = FormUtils.getFormField(FormFieldContent.MONTHLY_INCOME);
        FormField companyValue = FormUtils.getFormField(FormFieldContent.COMPANY);
        FormField industryTypeValue = FormUtils.getFormField(FormFieldContent.INDUSTRY_TYPE);

        String empType = (String) formData.get(employmentTypeFieldValue.getName());
        userDetails.setEmploymentType(EmploymentType.valueOf(empType.toUpperCase()));
        if (EmploymentType.SALARIED.equals(EmploymentType.valueOf(empType.toUpperCase()))) {
            BigDecimal monthlyIncome = BigDecimal.valueOf((Integer) formData.get(monthlyIncomeValue.getName()));
            validateIncome(monthlyIncome);
            userDetails.setMonthlyIncome(monthlyIncome);
            Map<String, String> companyMap = (Map<String, String>) formData.get(companyValue.getName());
            userDetails.setEmployerId(companyMap.get("id"));
            userDetails.setEmployerName(companyMap.get("title"));
        } else {
            BigDecimal annualTurnOver = BigDecimal.valueOf((Integer) formData.get(annualTurnOverValue.getName()));
            validateIncome(annualTurnOver);
            userDetails.setAnnualTurnOver(annualTurnOver);
            Map<String, String> industryMap = (Map<String, String>) formData.get(industryTypeValue.getName());
            userDetails.setIndustryName(industryMap.get("title"));
            userDetails.setIndustryId(industryMap.get("id"));
        }
        loanApplication.setUserDetails(userDetails);

        Map<String, Object> applicationDataMap = objectMapper.convertValue(loanApplication, Map.class);
        return applicationDataMap;
    }

    private void validateIncome(BigDecimal income) throws DataEnrichmentException {
        if ((new BigDecimal(0)).compareTo(income) >= 0) {
            throw new DataEnrichmentException("To opt for personal loan income should be greater than 0");
        }
    }

    @Override
    public List<DataEnumResponse> constructPageResponse(List<DataEnum> dataEnumList, LoanApplication loanApplication, MerchantUser merchantUser, FormType formType) throws PinakaException {
        List<DataEnumResponse> dataEnumResponseList = new ArrayList<>();
        for (DataEnum dataEnum : dataEnumList) {
            DataEnumResponse dataEnumResponse = new DataEnumResponse();
            switch (dataEnum) {
                case FORM:
                    dataEnumResponse = constructFormDataWidget(loanApplication);
                    break;
                case HELP:
                    dataEnumResponse = PageConstant.constructHelpWidget();
                    break;
                case ANNOUNCEMENT:
                    dataEnumResponse = constructAnnouncementWidget();
                    break;
                default:
                    throw new PinakaException("the requested widget is not supported for ProfessionalDetailsPage : " + dataEnum.name());
            }
            dataEnumResponseList.add(dataEnumResponse);
        }

        return dataEnumResponseList;
    }

    private AnnouncementDataEnumResponse constructAnnouncementWidget() {
        AnnouncementDataEnumResponse announcementDataEnumResponse
                = AnnouncementDataEnumResponse.builder()
                .dataEnum(DataEnum.ANNOUNCEMENT)
                .url(PageConstant.PL_ADDITIONAL_DETAILS_FORM.announcementImageURL)
                .aspectRatio(PageConstant.PL_ADDITIONAL_DETAILS_FORM.announcementImageAspectRatio)
                .build();

        return announcementDataEnumResponse;
    }

    private FormDataEnumResponse constructFormDataWidget(LoanApplication loanApplication) {

        FormField headerImageValue = FormUtils.getFormField(FormFieldContent.HEADER_IMAGE_VALUE);
        headerImageValue.setUrl(PageConstant.PL_ADDITIONAL_DETAILS_FORM.headerImageURL);

        FormField employmentTypeFieldValue = FormUtils.getFormField(FormFieldContent.EMPLOYMENT_TYPE);
        employmentTypeFieldValue.setViewType(ViewType.RADIO_TYPE);
        employmentTypeFieldValue.setOptions(PageConstant.PL_ADDITIONAL_DETAILS_FORM.employmentTypeOptions);

        FormField noteFieldValue = FormUtils.getFormField(FormFieldContent.NOTE);
        noteFieldValue.setTitle(PageConstant.PL_ADDITIONAL_DETAILS_FORM.noteTitle);

        FormField annualTurnOverValue = FormUtils.getFormField(FormFieldContent.ANNUAL_TURN_OVER);
        annualTurnOverValue.setMinValue(PageConstant.PL_ADDITIONAL_DETAILS_FORM.minAnnualTurnOver);

        FormField monthlyIncomeValue = FormUtils.getFormField(FormFieldContent.MONTHLY_INCOME);
        monthlyIncomeValue.setSubtitle(PageConstant.PL_ADDITIONAL_DETAILS_FORM.monthlyIncomeSubtext);
        monthlyIncomeValue.setMinValue(PageConstant.PL_ADDITIONAL_DETAILS_FORM.minMonthlyIncome);

        FormField companyValue = FormUtils.getFormField(FormFieldContent.COMPANY);
        companyValue.setOffline(false);
        companyValue.setActionType(Type.SEARCH);
        companyValue.setUrl(PageConstant.SearchUrl);

        FormField industryTypeValue = FormUtils.getFormField(FormFieldContent.INDUSTRY_TYPE);
        industryTypeValue.setOffline(true);
        industryTypeValue.setOptions(PageConstant.PL_ADDITIONAL_DETAILS_FORM.industryTypeOptions);


        FormField submitButtonFormField = FormUtils.getFormField(FormFieldContent.SUBMIT_DETAILS);
        submitButtonFormField.setActionType(Type.CALM_SUBMIT_BUTTON);
        submitButtonFormField.setTitle(PageConstant.PL_ADDITIONAL_DETAILS_FORM.buttonText);

        if (Status.RETRY_WITHOUT_EDIT.equals(loanApplication.getCode())) {
            employmentTypeFieldValue.setDefaultValue(loanApplication.getUserDetails().getEmploymentType().name());
            employmentTypeFieldValue.setDisabled(true);
            if (EmploymentType.SALARIED.equals(loanApplication.getUserDetails().getEmploymentType())) {
                monthlyIncomeValue.setDefaultValue(loanApplication.getUserDetails().getMonthlyIncome().toString());
                monthlyIncomeValue.setDisabled(true);
                companyValue.setDefaultValue(loanApplication.getUserDetails().getEmployerName());
                companyValue.setDisabled(true);
                companyValue.setDefaultIdValue(loanApplication.getUserDetails().getEmployerId());
            } else {
                annualTurnOverValue.setDefaultValue(loanApplication.getUserDetails().getAnnualTurnOver().toString());
                annualTurnOverValue.setDisabled(true);
                industryTypeValue.setDefaultValue(loanApplication.getUserDetails().getIndustryName());
                industryTypeValue.setDisabled(true);
                industryTypeValue.setDefaultIdValue(loanApplication.getUserDetails().getIndustryId());
            }
        }

        List<FormField> formFields = new ArrayList<>();
        formFields.add(headerImageValue);
        formFields.add(noteFieldValue);
        formFields.add(employmentTypeFieldValue);
        formFields.add(annualTurnOverValue);
        formFields.add(monthlyIncomeValue);
        formFields.add(companyValue);
        formFields.add(industryTypeValue);
        formFields.add(submitButtonFormField);

        Form form = new Form();
        form.setFormType(FormType.PL_ADDITIONAL_DETAILS_FORM);
        form.setFormFields(formFields);

        return FormDataEnumResponse.builder()
                .dataEnum(DataEnum.FORM)
                .form(form)
                .formType(FormType.PL_ADDITIONAL_DETAILS_FORM)
                .build();
    }
}
