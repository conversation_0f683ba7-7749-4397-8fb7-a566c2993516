package com.flipkart.fintech.pinaka.service.utils.v5;

import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.google.inject.Inject;
import java.util.Optional;
import javax.inject.Singleton;

import com.google.inject.Provides;
import lombok.CustomLog;

import java.util.List;

@CustomLog
@Singleton
public class ConfigUtils {
    private final DynamicBucket dynamicBucket;

    @Inject
    public ConfigUtils(DynamicBucket dynamicBucket) {
        this.dynamicBucket = dynamicBucket;
    }

    public boolean isFeatureEnabled(String identifier, String featureName) {
        boolean enabled = false;
        String configName = featureName + PinakaConstants.ConfigFeature.FEATURE_NAME_PREFIX;

        try {
            List<String> cugList = dynamicBucket.getStringArray(configName);

            if (cugList.isEmpty()) {
                enabled = true;
            } else if (cugList.contains(identifier)) {
                enabled = true;
            }
        }
        catch (Exception e) {
            log.error("Failed to get config from config bucket {}", configName);
        }

        return enabled;
    }

    public Optional<EncryptionData> getEncryptionData() {
        try {
            if (!isEncryptionEnabled()) {
                return Optional.empty();
            }
            String publicKey = dynamicBucket.getString(Encryption.PUBLIC_KEY);
            String keyId = dynamicBucket.getString(Encryption.KEY_ID);
            EncryptionData encryptionData = new EncryptionData(publicKey, keyId);
            return Optional.of(encryptionData);
        } catch (Exception e) {
            log.error("Failed to get config from config bucket {}", Encryption.ENABLED);
        }
        return Optional.empty();
    }

    private Boolean isEncryptionEnabled() {
        return dynamicBucket.getBoolean(Encryption.ENABLED);
    }

    public Boolean isWinterfellMocked() {
        try {
            return dynamicBucket.getBoolean("winterfell.isMocked");
        } catch (Exception e) {
            log.error("Error in reading the config: {}", "winterfell.isMocked", e);
            return false;
        }
    }

    public Boolean isPIIDataEncrypted() {
        try {
            return dynamicBucket.getBoolean("piiData.isEncrypted");
        } catch (Exception e) {
            log.error("Error in reading the config: {}", "piiData.isEncrypted", e);
            return false;
        }
    }

    private static class Encryption {
        static String ENABLED = "encryption.enabled";
        static String PUBLIC_KEY = "encryption.publicKey";
        static String KEY_ID = "encryption.keyId";
    }
}
