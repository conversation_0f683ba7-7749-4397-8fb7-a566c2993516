package com.flipkart.fintech.pinaka.service.core.v6;

import com.flipkart.fintech.kafka.models.amsEvents.ApplicationEvent;
import com.flipkart.fintech.pinaka.api.request.v6.AlmDiscardRequest;
import com.flipkart.fintech.pinaka.api.request.v6.MigrateApplicationsRequest;
import com.flipkart.fintech.pinaka.api.request.v6.SandboxWebhooksRequest;
import com.flipkart.fintech.pinaka.api.request.v6.StateChangeRequest;
import com.flipkart.fintech.pinaka.api.request.v6.SubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.AxisWebhooksRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.api.response.v6.SearchResponse;
import com.flipkart.fintech.pinaka.api.response.v6.StateChangeResponse;
import com.flipkart.fintech.pinaka.api.response.v6.WebhooksResponse;
import com.flipkart.fintech.pinaka.service.core.v6.impl.EventHandlerImpl;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.rome.datatypes.request.fintech.calm.IfscSearchRequest;
import com.flipkart.rome.datatypes.response.fintech.calm.IfscSearchResponse;
import com.google.inject.ImplementedBy;

@ImplementedBy(EventHandlerImpl.class)
public interface EventHandler {
    PageActionResponse submitUserAction(SubmitRequest submitRequest, String merchantId, String requestId) throws PinakaException;

    WebhooksResponse submitLenderEvent(AxisWebhooksRequest axisWebhooksRequest, String requestId);

    WebhooksResponse submitSandboxLenderEvent(SandboxWebhooksRequest sandboxWebhooksRequest);

    SearchResponse getEmpSuggestions(String prefix);

    SearchResponse getEmpSuggestionV2(String prefix);

    StateChangeResponse hasStateChanged(StateChangeRequest stateChangeRequest) throws PinakaException;

    IfscSearchResponse searchIfsc(IfscSearchRequest request);

  boolean almDiscard(AlmDiscardRequest applicationId) throws PinakaException;

}
