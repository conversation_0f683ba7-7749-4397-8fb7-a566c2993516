package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.service.core.v6.UserOfferDataHandler;
import com.flipkart.fintech.pinaka.api.response.v6.UserOfferDataResponse;
import com.flipkart.fintech.pinaka.service.exception.InvalidOfferDataException;

import lombok.CustomLog;

import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/offer-data")
public class UserOfferDataResource {

   private final UserOfferDataHandler userOfferDataHandler;

   @Inject
    public UserOfferDataResource(UserOfferDataHandler userOfferDataHandler) {
        this.userOfferDataHandler = userOfferDataHandler;
    }

    @GET
    @Timed
    @ExceptionMetered
    @Path("/{applicationId}")
    public UserOfferDataResponse getUserOfferData(@NotNull @PathParam("applicationId") String applicationId,
                                                  @NotNull @QueryParam(value = "smUserId") String smUserId,
                                                  @NotNull @QueryParam(value = "lender") String lender) throws InvalidOfferDataException {
        return userOfferDataHandler.getUserOfferData(applicationId, smUserId, lender);
    }
}