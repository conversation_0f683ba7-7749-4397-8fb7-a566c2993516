package com.flipkart.fintech.pinaka.service.core.v6.impl;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.FETCH_BULK_DATA;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.ENABLE_PL_MAINTENANCE;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.FetchBulkDataRequest;
import com.flipkart.fintech.pinaka.api.request.v6.LoanApplication;
import com.flipkart.fintech.pinaka.api.request.v6.PageServiceRequest;
import com.flipkart.fintech.pinaka.api.response.idfc.WidgetEntity;
import com.flipkart.fintech.pinaka.api.response.v6.DataEnumResponse;
import com.flipkart.fintech.pinaka.api.response.v6.FetchBulkDataResponse;
import com.flipkart.fintech.pinaka.api.response.v6.FetchBulkDataResponseV2;
import com.flipkart.fintech.pinaka.client.PinakaCalvinClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.adaptor.v6.IPageAdapter;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.core.v6.FetchPageDataHandler;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataDecryption;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.factory.v6.PageAdapterFactory;
import com.flipkart.fintech.pinaka.service.pagehandler.PageHandler;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.profile.client.ProfileClientConfiguration;
import com.flipkart.fintech.profile.config.ProfileServiceConfig;
import com.flipkart.fintech.profile.pagehandler.handlers.PageHandlerV2;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Named;
import javax.ws.rs.client.Client;

import com.flipkart.kloud.config.DynamicBucket;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.concurrent.ExecutorService;

@CustomLog
public class FetchPageDataHandlerImpl implements FetchPageDataHandler {
    private static final String STATIC_CONTENT = "STATIC_CONTENT";

    @Inject
    public FetchPageDataHandlerImpl(ProfileServiceConfig profileServiceConfig, @Named("robinhoodExecutorService") ExecutorService executorService, PinakaCalvinClientConfig pinakaCalvinClientConfig, PageAdapterFactory pageAdapterFactory, ConfigUtils configUtils, ApplicationService applicationService, FormDataDecryption formDataDecryptor, PageHandler pageHandler, ProfileClientConfiguration profileClientConfiguration,
                                    @Named("provideClientForCsVaradhiEvent")Client client, DynamicBucket dynamicBucket) {
        this.profileServiceConfig = profileServiceConfig;
        this.dynamicBucket = dynamicBucket;
        this.pageAdapterFactory = pageAdapterFactory;
        this.configUtils = configUtils;
        this.applicationService = applicationService;
        this.formDataDecryptor = formDataDecryptor;
        this.pageHandler = pageHandler;
        this.pageHandlerV2 = new PageHandlerV2(profileClientConfiguration,
                configUtils.getEncryptionData().orElse(null),pinakaCalvinClientConfig,executorService, profileServiceConfig, client,dynamicBucket);
        this.metricRegistry = PinakaMetricRegistry.getMetricRegistry();

    }
    private final ProfileServiceConfig profileServiceConfig;
    private final PageAdapterFactory pageAdapterFactory;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ConfigUtils configUtils;
    private final DynamicBucket dynamicBucket;
    private final ApplicationService applicationService;
    private final FormDataDecryption formDataDecryptor;
    private final PageHandler pageHandler;
    private final PageHandlerV2 pageHandlerV2;
    private final MetricRegistry metricRegistry;


    @Override
    public FetchBulkDataResponse fetchBulkData(FetchBulkDataRequest fetchBulkDataRequest, String merchantId) throws PinakaException {
        MerchantUser merchantUser = MerchantUserUtils.getMerchantUser(fetchBulkDataRequest.getAccountId(), fetchBulkDataRequest.getSmUserId());
        IPageAdapter iPageAdapter = pageAdapterFactory.getAdapterByPage(fetchBulkDataRequest.getFormType().name());

        ApplicationDataResponse applicationDataResponse = applicationService.fetchApplicationData(
            merchantUser, fetchBulkDataRequest.getApplicationId());

        LoanApplication loanApplication = objectMapper.convertValue(applicationDataResponse.getApplicationData(), LoanApplication.class);
        List<DataEnumResponse> dataEnumResponses = iPageAdapter.
            constructPageResponse(fetchBulkDataRequest.getDataEnum(), loanApplication,
                merchantUser, fetchBulkDataRequest.getFormType());
        return new FetchBulkDataResponse(dataEnumResponses);
    }

    @Override
    public FetchBulkDataResponseV2 fetchBulkDataV2(String merchantId, PageServiceRequest pageServiceRequest)
        throws PinakaException {
        if (pageServiceRequest.getContext() != null && !STATIC_CONTENT.equals(pageServiceRequest.getContext())) {
            try {
                List<WidgetEntity> pageResponse = pageHandlerV2.createPageHandlerResponse(pageServiceRequest);
                return new FetchBulkDataResponseV2(pageResponse);
            } catch (PinakaException | ParseException | PinakaClientException | IOException e) {
                throw new RuntimeException(e);
            }
        }

        ApplicationDataResponse applicationDataResponse = null;

        if (!dynamicBucket.getBoolean(ENABLE_PL_MAINTENANCE)) {
            if (StringUtils.isEmpty(pageServiceRequest.getContext())) {
                MerchantUser merchantUser = MerchantUserUtils.getMerchantUser(
                    pageServiceRequest.getAccountId(), pageServiceRequest.getSmUserId());
                applicationDataResponse = getApplicationDataResponse(merchantUser,
                    pageServiceRequest.getApplicationId());
                if (configUtils.isPIIDataEncrypted()) {
                    applicationDataResponse.setApplicationData(formDataDecryptor.decryptFormData(
                        applicationDataResponse.getApplicationData()));
                }
            }
        }
        try(Timer.Context timer = metricRegistry.timer(FETCH_BULK_DATA + applicationDataResponse.getApplicationState()).time()) {
            List<WidgetEntity> pageResponse = pageHandler.createPageHandlerResponse(merchantId,
                pageServiceRequest,
                applicationDataResponse);
            return new FetchBulkDataResponseV2(pageResponse);
        }catch (Exception e){
            log.error("fetch bulk data error: {}", e.getMessage());
            throw e;
        }
    }


    private ApplicationDataResponse getApplicationDataResponse(MerchantUser merchantUser,
        String applicationId) throws PinakaException {
        if (applicationId != null) {
            return applicationService.fetchApplicationData(merchantUser, applicationId);
        }
        Optional<ApplicationDataResponse> applicationDataResponse = applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.PERSONAL_LOAN);
        if(applicationDataResponse.isPresent()){
            return applicationDataResponse.orElse(null);
        }
        return applicationService.findLatestActiveApplicationV2(merchantUser, ProductType.LEAD)
            .orElseThrow(() -> new PinakaException(String.format("Unable to find application for account_id: %s", merchantUser.getMerchantUserId())));
    }

}
