package com.flipkart.fintech.pinaka.service.widgettransformer.creditLine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.pinaka.common.decrypter.DecrypterImpl;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.DateUtils;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.profile.model.EmploymentType;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.fintech.insurtech.CardFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.*;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.DropdownType;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GroupedFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@CustomLog
public class CreditlineReviewPageTransformer {
    private static String reviewJson;
    private final String ENCRYPTED = "ENCRYPTED";
    private final String DECRYPTED = "DECRYPTED";
    private final DecrypterImpl decrypter = new DecrypterImpl();

    static {
        reviewJson = TransformerUtils.readFileasString("template/ltfs/ReviewDetails.json");
    }

    public GroupedFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse, ReviewUserDataSourceResponse reviewUserDataSourceResponse) throws JsonProcessingException {
        GroupedFormWidgetData reviewWidgetData = ObjectMapperUtil.get().readValue(reviewJson, GroupedFormWidgetData.class);
        try {
            updateSubmitButton(reviewWidgetData.getSubmitButton(), reviewUserDataSourceResponse);
            updateBasicDetails(reviewWidgetData, applicationDataResponse, reviewUserDataSourceResponse);
            updateAddressDetails(reviewWidgetData, applicationDataResponse, reviewUserDataSourceResponse);
            return reviewWidgetData;
        } catch (Exception e) {
            log.warn("Failed to parse review data {}", e.getMessage());
        }
        return reviewWidgetData;
    }

    private void updateSubmitButton(SubmitButtonValue submitButton, ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        Action action = submitButton.getButton().getAction();
        action.setParams(reviewUserDataSourceResponse.getQueryParams());
        action.setEncryption(reviewUserDataSourceResponse.getEncryptionData());
    }

    private void updateBasicDetails(GroupedFormWidgetData reviewWidgetData, ApplicationDataResponse applicationDataResponse, ReviewUserDataSourceResponse reviewUserDataSourceResponse) throws ParseException {
        List<FormGroupValue> formGroupsList = reviewWidgetData.getFormGroups();

        CardFormFieldValue panCardFormFieldValue = (CardFormFieldValue) formGroupsList.get(0).getFormGroupData().get(0).getData();
        CardFormFieldValue firstMiddleNameFormFieldValue = (CardFormFieldValue) formGroupsList.get(1).getFormGroupData().get(0).getData();
        CardFormFieldValue lastNameFormFieldValue = (CardFormFieldValue) formGroupsList.get(2).getFormGroupData().get(0).getData();
        CardFormFieldValue emailIdFormFieldValue = (CardFormFieldValue) formGroupsList.get(3).getFormGroupData().get(0).getData();
        CardFormFieldValue dobFormFieldValue = (CardFormFieldValue) formGroupsList.get(4).getFormGroupData().get(0).getData();
        CardFormFieldValue genderFormFieldValue = (CardFormFieldValue) formGroupsList.get(5).getFormGroupData().get(0).getData();
        CardFormFieldValue employmentFormFieldValue = (CardFormFieldValue) formGroupsList.get(9).getFormGroupData().get(0).getData();

        Map<String, Object> applicationData = applicationDataResponse.getApplicationData();

        UserDetails userDetails = extractUserDetails(applicationData, reviewUserDataSourceResponse.getInitialUserDataResponse(), reviewUserDataSourceResponse.getProfile());

        updatePan(panCardFormFieldValue, userDetails.pan, userDetails.panEncryption);
        updateFirstName(firstMiddleNameFormFieldValue, userDetails.firstName, userDetails.firstNameEncryption);
        updateLastName(lastNameFormFieldValue, userDetails.lastName, userDetails.lastNameEncryption);
        updateEmail(emailIdFormFieldValue, userDetails.email, userDetails.emailEncryption);
        updateDob(dobFormFieldValue, userDetails.dob, userDetails.dobEncryption);
        updateGender(genderFormFieldValue, userDetails.gender, userDetails.genderEncryption);
        updateEmployment(employmentFormFieldValue,userDetails.employmentType, userDetails.employmentTypeEncryption);
    }

    private void updateEmployment(CardFormFieldValue reviewPageWidget, String employmentType, String encryptionState) {
        if (Objects.isNull(employmentType)) return;

        String employment = mapEmployment(employmentType);
        updateFormField(reviewPageWidget, employment);
    }

    private String mapEmployment(String employmentType) {
        if ("S".equals(employmentType) || "Salaried".equals(employmentType)) {
            return "SALARIED";
        } else {
            return "SELF_EMPLOYED";
        }
    }

    private UserDetails extractUserDetails(Map<String, Object> applicationData, InitialUserDataResponse initialUserData, ProfileDetailedResponse profileDetailedResponse) throws ParseException {
        UserDetails details = new UserDetails();

        details.firstName = getStringValue(applicationData, "firstName");
        details.firstNameEncryption = ENCRYPTED;

        details.lastName = getStringValue(applicationData, "lastName");
        details.lastNameEncryption = ENCRYPTED;

        details.email = getStringValue(applicationData, "email");
        details.emailEncryption = ENCRYPTED;

        Map<String, String> namePage = (Map<String, String>) applicationData.get("namePage");
        if (namePage != null) {
            if (isNullOrEmpty(details.firstName)) {
                details.firstName = namePage.get("firstName");
                details.firstNameEncryption = DECRYPTED;
            }
            if (isNullOrEmpty(details.lastName)) {
                details.lastName = namePage.get("lastName");
                details.lastNameEncryption = DECRYPTED;
            }
            if (isNullOrEmpty(details.email)) {
                details.email = namePage.get("email");
                details.emailEncryption = DECRYPTED;
            }
        }

        Map<String, String> panPage = (Map<String, String>) applicationData.get("panPage");
        if (panPage != null) {
            details.dob = panPage.get("dob");
            details.dobEncryption = DECRYPTED;

            details.pan = panPage.get("panNumber");
            details.panEncryption = DECRYPTED;

            if (isNullOrEmpty(details.gender)) {
                details.gender = panPage.get("Gender");
                details.emailEncryption = DECRYPTED;
            }
        }

        if (profileDetailedResponse != null) {
            if (isNullOrEmpty(details.firstName)) {
                details.firstName = profileDetailedResponse.getFirstName();
                details.firstNameEncryption = ENCRYPTED;
            }
            if (isNullOrEmpty(details.lastName)) {
                details.lastName = profileDetailedResponse.getLastName();
                details.lastNameEncryption = ENCRYPTED;
            }
            if (isNullOrEmpty(details.email)) {
                details.email = profileDetailedResponse.getEmail();
                details.emailEncryption = ENCRYPTED;
            }
            if (isNullOrEmpty(details.dob)) {
                details.dob = profileDetailedResponse.getDob();
                details.dobEncryption = ENCRYPTED;
            }
            if (isNullOrEmpty(details.pan)) {
                details.pan = profileDetailedResponse.getPan();
                details.panEncryption = ENCRYPTED;
            }
            if (isNullOrEmpty(details.gender)) {
                details.gender = profileDetailedResponse.getGender();
                details.genderEncryption = DECRYPTED;
            }
            if (isNullOrEmpty(details.employmentType)) {
                EmploymentType empType = profileDetailedResponse.getEmploymentType();
                if (empType != null) {
                    details.employmentType = empType.toString();
                    details.employmentTypeEncryption = DECRYPTED;
                }
            }
        }

        if (initialUserData != null) {
            if (isNullOrEmpty(details.firstName)) {
                details.firstName = initialUserData.getFirstAndMiddleName();
                details.firstNameEncryption = DECRYPTED;
            }
            if (isNullOrEmpty(details.lastName)) {
                details.lastName = initialUserData.getLastName();
                details.lastNameEncryption = DECRYPTED;
            }
            if (isNullOrEmpty(details.email)) {
                details.email = initialUserData.getEmail();
                details.emailEncryption = DECRYPTED;
            }
            if (isNullOrEmpty(details.dob)) {
                details.dob = initialUserData.getDateOfBirth();
                String dobExpectedFormat = DateUtils.convertDateFormat(details.dob, DateUtils.experianDateFormat, DateUtils.getSimpleDateFormat9);
                details.dob = dobExpectedFormat;
                details.dobEncryption = DECRYPTED;
            }
            if (isNullOrEmpty(details.pan)) {
                details.pan = initialUserData.getPanNumber();
                details.panEncryption = DECRYPTED;
            }
            if (isNullOrEmpty(details.gender)) {
                mapGender(details, initialUserData);
            }
            if (isNullOrEmpty(details.employmentType)){
                details.employmentType = initialUserData.getEmploymentType();
                details.employmentTypeEncryption = DECRYPTED;
            }
        }

        return details;
    }

    private void mapGender(UserDetails details, InitialUserDataResponse initialUserData) {
        if (isNullOrEmpty(details.gender)) {
            String genderFromInitialData = initialUserData.getGender();
            if ("1".equals(genderFromInitialData)) {
                details.gender = "M";
            } else if ("2".equals(genderFromInitialData)) {
                details.gender = "F";
            } else if ("3".equals(genderFromInitialData)) {
                details.gender = "O";
            } 
            details.genderEncryption = DECRYPTED;
        }
    }

    private void updateAddressDetails(GroupedFormWidgetData reviewWidgetData, ApplicationDataResponse applicationDataResponse, ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        List<FormGroupValue> formGroupsList = reviewWidgetData.getFormGroups();

        CardFormFieldValue pincodeFormFieldValue = (CardFormFieldValue) formGroupsList.get(8).getFormGroupData().get(0).getData();
        List<FormGroupDataValue> pincodeFormField = formGroupsList.get(8).getFormGroupData();
        CardFormFieldValue addressLine1FormFieldValue = (CardFormFieldValue) formGroupsList.get(6).getFormGroupData().get(0).getData();
        CardFormFieldValue addressLine2FormFieldValue = (CardFormFieldValue) formGroupsList.get(7).getFormGroupData().get(0).getData();

        Map<String, Object> applicationData = applicationDataResponse.getApplicationData();

        AddressDetails addressDetails = extractAddressDetails(applicationData, reviewUserDataSourceResponse.getInitialUserDataResponse(), reviewUserDataSourceResponse.getProfile());

        updateAddressLine1(addressLine1FormFieldValue, addressDetails.line1, addressDetails.line1Encryption);
        updateAddressLine2(addressLine2FormFieldValue, addressDetails.line2, addressDetails.line2Encryption);
        updatePincode(pincodeFormFieldValue, pincodeFormField, addressDetails.pincode, addressDetails.pincodeDetails, addressDetails.pincodeEncryption);
    }

    private AddressDetails extractAddressDetails(Map<String, Object> applicationData, InitialUserDataResponse initialUserData, ProfileDetailedResponse profileDetailedResponse) {
        AddressDetails details = new AddressDetails();

        Map<String, Object> addressDetails = (Map<String, Object>) applicationData.get("addressDetails");
        if (addressDetails != null) {
            details.line1 = (String) addressDetails.get("line1");
            details.line1Encryption = DECRYPTED;

            details.line2 = (String) addressDetails.get("line2");
            details.line2Encryption = DECRYPTED;

            Map<String, String> pincodeDetails = (Map<String, String>) addressDetails.get("pincodeDetails");
            if (pincodeDetails != null) {
                details.pincode = pincodeDetails.get("pincode");
                details.pincodeDetails = pincodeDetails;
                details.pincodeEncryption = DECRYPTED;
            }
        }

        if (profileDetailedResponse != null) {
            if (isNullOrEmpty(details.line1)) {
                details.line1 = profileDetailedResponse.getAddressLine1();
                details.line1Encryption = ENCRYPTED;
            }
            if (isNullOrEmpty(details.line2)) {
                details.line2 = profileDetailedResponse.getAddressLine2();
                details.line2Encryption = ENCRYPTED;
            }
            if (isNullOrEmpty(details.pincode)) {
                Integer shippingPincode = profileDetailedResponse.getShippingPincode();
                if (shippingPincode != null) {
                    details.pincode = shippingPincode.toString();
                    details.pincodeEncryption = DECRYPTED;
                }
            }
        }

        if (initialUserData != null) {
            AddressDetailResponse userAddress = initialUserData.getAddressDetailResponse();
            if (userAddress != null) {
                if (isNullOrEmpty(details.line1)) {
                    details.line1 = userAddress.getAddressLine1();
                    details.line1Encryption = DECRYPTED;
                }
                if (isNullOrEmpty(details.line2)) {
                    details.line2 = userAddress.getAddressLine2();
                    details.line2Encryption = DECRYPTED;
                }
                if (isNullOrEmpty(details.pincode)) {
                    details.pincode = userAddress.getPincode();
                    details.pincodeDetails = new HashMap<String, String>() {{
                        put("pincode", userAddress.getPincode());
                        put("city", userAddress.getCity());
                        put("state", userAddress.getState());
                    }};
                    details.pincodeEncryption = DECRYPTED;
                }
            }
        }

        return details;
    }

    private String decryptIfNeeded(String value, String encryptionState) {
        if (Objects.isNull(value) || Objects.isNull(encryptionState)) {
            return value;
        }

        if (ENCRYPTED.equals(encryptionState)) {
            return decrypter.decryptString(value);
        }

        return value;
    }

    private void updateAddressLine2(CardFormFieldValue reviewPageWidget, String addressLine2, String encryptionState) {
        if (Objects.isNull(addressLine2)) return;

        String decryptedValue = decryptIfNeeded(addressLine2, encryptionState);
        updateFormField(reviewPageWidget, decryptedValue);
    }

    private void updateAddressLine1(CardFormFieldValue reviewPageWidget, String addressLine1, String encryptionState) {
        if (Objects.isNull(addressLine1)) return;

        String decryptedValue = decryptIfNeeded(addressLine1, encryptionState);
        updateFormField(reviewPageWidget, decryptedValue);
    }

    private void updateDob(CardFormFieldValue reviewPageWidget, String dob, String encryptionState) {
        if (Objects.isNull(dob)) return;

        String decryptedValue = decryptIfNeeded(dob, encryptionState);
        updateFormField(reviewPageWidget, decryptedValue);
    }

    private void updatePan(CardFormFieldValue reviewPageWidget, String pan, String encryptionState) {
        if (Objects.isNull(pan)) return;

        String decryptedValue = decryptIfNeeded(pan, encryptionState);
        updateFormField(reviewPageWidget, decryptedValue);
    }

    private void updateFirstName(CardFormFieldValue reviewPageWidget, String firstName, String encryptionState) {
        if (Objects.isNull(firstName)) return;

        String decryptedValue = decryptIfNeeded(firstName, encryptionState);
        updateFormField(reviewPageWidget, decryptedValue);
    }

    private void updateLastName(CardFormFieldValue reviewPageWidget, String lastName, String encryptionState) {
        if (Objects.isNull(lastName)) return;

        String decryptedValue = decryptIfNeeded(lastName, encryptionState);
        updateFormField(reviewPageWidget, decryptedValue);
    }

    private void updateEmail(CardFormFieldValue reviewPageWidget, String email, String encryptionState) {
        if (Objects.isNull(email)) return;

        String decryptedValue = decryptIfNeeded(email, encryptionState);
        updateFormField(reviewPageWidget, decryptedValue);
    }

    private void updatePincode(CardFormFieldValue reviewPageWidget, List<FormGroupDataValue> pincodeFormField, String pincode, Map<String, String> pincodeDetails, String encryptionState) {
        if (Objects.isNull(pincode)) return;

        String decryptedValue = decryptIfNeeded(pincode, encryptionState);
        updateFormField(reviewPageWidget, decryptedValue);
        if (Objects.nonNull(pincodeDetails)) {
            updateFormField((CardFormFieldValue) pincodeFormField.get(0).getData(), pincodeDetails.get("pincode"));
            ((CardFormFieldValue)pincodeFormField.get(1).getData()).getCard().getDescription().getValue().setText(pincodeDetails.get("city"));
            ((CardFormFieldValue)pincodeFormField.get(3).getData()).getCard().getDescription().getValue().setText(pincodeDetails.get("state"));
        }

        Map<String, Object> params = reviewPageWidget.getCard().getNext().getAction().getParams();
        Map<String, Object> pageResponse = (Map<String, Object>) params.get("pageResponse");
        List<?> slots = (List<?>) pageResponse.get("slots");
        Map<String, Object> firstSlot = (Map<String, Object>) slots.get(0);
        Map<String, Object> widget = (Map<String, Object>) firstSlot.get("widget");
        Map<String, Object> data = (Map<String, Object>) widget.get("data");
        List<Map<String, Object>> renderableComponents = (List<Map<String, Object>>) data.get("renderableComponents");
        Map<String, Object> firstComponent = renderableComponents.get(0);
        Map<String, Object> valueMap = (Map<String, Object>) firstComponent.get("value");
        Map<String, Object> customFieldData = (Map<String, Object>) valueMap.get("customFieldData");
        Map<String, Object> pincodeField = (Map<String, Object>) customFieldData.get("pincode");
        ((Map<String, Object>) pincodeField.get("value")).put("value", decryptedValue);

    }

    private void updateGender(CardFormFieldValue reviewPageWidget, String gender, String encryptionState) {
        if (Objects.isNull(gender)) return;

        String decryptedValue = decryptIfNeeded(gender, encryptionState);
        updateGenderFormField(reviewPageWidget, decryptedValue);
    }

    private void updateFormField(CardFormFieldValue reviewPageWidget, String value) {
        RichTextValue textValue = (RichTextValue) reviewPageWidget.getCard().getDescription().getValue();
        textValue.setText(value);

        Map<String, Object> params = reviewPageWidget.getCard().getNext().getAction().getParams();
        Map<String, Object> pageResponse = (Map<String, Object>) params.get("pageResponse");
        List<?> slots = (List<?>) pageResponse.get("slots");
        Map<String, Object> firstSlot = (Map<String, Object>) slots.get(0);
        Map<String, Object> widget = (Map<String, Object>) firstSlot.get("widget");
        Map<String, Object> data = (Map<String, Object>) widget.get("data");
        List<Map<String, Object>> renderableComponents = (List<Map<String, Object>>) data.get("renderableComponents");
        Map<String, Object> firstComponent = renderableComponents.get(0);
        Map<String, Object> valueMap = (Map<String, Object>) firstComponent.get("value");

        valueMap.put("value", value);
    }

    private void updateGenderFormField(CardFormFieldValue reviewPageWidget, String genderValue) {
        RichTextValue textValue = (RichTextValue) reviewPageWidget.getCard().getDescription().getValue();
        textValue.setText(genderValue);

        Map<String, Object> params = reviewPageWidget.getCard().getNext().getAction().getParams();
        Map<String, Object> pageResponse = (Map<String, Object>) params.get("pageResponse");
        List<?> slots = (List<?>) pageResponse.get("slots");
        Map<String, Object> firstSlot = (Map<String, Object>) slots.get(0);
        Map<String, Object> widget = (Map<String, Object>) firstSlot.get("widget");
        Map<String, Object> data = (Map<String, Object>) widget.get("data");
        List<Map<String, Object>> renderableComponents = (List<Map<String, Object>>) data.get("renderableComponents");
        Map<String, Object> firstComponent = renderableComponents.get(0);
        Map<String, Object> valueMap = (Map<String, Object>) firstComponent.get("value");

        Map<String, Object> selectedOption = new HashMap<>();
        selectedOption.put("id", genderValue);
        selectedOption.put("type", DropdownType.PLAIN_TEXT);
        selectedOption.put("fullWidthPress", false);

        valueMap.put("selectedOption", selectedOption);
    }

    private String getStringValue(Map<String, Object> map, String key) {
        return (String) map.get(key);
    }

    private boolean isNullOrEmpty(String str) {
        return str == null || str.isEmpty();
    }

    private static class UserDetails {
        String firstName;
        String firstNameEncryption;
        String lastName;
        String lastNameEncryption;
        String email;
        String emailEncryption;
        String dob;
        String dobEncryption;
        String pan;
        String panEncryption;
        String gender;
        String genderEncryption;
        String employmentType;
        String employmentTypeEncryption;
    }

    private static class AddressDetails {
        String line1;
        String line1Encryption;
        String line2;
        String line2Encryption;
        String pincode;
        Map<String,String> pincodeDetails;
        String pincodeEncryption;
    }
}