package com.flipkart.fintech.pinaka.service.utils.FormUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.http.NameValuePair;

public class QueryParamUtils {

    public static Map<String, Object> getQueryParams(List<NameValuePair> queryParams) {
        Map<String, Object> queryParamsMap = new HashMap<>();
        queryParams.forEach(nameValuePair -> {
            queryParamsMap.put(nameValuePair.getName(), nameValuePair.getValue());
        });
        return queryParamsMap;
    }

    public Map<String, Object> getResendOtpFormData() {
        Map<String, Object> resendOtpFormData = new HashMap<>();
        resendOtpFormData.put("status","RESEND");
        return resendOtpFormData;
    }

}
