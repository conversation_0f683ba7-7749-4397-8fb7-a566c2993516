package com.flipkart.fintech.pinaka.service.helper;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 04/07/18.
 */
@Constraint(validatedBy = CreateRequestValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(value = RetentionPolicy.RUNTIME)
public @interface ValidateCreateRequest {
    String message() default "Invalid create application request";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
