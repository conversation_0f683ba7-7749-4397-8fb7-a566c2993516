package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.ams.ApplicationService;
import com.flipkart.ams.ApplicationServiceV2;
import com.flipkart.ams.ApplicationTypeUtils;
import com.flipkart.ams.LEAD_APPLICATION_TYPES;
import com.flipkart.fintech.citadel.api.models.ActiveApplicationResponse;
import com.flipkart.fintech.lending.orchestrator.model.LendingPageRequest;
import com.flipkart.fintech.lending.orchestrator.service.ReadRepairDataService;
import com.flipkart.fintech.lending.orchestrator.service.PersonalLoanOrchestrator;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.TaskKey;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserRequestActionType;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Params;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.core.actionfactory.ActionFactory;
import com.flipkart.fintech.pinaka.service.core.page.RetryWithEditBehaviour;
import com.flipkart.fintech.pinaka.service.core.page.RetryWithoutEditBehaviour;
import com.flipkart.fintech.pinaka.service.core.v6.UserActionHandler;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataDecryption;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataEncryption;
import com.flipkart.fintech.pinaka.service.exception.InvalidInputException;
import com.flipkart.fintech.lead.model.LeadV4DataGatheringResponse;
import com.flipkart.fintech.lead.service.LeadV4DataGatheringService;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.pinaka.service.helper.UserActionSubmitRequestHelper;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;

import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.inject.Inject;
import com.supermoney.ams.bridge.AmsBridge;
import com.supermoney.ams.bridge.exceptions.InvalidMerchantException;
import lombok.CustomLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;

import java.net.URISyntaxException;
import java.util.*;

import static com.flipkart.fintech.pinaka.api.enums.ProductType.PERSONAL_LOAN;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.SUBMIT;
import static com.flipkart.fintech.pinaka.service.helper.UserActionSubmitRequestHelper.*;

@CustomLog
public class UserActionHandlerImpl implements UserActionHandler {
    private final ApplicationService applicationService;
    private final ApplicationServiceV2 applicationServiceV2;
    private final AmsBridge amsBridge;

    private final ActionFactory actionFactory;
    private final PersonalLoanOrchestrator lendingOrchestrator;
    private final ReadRepairDataService readRepairDataService;
    private final MetricRegistry metricRegistry;
    private final FormDataDecryption formDataDecryption;
    private final FormDataEncryption formDataEncryption;
    private final BqIngestionHelper bqIngestionHelper;
    private final LeadV4DataGatheringService leadV4DataGatheringService;

    @Inject
    public UserActionHandlerImpl(ApplicationService applicationService,
                                 ApplicationServiceV2 applicationServiceV2,
                                 AmsBridge amsBridge,
                                 PersonalLoanOrchestrator lendingOrchestrator,
                                 ReadRepairDataService readRepairDataService,
                                 ActionFactory actionFactory,
                                 FormDataDecryption formDataDecryption,
                                 FormDataEncryption formDataEncryption,
                                 BqIngestionHelper bqIngestionHelper,
                                 LeadV4DataGatheringService leadV4DataGatheringService) {
        this.applicationService = applicationService;
        this.applicationServiceV2 = applicationServiceV2;
        this.amsBridge = amsBridge;
        this.lendingOrchestrator = lendingOrchestrator;
        this.readRepairDataService = readRepairDataService;
        this.actionFactory = actionFactory;
        this.metricRegistry = PinakaMetricRegistry.getMetricRegistry();
        this.formDataDecryption = formDataDecryption;
        this.formDataEncryption = formDataEncryption;
        this.bqIngestionHelper = bqIngestionHelper;
        this.leadV4DataGatheringService = leadV4DataGatheringService;
    }

    @Override
    public PageActionResponse submit(UserActionRequest submitRequest, String requestId, String userAgent)
            throws PinakaException {
        Timer.Context timer = null;
        MerchantUser merchantUser = MerchantUserUtils.getMerchantUser(submitRequest.getAccountId(), submitRequest.getSmUserId());
        String applicationId = getApplicationId(submitRequest, merchantUser);
        try {
            ApplicationDataResponse applicationDataResponse = applicationService.fetchActiveApplicationData(merchantUser, applicationId);
            if (Objects.isNull(applicationDataResponse)) {  // this scenario is only expected if applicationDataResponse is not received due to system issues
                return RetryWithEditBehaviour.getPageActionResponse();
            }
            if (LV3Util.isLv3Application(applicationDataResponse)) {
                UserActionSubmitRequestHelper.processFormDataFields(submitRequest, formDataEncryption, formDataDecryption);
            }
            if(LV4Util.isLv4Application(applicationDataResponse)) {
                // all these 3 steps needs to happen in the same sequence as processFormDataFields updates modifies some fields, don't change.
                UserActionSubmitRequestHelper.validateRequest(submitRequest);
                this.bqIngestionHelper.insertLeadEvents(UserActionSubmitRequestHelper.getLeadEvents(applicationDataResponse, submitRequest));
                UserActionSubmitRequestHelper.processFormDataFields(submitRequest, formDataEncryption, formDataDecryption);

                // Cache user data right after form processing for LV4 pages using taskKey
                if (shouldCacheUserDataByTaskKey(submitRequest.getTaskKey())) {
                    gatherAndCacheUserDataInRequest(submitRequest.getTaskKey(), applicationDataResponse, merchantUser);
                }
            }

            timer = metricRegistry.timer(SUBMIT + applicationDataResponse.getApplicationState()).time();
            if (!amsBridge.isResumable(submitRequest, applicationDataResponse)) {
                return create(requestId, merchantUser, applicationDataResponse);
            }

            // Use the enhanced resumeApplication method that returns complete ApplicationDataResponse
            ApplicationDataResponse updatedApplicationDataResponse = resumeApplicationV2(submitRequest,
                    applicationDataResponse, merchantUser);

            // todo : revisit when breaking lead in 3 pages
            if (EnumUtils.isValidEnum(LEAD_APPLICATION_TYPES.class, updatedApplicationDataResponse.getApplicationType()) && updatedApplicationDataResponse.getApplicationState().equals("CREATE_PROFILE_END")) {
                log.info("current lead userActionRequest : {}", submitRequest);
                LendingPageRequest pageRequest = LendingPageRequest.builder()
                        .applicationId(submitRequest.getApplicationId())
                        .accountId(submitRequest.getAccountId())
                        .smUserId(submitRequest.getSmUserId())
                        .build();
                Optional<ApplicationDataResponse> activeApplication = lendingOrchestrator.getOngoingLoanApplication(merchantUser);
                return lendingOrchestrator.getStatusV2(pageRequest, merchantUser, requestId, userAgent, activeApplication);
            }
            return create(requestId, merchantUser, updatedApplicationDataResponse);
        } catch (DataEnrichmentException | InvalidInputException ex) {
            log.error("Error while enriching the submitted details for accountId: {}, request: {}: {}",
                    merchantUser.getMerchantUserId(), submitRequest, ex.getMessage());
            metricRegistry.meter(MetricRegistry.name(UserActionHandler.class, "submit_exception")).mark();
            return RetryWithEditBehaviour.getPageActionResponse();
        }
        catch (PinakaException ex) {
            log.error("Error while submitting details for accountId: {}, request: {}: {}",
                    merchantUser.getMerchantUserId(), submitRequest, ex.getMessage());
            metricRegistry.meter(MetricRegistry.name(UserActionHandler.class, "submit_exception")).mark();
            return RetryWithoutEditBehaviour.getPageActionResponse();
        } finally {
            if(timer != null) {
                timer.stop();
            }
        }
    }

    private String getApplicationId(UserActionRequest submitRequest, MerchantUser merchantUser) throws
            PinakaException {
        String applicationId = submitRequest.getApplicationId();
        if (StringUtils.isNotEmpty(applicationId)) {
            return applicationId;
        }
        UserRequestActionType type = submitRequest.getType();
        if (UserRequestActionType.MANDATE.equals(type) || UserRequestActionType.VKYC.equals(type)) {
            String applicationType = "PERSONAL_LOAN_IDFC";
            ActiveApplicationResponse activeApplications = applicationService.getActiveApplications(merchantUser, applicationType);
            List<String> applicationIds = activeApplications.getApplicationList().get(
                    applicationType);
            if (CollectionUtils.isNotEmpty(applicationIds)) {
                applicationId = applicationIds.get(0);
            }
        }

        return applicationId;
    }

    public PageActionResponse create(String requestId, MerchantUser merchantUser,
                                     ApplicationDataResponse applicationDataResponse) throws PinakaException {
        String applicationType = applicationDataResponse.getApplicationType();
        if(applicationType.equals(ApplicationTypeUtils.getApplicationType(PERSONAL_LOAN, Lender.AXIS.name()))) {
            if ("REJECTED".equals(applicationDataResponse.getApplicationState()) || ("rejected".equals(applicationDataResponse.getApplicationState()))) {
                return getPageActionResponse(merchantUser, applicationDataResponse);
            }
            if( !applicationDataResponse.getPendingTask().isEmpty() ){
                String pendingTaskKey = applicationDataResponse.getPendingTask().get(0).getTaskKey();
                if (TaskKey.workDetails.name().equals(pendingTaskKey) || TaskKey.addressDetails.name().equals(pendingTaskKey)) {
                    return getPageActionResponse(merchantUser, applicationDataResponse);
                }
            }
            return createForAxis(requestId, merchantUser, applicationDataResponse);
        }
        else{
            return getPageActionResponse(merchantUser, applicationDataResponse);
        }
    }

    private PageActionResponse createForAxis(String requestId, MerchantUser merchantUser,
                                             ApplicationDataResponse applicationDataResponse) throws PinakaException {
        try {
            Action action = actionFactory.getAction(requestId, merchantUser, applicationDataResponse);
            Params params=new Params();
            params.setApplicationId(applicationDataResponse.getApplicationId());
            return new PageActionResponse(action, true, null, params);
        } catch (URISyntaxException | JsonProcessingException | InvalidMerchantException e) {
            throw new PinakaException(e);
        }
    }

    private PageActionResponse getPageActionResponse(MerchantUser merchantUser, ApplicationDataResponse applicationDataResponse) {
        return amsBridge.getPageActionResponse(applicationDataResponse, merchantUser);
    }


    /**
     * Enhanced method that returns complete ApplicationDataResponse
     */
    private ApplicationDataResponse resumeApplicationV2(UserActionRequest submitRequest,
                                                      ApplicationDataResponse applicationDataResponse, MerchantUser merchantUser) throws PinakaException {
        String applicationId = applicationDataResponse.getApplicationId();
        ResumeApplicationRequest request = amsBridge.getResumeRequest(submitRequest, applicationDataResponse);
        log.info("ResumeApplicationRequest request created for V2: {}", request.toString());
        return applicationServiceV2.resumeApplication(merchantUser, applicationId, request);
    }

    /**
     * Check if user data should be cached based on taskKey (leadV4Page1 or leadV4Page2)
     */
    protected boolean shouldCacheUserDataByTaskKey(String taskKey) {
        return LEAD_V4_LANDING_PAGE.equals(taskKey);
    }

    /**
     * Gather user data and store it in applicationDataResponse for inclusion in resumeApplicationV2
     */
    protected void gatherAndCacheUserDataInRequest(String taskKey,
                                               ApplicationDataResponse applicationDataResponse,
                                               MerchantUser merchantUser) {
        try {
            LeadV4DataGatheringResponse cacheResponse = leadV4DataGatheringService.gatherData(merchantUser, taskKey,
                    applicationDataResponse.getApplicationData());

            if (cacheResponse.hasCachedUserData()) {
                // Add cached user data directly to applicationDataResponse so it gets included in the resume request
                Map<String, Object> applicationData = applicationDataResponse.getApplicationData();
                if (applicationData == null) {
                    applicationData = new HashMap<>();
                    applicationDataResponse.setApplicationData(applicationData);
                }

                // Merge cached data if key already exists
                if (applicationData.containsKey(LV4Util.CACHED_USER_DATA_KEY)) {
                    mergeCachedUserData(applicationData, cacheResponse.getCachedUserData(), merchantUser.getSmUserId());
                } else {
                    applicationData.put(LV4Util.CACHED_USER_DATA_KEY, cacheResponse.getCachedUserData());
                }

                log.info("Cached user data added to applicationDataResponse for taskKey: {} userId: {}, cache size: {}",
                        taskKey, merchantUser.getSmUserId(), cacheResponse.getCachedUserData().size());
            }
        } catch (Exception e) {
            log.warn("Failed to cache user data in applicationDataResponse for userId: {}: {}",
                    merchantUser.getSmUserId(), e.getMessage());
        }
    }

    protected void mergeCachedUserData(Map<String, Object> applicationData, Map<String, Object> newCachedData, String userId) {
        try {
            Map<String, Object> existingCachedData = (Map<String, Object>) applicationData.get(LV4Util.CACHED_USER_DATA_KEY);
            if (existingCachedData == null) {
                applicationData.put(LV4Util.CACHED_USER_DATA_KEY, newCachedData);
                log.info("No existing cached data found, adding new cached data for userId: {}", userId);
                return;
            }

            // Merge the cached data structures
            Map<String, Object> mergedCachedData = new HashMap<>(existingCachedData);

            // Update with new data, giving priority to new data
            for (Map.Entry<String, Object> entry : newCachedData.entrySet()) {
                String key = entry.getKey();
                Object newValue = entry.getValue();

                if (LV4Util.CACHED_REVIEW_DATA_KEY.equals(key)) {
                    // Merge ReviewUserDataSourceResponse objects
                    mergeReviewUserDataSourceResponse(mergedCachedData, newValue, userId);
                } else {
                    // For other keys, just update with new value
                    mergedCachedData.put(key, newValue);
                }
            }

            applicationData.put(LV4Util.CACHED_USER_DATA_KEY, mergedCachedData);
            log.info("Successfully merged cached data for userId: {}", userId);

        } catch (Exception e) {
            log.warn("Failed to merge cached data for userId: {}, using new data: {}", userId, e.getMessage());
            applicationData.put(LV4Util.CACHED_USER_DATA_KEY, newCachedData);
        }
    }

    protected void mergeReviewUserDataSourceResponse(Map<String, Object> mergedCachedData, Object newReviewData, String userId) {
        try {
            ReviewUserDataSourceResponse existingReviewData =
                (ReviewUserDataSourceResponse) mergedCachedData.get(LV4Util.CACHED_REVIEW_DATA_KEY);
            ReviewUserDataSourceResponse newReviewDataResponse = (ReviewUserDataSourceResponse) newReviewData;

            if (existingReviewData == null) {
                mergedCachedData.put(LV4Util.CACHED_REVIEW_DATA_KEY, newReviewDataResponse);
                log.info("No existing review data found, adding new review data for userId: {}", userId);
                return;
            }

            // Merge the ReviewUserDataSourceResponse objects
            // Priority: new data overwrites existing data when both are present
            if (newReviewDataResponse.getProfile() != null) {
                existingReviewData.setProfile(newReviewDataResponse.getProfile());
                log.info("Updated profile in existing cached data for userId: {}", userId);
            }
            if (newReviewDataResponse.getInitialUserDataResponse() != null) {
                existingReviewData.setInitialUserDataResponse(newReviewDataResponse.getInitialUserDataResponse());
                log.info("Updated initialUserDataResponse in existing cached data for userId: {}", userId);
            }
            if (newReviewDataResponse.getQueryParams() != null) {
                existingReviewData.setQueryParams(newReviewDataResponse.getQueryParams());
                log.info("Updated queryParams in existing cached data for userId: {}", userId);
            }
            if (newReviewDataResponse.getEncryptionData() != null) {
                existingReviewData.setEncryptionData(newReviewDataResponse.getEncryptionData());
                log.info("Updated encryptionData in existing cached data for userId: {}", userId);
            }

            mergedCachedData.put(LV4Util.CACHED_REVIEW_DATA_KEY, existingReviewData);

        } catch (Exception e) {
            log.warn("Failed to merge ReviewUserDataSourceResponse for userId: {}, using new data: {}", userId, e.getMessage());
            mergedCachedData.put(LV4Util.CACHED_REVIEW_DATA_KEY, newReviewData);
        }
    }
}
