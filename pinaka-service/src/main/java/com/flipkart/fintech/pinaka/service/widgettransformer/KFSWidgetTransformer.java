package com.flipkart.fintech.pinaka.service.widgettransformer;

import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.KFSDetail.*;
import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.KFSDetailV2.*;
import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.KFSDetailV2.DATECOMMENCEMENT;
import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.KFSDetailV2.INSTALLMENT_DETAILS;
import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.KFSDetailV2.LOAN_TERM;
import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.KFSDetailV2.REPAYMENT_SCHEDULE;
import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.UPDATE_TEXT;

import com.flipkart.fintech.pandora.api.model.pl.response.KfsDetail;
import com.flipkart.fintech.pandora.api.model.pl.response.LoanOnboardingResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.RepaySchedule;
import com.flipkart.fintech.pinaka.api.request.v6.SlotInfo;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.pagedatasource.KFSDetailsPageDataSource;
import com.flipkart.fintech.pinaka.service.response.KFSDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.submitbuttonwidget.SubmitButtonWidgetTransformer;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.TextDataValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.RowCellType;
import com.flipkart.rome.datatypes.response.fintech.supermoney.table.RowCellValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.StaticHtmlShowcaseWidgetData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.TableWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.RichMessageWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.CbcOptionsNoteWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import java.util.ArrayList;
import java.util.List;
import lombok.CustomLog;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;

@CustomLog
public class KFSWidgetTransformer implements SubmitButtonWidgetTransformer {

    private static final String submitButtonJson;
    private static final String tableWidgetJson;
    private static final String textWidgetJson;
    private static final String htmlWidgetJson;


    static {
        submitButtonJson = TransformerUtils.readFileasString("template/idfc/KFSSubmitButtonWidget.json");
        tableWidgetJson = TransformerUtils.readFileasString("template/idfc/KFSTableWidget.json");
        textWidgetJson = TransformerUtils.readFileasString("template/idfc/KFSTextV2Widget.json");
        htmlWidgetJson = TransformerUtils.readFileasString("template/idfc/KfsHtmlWidget.json");

    }

    @Override
    public SubmitButtonWidgetData buildSubmitButtonWidgetData(
        ApplicationDataResponse applicationDataResponse)
            throws PinakaClientException {
        try {
            KFSDetailsPageDataSourceResponse kfsDetailsPageDataSourceResponse =
                new KFSDetailsPageDataSource().getData(applicationDataResponse);
            SubmitButtonWidgetData submitButtonWidgetData = ObjectMapperUtil.get().readValue(submitButtonJson, SubmitButtonWidgetData.class);
            updateSubmitButton(kfsDetailsPageDataSourceResponse,submitButtonWidgetData);
            return submitButtonWidgetData;
        } catch (Exception e) {
            log.error("SubmitButtonWidgetData build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }

    }


    public TableWidgetData buildtableWidgetData(KFSDetailsPageDataSourceResponse pageDataSourceReponse,
            SlotInfo slotInfo)
            throws PinakaClientException {
        try {
            log.info("Building kfs table for slot : {}",ObjectMapperUtil.get().writeValueAsString(slotInfo));
            KfsDetail kfsDetail= pageDataSourceReponse.getLoanOnboardingResponse().getResult().getResponse().getKfsDetails().get(0);
            String loanId = pageDataSourceReponse.getLoanOnboardingResponse().getResult().getResponse().getLoanId();
            String date = pageDataSourceReponse.getLoanOnboardingResponse().getResult().getResponse().getRepaySchedule().get(0).getDueDate();
            List<RepaySchedule> repayScheduleList = pageDataSourceReponse.getLoanOnboardingResponse().getResult().getResponse().getRepaySchedule();
            if(slotInfo.getContentId()!=null && slotInfo.getContentId().equals("TABLE_1")) {
                TableWidgetData tableWidgetData = ObjectMapperUtil.get().readValue(tableWidgetJson, TableWidgetData.class);
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(PART1, ""));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(LOAN_PROPOSAL_ACCOUNT_NO, loanId));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(TYPE_OF_LOAN, PERSONAL_LOAN));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(SANCTIONED_LOAN_AMOUNT, kfsDetail.getSanctLimit()));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(LOAN_AMOUNT, kfsDetail.getLoanAmt()));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(NET_DISBURSED_AMOUNT, kfsDetail.getNetDisbAmt()));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(DISBURSAL_SCHEDULE,"100% upfront"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(LOAN_TERM, kfsDetail.getTenor() + "months"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(INSTALLMENT_DETAILS, ""));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(INSTALMENT_TYPE, "Equated Monthly Installment"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(NUMBER_OF_EPIS, kfsDetail.getNumInstallment()));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(EPI_AMOUNT, kfsDetail.getAmtInstall()));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(DATECOMMENCEMENT, date));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(INTEREST_RATE, kfsDetail.getAppRoi()+" % p.a. (fixed)"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(ADDITIONAL_INFO_FLOATING_RATE, ""));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(REFERENCE_BENCHMARK, "NA"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(BENCHMARK_RATE, "NA"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(FEECHARGES, ""));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(PAYABLE, ""));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(PROCESSING_FEES, "One-Time, "+kfsDetail.getProcessFee()));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(INSURANCE_CHARGES,"NA"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(VALUATION_FEES, "NA"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(OTHER_FEES, "NA"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(ANNUAL_PERCENTAGE_RATE, kfsDetail.getApr()));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(CONTINGENTCHARGES, ""));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(PENAL_CHARGES,kfsDetail.getLatePayCharge()));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(BOUNCE_CHARGES, kfsDetail.getBounceCharge()));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(FORECLOSURE_CHARGES, kfsDetail.getForeclosureCharge()));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(SWITCHING_LOAN_CHARGES, "NA"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(USAGE_FEE, "NA"));

                return tableWidgetData;
            }
            else if(slotInfo.getContentId()!=null && slotInfo.getContentId().equals("TABLE_2")){
                TableWidgetData tableWidgetData = ObjectMapperUtil.get().readValue(tableWidgetJson, TableWidgetData.class);
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(PART2, ""));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(RECOVERY_AGENT_CLAUSE, "Clause 7 (a) (xxxi)"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(GRIEVANCE_REMEDY_CLAUSE, "Clause 16 (s)"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(GRIEVANCE_CONTACT_PHONE, IDFC_PNO+" \n"+PRIVACY_LINK+" \n"+SCAPIC_NODAL_OFFICER+" \n"+SCAPIC_PRIVACY_LINK));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(LOAN_TRANSFER_POSSIBILITY, "Yes"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(COLLABORATIVE_LENDING_ARRANGEMENTS, ""));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(ORIGINATING_RE, "NA"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(PARTNER_RE, "NA"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(BLENDED_RATE, "NA"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(DIGITAL_LOANS, ""));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(COOLING_OFF_PERIOD, "3 days"));
                tableWidgetData.getTableData().getRows().add(getRowCellValueList(LSP_RECOVERY_AGENT, kfsDetail.getLspDetails()));
                return tableWidgetData;
            }
            else
            {
                TableWidgetData thirdtableWidgetData = ObjectMapperUtil.get().readValue(tableWidgetJson, TableWidgetData.class);
                int emiNo = 1;
                thirdtableWidgetData.getTableData().getRows().add(getRowCellValueList(PART3, ""));
                for(RepaySchedule repaySchedule :repayScheduleList){
                    thirdtableWidgetData.getTableData().getRows().add(getRowCellValueList(EMINO, String.valueOf(emiNo)));
                    thirdtableWidgetData.getTableData().getRows().add(getRowCellValueList(DUEDATE, repaySchedule.getDueDate()));
                    thirdtableWidgetData.getTableData().getRows().add(getRowCellValueList(AMT, repaySchedule.getInstallment()));
                    emiNo = emiNo +1;
                }
                return thirdtableWidgetData;
            }
        }
        catch (Exception e) {
            log.error("TableWidgetData build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }

    }

    private List<RowCellValue> getRowCellValueList(String title,String text) {
        RowCellValue titleCellValue = getRowCellValue(title);
        RowCellValue textCellValue = getRowCellValue(text);
        List<RowCellValue>rowCellValueList = new ArrayList<>();
        rowCellValueList.add(titleCellValue);
        rowCellValueList.add(textCellValue);
        return rowCellValueList;
    }

    public StaticHtmlShowcaseWidgetData buildHtmlWidgetData(KFSDetailsPageDataSourceResponse pageDataSourceReponse)
            throws PinakaClientException {
        try {
            StaticHtmlShowcaseWidgetData staticHtmlShowcaseWidgetData = ObjectMapperUtil.get()
                    .readValue(htmlWidgetJson, StaticHtmlShowcaseWidgetData.class);
            return staticHtmlShowcaseWidgetData;
        }
        catch (Exception e) {
            log.error("StaticHtmlShowcaseWidgetData build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }

    }
    private RowCellValue getRowCellValue(String text)
    {
        RowCellValue rowCellValue = new RowCellValue<>();
        TextDataValue textDataValue = new TextDataValue();
        textDataValue.setText(text);
        rowCellValue.setValue(textDataValue);
        rowCellValue.setCellType(RowCellType.TEXT);
        return rowCellValue;
    }


    public RichMessageWidgetData buildtextWidgetData(KFSDetailsPageDataSourceResponse pageDataSourceReponse)
            throws  PinakaClientException {
        try {
            RichMessageWidgetData richMessageWidgetData = ObjectMapperUtil.get()
                    .readValue(textWidgetJson, RichMessageWidgetData.class);
            LoanOnboardingResponse loanOnboardingResponse = pageDataSourceReponse.getLoanOnboardingResponse();
            String loandate = loanOnboardingResponse.getResult().getResponse().getKfsDetails().get(0).getAgreementDate();
            RichTextValue richTextValue = new RichTextValue();
            richTextValue.setText(UPDATE_TEXT + loandate);
            richMessageWidgetData.getMessage().getValue().setSubtitle(richTextValue);
            return richMessageWidgetData;
        }
        catch (Exception e) {
            log.error("RichMessageWidgetData build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }

    }

    private static void updateSubmitButton(KFSDetailsPageDataSourceResponse kfsDetailsPageDataSourceResponse,SubmitButtonWidgetData submitButtonWidgetData) {
        Action action = submitButtonWidgetData.getSubmitButton().getButton().getAction();
        action.setParams(kfsDetailsPageDataSourceResponse.getQueryParams());
        action.setEncryption(kfsDetailsPageDataSourceResponse.getEncryptionData());
        submitButtonWidgetData.getSubmitButton().getButton().setAction(action);
    }

}
