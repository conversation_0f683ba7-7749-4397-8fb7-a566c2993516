package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.logger.core.FintechLogger;
import com.flipkart.fintech.logger.core.flogger.FintechFlogger;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.*;
import com.flipkart.fintech.pinaka.service.response.*;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.NamePageFormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.ReviewPage1FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.ReviewPage2FormTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;

import java.text.ParseException;
import java.util.Map;

public class FormWidgetTransformer {

    private final SandboxOfferScreenTransformer sandboxOfferScreenTransformer;
    public static final FintechFlogger logger = FintechLogger.flogger(FormWidgetTransformer.class);
    private final Map<String, BasicDetailsFormTransformer.FormFields> formFieldsMap;
    private final Map<String, NamePageFormTransformer.FormFields> namePageFormFieldsMap;
    private final Map<String, ReviewPage1FormTransformer.FormFields> initialUserDatFormFieldsMap;
    private final DynamicBucket dynamicBucket;
    private final Decrypter decrypter;
    private final Map<String, ReviewPage2FormTransformer.FormField> initialUserDataFormMapPage2;

    public FormWidgetTransformer(SandboxOfferScreenTransformer sandboxOfferScreenTransformer, Map<String, BasicDetailsFormTransformer.FormFields> formFieldsMap,
                                 Map<String, ReviewPage1FormTransformer.FormFields> initialUserDatFormFieldsMap, Map<String, ReviewPage2FormTransformer.FormField> initialUserDataFormMapPage2, Map<String, NamePageFormTransformer.FormFields> namePageFormFieldsMap,
                                 DynamicBucket dynamicBucket, Decrypter decrypter) {
        this.sandboxOfferScreenTransformer = sandboxOfferScreenTransformer;
        this.formFieldsMap = formFieldsMap;
        this.namePageFormFieldsMap = namePageFormFieldsMap;
        this.initialUserDatFormFieldsMap = initialUserDatFormFieldsMap;
        this.initialUserDataFormMapPage2 = initialUserDataFormMapPage2;
        this.dynamicBucket = dynamicBucket;
        this.decrypter = decrypter;
    }

    public GenericFormWidgetData buildWidgetData(String formType, ApplicationDataResponse applicationDataResponse)
            throws PinakaClientException, ParseException, JsonProcessingException, PinakaException {
        switch (formType) {
            case "IDFC_BASIC_DETAILS_FORM":
                PageDataSource<BasicDetailsPageDataSourceResponse> basicDetailsPageDataSource = new BasicDetailsPageDataSource();
                BasicDetailsPageDataSourceResponse basicDetailsPageDataSourceResponse = basicDetailsPageDataSource.getData(applicationDataResponse);
                return new BasicDetailsFormTransformer(basicDetailsPageDataSourceResponse, formFieldsMap, dynamicBucket).buildWidgetData(applicationDataResponse);
            case "PL_LEAD_NAME_FORM":
                PageDataSource<LeadPageDataSourceResponse> leadPageDataSource = new LeadPageDataSource();
                LeadPageDataSourceResponse leadPageDataSourceResponse = leadPageDataSource.getData(applicationDataResponse);
                return new NamePageFormTransformer(leadPageDataSourceResponse, namePageFormFieldsMap, decrypter, dynamicBucket).buildWidgetData(applicationDataResponse);
            case "ADDRESS_DETAILS":
                PageDataSource<AddressDetailsPageDataSourceResponse> addressDetailsPageDataSource = new AddressDetailsPageDataSource();
                AddressDetailsPageDataSourceResponse addressDetailsPageDataSourceResponse = addressDetailsPageDataSource.getData(applicationDataResponse);
                return new AddressDetailsFormTransformer(addressDetailsPageDataSourceResponse).buildWidgetData(applicationDataResponse);
            case "WORK_DETAILS":
                PageDataSource<WorkDetailsPageDataSourceResponse> workDetailsPageDataSourceResponse = new WorkDetailsPageDataSource();
                WorkDetailsPageDataSourceResponse workDetailsPageDataSourceResponseData = workDetailsPageDataSourceResponse.getData(applicationDataResponse);
                return new WorkDetailsFormTransformer(workDetailsPageDataSourceResponseData, dynamicBucket).buildWidgetData(applicationDataResponse);
            case "OFFER_SCREEN":
                PageDataSource<OfferScreenPageDataSourceResponse> offerScreenDataSource = new OfferScreenDataSource();
                OfferScreenPageDataSourceResponse offerScreenPageDataSourceResponse = offerScreenDataSource.getData(applicationDataResponse);
                return new OfferScreenFormTransformer().buildWidgetData(offerScreenPageDataSourceResponse);
            case "AADHAR_FORM":
                PageDataSource<AadharFormPageDataSourceResponse> aadharFormPageDataSource = new AadharFormPageDataSource();
                AadharFormPageDataSourceResponse aadharFormPageDataSourceResponse = aadharFormPageDataSource.getData(applicationDataResponse);
                return new AadharFormTransformer().buildWidgetData(aadharFormPageDataSourceResponse);
            case "OTP_VERIFICATION_SCREEN":
                PageDataSource<OtpVerificationPageDataSourceResponse> otpVerificationPageDataSource = new OtpVerificationPageDataSource();
                OtpVerificationPageDataSourceResponse otpVerificationPageDataSourceResponse = otpVerificationPageDataSource.getData(applicationDataResponse);
                return new OtpVerificationFormTransformer().buildWidgetData(otpVerificationPageDataSourceResponse);
            case "BANK_DETAILS":
                PageDataSource<BankDetailsPageDataSourceResponse> bankDetailsPageDataSource = new BankDetailsPageDataSource();
                BankDetailsPageDataSourceResponse bankDetailsPageDataSourceResponse = bankDetailsPageDataSource.getData(applicationDataResponse);
                return new BankDetailsFormTransformer().buildWidgetData(bankDetailsPageDataSourceResponse);
            case "REPAYMENT_MODES":
                PageDataSource<RepaymentModesPageDataSourceResponse> repaymentModesDataSource = new RepaymentModesDataSource();
                RepaymentModesPageDataSourceResponse repaymentModesPageDataSourceResponse = repaymentModesDataSource.getData(applicationDataResponse);
                return new RepaymentModesFormTransformer().buildWidgetData(repaymentModesPageDataSourceResponse);
            case "KFS_OTP":
                PageDataSource<KFSOtpPageDataSourceResponse> kfsOtpPageDataSource = new KFSOtpPageDataSource();
                KFSOtpPageDataSourceResponse kfsOtpPageDataSourceResponse = kfsOtpPageDataSource.getData(applicationDataResponse);
                return new KFSOtpFormTransformer().buildWidgetData(kfsOtpPageDataSourceResponse);
            case "ETB_OTP_FORM":
                PageDataSource<ETBGenerateOtpResponse> etbGenerateOtp = new ETBGenerateOtp();
                ETBGenerateOtpResponse etbGenerateOtpResponse = etbGenerateOtp.getData(applicationDataResponse);
                return new ETBFormTransformer().buildWidgetData(etbGenerateOtpResponse);
            case "ETB_VERIFY_OTP_FORM":
                PageDataSource<ETBVerifyOTPResponse> etbVerifyOtp = new ETBVerifyOtp();
                ETBVerifyOTPResponse etbVerifyOTPResponse = etbVerifyOtp.getData(applicationDataResponse);
                return new ETBFormTransformer().buildVerifyOtpWidgetData(etbVerifyOTPResponse);
            case "SANDBOX_OFFER_DETAILS_FORM":
                PageDataSource<SandboxOfferScreenResponse> sandboxOfferScreenDataSource = new SandboxOfferScreenDataSource();
                SandboxOfferScreenResponse sandboxOfferScreenPageDataSourceResponse = sandboxOfferScreenDataSource.getData(applicationDataResponse);
                return sandboxOfferScreenTransformer.buildWidgetData(sandboxOfferScreenPageDataSourceResponse);
            case "REVIEW_PAGE_1_FORM":
                PageDataSource<ReviewUserDataSourceResponse> reviewDataSource = new InitialUserReviewDataSource();
                ReviewUserDataSourceResponse reviewUserDataSourceResponse = reviewDataSource.getData(applicationDataResponse);
                return new ReviewPage1FormTransformer(reviewUserDataSourceResponse, initialUserDatFormFieldsMap, dynamicBucket).buildWidgetData(applicationDataResponse);
            case "REVIEW_PAGE_2_FORM":
                PageDataSource<ReviewUserDataSourceResponse> reviewDataSource2 = new InitialUserReviewDataSource();
                ReviewUserDataSourceResponse reviewUserDataSourceResponse2 = reviewDataSource2.getData(applicationDataResponse);
                return new ReviewPage2FormTransformer(reviewUserDataSourceResponse2, initialUserDataFormMapPage2, dynamicBucket).buildWidgetData(applicationDataResponse);
            default:
                logger.error("Unknown Widget Type {}", formType);
        }

        return null;
    }

}
