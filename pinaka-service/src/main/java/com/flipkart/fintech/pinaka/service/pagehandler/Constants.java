package com.flipkart.fintech.pinaka.service.pagehandler;

import com.flipkart.rome.datatypes.request.fintech.calm.enums.TenureUnit;

public class Constants {

    public static final int BANK_OFFER_STEP_SIZE = 1000;

    public static final int DEFAULT_PRINCIPAL_AMT = 1000;
    public static final TenureUnit TENURE_UNIT= TenureUnit.MONTH;
    public static final String EMI_DATE_PATTERN  = "dd MMM yyyy";
    public static final int EMI_CALCULATOR_AMT=-1000;
    public static final String UPDATE_TEXT = "Last updated: ";

    public class ApplicationStatus
    {
        public static final String LOAN_ID_TEXT = "Loan application number: ";
    }
    private Constants() {
    }

    public class KFSDetail
    {
        public static final String AGGREEMENT_TEXT = "Date and Place of Execution of the Agreement";
        public static final String PURPOSE_OF_LOAN = "Type of Loan";
        public static final String PERSONAL_LOAN = "Personal Loan";
        public static final String BANK_DETAILS="Bank's Details: Name and Registered Address\n";
        public static final String BANK_DETAILS_VALUE = "IDFC FIRST Bank Ltd. KRM Tower, 7th Floor, No. 1, Harrington Road, Chetpet, Chennai - 600031";
        public static final String SANC_AMT="Sanctioned Limit (in Rupees)";
        public static final String FAC_AMT ="Facility Amount (in Rupees)";
        public static final String LOAN_AMT = "Loan Amount (in Rupees)";
        public static final String INTEREST_TEXT = "Total interest charge during the entire tenure of the loan";
        public static final String PROC_FEE = "Processing fees, if any (in Rupees)";
        public static final String PRE_EMI ="Pre EMI Charges";
        public static final String NET_DIS ="Net disbursed amount (in Rupees)";
        public static final String TOTAL_AMT = "Total amount to be paid by borrower";
        public static final String INTEREST_T = "Applicable Rate of Interest";
        public static final String ANNUAL_RATE = "Annual Percentage Rate - Effective annulaized interest rate (in percentage)(computed on net disbursed amount using IRR approach and reducing method)";
        public static final String TENOR_TEXT = "Tenor of Loan (in months/days)";
        public static final String REPAYMENT_FREQ = "Repayment frequency of the borrower";
        public static final String NUM_INSTALLMENT = "Number of instalments of repayment";
        public static final String MONTHLY_EMI = "Amount of each instalments of repayment (in Rupees)";
        public static final String BOUNCE_CHARGE = "Bounce Charge";
        public static final String LATE_PAYMENT_CHARGE="Late Payment Charge";
        public static final String FORECLOSURE_CHARGE = "Foreclosure Charge";
        public static final String OVERDUE_CHARGE = "Overdue Charges";
        public static final String ACTIVATION_FEE = "Activation Fee (in Rupees)";
        public static final String COOLOFF_PERIOD = "Cooling off/look-up period during which borrower shall not be charged any penalty on prepayment of loan";
        public static final String LSP_DETAILS = "Details of LSP acting as recovery agent and authorized to approach the borrower";
        public static final String IDFC_NODAL = "Name, designation, address and phone number of nodal grievance redressal officer (IDFC BANK)";
        public static final String SCAPIC_NODAL = "Name, designation, address and phone number of nodal grievance redressal officer (SCAPIC)";
        public static final String SCAPIC_NODAL_OFFICER = "Mr. Bharath Pusala, Scapic Innovations Private Limited Ground Floor,Shilpitha Tech Park, Bellandur,Bengaluru, Karnataka 560103,Email Id: <EMAIL>, Contact : +91 **********";

        public static final String PRIVACY_LINK = "Privacy Policy: https://www.idfcfirstbank.com/privacy-policy Recovery Agencies: https://idfcfr.in/yz85n6";
        public static final String SCAPIC_PRIVACY_LINK = "Privacy Policy: /https://www.flipkart.com/pages/scapic-privacy-policy";

        public static final String IDFC_PNO = "Mr. Vipul Raj, PNO, IDFC FIRST Bank Ltd. Building no. 09, 17th floor, Gigaplex Raheja Mindspace IT-5, Airoli, Navi Mumbai – 400708, Maharashtra, India. Email Id: <EMAIL> / <EMAIL>, 022-******** /1800 209 9771";
        public static final String DISCLOSURE = "Disclosure";
        public static final String RECOVERY_AGENT = "Recovery Agent: IDFC FIRST BANK Limited";

    }
    public class RepaymentsModes
    {
        public static final String DEBIT_CARD = "Debit Card";
        public static final String NET_BANKING = "Net Banking";
        public static final String DEBIT_CARD_IMG_URL = "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/26/09/2023/9091673f-35d9-4bba-8026-4c0b5c4804ca.png?q={@quality}";
        public static final String NET_BANKING_IMG_URL = "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/26/09/2023/1699482d-01d7-415f-ad1d-a5121b5ae4da.png?q={@quality}";
        public static final String IMAGE_VALUE_TYPE = "ImageValue";
        public static final int imageHt = 32;
    }
    public class KFSDetailV2{
        public static final String LOAN_PROPOSAL_ACCOUNT_NO = "Loan proposal/ account No.";
        public static final String TYPE_OF_LOAN = "Type of Loan";
        public static final String SANCTIONED_LOAN_AMOUNT = "Sanctioned Loan amount (in Rupees)";
        public static final String LOAN_AMOUNT = "Loan amount (in Rupees)";
        public static final String NET_DISBURSED_AMOUNT = "Net disbursed amount (in Rupees)";
        public static final String DISBURSAL_SCHEDULE = "Disbursal schedule \n(i) Disbursement in stages or 100% upfront.";
        public static final String DISBURSEMENT_STAGES = "Disbursement in stages or 100% upfront";
        public static final String LOAN_TERM = "Loan term (year/months/days)";
        public static final String INSTALMENT_TYPE = "Type of installments";
        public static final String INSTALLMENT_DETAILS = "Installment details";

        public static final String NUMBER_OF_EPIS = "Number of EPIs";
        public static final String EPI_AMOUNT = "EPI (₹)";
        public static final String DATECOMMENCEMENT = "Commencement of repayment, post sanction";
        public static final String INTEREST_RATE = "Interest rate (%) and type (fixed or floating or hybrid)";
        public static final String ADDITIONAL_INFO_FLOATING_RATE = "Additional Information in case of Floating rate of interest";
        public static final String REFERENCE_BENCHMARK = "Reference Benchmark";
        public static final String BENCHMARK_RATE = "Benchmark rate (%) (B)";
        public static final String SPREAD = "Spread (%) (S)";
        public static final String FINAL_RATE = "Final rate (%) R = (B) + (S)";
        public static final String RESET_PERIODICITY_B = "Reset periodicity (Months) B";
        public static final String RESET_PERIODICITY_S = "Reset periodicity (Months) S";
        public static final String IMPACT_CHANGE_R_EPI = "Impact of change in the reference benchmark (for 25 bps change in ‘R’, change in EPI (₹))";
        public static final String IMPACT_CHANGE_R_NO_OF_EPIS = "Impact of change in the reference benchmark (for 25 bps change in ‘R’, change in No. of EPIs)";

        public static final String FEECHARGES = "Fee/ Charges ";
        public static final String PAYABLE  = "Payable to the RE (A) / Payable to a third party through RE (B)";
        public static final String PROCESSING_FEES = "Processing fees";
        public static final String INSURANCE_CHARGES = "Insurance charges";
        public static final String VALUATION_FEES = "Valuation fees";
        public static final String OTHER_FEES = "Any other (please specify)";
        public static final String ANNUAL_PERCENTAGE_RATE = "Annual Percentage Rate (APR) (%)";
        public static final String CONTINGENTCHARGES = "Details of Contingent Charges (in ₹ or %, as applicable)";
        public static final String PENAL_CHARGES = "Penal charges, if any, in case of delayed payment";
        public static final String BOUNCE_CHARGES = "Bounce charges";
        public static final String FORECLOSURE_CHARGES = "Foreclosure charges, if applicable";
        public static final String SWITCHING_LOAN_CHARGES = "Charges for switching of loans from floating to fixed rate and vice versa";
        public static final String USAGE_FEE = "Usage fee";

        public static final String RECOVERY_AGENT_CLAUSE = "Clause of Loan agreement relating to engagement of recovery agents";
        public static final String GRIEVANCE_REMEDY_CLAUSE = "Clause of Loan agreement which details grievance redressal mechanism";
        public static final String GRIEVANCE_CONTACT_PHONE = "Phone number and email id of the nodal grievance redressal officer";
        public static final String LOAN_TRANSFER_POSSIBILITY = "Whether the loan is, or in future maybe, subject to transfer to other REs or securitisation (Yes/ No)";
        public static final String COLLABORATIVE_LENDING_ARRANGEMENTS = "In case of lending under collaborative lending arrangements (e.g., co-lending/ outsourcing),following additional details may be furnished:";
        public static final String DIGITAL_LOANS = "In case of digital loans, following specific disclosures may be furnished:";
        public static final String ORIGINATING_RE = "Name of the originating RE, along with its funding proportion";
        public static final String PARTNER_RE = "Name of the partner RE along with its proportion of funding";
        public static final String BLENDED_RATE = "Blended rate of interest";
        public static final String COOLING_OFF_PERIOD = "Cooling off/look-up period, during which borrower shall not be charged any penalty on prepayment of loan";
        public static final String LSP_RECOVERY_AGENT = "Details of LSP acting as recovery agent and authorized to approach the borrower";

        public static final String REPAYMENT_SCHEDULE = "Repayment Schedule";
        public static final String PART1 = "Part 1 (Interest rate and fees/charges): ";

        public static final String PART2 = "Part 2 (Other qualitative information): ";
        public static final String PART3 = "Part 3 - Repayment Schedule: ";
        public static final String EMINO =  "EMI No.";
        public static final String DUEDATE = "Due Date";
        public static final String AMT = "Amount";


    }
}
