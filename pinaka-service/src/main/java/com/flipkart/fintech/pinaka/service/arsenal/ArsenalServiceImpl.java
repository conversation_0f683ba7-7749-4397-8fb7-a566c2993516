package com.flipkart.fintech.pinaka.service.arsenal;

import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricRegistry;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.Lender;
import com.flipkart.fintech.pinaka.api.enums.MerchantStatus;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScores;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.arsenal.selector.BorrowerSelector;
import com.flipkart.fintech.pinaka.service.core.v7.BorrowerServiceV7;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.flipkart.fintech.pinaka.service.data.model.MerchantEntity;
import com.flipkart.fintech.pinaka.service.data.model.WhitelistEntity;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.google.common.collect.Lists;

import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static com.flipkart.fintech.pinaka.api.enums.Lender.FIBE;
import static com.flipkart.fintech.pinaka.api.enums.Lender.MONEYVIEWOPENMKT;

public class ArsenalServiceImpl implements ArsenalService {
    private final BorrowerServiceV7 borrowerServiceV7;
    private final BorrowerSelector borrowerSelector;
    private final Meter ineligibleBinScoreMeter;
    private final UserProfileScores userProfileScores;

    @Inject
    public ArsenalServiceImpl(BorrowerServiceV7 borrowerServiceV7, BorrowerSelector borrowerSelector,
                              UserProfileScores userProfileScores) {
        this.borrowerServiceV7 = borrowerServiceV7;
        this.borrowerSelector = borrowerSelector;
        this.ineligibleBinScoreMeter = PinakaMetricRegistry.getMetricRegistry().meter(
                MetricRegistry.name(ArsenalServiceImpl.class, "ineligible.bin.score.meter"));
        this.userProfileScores = userProfileScores;

    }

    @NotNull
    private static List<BorrowerEntity> filterOutLender(List<BorrowerEntity> activeBorrowers,
                                                        com.flipkart.fintech.pinaka.api.enums.Lender lender) {
        return activeBorrowers.stream()
                .filter(e -> !lender.toString().equals(e.getWhitelist().getLender()))
                .collect(Collectors.toList());
    }

    @Override
    public Optional<BorrowerEntity> getBorrower(MerchantUser merchantUser, ProductType productType, String requestId) throws PinakaException {
        ArrayList<ProductType> productTypes = Lists.newArrayList(productType);
        List<BorrowerEntity> activeBorrowers = borrowerServiceV7.getActiveBorrowers(merchantUser,
                productTypes);
        if (!LenderHelper.isIdfcSupported()) {
            activeBorrowers = filterOutLender(activeBorrowers, com.flipkart.fintech.pinaka.api.enums.Lender.IDFC);
        }
        Optional<BorrowerEntity> borrower = borrowerSelector.select(merchantUser, activeBorrowers);
        if (borrower.isPresent()) {
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(ArsenalServiceImpl.class, "lender", borrower.get().getWhitelist().getLender()));
            return borrower;
        }
        Double bin = userProfileScores.getUserBin(merchantUser, requestId);
        Optional<Lender> lender = getLender(bin, merchantUser);
        if (!lender.isPresent()) {
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(ArsenalServiceImpl.class, "lender", "empty"));
            return Optional.empty();
        }
        BorrowerEntity defaultBorrower = getDefaultBorrower(merchantUser);
        PinakaMetricRegistry.getMetricRegistry().meter(
                MetricRegistry.name(ArsenalServiceImpl.class, defaultBorrower.getWhitelist().getLender()));
        PinakaMetricRegistry.getMetricRegistry().meter(
                MetricRegistry.name(ArsenalServiceImpl.class, "lender", "default"));
        return Optional.of(defaultBorrower);
    }

    private BorrowerEntity getDefaultBorrower(MerchantUser merchantUser) {
        switch (merchantUser.getMerchantKey()) {
            case MerchantUser.MerchantKeys.SHOPSY_MERCHANT_KEY:
                return getDefaultBorrowerForShopsy(merchantUser);
            case MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY:
                return getDefaultBorrowerForMyntra(merchantUser);
            default:
                return getDefaultBorrowerForFlipkart(merchantUser);
        }
    }

    private BorrowerEntity getDefaultBorrowerForFlipkart(MerchantUser merchantUser) {
        MerchantEntity axisMerchant = new MerchantEntity();
        axisMerchant.setMerchantName("marketplace flipkart");
        axisMerchant.setMerchantKey(merchantUser.getMerchantKey());
        axisMerchant.setStatus(MerchantStatus.ACTIVE);
        WhitelistEntity whitelistEntity = new WhitelistEntity();
        whitelistEntity.setEnabled(true);
        whitelistEntity.setLender("AXIS");
        whitelistEntity.setMerchant(axisMerchant);
        whitelistEntity.setProductType(ProductType.PERSONAL_LOAN);
        BorrowerEntity borrower = new BorrowerEntity();
        borrower.setWhitelist(whitelistEntity);
        borrower.setExternalId(merchantUser.getMerchantUserId());
        return borrower;
    }

    private BorrowerEntity getDefaultBorrowerForMyntra(MerchantUser merchantUser) {
        MerchantEntity axisMerchant = new MerchantEntity();
        axisMerchant.setMerchantName("marketplace myntra");
        axisMerchant.setMerchantKey(merchantUser.getMerchantKey());
        axisMerchant.setStatus(MerchantStatus.ACTIVE);
        WhitelistEntity whitelistEntity = new WhitelistEntity();
        whitelistEntity.setEnabled(true);
        whitelistEntity.setLender("AXIS");
        whitelistEntity.setMerchant(axisMerchant);
        whitelistEntity.setProductType(ProductType.PERSONAL_LOAN);
        BorrowerEntity borrower = new BorrowerEntity();
        borrower.setWhitelist(whitelistEntity);
        borrower.setExternalId(merchantUser.getMerchantUserId());
        return borrower;
    }

    private BorrowerEntity getDefaultBorrowerForShopsy(MerchantUser merchantUser) {
        MerchantEntity mvMerchant = new MerchantEntity();
        mvMerchant.setMerchantName("marketplace shopsy");
        mvMerchant.setMerchantKey(merchantUser.getMerchantKey());
        mvMerchant.setStatus(MerchantStatus.ACTIVE);
        WhitelistEntity whitelistEntity = new WhitelistEntity();
        whitelistEntity.setEnabled(true);
        whitelistEntity.setLender("MONEYVIEWOPENMKT");
        whitelistEntity.setMerchant(mvMerchant);
        whitelistEntity.setProductType(ProductType.PERSONAL_LOAN);
        BorrowerEntity borrower = new BorrowerEntity();
        borrower.setWhitelist(whitelistEntity);
        borrower.setMetadata("{\"offer_id\" : \"\"}");
        borrower.setExternalId(merchantUser.getMerchantUserId());
        return borrower;
    }

    private Optional<Lender> getLender(Double bin, MerchantUser merchantUser) {
        if (merchantUser.getMerchantKey().equals(MerchantUser.MerchantKeys.SHOPSY_MERCHANT_KEY)) {
            return Optional.of(Lender.SMALL_LENDERS_COMMON);
        }
        if (Objects.isNull(bin) || bin <= 7) {
            ineligibleBinScoreMeter.mark();
            return Optional.empty();
        }
        return Optional.of(Lender.AXIS);

    }


    private String getBucket(String accountId) {
        if (accountId.hashCode() % 4 == 0) {
            return MONEYVIEWOPENMKT.name();
        }
        return FIBE.name();
    }


}
