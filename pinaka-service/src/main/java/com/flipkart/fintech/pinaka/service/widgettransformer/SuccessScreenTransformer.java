package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import java.util.Map;

@CustomLog
public class SuccessScreenTransformer{
    private final Map<String, String> successScreensMap;

    @Inject
    public SuccessScreenTransformer(@Named("lenderSuccessScreens") Map<String, String> successScreensMap) {
        this.successScreensMap = successScreensMap;
    }

    public AnnouncementV2WidgetData buildWidgetData(
            ApplicationDataResponse applicationDataResponse) throws PinakaClientException {
        AnnouncementV2WidgetData announcementV2WidgetData;
        try {
            String lender = (String) applicationDataResponse.getApplicationData().get("financial_provider");
            announcementV2WidgetData = ObjectMapperUtil.get().readValue(successScreensMap.get(lender), AnnouncementV2WidgetData.class);
            return announcementV2WidgetData;
        } catch (Exception e) {
            log.error("AnnouncementV2WidgetData build offerRejectionWidget Data failed with error : {}",
                    e.getMessage());
            throw new PinakaClientException(e);
        }
    }

}
