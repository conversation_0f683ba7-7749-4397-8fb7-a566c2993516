package com.flipkart.fintech.pinaka.service.widgettransformer.lead;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.LoadingWidgetDataV0;

import java.util.HashMap;
import java.util.Map;


public class LoadingScreenTransformer {

    private static final String GENERIC_LOADER;

    static {
        GENERIC_LOADER = TransformerUtils.readFileasString("template/lead/Loader.json");
    }

    public LoadingWidgetDataV0 buildWidgetGroupData(ApplicationDataResponse applicationDataResponse) throws PinakaException, JsonProcessingException {

        Map<String,Object> queryParams = new HashMap<>();
        queryParams.put("applicationId", applicationDataResponse.getApplicationId());
        queryParams.put("applicationState", applicationDataResponse.getApplicationState());
        LoadingWidgetDataV0 loadingWidgetData = ObjectMapperUtil.get().readValue(GENERIC_LOADER, LoadingWidgetDataV0.class);
        queryParams.putAll(loadingWidgetData.getPollingContext().getAction().getParams());
        loadingWidgetData.getPollingContext().getAction().setParams(queryParams);

        return loadingWidgetData;
    }
}
