package com.flipkart.fintech.pinaka.service.adaptor.v6.formFields;

import com.flipkart.fintech.pinaka.api.response.v6.FormFieldType;
import lombok.Getter;

@Getter
public enum FormFieldContent {
    HEADER_IMAGE_VALUE(FormFieldType.IMAGE, "headerImage", "HeaderImage", true, false),
    PAN_TEXT_BOX(FormFieldType.TEXT, "pan", "PAN Number", true, false),
    DOB_TYPE(FormFieldType.DATE, "dob", "Date of Birth", true, false),
    GENDER(FormFieldType.MULTI_DROPDOWN,"gender", "Gender", true, false ),

    CONSENT_PAN_DETAILS(FormFieldType.CHECKBOX, "consentPan", "Consent", true, false),
    HOUSE_NUMBER(FormFieldType.TEXT, "houseNumber", "House no.,Building name*", true, false),
    AREA(FormFieldType.TEXT, "area", "Road name, Area, Colony*", true, false),
    CITY(FormFieldType.TEXT, "city", "City*", true, false),
    STATE(FormFieldType.TEXT, "state", "State*", true, false),
    PINCODE(FormFieldType.TEXT, "pincode", "Pincode*", true, false),
    SUBMIT_DETAILS(FormFieldType.SUBMIT_BUTTON, "submitDetails", "SubmitDetails", true, false),
    NOTE(FormFieldType.NOTE, "note", "Note", true, false),
    EMPLOYMENT_TYPE(FormFieldType.MULTI_DROPDOWN, "employmentType", "Employment Type", true, false),
    ANNUAL_TURN_OVER(FormFieldType.NUMBER, "annualTurnOver", "Annual Turnover", true, false),
    MONTHLY_INCOME(FormFieldType.NUMBER, "monthlyIncome", "Monthly Net Income", true, false),
    COMPANY(FormFieldType.AUTO_SUGGEST_BOX, "company", "Company", true, false),
    INDUSTRY_TYPE(FormFieldType.AUTO_SUGGEST_BOX, "industryType", "IndustryType", true, false),
    LOAN_AMOUNT(FormFieldType.NUMBER, "loanAmount", "LoanAmount", true, false),
    LOAN_AMOUNT_RANGE(FormFieldType.RANGE, "loanAmountRange", "LoanAmountRange", true, false),
    LOAN_EMI(FormFieldType.EMI_CALM, "loanEmi", "LoanEmi", true, false);

  private FormFieldType formFieldType;
  private String name;
  private String label;

  private boolean mandatory;

  private boolean disabled;

   private FormFieldContent(FormFieldType formFieldType, String name, String label, boolean mandatory, boolean disabled) {
    this.formFieldType= formFieldType;
    this.name = name;
    this.label = label;
    this.mandatory = mandatory;
    this.disabled =disabled;
    }
}
