package com.flipkart.fintech.pinaka.service.core.v6.mapper.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.response.v6.UserOfferDataResponse;
import com.flipkart.fintech.pinaka.service.core.v6.mapper.LenderOfferMapper;
import com.flipkart.fintech.pinaka.service.exception.InvalidOfferDataException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import lombok.CustomLog;

import javax.inject.Inject;

@CustomLog
public class SandboxV2OfferMapper implements LenderOfferMapper {

    private final ObjectMapper objectMapper;

    @Inject
    public SandboxV2OfferMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public UserOfferDataResponse mapToUserOfferData(ApplicationDataResponse applicationDataResponse) throws InvalidOfferDataException {
        try {
            if (applicationDataResponse == null || applicationDataResponse.getApplicationData() == null) {
                log.error("Null application data received for Sandbox V2 offer");
                throw new InvalidOfferDataException("Null application data received ");
            }

            JsonNode appDataNode = objectMapper.valueToTree(applicationDataResponse.getApplicationData());
            JsonNode generatedOfferNode = appDataNode.path("getOffer").path("generatedOffer").path("offer");

            if (generatedOfferNode.isMissingNode()) {
                log.error("Missing offer data in Sandbox V2 response");
                throw new InvalidOfferDataException("Missing offer data in Sandbox V2 response" + applicationDataResponse.getApplicationData());
            }

            int maxSanctionedAmount = generatedOfferNode.path("maxSanctionedAmount").asInt();
            JsonNode offerTableNode = generatedOfferNode.path("offerTable");

            for (JsonNode offerNode : offerTableNode) {
                int sanctionedAmountMax = offerNode.path("sanctionedAmount").path("max").asInt();

                if (sanctionedAmountMax == maxSanctionedAmount) {
                    double roi = offerNode.path("roi").asDouble();
                    int tenure = offerNode.path("tenure").path("value").asInt();
                    return UserOfferDataResponse.builder()
                            .loanAmount(String.valueOf(sanctionedAmountMax))
                            .roi(String.valueOf(roi))
                            .tenure(String.valueOf(tenure))
                            .build();
                }
            }
            return null;

        } catch (Exception e) {
            log.error("Error processing Sandbox V2 offer data", e);
            throw new InvalidOfferDataException("Error processing Sandbox V2 offer data");
        }
    }
}