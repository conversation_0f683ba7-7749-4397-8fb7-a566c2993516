package com.flipkart.fintech.pinaka.service.widgettransformer.creditLine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.submitbuttonwidget.SubmitButtonWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardWidgetData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.google.inject.Inject;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import org.apache.http.NameValuePair;

import java.util.List;
import java.util.Optional;

public class CreditLineBuildFailedAnnouncementTransformer implements SubmitButtonWidgetTransformer {

    private static String validationFailedSubmitButtonJson;
    private static String validationFailedCardWidgetJson;


    private final ConfigUtils configUtils;

    static {
        validationFailedSubmitButtonJson = TransformerUtils.readFileasString("template/ltfs/CreditLineSubmitButtonWidget.json");
        validationFailedCardWidgetJson = TransformerUtils.readFileasString("template/ltfs/CreditLineFailedAnnouncementCard.json");
    }

    @Inject
    public CreditLineBuildFailedAnnouncementTransformer(ConfigUtils configUtils) {
        this.configUtils = configUtils;
    }

    @Override
    public SubmitButtonWidgetData buildSubmitButtonWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaClientException, JsonProcessingException, PinakaException {
        SubmitButtonWidgetData submitButtonWidgetData = ObjectMapperUtil.get().readValue(validationFailedSubmitButtonJson, SubmitButtonWidgetData.class);
        updateSubmitButton(applicationDataResponse,submitButtonWidgetData);
        return submitButtonWidgetData;
    }

    private void updateSubmitButton(ApplicationDataResponse applicationDataResponse, SubmitButtonWidgetData submitButtonWidgetData) {
        Action action = submitButtonWidgetData.getSubmitButton().getButton().getAction();

        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        action.setParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        action.setEncryption(encryption.orElse(null));

        submitButtonWidgetData.getSubmitButton().getButton().setAction(action);
    }

    public CardWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse) throws JsonProcessingException {
        return (CardWidgetData) ObjectMapperUtil.get().readValue(validationFailedCardWidgetJson, CardWidgetData.class);
    }

}
