package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pandora.api.model.pl.response.AutoDisbursalResponse;
import com.flipkart.fintech.pinaka.service.response.RepaymentScheduleResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

public class RepaymentScheduleDataSource implements PageDataSource<RepaymentScheduleResponse>{

    @Override
    public RepaymentScheduleResponse getData(ApplicationDataResponse applicationDataResponse) {
        AutoDisbursalResponse autoDisbursalResponse = ObjectMapperUtil.get()
                .convertValue(applicationDataResponse.getApplicationData().get("authAutoDisbursal"),
                        AutoDisbursalResponse.class);
        RepaymentScheduleResponse repaymentScheduleResponse = new RepaymentScheduleResponse();
        repaymentScheduleResponse.setAutoDisbursalResponse(autoDisbursalResponse);
        return repaymentScheduleResponse;
    }
}
