package com.flipkart.fintech.pinaka.service.core;

import com.flipkart.fintech.pinaka.api.request.WhitelistCreateRequest;
import com.flipkart.fintech.pinaka.api.response.WhitelistCreateResponse;
import com.flipkart.fintech.pinaka.service.data.model.WhitelistEntity;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;

import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 07/11/17.
 */
public interface WhitelistService {

    /**
     * Create a whitelist against which borrowers are on-boarded
     * @param merchantId
     * @param request
     * @return
     * @throws PinakaException
     */
    WhitelistCreateResponse create(String merchantId, WhitelistCreateRequest request) throws PinakaException;

    /**
     *
     * @param whiteListId
     * @return Return a whitelist
     */
    Optional<WhitelistEntity> getWhitelistById(Long whiteListId);

    /**
     *
     * @param whitelistName
     * @param product
     * @return Return a whitelist
     */
    WhitelistEntity getWhitelistByNameAndProduct(String whitelistName, String product);

    void disable(Long whitelistId) throws PinakaException;
}
