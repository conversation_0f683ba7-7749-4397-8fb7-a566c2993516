package com.flipkart.fintech.pinaka.service.datacryptography;

import com.flipkart.fintech.pinaka.service.utils.FormUtils.PIIData;
import com.flipkart.fintech.security.aes.AESService;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import lombok.NonNull;
import org.apache.commons.io.IOUtils;

public class FormDataDecryption implements DataDecryption {

    private static final String ENCRYPT_KEY = "cGlpRW5jcnlwdGlvbktleQ==";
    private static final PIIData piiData;

    static {
        try( InputStream inputStream = com.flipkart.fintech.pinaka.service.utils.EncryptionUtil.class.getClassLoader().getResourceAsStream("PIIData/PIIData.json");) {
            String piiDataJson = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            piiData = ObjectMapperUtil.get().readValue(piiDataJson, PIIData.class);
        } catch(IOException e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, Object> decryptFormData(Map<String, Object> applicationData) {
        for(String formKey : applicationData.keySet()) {
            if(piiData.containsFormKey(formKey)) {
                Map<String,Object>formKeyData = (Map<String, Object>) applicationData.get(formKey);
                for(String field:formKeyData.keySet()) {
                    if(piiData.containsField(formKey,field)) {
                        String value = (String) formKeyData.get(field);
                        String decryptedPlainTextString = getDecryptedPlainTextString(value);
                        formKeyData.put(field, decryptedPlainTextString);
                    }
                }
                applicationData.put(formKey,formKeyData);
            }
        }
        return applicationData;
    }

    @NonNull
    public String getDecryptedPlainTextString(@NonNull String value) {
        byte[] plaintextBytes = AESService.decrypt(ENCRYPT_KEY, Base64.getDecoder().decode(value.getBytes(StandardCharsets.UTF_8)));
        return new String(plaintextBytes, StandardCharsets.UTF_8);
    }

}
