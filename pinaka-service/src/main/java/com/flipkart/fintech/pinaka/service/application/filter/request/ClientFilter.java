package com.flipkart.fintech.pinaka.service.application.filter.request;

import com.flipkart.fintech.pinaka.service.application.Constants;
import com.flipkart.fintech.pinaka.service.application.filter.FilterPriority;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Priority;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by su<PERSON><PERSON>kumar.r on 30/08/17.
 */
@Priority(FilterPriority.CLIENT_FILTER)
public class ClientFilter implements ContainerRequestFilter {

    private static final Map<String,List<String>> merchantClientMappingWhitelist = new HashMap<>();

    static {
        List<String> mpFlipkartClients = new ArrayList<>();
        mpFlipkartClients.add("ardour");
        mpFlipkartClients.add("robinhood");
        mpFlipkartClients.add("pinaka");
        mpFlipkartClients.add("flux");
        mpFlipkartClients.add("rome");
        mpFlipkartClients.add("AAPI");
        mpFlipkartClients.add("fintech-stratum");
        mpFlipkartClients.add("fkpay");
        mpFlipkartClients.add("pandora-client");
        mpFlipkartClients.add("onboarding");
        mpFlipkartClients.add("khaata");
        mpFlipkartClients.add("aapi");
        mpFlipkartClients.add("ffb");
        mpFlipkartClients.add("shylock");

        List<String> testMerchantClients = new ArrayList<>();
        testMerchantClients.add("test_client");

        merchantClientMappingWhitelist.put("mp_flipkart",mpFlipkartClients);
        merchantClientMappingWhitelist.put("test_merchant",testMerchantClients);
    }

    @Override
    public void filter(ContainerRequestContext containerRequestContext) throws IOException {

        if (containerRequestContext.getUriInfo().getPath().contains("swagger")) {
            return;
        }

        String clientId = containerRequestContext.getHeaderString(Constants.X_CLIENT_ID);
        String merchantId = containerRequestContext.getHeaderString(Constants.X_MERCHANT_ID);

        List<String> whitelistedClients = merchantClientMappingWhitelist.get(merchantId);

        if(StringUtils.isEmpty(clientId) || whitelistedClients.isEmpty() || !whitelistedClients.contains(clientId))
        {
            throw new WebApplicationException("No client identifier found for " + merchantId,
                    new IllegalAccessException("No client identifier found for "+merchantId),
                    Response.Status.FORBIDDEN);
        }

    }
}
