package com.flipkart.fintech.pinaka.service.utils.v6;

import com.flipkart.fintech.pinaka.api.response.v6.FormField;
import com.flipkart.fintech.pinaka.service.adaptor.v6.formFields.FormFieldContent;

public class FormUtils {

    public static FormField getFormField(FormFieldContent content) {
        FormField formField = new FormField();
        formField.setFormFieldType(content.getFormFieldType());
        formField.setName(content.getName());
        formField.setLabel(content.getLabel());
        formField.setMandatory(content.isMandatory());
        formField.setDisabled(content.isDisabled());

        return formField;
    }

}
