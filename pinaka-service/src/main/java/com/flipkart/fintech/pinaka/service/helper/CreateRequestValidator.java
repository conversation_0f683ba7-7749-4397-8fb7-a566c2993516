package com.flipkart.fintech.pinaka.service.helper;

import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.ApplicationCreateRequest;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 04/07/18.
 */
public class CreateRequestValidator implements ConstraintValidator<ValidateCreateRequest, ApplicationCreateRequest> {

    @Override
    public void initialize(ValidateCreateRequest validateCreateRequest) {

    }

    @Override
    public boolean isValid(ApplicationCreateRequest request,
                           ConstraintValidatorContext constraintValidatorContext) {
        return true;
    }
}
