package com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils;

import com.codahale.metrics.MetricRegistry;
import com.flipkart.fintech.pinaka.api.response.v6.PincodeDetailsResponse;
import com.flipkart.fintech.pinaka.library.entities.CAISHolderAddressDetails;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.rome.datatypes.response.common.enums.FontWeight;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.TextStyle;
import com.flipkart.rome.datatypes.response.fintech.calm.AutoSuggestFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.calm.MultiDropdownFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.calm.MultiDropdownValue;
import com.flipkart.rome.datatypes.response.fintech.insurtech.CardFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.insurtech.PriceTextBoxFormFieldValueV0;
import com.flipkart.rome.datatypes.response.fintech.insurtech.TextBoxFormFieldValueV0;
import com.flipkart.rome.datatypes.response.fintech.onboarding.*;
import com.flipkart.rome.datatypes.response.fintech.onboarding.enums.AutoCapitalize;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.DropdownType;
import lombok.AllArgsConstructor;
import lombok.CustomLog;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.*;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.*;
import static com.flipkart.rome.datatypes.response.fintech.supermoney.enums.DropdownType.PLAIN_TEXT;

@CustomLog
public class FormWidgetDataPrefillUtils {

    public static final String PINCODE_STRING = "pincode";
    public static final String SALARIED = "Salaried";

    private static final String PHONE_NUMBER_STRING = "phoneNumber";
    private static final String FULL_NAME_STRING = "fullName";
    public static final String PAN_NUMBER = "panNumber";
    public static final String DOB_STRING = "dob";
    public static final String GENDER_STRING = "gender";
    public static final String COMPANY_NAME_STRING = "companyName";
    public static final String INDUSTRY_NAME_STRING = "industryName";
    public static final String INDUSTRY_ID_STRING = "industryId";
    public static final String HOUSE_NUMBER_STRING = "houseNumber";
    public static final String AREA_STRING = "area";
    public static final String PINCODE_DETAILS_STRING = "pincodeDetails";
    public static final String MONTHLY_INCOME_STRING = "monthlyIncome";
    public static final String INCOME_SOURCE_STRING = "incomeSource";
    public static final String EMPLOYMENT_TYPE_STRING = "employmentType";
    public static final String EMAIL_STRING = "email";
    public static final String CITY = "city";
    public static final String STATE = "state";
    public static final String DATA = "data";

    public enum GroupedFormFields {
        FULL_NAME("fullName") {
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String prefilledValue = (String) userData.get(FULL_NAME_STRING);
                setupPrefillValueAndTrackingDataForGroupedFormField(formFieldValue, prefilledValue);
            }
        },
        PHONE_NUMBER("phoneNumber") {
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String prefilledValue = (String) userData.get(PHONE_NUMBER_STRING);
                setupPrefillValueAndTrackingDataForGroupedFormField(formFieldValue, prefilledValue);
            }
        },
        PAN("panNumber") {
            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String panNumber = (String) userData.get(PAN_NUMBER);
                if (StringUtils.isNotBlank(panNumber)) {
                    if (formFieldValue.getData() instanceof TextBoxFormFieldValueV0) {
                        TextBoxFormFieldValueV0 panNumberTextBoxFormFieldValueV0 = (TextBoxFormFieldValueV0) formFieldValue.getData();
                        panNumberTextBoxFormFieldValueV0.setAutoCapitalize(AutoCapitalize.characters);
                        panNumberTextBoxFormFieldValueV0.setValue(panNumber);
                        panNumberTextBoxFormFieldValueV0.setInitialValue(null);
                    } else {
                        setupPrefillValueAndTrackingDataForGroupedFormField(formFieldValue, panNumber);
                    }
                } else {
                    formFieldValue.getData().setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getData().getName(), ""));
                }
            }
        },
        DOB("dob") {
            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String dob = (String) userData.get(DOB_STRING);
                if (formFieldValue.getData() instanceof DateFormFieldValueV0) {
                    DateFormFieldValueV0 dateFormFieldValue = (DateFormFieldValueV0) formFieldValue.getData();
                    dateFormFieldValue.setMinValue(getYearsBackDateFromNow(60));
                    dateFormFieldValue.setMaxValue(getYearsBackDateFromNow(18));
                }
                if (StringUtils.isNotBlank(dob)) {
                    if (formFieldValue.getData() instanceof DateFormFieldValueV0) {
                        DateFormFieldValueV0 dateFormFieldValue = (DateFormFieldValueV0) formFieldValue.getData();
                        dateFormFieldValue.setValue(dob);
                        dateFormFieldValue.setInitialValue(null);
                    } else {
                        GroupedFormFields.setupPrefillValueAndTrackingDataForGroupedFormField(formFieldValue, dob);
                    }
                } else {
                    formFieldValue.getData().setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getData().getName(), ""));
                }
            }
        },
        GENDER("gender") {
            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String genderString = (String) userData.get(GENDER_STRING);
                if (StringUtils.isNotBlank(genderString)) {
                    if (formFieldValue.getData() instanceof DropdownFormFieldValueV0) {
                        DropdownFormFieldValueV0 genderDropDown = (DropdownFormFieldValueV0) formFieldValue.getData();
                        switch (genderString) {
                            case "2":
                            case "F": {
                                DropdownValue selectedOption = new DropdownValue();
                                selectedOption.setId("F");
                                selectedOption.setTitle("Female");
                                selectedOption.setType(PLAIN_TEXT);
                                genderDropDown.setSelectedOption(selectedOption);
                                break;
                            }
                            case "3":
                            case "O": {
                                DropdownValue selectedOption = new DropdownValue();
                                selectedOption.setId("O");
                                selectedOption.setTitle("Others");
                                selectedOption.setType(PLAIN_TEXT);
                                genderDropDown.setSelectedOption(selectedOption);
                                break;
                            }
                            default: {
                                DropdownValue selectedOption = new DropdownValue();
                                selectedOption.setId("M");
                                selectedOption.setTitle("Male");
                                selectedOption.setType(PLAIN_TEXT);
                                genderDropDown.setSelectedOption(selectedOption);
                                break;
                            }
                        }
                        genderDropDown.setTracking(WidgetTransformerUtils
                                .getTrackingMetadata(Objects.requireNonNull(
                                                formFieldValue.getData()).getName(),
                                        genderDropDown.getSelectedOption().getId()
                                )
                        );
                        genderDropDown.setInitialValue(null);
                    } else {
                        String id = "M" ;
                        if("Female".equals(genderString)){
                            id = "F";
                        } else if("Others".equals(genderString)){
                            id = "O";
                        }
                        CardFormFieldValue cardFormFieldValue = (CardFormFieldValue) formFieldValue.getData();
                        if (StringUtils.isNotBlank(genderString) && isDescriptionFieldWithValuePresent(cardFormFieldValue)) {
                            cardFormFieldValue.getCard().getDescription().getValue().setText(id);
                            cardFormFieldValue.getCard().getDescription().getValue().setTitle(id);
                            formFieldValue.getData().setInitialValue(null);
                        }
                    }
                }
                formFieldValue.getData().setTracking(WidgetTransformerUtils.getTrackingMetadata(Objects.requireNonNull(formFieldValue.getData()).getName(), ""));
            }
        },
        EMAIL("email") {
            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String email = (String) userData.get(EMAIL_STRING);
                setupPrefillValueAndTrackingDataForGroupedFormField(formFieldValue, email);
            }
        },
        EMPLOYMENT_TYPE("employmentType") {
            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String employmentType = (String) userData.get(EMPLOYMENT_TYPE_STRING);
                setupPrefillValueAndTrackingDataForGroupedFormField(formFieldValue, employmentType);
            }
        },
        ADDRESS("address") {

            @Data
            @AllArgsConstructor
            class Address {
                private String firstLineOfAddress;
                private String secondLineOfAddress;
                private String pincode;
                private String city;
                private String state;
            }

            @Data
            @AllArgsConstructor
            class AddressOptionsResult {
                private List<DropdownValue> dropdownOptions;
                private List<Map<String, Object>> initialOptionsData;
            }

            private final int MAX_ADDRESS_OPTIONS = 5;

            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                DropdownFormFieldValueV0 dropdownFormFieldValue = (DropdownFormFieldValueV0) formFieldValue.getData();
                AddressOptionsResult addressOptionsResult = getAddressOptions(userData);
                if (addressOptionsResult.getDropdownOptions().isEmpty()) {
                    dropdownFormFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getData().getName(), ""));
                    return;
                }
                if (dropdownFormFieldValue != null) {
                    if (!addressOptionsResult.dropdownOptions.isEmpty() && addressOptionsResult.dropdownOptions.get(0) != null) {
                        DropdownValue selectedOption = addressOptionsResult.dropdownOptions.get(0);
                        String selectedOptionId = selectedOption.getId();
                        Map<String, Object> selectedOptionData = null;
                        for(Map<String, Object> initialOptionData: addressOptionsResult.initialOptionsData) {
                            if (selectedOptionId.equals(initialOptionData.get("id"))) {
                                selectedOptionData = initialOptionData;
                                break;
                            }
                        }
                        if (selectedOptionData != null) {
                            dropdownFormFieldValue.setOptions(addressOptionsResult.dropdownOptions);
                            dropdownFormFieldValue.setInitialOptionsDataMap(addressOptionsResult.initialOptionsData);
                            dropdownFormFieldValue.setSelectedOption(selectedOption);
                            dropdownFormFieldValue.setInitialValue(getInitialValue(selectedOptionData));
                        }
                    }
                }
            }

            @NotNull
            private Map<String, Object> getInitialValue(Map<String, Object> selectedOptionData) {
                Map<String, Object> initialValue = new HashMap<>();
                if (selectedOptionData.get(DATA) != null && selectedOptionData.get(DATA) instanceof Map) {
                    Map<String, Object> userData = (Map<String, Object>) selectedOptionData.get(DATA);
                    if (userData.get(PINCODE_DETAILS_STRING) != null && userData.get(PINCODE_DETAILS_STRING) instanceof Map &&
                            StringUtils.isNotBlank((String) userData.getOrDefault(HOUSE_NUMBER_STRING, "")) &&
                            StringUtils.isNotBlank((String) userData.getOrDefault(AREA_STRING, ""))) {
                        Map<String, Object> pincodeDetails = (Map<String, Object>) userData.get(PINCODE_DETAILS_STRING);
                        if (StringUtils.isNotBlank((String) pincodeDetails.getOrDefault(PINCODE_STRING, "")) &&
                                StringUtils.isNotBlank((String) pincodeDetails.getOrDefault(CITY, "")) &&
                                StringUtils.isNotBlank((String) pincodeDetails.getOrDefault(STATE, ""))) {
                            initialValue.put(HOUSE_NUMBER_STRING, userData.get(HOUSE_NUMBER_STRING));
                            initialValue.put(AREA_STRING, userData.get(AREA_STRING));
                            initialValue.put(PINCODE_DETAILS_STRING, pincodeDetails);
                        }
                    }
                }
                if (initialValue.size() == 0) {
                    initialValue.put("address", "");
                }
                return initialValue;
            }

            @NotNull
            private AddressOptionsResult getAddressOptions(Map<String, Object> userData) {
                List<Address> processedAddresses = getProcessedAddresses(userData);
                Map<Integer, String> indexMapping = getIndexAddressNumberMapping();
                Map<Integer, String> indexIdMapping = getIndexAddressIdMapping();
                List<DropdownValue> options = new ArrayList<>();
                List<Map<String, Object>> initialOptionsDataMap = new ArrayList<>();
                int count = 0;
                for (int i = 0; i < processedAddresses.size() && count < MAX_ADDRESS_OPTIONS; i++) {
                    Address current = processedAddresses.get(i);
                    DropdownValue option = new DropdownValue();
                    if (StringUtils.isEmpty(current.getFirstLineOfAddress())) continue;
                    String address = current.getFirstLineOfAddress();
                    if (StringUtils.isNotBlank(current.getSecondLineOfAddress())) {
                        address += ", " + current.getSecondLineOfAddress();
                    }
                    if (StringUtils.isNotBlank(current.getPincode())) {
                        address += ", " + current.getPincode();
                    }
                    option.setTitle("Address " + indexMapping.get(count));
                    option.setTitleStyle(getTitleStyle());
                    option.setType(DropdownType.PLAIN_TEXT);
                    RenderableComponent<RichTextValue> description = new RenderableComponent<>();
                    RichTextValue value = new RichTextValue();
                    value.setText(address);
                    value.setTitle(address);
                    value.setTextColor("#667085");
                    value.setStyle(getDescriptionStyleForOption());
                    description.setValue(value);
                    option.setDescription(description);
                    option.setId(indexIdMapping.get(count));
                    options.add(option);
                    int finalCount = count;
                    initialOptionsDataMap.add(getInitialOptionDataForOption(indexIdMapping, finalCount, current));
                    count++;
                }
                AddressOptionsResult addressOptionsResult = new AddressOptionsResult(options, initialOptionsDataMap);
                return addressOptionsResult;
            }

            @NotNull
            private HashMap<String, Object> getInitialOptionDataForOption(Map<Integer, String> indexIdMapping, int finalCount, Address current) {
                return new HashMap<String, Object>() {{
                    put("id", indexIdMapping.get(finalCount));
                    put(DATA, new HashMap<String, Object>() {{
                        put("area", current.getFirstLineOfAddress());
                        put("houseNumber", current.getSecondLineOfAddress());
                        put("pincodeDetails", new HashMap<String, String>() {{
                            put("pincode", current.getPincode());
                            put(CITY, current.getCity());
                            put(STATE, current.getState());
                        }});
                    }});
                }};
            }

            @NotNull
            private List<Address> getProcessedAddresses(Map<String, Object> userData) {
                List<Address> processedAddresses = new ArrayList<>();
                List<CAISHolderAddressDetails> availableAddresses = (List<CAISHolderAddressDetails>) userData.get(ADDRESSES_STRING);

                // selected Address
                String firstLineOfAddress = (String) userData.get(HOUSE_NUMBER_STRING);
                String secondLineOfAddress = (String) userData.get(AREA_STRING);
                PincodeDetailsResponse pincodeDetailsResponse = (PincodeDetailsResponse) userData.get(PINCODE_DETAILS_STRING);
                if (pincodeDetailsResponse.isValid()) {
                    // Only append city and state if they're not already present in the address string
                    if (StringUtils.isNotBlank(pincodeDetailsResponse.getCity()) &&
                        !secondLineOfAddress.contains(pincodeDetailsResponse.getCity())) {
                        secondLineOfAddress += ", " + pincodeDetailsResponse.getCity();
                    }
                    if (StringUtils.isNotBlank(pincodeDetailsResponse.getState()) &&
                        !secondLineOfAddress.contains(pincodeDetailsResponse.getState())) {
                        secondLineOfAddress += ", " + pincodeDetailsResponse.getState();
                    }
                }
                processedAddresses.add(new Address(firstLineOfAddress, secondLineOfAddress, pincodeDetailsResponse.getPincode(), pincodeDetailsResponse.getCity(), pincodeDetailsResponse.getState()));

                availableAddresses.forEach(addressDetail -> processedAddresses.add(new Address(
                        addressDetail.getFirstLineOfAddress(),
                        addressDetail.getSecondLineOfAddress(),
                        addressDetail.getZipPostalCodeOfAddress(),
                        addressDetail.getCityOfAddress(),
                        addressDetail.getStateOfAddress()
                )));
                return processedAddresses;
            }

            @NotNull
            private Map<Integer, String> getIndexAddressIdMapping() {
                Map<Integer, String> indexIdMapping = new HashMap<>();
                indexIdMapping.put(0, "ONE");
                indexIdMapping.put(1, "TWO");
                indexIdMapping.put(2, "THREE");
                indexIdMapping.put(3, "FOUR");
                indexIdMapping.put(4, "FIVE");
                return indexIdMapping;
            }

            @NotNull
            private Map<Integer, String> getIndexAddressNumberMapping() {
                Map<Integer, String> indexMapping = new HashMap<>();
                indexMapping.put(0, "01");
                indexMapping.put(1, "02");
                indexMapping.put(2, "03");
                indexMapping.put(3, "04");
                indexMapping.put(4, "05");
                return indexMapping;
            }

            @NotNull
            private TextStyle getTitleStyle() {
                TextStyle textStyle = new TextStyle();
                textStyle.setFontWeight(FontWeight.bold);
                textStyle.setColor("#1D2939");
                textStyle.setFontSize(16);
                textStyle.setLineHeight(24);
                return textStyle;
            }

            @NotNull
            private TextStyle getDescriptionStyleForOption() {
                TextStyle descriptionStyle = new TextStyle();
                descriptionStyle.setFontWeight(FontWeight.normal);
                descriptionStyle.setColor("#667085");
                return descriptionStyle;
            }
        },
        ORGANIZATION("organization") {
            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String employmentType = (String) userData.get(EMPLOYMENT_TYPE_STRING);
                CardFormFieldValue cardFormFieldValue = (CardFormFieldValue) formFieldValue.getData();
                String companyName = "";
                if ("Salaried".equals(employmentType)) {
                    companyName = (String) userData.get(COMPANY_NAME_STRING);
                    String organizationId = (String) userData.get(ORGANIZATION_ID_STRING);
                    Map<String, Object> initialValue = new HashMap<>();
                    Map<String, String> organization = new HashMap<>();
                    if (StringUtils.isNotBlank(companyName)) {
                        organization.put("id", organizationId);
                        organization.put("title", companyName);
                    }
                    if (!organization.isEmpty()) {
                        initialValue.put("organization", organization);
                    }
                    cardFormFieldValue.getCard().getDescription().getValue().setText(companyName);
                    cardFormFieldValue.setInitialValue(initialValue);
                    cardFormFieldValue.getCard().getDescription().setTracking(WidgetTransformerUtils
                            .getTrackingMetadataForGroupFields(Objects.requireNonNull(formFieldValue.getData()).getName(), companyName));
                    return;
                } else {
                    cardFormFieldValue.setInitialValue(null);
                }
                setupPrefillValueAndTrackingDataForGroupedFormField(formFieldValue, companyName);
            }
        },
        BUSINESS_NAME("businessName") {
            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String employmentType = (String) userData.get(EMPLOYMENT_TYPE_STRING);
                String companyName = "";
                CardFormFieldValue cardFormFieldValue = (CardFormFieldValue) formFieldValue.getData();
                if ("SelfEmployed".equals(employmentType)) {
                    companyName = (String) userData.get(COMPANY_NAME_STRING);
                } else {
                    cardFormFieldValue.setInitialValue(null);
                }
                setupPrefillValueAndTrackingDataForGroupedFormField(formFieldValue, companyName);
            }
        },
        INDUSTRY_TYPE("industryName") {
            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String employmentType = (String) userData.get(EMPLOYMENT_TYPE_STRING);
                CardFormFieldValue cardFormFieldValue = (CardFormFieldValue) formFieldValue.getData();
                String industryType = "";
                if ("SelfEmployed".equals(employmentType)) {
                    industryType = (String) userData.get(INDUSTRY_NAME_STRING);
                    String organizationId = (String) userData.get(ORGANIZATION_ID_STRING);
                    Map<String, Object> initialValue = new HashMap<>();
                    Map<String, String> industryNameInitialValueObject = new HashMap<>();
                    if (StringUtils.isNotBlank(industryType)) {
                        industryNameInitialValueObject.put("id", organizationId);
                        industryNameInitialValueObject.put("title", industryType);
                    }
                    if (!industryNameInitialValueObject.isEmpty()) {
                        initialValue.put("industryName", industryNameInitialValueObject);
                    }
                    cardFormFieldValue.setInitialValue(initialValue);
                    cardFormFieldValue.getCard().getDescription().getValue().setText(industryType);
                    return;
                } else {
                    cardFormFieldValue.setInitialValue(null);
                }
                setupPrefillValueAndTrackingDataForGroupedFormField(formFieldValue, industryType);
            }
        },
        INCOME("income") {
            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String monthlyIncome = (String) userData.get(MONTHLY_INCOME_STRING);
                setupPrefillValueAndTrackingDataForGroupedFormField(formFieldValue, monthlyIncome);
            }
        },
        BONUS_OR_OTHER_INCOME("bonusIncome") {
            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                CardFormFieldValue cardFormFieldValue = (CardFormFieldValue) formFieldValue.getData();
                Integer bonusIncome = (Integer) userData.get(BONUS_INCOME_STRING);
                String bonusIncomeValue = "";
                if(bonusIncome > 0){
                    bonusIncomeValue = Integer.toString(bonusIncome);
                }
                if (cardFormFieldValue != null && cardFormFieldValue.getCard() != null && cardFormFieldValue.getCard().getDescription() != null) {
                    cardFormFieldValue.getCard().getDescription().setTracking(WidgetTransformerUtils
                            .getTrackingMetadataForGroupFields(Objects.requireNonNull(formFieldValue.getData()).getName(), bonusIncomeValue));
                }
                cardFormFieldValue.getCard().getDescription().getValue().setText(bonusIncomeValue);
            }
        },
        INCOME_SOURCE("incomeSource") {
            @Override
            public void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData) {
                String incomeSource = (String) userData.get(INCOME_SOURCE_STRING);
                setupPrefillValueAndTrackingDataForGroupedFormField(formFieldValue, incomeSource);
                PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), INCOME_SOURCE_PREFILL_FROM_PROFILE)).mark();
            }
        };

        private static void setupPrefillValueAndTrackingDataForGroupedFormField(FormGroupDataValue formFieldValue, String prefillValue) {
            CardFormFieldValue cardFormFieldValue = (CardFormFieldValue) formFieldValue.getData();
            if (StringUtils.isNotBlank(prefillValue) && isDescriptionFieldWithValuePresent(cardFormFieldValue)) {
                cardFormFieldValue.getCard().getDescription().getValue().setText(prefillValue);
                formFieldValue.getData().setInitialValue(null);
            }
            if (isDescriptionFieldPresent(cardFormFieldValue)) {
                cardFormFieldValue.getCard().getDescription().setTracking(WidgetTransformerUtils
                        .getTrackingMetadataForGroupFields(Objects.requireNonNull(formFieldValue.getData()).getName(), prefillValue));
            }
        }

        private static boolean isDescriptionFieldPresent(CardFormFieldValue value) {
            return value != null && value.getCard() != null && value.getCard().getDescription() != null;
        }

        private static boolean isDescriptionFieldWithValuePresent(CardFormFieldValue value) {
            return isDescriptionFieldPresent(value) && value.getCard().getDescription().getValue() != null;
        }

        private final String name;

        GroupedFormFields(String name) {
            this.name = name;
        }

        public abstract void prefill(FormGroupDataValue formFieldValue, Map<String, Object> userData);

        public static GroupedFormFields getFieldByName(String name) {
            return Arrays.stream(GroupedFormFields.values()).filter(n -> n.name.equals(name)).findFirst().orElse(null);
        }
    }

    public enum FormFields {
        FULL_NAME("fullName") {
            public void prefill(FormFieldValue formFieldValue, Map<String, Object> userData) {
                String prefilledValue = (String) userData.get(FULL_NAME_STRING);
                setupPrefillAndTrackingForFormField(formFieldValue, prefilledValue);
            }
        },
        PHONE_NUMBER("phoneNumber") {
            public void prefill(FormFieldValue formFieldValue, Map<String, Object> userData) {
                String prefilledValue = (String) userData.get(PHONE_NUMBER_STRING);
                setupPrefillAndTrackingForFormField(formFieldValue, prefilledValue);
            }
        },
        PAN("panNumber") {
            @Override
            public void prefill(FormFieldValue formFieldValue, Map<String, Object> userData) {
                String panNumber = (String) userData.get(PAN_NUMBER);
                TextBoxFormFieldValueV0 textBoxFormFieldValue = (TextBoxFormFieldValueV0) formFieldValue;
                textBoxFormFieldValue.setAutoCapitalize(AutoCapitalize.characters);
                setupPrefillAndTrackingForFormField(formFieldValue, panNumber);
            }
        },
        DOB("dob") {
            @Override
            public void prefill(FormFieldValue formFieldValue, Map<String, Object> userData) {
                if(formFieldValue instanceof DateFormFieldValue) {
                    DateFormFieldValue dateFormFieldValue = (DateFormFieldValue) formFieldValue;
                    dateFormFieldValue.setMinValue(getYearsBackDateFromNow(60));
                    dateFormFieldValue.setMaxValue(getYearsBackDateFromNow(18));
                } else if(formFieldValue instanceof DateFormFieldValueV0) {
                    DateFormFieldValueV0 dateFormFieldValue = (DateFormFieldValueV0) formFieldValue;
                    dateFormFieldValue.setMinValue(getYearsBackDateFromNow(60));
                    dateFormFieldValue.setMaxValue(getYearsBackDateFromNow(18));
                }
                String dob = (String) userData.get(DOB_STRING);
                setupPrefillAndTrackingForFormField(formFieldValue, dob);
            }
        },
        EMAIL("email") {
            @Override
            public void prefill(FormFieldValue formFieldValue, Map<String, Object> userData) {
                String email = (String) userData.get(EMAIL_STRING);
                setupPrefillAndTrackingForFormField(formFieldValue, email);
            }
        },
        GENDER("gender") {
            @Override
            public void prefill(FormFieldValue formFieldValue, Map<java.lang.String, Object> userData) {
                String gender = (String) userData.get(GENDER_STRING);
                String gender2 = (String) userData.get(GENDER_STRING_2);
                DropdownFormFieldValue genderDropDownFormFieldValue = (DropdownFormFieldValue) formFieldValue;
                DropdownValue selectedOption = new DropdownValue();
                selectedOption.setType(DropdownType.PLAIN_TEXT);
                String id = "M";
                String title = "Male";
                if ("M".equals(gender) || "1".equals(gender2)) {
                    id = "M";
                    title = "Male";
                } else if ("F".equals(gender) || "2".equals(gender2)) {
                    id = "F";
                    title = "Female";
                } else if ("O".equals(gender) || "3".equals(gender2)) {
                    id = "O";
                    title = "Others";
                }
                selectedOption.setTitle(title);
                selectedOption.setId(id);
                genderDropDownFormFieldValue.setValue(selectedOption);
                PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), GENDER_PREFILL_FROM_PROFILE)).mark();
                genderDropDownFormFieldValue.setSelectedOption(selectedOption);
                genderDropDownFormFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(genderDropDownFormFieldValue.getName(), selectedOption.getTitle()));
            }
        },
        EMPLOYMENT_TYPE("employmentType") {
            @Override
            public void prefill(FormFieldValue formFieldValue, Map<String, Object> userData) {
                String employmentType = (String) userData.get(EMPLOYMENT_TYPE_STRING);
                MultiDropdownFormFieldValue employmentTypeDropDownValue = (MultiDropdownFormFieldValue) formFieldValue;

                List<MultiDropdownValue> options = employmentTypeDropDownValue.getOptions();
                if (options != null && !options.isEmpty()) {
                    options.forEach(option -> {
                        if (option.getId().equals("Salaried")) {
                            option.setSubtitle("You get a monthly salary");
                        }
                        if (option.getId().equals("SelfEmployed")) {
                            option.setSubtitle("You have a business or shop");
                        }
                    });
                }

                String selectionOption = "";
                if (StringUtils.isNotBlank(employmentType)) {
                    if (employmentType.equals("SelfEmployed")) {
                        selectionOption = "SelfEmployed";
                    } else if (employmentType.equals("Salaried")) {
                        selectionOption = "Salaried";
                    }
                    employmentTypeDropDownValue.setValue(selectionOption);
                    employmentTypeDropDownValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(employmentTypeDropDownValue.getName(), selectionOption));
                    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), EMPLOYMENT_TYPE_PREFILL_FROM_PROFILE)).mark();
                }
            }
        },
        COMPANY_NAME("organization") {
            @Override
            public void prefill(FormFieldValue formFieldValue, Map<java.lang.String, Object> userData) {
                String companyName = (String) userData.get(COMPANY_NAME_STRING);
                String employmentType = (String) userData.get(EMPLOYMENT_TYPE_STRING);
                String organizationId = (String) userData.get(ORGANIZATION_ID_STRING);
                if(StringUtils.isNotBlank(companyName)) {
                    if (SALARIED.equals(employmentType)) {
                        AutoSuggestFormFieldValue getAutoFilledCompanyName = (AutoSuggestFormFieldValue) formFieldValue;
                        DropdownValue value = new DropdownValue();
                        if (StringUtils.isNotBlank(organizationId)) {
                            value.setId(organizationId);
                            value.setTitle(companyName);
                            value.setType(PLAIN_TEXT);
                            getAutoFilledCompanyName.setValue(value);
                        }
                    } else {
                        formFieldValue.setValue(companyName);
                    }
                }
                formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), companyName));
            }
        },
        BUSINESS_NAME("businessName") {
            @Override
            public void prefill(FormFieldValue formFieldValue, Map<java.lang.String, Object> userData) {
                String company = (String) userData.get(COMPANY_NAME_STRING);
                String employmentType = (String) userData.get(EMPLOYMENT_TYPE_STRING);
                if ("SelfEmployed".equals(employmentType)) {
                    setupPrefillAndTrackingForFormField(formFieldValue, company);
                }
            }
        },
        MONTHLY_INCOME("income") {
            @Override
            public void prefill(FormFieldValue formFieldValue, Map<java.lang.String, Object> userData) {
                String income = userData.get(MONTHLY_INCOME_STRING).toString();
                setupPrefillAndTrackingForFormField(formFieldValue, income);
            }
        },
        BONUS_OR_OTHER_INCOME("bonusIncome") {
            @Override
            public void prefill(FormFieldValue formFieldValue, Map<java.lang.String, Object> userData) {
                PriceTextBoxFormFieldValueV0 priceTextBoxFormFieldValueV0 = (PriceTextBoxFormFieldValueV0) formFieldValue;
                Integer bonusIncome = (Integer) userData.get(BONUS_INCOME_STRING);
                String bonusIncomeValue = "";
                if(bonusIncome > 0) {
                    bonusIncomeValue = Integer.toString(bonusIncome);
                }
                priceTextBoxFormFieldValueV0.setValue(bonusIncomeValue);
                formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), bonusIncomeValue));
            }
        },
        INCOME_SOURCE("incomeSource") {
            @Override
            public void prefill(FormFieldValue formFieldValue, Map<String, Object> userData) {
                String incomeSource = (String) userData.get(INCOME_SOURCE_STRING);
                setupPrefillAndTrackingForFormField(formFieldValue, incomeSource);
            }
        },
        INDUSTRY_TYPE("industryName") {
            @Override
            public void prefill(FormFieldValue formFieldValue, Map<java.lang.String, Object> userData) {
                String employmentType = (String) userData.get(EMPLOYMENT_TYPE_STRING);
                if ("SelfEmployed".equals(employmentType)) {
                    String industryType = (String) userData.get(INDUSTRY_NAME_STRING);
                    String industryId = (String) userData.get(INDUSTRY_ID_STRING);
                    if (StringUtils.isNotBlank(industryType) && StringUtils.isNotBlank(industryId)) {
                        Map<String, Object> value = new HashMap<>();
                        value.put("fullWidthPress", false);
                        value.put("id", industryId);
                        value.put("title", industryType);
                        value.put("type", "PLAIN_TEXT");
                        formFieldValue.setValue(value);
                        formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), industryType));
                    } else {
                        formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), industryType));
                    }
                }
            }
        };

        private static void setupPrefillAndTrackingForFormField(FormFieldValue formFieldValue, String prefillValue) {
            if (StringUtils.isNotBlank(prefillValue)) {
                formFieldValue.setValue(prefillValue);
            }
            formFieldValue.setTracking(WidgetTransformerUtils.getTrackingMetadata(formFieldValue.getName(), prefillValue));
        }

        public final String name;

        FormFields(String name) {
            this.name = name;
        }

        public abstract void prefill(FormFieldValue formFieldValue, Map<String, Object> userData);

        public static FormFields getFieldByName(String name) {
            return Arrays.stream(FormFields.values()).filter(n -> n.name.equals(name)).findFirst().orElse(null);
        }
    }

    public void prefillFormFieldValues(Map<String, FormFieldValue> formFieldValueMapToPrefill, Map<String, Object> userData) {
        formFieldValueMapToPrefill.forEach((key, formFieldValue) -> {
            if (FormFields.getFieldByName(key) != null) {
                FormFields.getFieldByName(key).prefill(formFieldValue, userData);
            }
        });
    }

    public void prefillGroupedFormFieldValues(Map<String, FormGroupDataValue> groupFieldValueMap, Map<String, Object> userData) {
        groupFieldValueMap.forEach((key, formFieldValue) -> {
            if (GroupedFormFields.getFieldByName(key) != null) {
                GroupedFormFields.getFieldByName(key).prefill(formFieldValue, userData);
            }
        });
    }

    private static String getYearsBackDateFromNow(int numberOfYearsBack) {
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime date21YearsAgo = now.minusYears(numberOfYearsBack);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        return date21YearsAgo.format(formatter);
    }

}
