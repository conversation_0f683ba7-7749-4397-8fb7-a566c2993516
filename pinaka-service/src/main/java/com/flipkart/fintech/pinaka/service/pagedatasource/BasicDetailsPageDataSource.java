package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.response.BasicDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.profile.service.ProfileService;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.UrlUtil;
import lombok.CustomLog;
import org.apache.http.NameValuePair;

import javax.inject.Inject;
import java.util.List;
import java.util.Optional;

@CustomLog
public class BasicDetailsPageDataSource implements PageDataSource<BasicDetailsPageDataSourceResponse> {
    @Inject
    private static ConfigUtils configUtils;
    @Inject
    private static ProfileService profileService;

    @Override
    public BasicDetailsPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse)
            throws PinakaException {
        BasicDetailsPageDataSourceResponse basicDetailsPageDataSourceResponse = new BasicDetailsPageDataSourceResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        basicDetailsPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        basicDetailsPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        MerchantUser merchantUser = MerchantUserUtils.getMerchantUser(applicationDataResponse.getExternalUserId(), applicationDataResponse.getSmUserId());
        // Adding a try catch to fix myntra as this is not mandatory. this will properly solved by another PR where we pass all info.
        try {
            basicDetailsPageDataSourceResponse.setProfile(profileService.getProfileByUserId(merchantUser.getMerchantUserId(),merchantUser.getSmUserId(), false));
        } catch (Exception exp) {
            //Logging non myntra cases as myntra will fail as of now. this will be fixed later
            if (!MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY.equals(merchantUser.getMerchantKey())) {
                log.error("Error while fetching the profile for user: {}", merchantUser, exp);
            }
        }
        return basicDetailsPageDataSourceResponse;
    }
}
