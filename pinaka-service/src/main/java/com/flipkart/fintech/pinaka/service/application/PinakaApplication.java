package com.flipkart.fintech.pinaka.service.application;

import com.codahale.metrics.JmxReporter;
import com.codahale.metrics.MetricRegistry;
import com.flipkart.cp.transact.mini.proxy.MiniProxyServer;
import com.flipkart.fintech.cryptex.CryptexBundle;
import com.flipkart.fintech.cryptex.config.CryptexBundleConfiguration;
import com.flipkart.fintech.filter.FintechContextFilter;
import com.flipkart.fintech.filter.RequestContextCleanUpFilter;
import com.flipkart.fintech.filter.impl.UserAccountFilter;
import com.flipkart.fintech.logger.core.FintechLogger;
import com.flipkart.fintech.logger.filters.LogTraceFilter;
import com.flipkart.fintech.mapper.ServiceExceptionMapper;
import com.flipkart.fintech.outbound.OutboundBundle;
import com.flipkart.fintech.outbound.OutboundBundleConfiguration;
import com.flipkart.fintech.pinaka.service.application.filter.FilterBindingFeature;
import com.flipkart.fintech.pinaka.service.application.filter.request.AccountIdFilter;
import com.flipkart.fintech.pinaka.service.application.filter.request.DeviceDetailsFilter;
import com.flipkart.fintech.pinaka.service.application.filter.request.MerchantIdFilter;
import com.flipkart.fintech.pinaka.service.application.filter.request.UserAgentFilter;
import com.flipkart.fintech.pinaka.service.filters.ABHeaderFilter;
import com.flipkart.fintech.pinaka.service.helper.CreateRequestValidator;
import com.flipkart.fintech.pinaka.service.kafka.ThreadContextHook;
import com.flipkart.fintech.pinaka.service.utils.HibernateUtils;
import com.flipkart.fintech.pinaka.service.web.SecurityResource;
import com.flipkart.fintech.pinaka.service.web.v6.*;
import com.flipkart.fintech.pinaka.web.v1.PinakaCommonResource;
import com.flipkart.fintech.profile.config.ProfileServiceDatabaseConfig;
import com.flipkart.fintech.profile.model.AddressDetails;
import com.flipkart.fintech.profile.model.BasicDetails;
import com.flipkart.fintech.profile.model.ContactDetails;
import com.flipkart.fintech.profile.model.EmploymentDetails;
import com.flipkart.fintech.profile.model.Profile;
import com.flipkart.fintech.profile.web.v1.ExperianServiceManagerResource;
import com.flipkart.fintech.profile.web.v1.ProfileServiceResource;
import com.flipkart.fintech.profile.web.v2.ExperianServiceManagerResourceV2;
import com.flipkart.fintech.profile.web.v2.ProfileServiceResourceV2;
import com.flipkart.fintech.rotator.RotationBundle;
import com.flipkart.fintech.rotator.RotationConfiguration;
import com.flipkart.fintech.security.gibraltar.GibraltarService;
import com.flipkart.fintech.viesti.config.ViestiConsumerManager;
import com.flipkart.flux.client.FluxClientComponentModule;
import com.flipkart.flux.client.FluxClientInterceptorModule;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.restbus.turbo.config.TurboConfigProvider;
import com.flipkart.sensitive.SensitiveAnnotationModule;
import com.flipkart.sm.pages.resource.PageFetchResource;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.netflix.hystrix.contrib.codahalemetricspublisher.HystrixCodaHaleMetricsPublisher;
import com.netflix.hystrix.strategy.HystrixPlugins;
import com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategy;
import com.netflix.hystrix.strategy.concurrency.HystrixContextCallable;
import com.palominolabs.metrics.guice.MetricsInstrumentationModule;
import com.supermoney.publisher.EventPublisherModule;
import io.dropwizard.Application;
import io.dropwizard.configuration.EnvironmentVariableSubstitutor;
import io.dropwizard.configuration.SubstitutingSourceProvider;
import io.dropwizard.db.DataSourceFactory;
import io.dropwizard.hibernate.HibernateBundle;
import io.dropwizard.setup.Bootstrap;
import io.dropwizard.setup.Environment;
import io.federecio.dropwizard.swagger.SwaggerBundle;
import io.federecio.dropwizard.swagger.SwaggerBundleConfiguration;
import org.glassfish.jersey.media.multipart.MultiPartFeature;
import org.hibernate.SessionFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * Created by sujeetkumar.r on 09/08/17.
 */
public class PinakaApplication extends Application<PinakaConfiguration> {

    private CryptexBundle<PinakaConfiguration> cryptexBundle = new CryptexBundle<PinakaConfiguration>() {
        @Override
        protected CryptexBundleConfiguration getCryptexConfiguration(PinakaConfiguration pinakaConfiguration) {
            return pinakaConfiguration.getCryptexConfiguration();
        }
    };

    private final HibernateBundle<PinakaConfiguration> profileServiceBundle = new HibernateBundle<PinakaConfiguration>(
            AddressDetails.class,
            BasicDetails.class,
            EmploymentDetails.class,
            Profile.class,
            ContactDetails.class
            ) {
        @Override
        public DataSourceFactory getDataSourceFactory(PinakaConfiguration configuration) {
            DataSourceFactory dataSourceFactory = new DataSourceFactory();
            ProfileServiceDatabaseConfig config = configuration.getProfileServiceDatabaseConfig();
            dataSourceFactory.setUrl(config.getEncryptedUrl());
            dataSourceFactory.setUser(config.getEncryptedUser());
            dataSourceFactory.setPassword(config.getEncryptedPassword());
            dataSourceFactory.setDriverClass(config.getDriverClass());
            Map<String, String> properties = new HashMap<>();
            properties.put("hibernate.dialect", "org.hibernate.dialect.MySQL57Dialect");
            dataSourceFactory.setProperties(properties);
            dataSourceFactory.setMaxSize(config.getMaxSize());
            return dataSourceFactory;
        }

        @Override
        public String name() {
            return "profile_service";
        }
    };

    private OutboundBundle<PinakaConfiguration> outboundBundle = new OutboundBundle<PinakaConfiguration>() {
        @Override
        protected OutboundBundleConfiguration getOutboundBundleConfiguration(PinakaConfiguration pinakaConfiguration) {
            return pinakaConfiguration.getOutboundBundleConfiguration();
        }

        @Override
        protected SessionFactory getSessionFactoryProvider() {
            return HibernateUtils.getInstance().getSessionFactory();
        }
    };

    @Override
    public void initialize(Bootstrap<PinakaConfiguration> bootstrap){

        bootstrap.addBundle(cryptexBundle);
        bootstrap.addBundle(HibernateUtils.getInstance().getHibernateBundle());
        bootstrap.addBundle(HibernateUtils.getInstance().getHibernateSlaveBundle());
        bootstrap.addBundle(profileServiceBundle);
        bootstrap.addBundle(new SwaggerBundle<PinakaConfiguration>() {
            @Override
            protected SwaggerBundleConfiguration getSwaggerBundleConfiguration(PinakaConfiguration pinakaConfiguration) {
                return  pinakaConfiguration.getSwaggerBundleConfiguration();
            }
        });
        bootstrap.addBundle(new RotationBundle<PinakaConfiguration>() {
            @Override
            protected RotationConfiguration getRotationConfiguration(PinakaConfiguration applicationConfiguration) {
                return applicationConfiguration.getRotationConfiguration();
            }
        });
        bootstrap.setConfigurationSourceProvider(
                new SubstitutingSourceProvider(
                        bootstrap.getConfigurationSourceProvider(),
                        new EnvironmentVariableSubstitutor(false))
        );
        bootstrap.addBundle(outboundBundle);
    }

    @Override
    public void run(PinakaConfiguration pinakaConfiguration, Environment environment) {

        Injector injector = Guice.createInjector(new PinakaModule(pinakaConfiguration, cryptexBundle.getDynamicBucket(), profileServiceBundle),
                new FluxClientInterceptorModule(),
                new FluxClientComponentModule(),
                new EventPublisherModule(pinakaConfiguration.getEventPublishEnvironment()),
                new MetricsInstrumentationModule(environment.metrics()));
        GuiceInjector.register(injector);

        DynamicBucket dynamicBucket = injector.getProvider(DynamicBucket.class).get();
        pinakaConfiguration.getEncryptionKeys().entrySet().stream().forEach(k-> k.setValue(dynamicBucket.getString(k.getKey())));
        Guice.createInjector(new SensitiveAnnotationModule(pinakaConfiguration.getSensitiveAnnotationConfiguration()));

        HystrixPlugins.getInstance().registerMetricsPublisher(new HystrixCodaHaleMetricsPublisher(environment.metrics()));
        HystrixPlugins.getInstance()
                .registerConcurrencyStrategy(
                        new HystrixConcurrencyStrategy() {
                            @Override
                            public <T> Callable<T> wrapCallable(Callable<T> callable) {
                                return new HystrixContextCallable<>(callable);
                            }
                        });

        MiniProxyServer.getInstance().start(10003);
        // Register Resources
        environment.jersey().register(ABHeaderFilter.class);
        environment.jersey().register(new ServiceExceptionMapper());
        environment.jersey().register(injector.getInstance(SecurityResource.class));
        environment.jersey().register(injector.getInstance(MultiPartFeature.class));
        environment.jersey().register(injector.getInstance(WinterfellResource.class));
        environment.jersey().register(injector.getInstance(WinterfellResourceV2.class));
        environment.jersey().register(injector.getInstance(EventResource.class));
        environment.jersey().register(injector.getInstance(LoanResource.class));
        environment.jersey().register(injector.getInstance(LoanApplicationResource.class));
        environment.jersey().register(injector.getInstance(LocationResource.class));
        environment.jersey().register(injector.getInstance(UserActionResource.class));
        environment.jersey().register(injector.getInstance(BulkDataResource.class));
        environment.jersey().register(injector.getInstance(FetchPageDataResource.class));
        environment.jersey().register(injector.getInstance(SecurityResourceV6.class));
        environment.jersey().register(injector.getInstance(DocumentResource.class));
        environment.jersey().register(injector.getInstance(ProfileServiceResource.class));
        environment.jersey().register(injector.getInstance(ActionResourceV2.class));
        environment.jersey().register(injector.getInstance(CheckScorePageFetchResource.class));
        environment.jersey().register(injector.getInstance(ExperianServiceManagerResource.class));
        environment.jersey().register(injector.getInstance(ExperianServiceManagerResourceV2.class));
        environment.jersey().register(injector.getInstance(ProfileServiceResourceV2.class));
        environment.jersey().register(injector.getInstance(PinakaCommonResource.class));
        environment.jersey().register(injector.getInstance(PageFetchResource.class));
        environment.jersey().register(injector.getInstance(UserOfferDataResource.class));
        environment.jersey().register(injector.getInstance(UIEventResource.class));
        FintechLogger.initialize(injector);

        MetricRegistry metricRegistry = PinakaMetricRegistry.getMetricRegistry();
        final JmxReporter jmxReporter = JmxReporter.forRegistry(metricRegistry).build();
        jmxReporter.start();
        TurboConfigProvider.setConfig(pinakaConfiguration.getTurboConfig());

        MetricRegistry metricRegistryForProfileService = com.flipkart.fintech.profile.common.PinakaMetricRegistry.getMetricRegistry();
        final JmxReporter jmxReporterForProfileService = JmxReporter.forRegistry(metricRegistryForProfileService).build();
        jmxReporterForProfileService.start();

        environment.jersey().register(FilterBindingFeature.class);
        environment.jersey().register(CreateRequestValidator.class);
        environment.lifecycle().manage(injector.getInstance(GibraltarService.class));
        environment.lifecycle().manage(new CryptoInitializer(pinakaConfiguration.getCryptoConfiguration()));
        environment.jersey().register(FintechContextFilter.class);
        environment.jersey().register(UserAccountFilter.class);
        environment.jersey().register(DeviceDetailsFilter.class);
        environment.jersey().register(UserAgentFilter.class);
        environment.jersey().register(AccountIdFilter.class);
        environment.jersey().register(MerchantIdFilter.class);
        environment.jersey().register(injector.getInstance(LogTraceFilter.class));
        environment.jersey().register(RequestContextCleanUpFilter.class);

        // Viesti consumer initialization
        environment.lifecycle().manage(injector.getInstance(ViestiConsumerManager.class));



    }

    public static void main(String[] args) throws Exception {
        new PinakaApplication().run(args);
    }
}
