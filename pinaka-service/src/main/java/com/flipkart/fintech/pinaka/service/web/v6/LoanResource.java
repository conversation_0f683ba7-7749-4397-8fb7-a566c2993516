package com.flipkart.fintech.pinaka.service.web.v6;

import static com.flipkart.fintech.pinaka.api.response.v6.ActionType.NAVIGATION;

import com.codahale.metrics.Timer;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.ENABLE_PL_MAINTENANCE;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.lending.orchestrator.model.LendingPageRequest;
import com.flipkart.fintech.lending.orchestrator.service.LendingOrchestrator;
import com.flipkart.fintech.lending.orchestrator.service.PersonalLoanOrchestrator;
import com.flipkart.fintech.pinaka.api.request.v6.LandingPageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.ResumePageRequest;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ErrorOperation;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.PinakaWebAppException;
import com.flipkart.kloud.config.DynamicBucket;
import de.client.shade.javax.validation.Valid;
import io.dropwizard.hibernate.UnitOfWork;
import io.swagger.annotations.ApiOperation;
import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.CustomLog;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/pl")
public class LoanResource {

  private final LendingOrchestrator lendingOrchestrator;

  private final DynamicBucket dynamicBucket;

  @Inject
  public LoanResource(PersonalLoanOrchestrator lendingOrchestrator, DynamicBucket dynamicBucket) {
    this.lendingOrchestrator = lendingOrchestrator;
    this.dynamicBucket = dynamicBucket;
  }

  @POST
  @Timed
  @ExceptionMetered
  @ApiOperation("Personal Loan Landing Page")
  @Path("/apply-now")
  @UnitOfWork
  @UnitOfWork(value = "profile_service")
  public PageActionResponse applyNow(@javax.validation.Valid LandingPageRequest landingPageRequest,
      @NotNull @HeaderParam("X-Request-Id") String requestId,
      @NotNull @HeaderParam("X-Merchant-Id") String merchantId,
      @NotNull @HeaderParam("X-User-Agent") String userAgent) {

    log.info("Landing Page Request received for accountId: {}, smUserId: {} and userAgent: {}",
        landingPageRequest.getAccountId(), landingPageRequest.getSmUserId(), userAgent);
    PageActionResponse pageActionResponse = new PageActionResponse();
    if(dynamicBucket.getBoolean(ENABLE_PL_MAINTENANCE)){
      return lendingOrchestrator.getMaintenancePage(merchantId, requestId);
    }
    try {
      pageActionResponse = lendingOrchestrator.getHomePageV2(transformLandingPageRequest(landingPageRequest),
          merchantId, userAgent, requestId);
    } catch (PinakaException e) {
      log.error("Pinaka exception caught: {}", e.getMessage());
      PinakaMetricRegistry.getMetricRegistry().meter(this.getClass().getName()+"_LoanResource_applyNow_pinakaexception").mark();
      pageActionResponse.setActionSuccess(false);
      ErrorOperation error = ErrorOperation.builder().message("Please try again after sometime.")
          .build();
      pageActionResponse.setError(error);
    } catch (Exception ex) {
      log.error("Error while apply now Request for accountId {}: {}",
          landingPageRequest.getAccountId(), ex.getMessage(), ex);
      pageActionResponse.setActionSuccess(false);
      ErrorOperation error = ErrorOperation.builder().message("Error while creating application")
          .build();
      pageActionResponse.setError(error);
      PinakaMetricRegistry.getMetricRegistry().meter(this.getClass().getName()+"_LoanResource_applyNow_exception").mark();
    }
    log.info("got page action response for smuserId {} response {}", landingPageRequest.getSmUserId(), pageActionResponse);

    return pageActionResponse;
  }


  private boolean secondLoanEnabled() {
    return Boolean.TRUE.equals(dynamicBucket.getBoolean("enableSecondLoan"));
  }

  private LendingPageRequest transformLandingPageRequest(LandingPageRequest landingPageRequest) {
    return LendingPageRequest.fromLandingPageRequest(landingPageRequest);
  }

  @POST
  @Timed
  @ExceptionMetered
  @ApiOperation("Personal Loan Resume Journey")
  @Path("/resume")
  @UnitOfWork
  @UnitOfWork(value = "profile_service")
  public PageActionResponse resume(@javax.validation.Valid ResumePageRequest resumePageRequest,
      @NotNull @HeaderParam("X-Request-Id") String requestId,
      @HeaderParam("X-User-Agent") String userAgent,
      @NotNull @HeaderParam("X-Merchant-Id") String merchantId) {
    log.info("Resume Page Request received for accountId: {}, applicationId: {}",
        resumePageRequest.getSmUserId(), resumePageRequest.getApplicationId());
    PageActionResponse pageActionResponse = new PageActionResponse();
    try {
      pageActionResponse = lendingOrchestrator.getStatus(
          transformResumePageRequest(resumePageRequest), merchantId, requestId, userAgent);
    } catch (Exception ex) {
      log.error("Error while Resume Page Request for accountId: {}, applicationId: {}: {}",
          resumePageRequest.getSmUserId(),
          resumePageRequest.getApplicationId(), ex.getMessage());
      pageActionResponse.setActionSuccess(false);
      ErrorOperation error = ErrorOperation.builder()
          .message("Error while resuming the application").build();
      pageActionResponse.setError(error);
      PinakaMetricRegistry.getMetricRegistry().meter(this.getClass().getName()+"_LoanResource_resume_exception").mark();
    }
    return pageActionResponse;
  }

  private LendingPageRequest transformResumePageRequest(ResumePageRequest resumePageRequest) {
    return LendingPageRequest.fromResumePageRequest(resumePageRequest);
  }

  @POST
  @Timed
  @ExceptionMetered
  @ApiOperation("Getting pageActionResponse without moving the application forward")
  @Path("/dummy")
  @UnitOfWork
  public PageActionResponse dummyResponse(@Valid String url,
      @NotNull @HeaderParam("X-Request-Id") String requestId) {
    Action action = new Action();
    action.setUrl("/ams/1/repayment-schedule");
    action.setActionType(NAVIGATION);
    return new PageActionResponse(action, true, null, null);
  }
}
