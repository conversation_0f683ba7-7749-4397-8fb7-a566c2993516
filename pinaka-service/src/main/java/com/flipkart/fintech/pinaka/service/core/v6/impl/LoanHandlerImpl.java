package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.LandingPageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.Operation;
import com.flipkart.fintech.pinaka.api.request.v6.ResumePageRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.arsenal.ArsenalService;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.core.page.NoActiveApplicationErrorBehavior;
import com.flipkart.fintech.pinaka.service.core.page.NonWhitelistedUserBehavior;
import com.flipkart.fintech.pinaka.service.core.page.PageActionResponseHandler;
import com.flipkart.fintech.pinaka.service.core.v6.LoanHandler;
import com.flipkart.fintech.pinaka.service.core.v7.CreateApplicationRequestFactory;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.flipkart.fintech.pinaka.service.data.model.WhitelistEntity;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.utils.CreateApplicationUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantCheckUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.winterfell.api.request.ApplicationResponse;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.AmsBridge;
import lombok.CustomLog;

import javax.inject.Inject;
import java.util.Objects;
import java.util.Optional;

@CustomLog
public class LoanHandlerImpl implements LoanHandler {
    private final PageActionResponseHandler pageActionResponseHandler;


    @Inject
    public LoanHandlerImpl(PageActionResponseHandler pageActionResponseHandler) {
        this.pageActionResponseHandler = pageActionResponseHandler;
    }

    public static Boolean isWhitelisted(Optional<BorrowerEntity> borrowerEntity) {
        return borrowerEntity
                .map(BorrowerEntity::getWhitelist)
                .map(LoanHandlerImpl::isWhitelisted)
                .orElse(false);
    }

    private static boolean isWhitelisted(WhitelistEntity whitelistEntity) {
        return whitelistEntity != null && whitelistEntity.getLender() != null;
    }

    @Override
    public PageActionResponse getPageActionResponse(ApplicationDataResponse applicationDataResponse,
                                                    String requestId, MerchantUser merchantUser) throws PinakaException {
        if (Objects.isNull(applicationDataResponse)) {
            return NoActiveApplicationErrorBehavior.getPageActionResponse();
        }
        return pageActionResponseHandler.create(requestId, merchantUser, applicationDataResponse);
    }
}
