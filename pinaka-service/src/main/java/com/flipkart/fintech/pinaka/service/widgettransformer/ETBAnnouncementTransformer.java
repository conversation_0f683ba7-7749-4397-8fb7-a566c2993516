package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;

public class ETBAnnouncementTransformer {
    private static String etbGenerateAnnouncementjson;
    private static String etbGenerateVerifyAnnouncementjson;

    static {
        etbGenerateAnnouncementjson = TransformerUtils.readFileasString("template/idfc/ETBAnnouncement.json");
        etbGenerateVerifyAnnouncementjson = TransformerUtils.readFileasString("template/idfc/ETBVerifyOtpAnnouncement.json");

    }

    public AnnouncementV2WidgetData buildWidgetData()
            throws JsonProcessingException {
        AnnouncementV2WidgetData announcementV2WidgetData = ObjectMapperUtil.get().readValue(etbGenerateAnnouncementjson, AnnouncementV2WidgetData.class);
        return announcementV2WidgetData;
    }

    public AnnouncementV2WidgetData buildVerifyOtpWidgetData()
            throws JsonProcessingException {
        AnnouncementV2WidgetData announcementV2WidgetData = ObjectMapperUtil.get().readValue(etbGenerateVerifyAnnouncementjson, AnnouncementV2WidgetData.class);
        return announcementV2WidgetData;
    }

}
