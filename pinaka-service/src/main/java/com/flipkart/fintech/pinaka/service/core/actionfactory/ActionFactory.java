package com.flipkart.fintech.pinaka.service.core.actionfactory;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.Status;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.winterfell.api.request.PendingTask;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import java.net.URISyntaxException;
import java.util.List;
import javax.inject.Inject;

import com.supermoney.ams.bridge.exceptions.InvalidMerchantException;
import lombok.CustomLog;

@CustomLog
public class ActionFactory {

  private final StatusActionBuilder statusActionBuilder;
  private final ResumeActionBuilder resumeActionBuilder;
  private final LenderActionBuilder lenderActionBuilder;

  @Inject
  public ActionFactory(StatusActionBuilder statusActionBuilder,
      ResumeActionBuilder resumeActionBuilder, LenderActionBuilder lenderActionBuilder) {
    this.statusActionBuilder = statusActionBuilder;
    this.resumeActionBuilder = resumeActionBuilder;
    this.lenderActionBuilder = lenderActionBuilder;
  }

  public Action getAction(String requestId, MerchantUser merchantUser,
      ApplicationDataResponse applicationDataResponse)
      throws URISyntaxException, JsonProcessingException, InvalidMerchantException {
    String applicationState = applicationDataResponse.getApplicationState();
    if (PinakaConstants.PLConstants.PL_TERMINAL_STATES.contains(applicationState)) {
      if (Status.APPLICATION_COMPLETED.name().equals(applicationState)) {
        return lenderActionBuilder.getAction(requestId, merchantUser, applicationDataResponse);
      }
      return statusActionBuilder.getAction(applicationDataResponse, merchantUser);
    } else if (hasPendingTask(applicationDataResponse)) {
      log.info("Task list not empty");
      return resumeActionBuilder.getAction(requestId, merchantUser, applicationDataResponse);
    } else {
      // TODO: trigger queue
      log.info("Task list empty");
      return statusActionBuilder.getAction(applicationDataResponse, merchantUser);
    }
  }

  private static boolean hasPendingTask(ApplicationDataResponse applicationDataResponse) {
    List<PendingTask> pendingTaskList = applicationDataResponse.getPendingTask();
    return !pendingTaskList.isEmpty();
  }
}