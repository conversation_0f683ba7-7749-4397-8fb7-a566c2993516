package com.flipkart.fintech.pinaka.service.utils;

import com.flipkart.fintech.pinaka.api.model.FullName;

/**
 * <AUTHOR>
 * @date 05/01/25
 */
public class Utils {
    public static FullName splitName(String fullName){
        FullName name = new FullName();
        String[] parts = fullName.trim().split("\\s+");
        if (parts.length == 0) {
            return name;
        }
        if (parts.length == 1) {
            name.setFirstName(parts[0]);
            return name;
        }
        name.setFirstName(String.join(" ", java.util.Arrays.copyOfRange(parts, 0, parts.length - 1)));
        name.setLastName(parts[parts.length - 1]);

        return name;
    }

}
