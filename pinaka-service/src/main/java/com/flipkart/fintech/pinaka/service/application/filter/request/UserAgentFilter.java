package com.flipkart.fintech.pinaka.service.application.filter.request;

import com.flipkart.fintech.filter.RequestContext;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pinaka.service.application.Constants;
import java.io.IOException;
import java.util.Objects;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import lombok.CustomLog;

@CustomLog
public class UserAgentFilter implements ContainerRequestFilter {

  @Override
  public void filter(ContainerRequestContext containerRequestContext) throws IOException {
    String userAgent = containerRequestContext.getHeaderString(Constants.X_USER_AGENT);
    if (Objects.isNull(RequestContextThreadLocal.REQUEST_CONTEXT.get())) {
      log.info("RequestContextThreadLocal's RequestContext is null");
      RequestContextThreadLocal.setRequestContext(new RequestContext(null, null, null, null, false));
    }
    RequestContextThreadLocal.REQUEST_CONTEXT.get().setHeader(Constants.X_USER_AGENT, userAgent);
  }
}
