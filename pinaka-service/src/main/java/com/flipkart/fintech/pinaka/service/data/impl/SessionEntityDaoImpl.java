package com.flipkart.fintech.pinaka.service.data.impl;

import com.flipkart.fintech.pinaka.api.enums.SessionType;
import com.flipkart.fintech.pinaka.service.data.SessionEntityDao;
import com.flipkart.fintech.pinaka.service.data.model.SessionEntity;
import com.google.inject.Inject;
import io.dropwizard.hibernate.AbstractDAO;
import org.hibernate.Criteria;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 * Created by su<PERSON><PERSON><PERSON>.r on 31/08/17.
 */
public class SessionEntityDaoImpl extends AbstractDAO<SessionEntity> implements SessionEntityDao{

    @Inject
    public SessionEntityDaoImpl(SessionFactory sessionFactory) {
        super(sessionFactory);
    }


    @Override
    public SessionEntity saveOrUpdate(SessionEntity entity) {
        return persist(entity);
    }

    @Override
    public List<SessionEntity> getByTypeAndRefId(SessionType type, String id) {
        Criteria criteria = criteria().add(Restrictions.eq("type", type))
                .add(Restrictions.eq("referenceId", id));
        return list(criteria);
    }

}
