package com.flipkart.fintech.pinaka.service.pagehandler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.request.v6.PageServiceRequest;
import com.flipkart.fintech.pinaka.api.request.v6.SlotInfo;
import com.flipkart.fintech.pinaka.api.response.idfc.WidgetEntity;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.core.v6.DocumentService;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.*;
import com.flipkart.fintech.pinaka.service.pagedatasource.repeatloan.RepeatLoanOfferPageDataSource;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantCheckUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.*;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.LoadingScreenTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.NamePageFormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.ReviewPage1FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.ReviewPage2FormTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.submitbuttonwidget.SubmitButtonWidgetTransformerFactory;
import com.flipkart.fintech.profile.pagehandler.widgettransformer.MarkUpWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.google.inject.name.Named;
import com.supermoney.ams.bridge.AmsBridge;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nullable;
import javax.inject.Inject;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.LEAD_V4_LANDING_PAGE;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.ENABLE_PL_MAINTENANCE;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.UNALLOCATED_USER;

@CustomLog
public class PageHandler {

  private final AmsBridge amsBridge;
  private final DocumentService documentService;
  private final RepeatLoanOfferPageDataSource repeatLoanOfferPageDataSource;
  private final RepeatLoanOfferPageTransformer repeatLoanOfferPageTransformer;
  private final SubmitButtonWidgetTransformerFactory submitButtonWidgetTransformerFactory;

  private final DynamicBucket dynamicBucket;
  private final Decrypter decrypter;

  private final SandboxOfferScreenTransformer sandboxOfferScreenTransformer;

  private final ErrorScreenTransformer errorScreenTransformer;

  private final Map<String, BasicDetailsFormTransformer.FormFields> formFieldsMap;
  private final Map<String, NamePageFormTransformer.FormFields> namePageFormFieldsMap;
  private final Map<String, ReviewPage1FormTransformer.FormFields> initialUserDataFormMap;

  private final PageDataSourceFactory pageDataSourceFactory;
  private final String noLenderAssignedAnnouncement;

  private final SuccessScreenTransformer successScreenTransformer;

  private final CustomScreenTransformer customScreenTransformer;
  private final Map<String, ReviewPage2FormTransformer.FormField> initialUserDataFormMapPage2;
  private final GroupedFormV4WidgetTransformer groupedFormV4WidgetTransformer;
  private final CardWidgetTransformer cardWidgetTransformer;
  private final List<String> loadingStates = Arrays.asList("pan", "profile", "pin");

  @Inject
  public PageHandler(AmsBridge amsBridge, DocumentService documentService,
                     RepeatLoanOfferPageDataSource repeatLoanOfferPageDataSource,
                     RepeatLoanOfferPageTransformer repeatLoanOfferPageTransformer,
                     SubmitButtonWidgetTransformerFactory submitButtonWidgetTransformerFactory,
                     SandboxOfferScreenTransformer sandboxOfferScreenTransformer,
                     DynamicBucket dynamicBucket,
                     Decrypter decrypter,
                     ErrorScreenTransformer errorScreenTransformer,
                     @Named("dataPageScreen") Map<String, ReviewPage1FormTransformer.FormFields> initialUserDataFormMap,
                     @Named("page2Screen") Map<String, ReviewPage2FormTransformer.FormField> intialUserDataFormMapPage2,
                     @Named("noLenderAssignedAnnouncement") String noLenderAssignedAnnouncement,
                     @Named("panPageScreen") Map<String, BasicDetailsFormTransformer.FormFields> formFieldsMap,
                     @Named("plLeadNamePageScreen") Map<String, NamePageFormTransformer.FormFields> namePageFormFieldsMap,
                     PageDataSourceFactory pageDataSourceFactory,
                     SuccessScreenTransformer successScreenTransformer,
                     CustomScreenTransformer customScreenTransformer,
                     GroupedFormV4WidgetTransformer groupedFormV4WidgetTransformer,
                     CardWidgetTransformer cardWidgetTransformer) {
    this.amsBridge = amsBridge;
    this.documentService = documentService;
    this.repeatLoanOfferPageDataSource = repeatLoanOfferPageDataSource;
    this.repeatLoanOfferPageTransformer = repeatLoanOfferPageTransformer;
    this.submitButtonWidgetTransformerFactory = submitButtonWidgetTransformerFactory;
    this.dynamicBucket = dynamicBucket;
    this.decrypter = decrypter;
    this.sandboxOfferScreenTransformer = sandboxOfferScreenTransformer;
    this.errorScreenTransformer = errorScreenTransformer;
    this.initialUserDataFormMap = initialUserDataFormMap;
    this.initialUserDataFormMapPage2 = intialUserDataFormMapPage2;
    this.formFieldsMap = formFieldsMap;
    this.namePageFormFieldsMap = namePageFormFieldsMap;
    this.pageDataSourceFactory = pageDataSourceFactory;
    this.successScreenTransformer = successScreenTransformer;
    this.noLenderAssignedAnnouncement= noLenderAssignedAnnouncement;
    this.customScreenTransformer = customScreenTransformer;
    this.groupedFormV4WidgetTransformer = groupedFormV4WidgetTransformer;
    this.cardWidgetTransformer = cardWidgetTransformer;
  }

  public List<WidgetEntity> createPageHandlerResponse(String merchantId, PageServiceRequest pageServiceRequest,
      ApplicationDataResponse applicationDataResponse)
      throws PinakaException {
    List<WidgetEntity> list = new ArrayList<>();
    for (SlotInfo slotInfo : pageServiceRequest.getSlotInfoList()) {
      WidgetEntity widgetEntity = getWidgetEntity(applicationDataResponse, slotInfo, merchantId);
      if(Objects.nonNull(widgetEntity)) {
        list.add(widgetEntity);
      }
    }
    return list;
  }

  private WidgetEntity getWidgetEntity(ApplicationDataResponse applicationDataResponse,
      SlotInfo slotInfo, String merchantId)
      throws PinakaException {
    WidgetTypeV4 widgetType = slotInfo.getWidgetTypeV4();
    try {
      WidgetData widgetData = getWidgetData(applicationDataResponse, slotInfo, widgetType,merchantId);
      if(Objects.nonNull(widgetData)) {
        return new WidgetEntity(slotInfo.getSlotId(), widgetType, widgetData);
      }
      return null;
    } catch (Exception e) {
      log.error("Error while creating widget", e);
      throw new PinakaException("Exception while creating Widget", e);
    }
  }

  @Nullable
  private WidgetData getWidgetData(ApplicationDataResponse applicationDataResponse,
      SlotInfo slotInfo, WidgetTypeV4 widgetType, String merchantId)
      throws IOException, PinakaClientException, PinakaException, ParseException {
    String formType = StringUtils.isBlank(slotInfo.getFormType()) ? "INVALID": slotInfo.getFormType();
    switch (widgetType) {
      case FORM_V4: {
        if (LEAD_V4_LANDING_PAGE.equalsIgnoreCase(applicationDataResponse.getApplicationState()))
          return cardWidgetTransformer.buildWidgetGroupData(applicationDataResponse, widgetType);
        if ("LEAD_V3_PAGE_3_FORM".equalsIgnoreCase(formType) || "LEAD_V4_REVIEW_PAGE_2".equalsIgnoreCase(formType)) {
          return groupedFormV4WidgetTransformer.buildWidgetData(formType, applicationDataResponse);
        }
        return new FormWidgetTransformer(sandboxOfferScreenTransformer, formFieldsMap, initialUserDataFormMap, initialUserDataFormMapPage2, namePageFormFieldsMap, dynamicBucket, decrypter).buildWidgetData(
                Objects.requireNonNull(formType), applicationDataResponse);
      }
      case CARD_CAROUSEL:
      case CARD_SUMMARY_LIST: {
        if("LEAD_V4_APPROVED_AMOUNT_WIDGET".equals(slotInfo.getStaticContentId()) || formType.equals("LEAD_V4_APPROVED_AMOUNT_WIDGET")){
          return cardWidgetTransformer.buildWidgetGroupData(formType, applicationDataResponse);
        }
        return cardWidgetTransformer.buildWidgetGroupData(slotInfo.getSlotId(), applicationDataResponse);
      }
      case GROUPED_FORM_V4: {
        return groupedFormV4WidgetTransformer.buildWidgetData(formType, applicationDataResponse);
      }
      case LOADING_WIDGET:
        if (loadingStates.stream().anyMatch(state -> applicationDataResponse.getApplicationState().toLowerCase().contains(state)))
          return new LoadingScreenTransformer().buildWidgetGroupData(applicationDataResponse);
        return new OfferWaitScreenLoadingWidgetTransformer().buildWidgetData(
            new OfferWaitScreenDataSource().getData(applicationDataResponse));
      case LIVE_SELFIE_WIDGET:
        return new LiveSelfieTransformer().buildWidgetData(
            new LiveSelfiePageDataSource().getData(applicationDataResponse));
      case KEY_VALUE:
        return getKeyValueDataUsingInteractionKey(applicationDataResponse);
      case TEXT_V2:
        return getTextWidgetDataUsingInteractionKey(applicationDataResponse, slotInfo);
      case SUBMIT_BUTTON_WIDGET:
        if (LEAD_V4_LANDING_PAGE.equalsIgnoreCase(applicationDataResponse.getApplicationState()))
          return cardWidgetTransformer.buildWidgetGroupData(applicationDataResponse, widgetType);
        return submitButtonWidgetTransformerFactory
                .getSubmitButtonWidgetTransformer(slotInfo.getContentId(), applicationDataResponse)
            .buildSubmitButtonWidgetData(applicationDataResponse);
      case TABLE_V4:
        return getTableWidgetDataUsingInteractionKey(applicationDataResponse, slotInfo);
      case STATIC_HTML_SHOWCASE:
        return new KFSWidgetTransformer().buildHtmlWidgetData(
            new KFSDetailsPageDataSource().getData(applicationDataResponse));
      case ANNOUNCEMENT_V2:
        return getAnnouncementWidgetData(applicationDataResponse, slotInfo, merchantId);
      case STEPPER_WIDGET:
        return new ApplicationStatusWidgetTransformer().buildstepperWidgetData(
            new ApplicationStatusPageDataSource().getData(applicationDataResponse));
      case ACCORDION_V4:
        return new ApplicationStatusWidgetTransformer().buildaccordianWidgetData(
            new ApplicationStatusPageDataSource().getData(applicationDataResponse));
      case BANNER:
        return pageDataSourceFactory.getBannerWidgetTransformer(applicationDataResponse,
                getInteractionKey(applicationDataResponse), dynamicBucket)
            .buildBannerWidgetData(applicationDataResponse);
      case ANNOUNCEMENT_CARD:
        return pageDataSourceFactory.getAnnouncementCardWidgetTransformer(applicationDataResponse, slotInfo)
                .buildAnnouncementCardWidgetData(applicationDataResponse);
      case APPROVED_OFFER:
        return pageDataSourceFactory.getApprovedOfferWidgetTransformer(applicationDataResponse, slotInfo)
                .buildApprovedOfferWidgetData(applicationDataResponse);
      case RICH_MULTI_IMAGE_BANNER:
        return repeatLoanOfferPageTransformer.buildRepeatLoanOfferMultiImageBannerWidgetData(
            repeatLoanOfferPageDataSource.getData(applicationDataResponse));
      case MARKUP:
        return new MarkUpWidgetTransformer().buildMarkUpWidgetData(slotInfo.getStaticContentId());
    }
    throw new PinakaException("Unknown widgetType : " + widgetType.name());
  }

  private String getInteractionKey(ApplicationDataResponse applicationDataResponse) {
    ApplicationState state = ApplicationState.create(applicationDataResponse);
    return amsBridge.getInteractionKey(state);
  }

  private WidgetData getTextWidgetDataUsingInteractionKey(
      ApplicationDataResponse applicationDataResponse, SlotInfo slotInfo)
      throws JsonProcessingException, PinakaException, PinakaClientException {
    if (slotInfo.getContentId() != null && slotInfo.getContentId().equals("LOGO")) {
      return new BankLogoTransformer().buildtextWidgetData();
    }
    String interactionKey = getInteractionKey(applicationDataResponse);
    switch (interactionKey) {
      case "KFS_SCREEN":
        return new KFSWidgetTransformer().buildtextWidgetData(
            new KFSDetailsPageDataSource().getData(applicationDataResponse));
      case "CKYC_DETAILS":
        KycDetailsPageDataSource ckycDetailsPageDataSource = new KycDetailsPageDataSource();
        ckycDetailsPageDataSource.setKyc(false);
        return new KycDetailsTransformer(documentService).buildtextV2WidgetData(false);
      case "EKYC_DETAILS":
        return new KycDetailsTransformer(documentService).buildtextV2WidgetData(true);
      case "APPLICATION_STATUS":
        return new ApplicationStatusWidgetTransformer().buildtextV2WidgetData(
            new ApplicationStatusPageDataSource().getData(applicationDataResponse));
      case "APPLICATION_STATUS_SANDBOX":
        return new ApplicationStatusWidgetTransformer().buildtextV2WidgetData(
            new ApplicationStatusSandboxPageDataSource().getData(applicationDataResponse));
    }
    throw new PinakaException("Invalid InteractionKey " + interactionKey);
  }

  private WidgetData getKeyValueDataUsingInteractionKey(
      ApplicationDataResponse applicationDataResponse)
      throws JsonProcessingException, PinakaException, PinakaClientException {
    String interactionKey = getInteractionKey(applicationDataResponse);
    switch (interactionKey) {
      case "CKYC_DETAILS":
        KycDetailsPageDataSource ckycDetailsPageDataSource = new KycDetailsPageDataSource();
        ckycDetailsPageDataSource.setKyc(false);
        return new KycDetailsTransformer(documentService).buildkeyValueWidgetData(
            ckycDetailsPageDataSource.getData(applicationDataResponse), false);
      case "EKYC_DETAILS":
        KycDetailsPageDataSource ekycDetailsPageDataSource = new KycDetailsPageDataSource();
        ekycDetailsPageDataSource.setKyc(true);
        return new KycDetailsTransformer(documentService).buildkeyValueWidgetData(
            ekycDetailsPageDataSource.getData(applicationDataResponse), true);

    }
    throw new PinakaException("Invalid InteractionKey " + interactionKey);
  }

  private WidgetData getTableWidgetDataUsingInteractionKey(
      ApplicationDataResponse applicationDataResponse, SlotInfo slotInfo)
      throws PinakaException, PinakaClientException {
    if (slotInfo.getContentId() != null && slotInfo.getContentId().equals("REPAYMENT_SCHEDULE")) {
      return new RepaymentScheduleWidgetTransformer().buildtableWidgetData(
          new RepaymentScheduleDataSource().getData(applicationDataResponse));
    }
    String interactionKey = getInteractionKey(applicationDataResponse);
    if (interactionKey.equals("KFS_SCREEN")) {
      return new KFSWidgetTransformer().buildtableWidgetData(
          new KFSDetailsPageDataSource().getData(applicationDataResponse), slotInfo);
    }
    throw new PinakaException("Invalid InteractionKey " + interactionKey);
  }

    private WidgetData getAnnouncementWidgetData(ApplicationDataResponse applicationDataResponse,
        SlotInfo slotInfo, String merchantId)
            throws PinakaException, PinakaClientException, JsonProcessingException {
    if (Boolean.TRUE.equals(dynamicBucket.getBoolean(ENABLE_PL_MAINTENANCE))) {
          return new MaintenanceScreenTransformer().buildMaintenanceScreenWidgetData();
        }
      if(UNALLOCATED_USER.equals(slotInfo.getContentId())){
        return ObjectMapperUtil.get().readValue(noLenderAssignedAnnouncement, AnnouncementV2WidgetData.class);
      }
        String interactionKey = getInteractionKey(applicationDataResponse);
        switch(interactionKey)
        {
            case "OFFER_SCREEN":
                return new OfferScreenTransformer().buildOfferScreen(applicationDataResponse);
            case "REJECT_SCREEN":
                return new RejectScreenTransformer().buildRejectScreen(applicationDataResponse);
            case "EMANDATE_RETRY_TIMER":
                return errorScreenTransformer.buildemandateWaitingScreen();
            case "GENERATE_ETB_OTP_SCREEN":
                return new ETBAnnouncementTransformer().buildWidgetData();
            case "ETB_OTP_VERIFICATION":
                return new ETBAnnouncementTransformer().buildVerifyOtpWidgetData();
            case "REJECTED":
            case "EXPIRED":
            case "LENDER_FAILURE":
                return errorScreenTransformer.buildofferRejectionErrorWidgetData(applicationDataResponse, slotInfo);
            case "CREATE_PROFILE_END":
              return ObjectMapperUtil.get().readValue(noLenderAssignedAnnouncement, AnnouncementV2WidgetData.class);
          case "RETRY_SCREEN":
          case "RETRY_SCREEN_OFFER":
          case "RETRY_SCREEN_1":
          case "RETRY_SCREEN_2":
            return new RetryScreenWidgetTransformer().buildRetryScreen(applicationDataResponse);

            case "ONBOARD_RETRY_SCREEN":
                return new OnBoardRetryAnnouncementTransformer().buildWidgetData(applicationDataResponse);
            case "SUCCESS":
                return successScreenTransformer.buildWidgetData(applicationDataResponse);
            case "CUSTOM_SCREEN":
              return customScreenTransformer.buildWidgetData(applicationDataResponse);
          default:
							if (!MerchantCheckUtils.isMerchantAllowedForJourney(merchantId,
									applicationDataResponse.getMerchantId())) {
									return ObjectMapperUtil.get()
											.readValue(noLenderAssignedAnnouncement, AnnouncementV2WidgetData.class);
							}
        }
        throw new PinakaException("Invalid InteractionKey " + interactionKey);

    }
}
