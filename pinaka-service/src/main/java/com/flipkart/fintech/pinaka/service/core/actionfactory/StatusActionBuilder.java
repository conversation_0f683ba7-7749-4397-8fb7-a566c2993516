package com.flipkart.fintech.pinaka.service.core.actionfactory;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.request.v6.Token;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ActionType;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.utils.TokenUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import java.net.URISyntaxException;
import javax.inject.Inject;

import com.supermoney.ams.bridge.exceptions.InvalidMerchantException;
import com.supermoney.ams.bridge.helpers.URLHelper;
import lombok.CustomLog;
import org.apache.http.client.utils.URIBuilder;

@CustomLog
public class StatusActionBuilder {

  private final URLHelper plURLHelper;

  @Inject
  public StatusActionBuilder(URLHelper plURLHelper) {
    this.plURLHelper = plURLHelper;
  }

  public Action getAction(ApplicationDataResponse applicationDataResponse, MerchantUser merchantUser) throws URISyntaxException, JsonProcessingException, InvalidMerchantException {
    String url = createStatusPageUrl(applicationDataResponse);
    return new Action(url, ActionType.WEB_VIEW);
  }

  private String createStatusPageUrl(ApplicationDataResponse applicationDataResponse)
      throws JsonProcessingException, URISyntaxException, InvalidMerchantException {
    String applicationId = applicationDataResponse.getApplicationId();
    String encrypt = createEncryptedToken(applicationId);
    String applicationStatus = null;
    applicationStatus = applicationDataResponse.getApplicationState();
    String merchantId = applicationDataResponse.getMerchantId();
    String plStatusURL = plURLHelper.getPLStatusUrl(merchantId);
    return new URIBuilder(plStatusURL)
        .addParameter(PLConstants.TOKEN, encrypt)
        .addParameter(PinakaConstants.PLConstants.APPLICATION_ID, applicationId)
        .addParameter(PinakaConstants.PLConstants.CUSTOMER_STATUS, applicationStatus)
        .addParameter(PLConstants.MERCHANT, merchantId)
        .toString();
  }

  private String createEncryptedToken(String applicationId) throws JsonProcessingException {
    Token token = Token.builder()
        .applicationId(applicationId)
        .operation(null)
        .build();
    return TokenUtils.getEncryptedToken(token);
  }
}