package com.flipkart.fintech.pinaka.service.core.impl;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.service.core.SecurityService;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.security.gibraltar.GibraltarService;
import com.flipkart.fintech.security.gibraltar.GibraltarServiceException;
import com.flipkart.gibraltar.api.response.GibraltarPublicKey;
import com.google.inject.Inject;
import java.util.Base64;
import java.util.function.Function;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by sujeet<PERSON>.r on 25/01/18.
 */
@CustomLog
public class SecurityServiceImpl implements SecurityService {

  private GibraltarService gibraltarService;

  @Inject
  public SecurityServiceImpl(GibraltarService gibraltarService) {
    this.gibraltarService = gibraltarService;
  }

  @Override
  @Timed
  @ExceptionMetered
  public SecurityKeyResponse getKey() throws PinakaException {
    GibraltarPublicKey key;
    try {
      key = gibraltarService.getKey();
      if (StringUtils.isBlank(key.getKeyRef()) || StringUtils.isBlank(key.getKeyValue())) {
        log.info(
            "Empty keyRef or keyValue in object of GibraltarPublicKey, keyRef: {} keyValue: {} ",
            key.getKeyRef(), key.getKeyValue());
      }
    } catch (GibraltarServiceException e) {
      throw new PinakaException(e.getMessage(), e);
    }
    log.info("got key from gibraltar client");
    return gibraltarKeyToSecurityKeyApi.apply(key);
  }

  Function<GibraltarPublicKey, SecurityKeyResponse> gibraltarKeyToSecurityKeyApi =
      key -> {
        SecurityKeyResponse keyResponse = new SecurityKeyResponse();
        keyResponse.setKeyRef(key.getKeyRef());
        //returning Base64 encoded string to support UI encryption
        String base64EncodeKey =
            new String(
                Base64.getEncoder().encode(Base64.getUrlDecoder().decode(key.getKeyValue())));
        keyResponse.setPublicKey(base64EncodeKey);
        return keyResponse;
      };
}
