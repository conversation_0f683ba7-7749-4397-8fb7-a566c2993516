package com.flipkart.fintech.pinaka.service.core.v7;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.ams.LEAD_APPLICATION_TYPES;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pinaka.api.enums.JourneyContext;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.LoanApplication;
import com.flipkart.fintech.pinaka.api.request.v6.UserDetails;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScores;
import com.flipkart.fintech.pinaka.common.utils.UserProfileInsightsUtils;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.profile.client.ProfileClient;
import com.flipkart.fintech.profile.dao.ProfileDao;
import com.flipkart.fintech.profile.model.Profile;
import com.flipkart.fintech.profile.response.ProfileBasicDetailResponse;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import lombok.CustomLog;

import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.*;

@CustomLog
public class CreateLeadRequestFactory {
    private final ObjectMapper objectMapper;
    private final UserProfileScores userProfileScores;
    private final ProfileClient profileClient;
    private final DynamicBucket dynamicBucket;

    @Inject
    public CreateLeadRequestFactory(ObjectMapper objectMapper, UserProfileScores userProfileScores, ProfileClient profileClient, DynamicBucket dynamicBucket) {
        this.objectMapper = objectMapper;
        this.userProfileScores = userProfileScores;
        this.profileClient = profileClient;
        this.dynamicBucket = dynamicBucket;
    }

    public CreateApplicationRequest create(MerchantUser merchantUser, ProductType productType, String requestId, String applicationType) throws DataEnrichmentException {

        UserProfileResponseV3 userProfileResponseV3 = new UserProfileResponseV3();
        FetchUserProfileResponse fetchUserProfileResponse = new FetchUserProfileResponse();

        Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
        if(dexterFlag){
            fetchUserProfileResponse = userProfileScores.getUserProfileByDexter(requestId, merchantUser);
        }else{
            userProfileResponseV3 = userProfileScores.getUserProfile(merchantUser);
        }
        String shippingAddressId = UserProfileInsightsUtils.getShippingAddressId(userProfileResponseV3, fetchUserProfileResponse, dynamicBucket);
        // TODO remove this to have caller send these details
        Double binScore = userProfileScores.getUserBinScore(merchantUser, requestId);


        Map<String, VariableData> workflowData = new HashMap<>();
        workflowData.put("shipping_address_id", new VariableData(false, shippingAddressId));
        workflowData.put("bin_score", new VariableData(false, binScore));
        workflowData.put("account_id", new VariableData(false, merchantUser.getMerchantUserId()));
        workflowData.put("category_type", new VariableData(false, applicationType));
        workflowData.put("merchant_id", new VariableData(false, merchantUser.getMerchantKey()));
        ProfileBasicDetailResponse profileBasicDetailResponse = null;
        try {
             profileBasicDetailResponse = profileClient.getBasicProfile(merchantUser.getSmUserId());
        } catch (Exception e){
            log.error("Error fetching profile for sm user : ", merchantUser.getSmUserId());
        }


        if (Objects.nonNull(profileBasicDetailResponse)) {
            workflowData.put("verified_first_name", new VariableData(false, profileBasicDetailResponse.getFirstName()));
            workflowData.put("verified_last_name", new VariableData(false, profileBasicDetailResponse.getLastName()));
            workflowData.put("match_score", new VariableData(false, profileBasicDetailResponse.getMatchScore()));
            workflowData.put("name_match", new VariableData(false, profileBasicDetailResponse.getNameMatch()));
        } else {
            workflowData.put("verified_first_name", new VariableData(false, null));
            workflowData.put("verified_last_name", new VariableData(false, null));
            workflowData.put("match_score", new VariableData(false, null));
            workflowData.put("name_match", new VariableData(false, null));
        }

        UserDetails userDetails = new UserDetails();
        userDetails.setShippingAddressId(shippingAddressId);
        userDetails.setBinScore(String.valueOf(binScore));

        Map<String, Object> applicationData = createApplicationData(merchantUser, productType, userDetails);

        CreateApplicationRequest createApplicationRequest = new CreateApplicationRequest();
        createApplicationRequest.setExternalUserId(merchantUser.getMerchantUserId());
        createApplicationRequest.setSmUserId(merchantUser.getSmUserId());
        createApplicationRequest.setApplicationType(applicationType);
        createApplicationRequest.setWorkflowData(workflowData);
        createApplicationRequest.setApplicationData(applicationData);
        createApplicationRequest.setMerchantId(merchantUser.getMerchantKey());
        createApplicationRequest.setFinancialProvider(applicationType);
        return createApplicationRequest;
    }


    private Map<String, Object> createApplicationData(MerchantUser user, ProductType productType, UserDetails userDetails) {
        Format formatter = new SimpleDateFormat(PinakaConstants.DATE_FORMAT_PATTERN);
        String applicationCreatedAt = formatter.format(new Date());

        LoanApplication loanApplicationData = LoanApplication.builder()
                .externalUserId(user.getMerchantUserId())
                .smUserId(user.getSmUserId())
                .userDetails(userDetails)
                .journeyContext(JourneyContext.LEAD_APPLICATION_JOURNEY.name())
                .productType(String.valueOf(productType))
                .applicationCreatedAt(applicationCreatedAt)
                .build();

        Map<String, Object> applicationData = objectMapper.convertValue(loanApplicationData, Map.class);
        return applicationData;
    }

}
