package com.flipkart.fintech.pinaka.service.core;

import com.flipkart.fintech.ardour.api.models.EncryptionKeyData;
import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;

/**
 * Created by <PERSON><PERSON><PERSON>kumar.r on 25/01/18.
 */
public interface SecurityService {

    SecurityKeyResponse getKey() throws PinakaException;
}
