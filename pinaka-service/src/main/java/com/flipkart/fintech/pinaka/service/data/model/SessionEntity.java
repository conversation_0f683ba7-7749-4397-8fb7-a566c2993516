package com.flipkart.fintech.pinaka.service.data.model;

import com.flipkart.fintech.pinaka.api.enums.SessionType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 20/08/18.
 */
@Entity
@Table(name = "session")
public class SessionEntity extends BaseEntity{

    @Column(name = "session_type")
    @Enumerated(EnumType.STRING)
    private SessionType type;

    @Column(name = "reference_id")
    private String referenceId;

    @Column(name = "session_key")
    private String sessionKey;

    @Column(name = "expires_at")
    private Timestamp expiresAt;

    public SessionType getType() {
        return type;
    }

    public void setType(SessionType type) {
        this.type = type;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public Timestamp getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(Timestamp expiresAt) {
        this.expiresAt = expiresAt;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }
}
