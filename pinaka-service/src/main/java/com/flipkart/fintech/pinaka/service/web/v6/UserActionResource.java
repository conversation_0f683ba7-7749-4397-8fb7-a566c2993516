package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.v6.ErrorOperation;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Status;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.core.v6.UserActionHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.google.inject.Inject;
import de.client.shade.javax.validation.Valid;
import io.dropwizard.hibernate.UnitOfWork;
import io.swagger.annotations.ApiOperation;
import javax.validation.constraints.NotNull;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.CustomLog;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/pl/user-action")
public class UserActionResource {

  private final UserActionHandler actionHandler;

  @Inject
  public UserActionResource(UserActionHandler actionHandler) {
    this.actionHandler = actionHandler;
  }

  @POST
  @Timed
  @ExceptionMetered
  @ApiOperation("UserAction Submit")
  @Path("/submit")
  @UnitOfWork
  @UnitOfWork(value="profile_service")
  public PageActionResponse submit(@Valid UserActionRequest submitRequest,
                                   @HeaderParam("X-User-Agent") String userAgent,
                                   @NotNull @HeaderParam("X-Request-Id") String requestId) {
    PageActionResponse pageActionResponse = new PageActionResponse();
    log.info("User Submit Request received for request {}",submitRequest);
    try {
      pageActionResponse = actionHandler.submit(submitRequest, requestId,userAgent);
    } catch (PinakaException ex) {
      log.error("Error while submitting the page for applicationId: {}, accountId: {}: {}", submitRequest.getAccountId(),
          submitRequest.getTaskId(), ex.getMessage());
      pageActionResponse.setActionSuccess(false);
      ErrorOperation errorOperation = new ErrorOperation();
      errorOperation.setMessage(Status.RETRY_WITHOUT_EDIT + PinakaConstants.PIPE_SEPARATOR + PinakaConstants.PLConstants.RETRY_WITHOUT_EDIT_MESSAGE);
      pageActionResponse.setError(errorOperation);
      PinakaMetricRegistry.getMetricRegistry().meter(this.getClass().getName()+"_UserActionResource_submit_RETRY_WITHOUT_EDIT").mark();
    } catch (Exception e) {
      log.error("Error in submit for applicationId: {}, accountId: {}: {}", submitRequest.getApplicationId(),
              submitRequest.getAccountId(), e.getMessage(), e);
      throw e;
    }
    log.info("returning following response: {} for app_id:{}",pageActionResponse,submitRequest.getApplicationId());

    return pageActionResponse;
  }
}
