package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.service.response.AuthTokenResponse;
import com.flipkart.fintech.pinaka.service.response.LiveSelfiePageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.http.NameValuePair;

public class LiveSelfiePageDataSource implements PageDataSource<LiveSelfiePageDataSourceResponse>{

    @Inject
    private static ConfigUtils configUtils;
    @Override
    public LiveSelfiePageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        AuthTokenResponse authTokenResponse = ObjectMapperUtil.get()
                .convertValue(applicationDataResponse.getApplicationData().get("generateSelfieToken"),
                        AuthTokenResponse.class);
        LiveSelfiePageDataSourceResponse liveSelfiePageDataSourceResponse = new LiveSelfiePageDataSourceResponse();
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        liveSelfiePageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        liveSelfiePageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        liveSelfiePageDataSourceResponse.setAuthTokenResponse(authTokenResponse);
        liveSelfiePageDataSourceResponse.setApplicationId(applicationDataResponse.getApplicationId());
        return liveSelfiePageDataSourceResponse;
    }
}