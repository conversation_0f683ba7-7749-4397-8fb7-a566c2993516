package com.flipkart.fintech.pinaka.service.core.v6;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.KfsRequest;
import com.flipkart.fintech.pinaka.api.response.v6.KfsResponse;
import com.flipkart.fintech.pinaka.api.response.v6.StatusResponse;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.api.response.v6.ApplicationStatusResponse;


public interface BulkDataRequestHandler {
    KfsResponse getKfsDetails(KfsRequest kfsRequest, String merchantId, String requestId) throws PinakaException;
    StatusResponse getStatus(String applicationId, String requestId, MerchantUser merchantUser) throws PinakaException;
    ApplicationStatusResponse getApplicationStatus(String applicationId, String requestId, MerchantUser merchantUser) throws PinakaException;
}
