package com.flipkart.fintech.pinaka.service.utils.FormUtils;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;

public class PIIData {
    @JsonProperty("piiDataMap")
    private Map<String,List<String>> piiDataMap;



    public boolean containsFormKey(String formKey) {
        return piiDataMap.containsKey(formKey);
    }

    public boolean containsField(String formKey,String field) {
        return piiDataMap.get(formKey).contains(field);
    }
}
