package com.flipkart.fintech.pinaka.service.core.impl;

import com.flipkart.fintech.pinaka.api.enums.SessionType;
import com.flipkart.fintech.pinaka.api.model.Session;
import com.flipkart.fintech.pinaka.service.core.SessionService;
import com.flipkart.fintech.pinaka.service.data.SessionEntityDao;
import com.flipkart.fintech.pinaka.service.data.model.SessionEntity;
import com.google.inject.Inject;
import org.apache.commons.collections.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

/**
 * Created by su<PERSON><PERSON><PERSON>.r on 20/08/18.
 */
public class SessionServiceImpl implements SessionService {

    private final SessionEntityDao sessionDao;

    @Inject
    public SessionServiceImpl(SessionEntityDao sessionDao){
        this.sessionDao = sessionDao;
    }

    @Override
    public void create(Session session) {
        sessionDao.saveOrUpdate(apiToEntity.apply(session));
    }

    @Override
    public Session getActiveSession(SessionType type, String refId) {
        List<SessionEntity> sessionEntityList = sessionDao.getByTypeAndRefId(type, refId);
        Session session = null;
        if(!CollectionUtils.isEmpty(sessionEntityList)) {
            Timestamp now = new Timestamp(System.currentTimeMillis());
            Optional<SessionEntity> optional =
                    sessionEntityList.stream().filter(entity -> entity.getExpiresAt().after(now)).findFirst();
            if (optional.isPresent()) {
                session = entityToApi.apply(optional.get());
            }
        }
        return session;
    }

    Function<Session, SessionEntity> apiToEntity = session -> {
        SessionEntity entity = new SessionEntity();
        entity.setExpiresAt(session.getExpiresAt());
        entity.setReferenceId(session.getReferenceId());
        entity.setType(session.getType());
        entity.setSessionKey(session.getSessionKey());
        return entity;
    };

    Function<SessionEntity, Session> entityToApi = entity -> {
        Session session = new Session();
        session.setExpiresAt(entity.getExpiresAt());
        session.setReferenceId(entity.getReferenceId());
        session.setType(entity.getType());
        session.setSessionKey(entity.getSessionKey());
        return session;
    };
}
