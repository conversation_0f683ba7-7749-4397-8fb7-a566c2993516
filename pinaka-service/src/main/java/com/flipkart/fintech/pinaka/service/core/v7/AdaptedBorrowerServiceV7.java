package com.flipkart.fintech.pinaka.service.core.v7;

import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.core.BorrowerService;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import java.util.List;
import javax.inject.Inject;

public class AdaptedBorrowerServiceV7 implements BorrowerServiceV7 {

  private BorrowerService borrowerService;

  @Inject
  public AdaptedBorrowerServiceV7(BorrowerService borrowerService) {
    this.borrowerService = borrowerService;
  }

  @Override
  public List<BorrowerEntity> getActiveBorrowers(MerchantUser user,
      List<ProductType> productTypeList) {
    return borrowerService.getActiveBorrowers(user.getMerchantUserId(), user.getMerchantKey(), productTypeList);
  }

}
