package com.flipkart.fintech.pinaka.service.adaptor.v6;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.UserDetails;
import com.flipkart.fintech.pinaka.api.request.v6.*;
import com.flipkart.fintech.pinaka.api.response.v6.*;
import com.flipkart.fintech.pinaka.service.adaptor.v6.formFields.FormFieldContent;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.pinaka.service.utils.EncryptionUtil;
import com.flipkart.fintech.pinaka.service.utils.v6.FormUtils;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.google.inject.Inject;
import com.supermoney.ams.bridge.exceptions.InvalidMerchantException;
import com.supermoney.ams.bridge.helpers.URLHelper;
import lombok.CustomLog;
import org.apache.http.client.utils.URIBuilder;

import java.net.URISyntaxException;
import java.util.*;


@CustomLog
public class OfferDetailsAdapter implements IPageAdapter {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final URLHelper plURLHelper;

    @Inject
    public OfferDetailsAdapter(URLHelper plURLHelper) {
        this.plURLHelper = plURLHelper;
    }

    @Override
    public Map<String, VariableData> constructWorkflowData(LoanApplication loanApplication, Map<String, Object> formData,
                                                           MerchantUser merchantUser, String applicationId, String requestId) throws DataEnrichmentException, InvalidMerchantException {
        Map<String, VariableData> variableDataMap = new HashMap<>();

        Map<String, Object> responseObj = (Map<String, Object>) formData.get("offer");
        LoanDetails offer = objectMapper.convertValue(responseObj, new TypeReference<LoanDetails>() {
        });

        variableDataMap.put(PinakaConstants.PLWorkflowVariable.LOAN_AMOUNT, new VariableData(false, offer.getLoanAmount()));
        variableDataMap.put(PinakaConstants.PLWorkflowVariable.ROI, new VariableData(false, offer.getRoi()));
        variableDataMap.put(PinakaConstants.PLWorkflowVariable.EMI, new VariableData(false, offer.getEmi().getAmount()));
        variableDataMap.put(PinakaConstants.PLWorkflowVariable.NET_DISBURSAL_AMOUNT, new VariableData(false, offer.getNetDisbursalAmount()));
        String callbackUri = "";
        try {
            Token token = Token.builder().applicationId(applicationId).operation(null).build();
            callbackUri = new URIBuilder(plURLHelper.getPLLenderCallbackUrl(merchantUser.getMerchantKey()))
                    .addParameter(PinakaConstants.PLConstants.TOKEN, EncryptionUtil.encryptWithAes(objectMapper.writeValueAsString(token), PinakaConstants.PLConstants.PL_ENCRYPTION_KEY))
                    .toString();
        } catch (URISyntaxException | JsonProcessingException e) {
            log.error("Error while creating token for PL callback url");
        }
        variableDataMap.put(PinakaConstants.PLWorkflowVariable.CALLBACK_URL, new VariableData(false, callbackUri));
        variableDataMap.put(PinakaConstants.PLWorkflowVariable.OFFER_CONSENT_PROVIDED, new VariableData(false, formData.get(PageConstant.PL_OFFER_SCREEN_FORM.OFFER_CONSENT)));
        variableDataMap.put(PinakaConstants.PLWorkflowVariable.LENDER_APPLICATION_ID, new VariableData(false, loanApplication.getLenderDetails().getLenderApplicationId()));
        variableDataMap.put(PinakaConstants.PLWorkflowVariable.TENURE, new VariableData(false, offer.getTenure()));
        variableDataMap.put(PinakaConstants.PLWorkflowVariable.CHARGES, new VariableData(false, offer.getCharges()));

        return variableDataMap;
    }

    @Override
    public Map<String, Object> constructApplicationData(LoanApplication loanApplication, Map<String, Object> formData,
                                                        MerchantUser merchantUser, String applicationId) throws DataEnrichmentException {
        LenderDetails lenderDetails = Objects.isNull(loanApplication.getLenderDetails()) ? new LenderDetails() : loanApplication.getLenderDetails();
        List<Offer> submittedOffer = new ArrayList<>();

        Map<String, Object> responseObj = (Map<String, Object>) formData.get(PinakaConstants.PLConstants.OFFER);
        LoanDetails loanDetails = objectMapper.convertValue(responseObj, new TypeReference<LoanDetails>() {
        });

        Offer offer = new Offer();
        offer.setNetDisbursalAmount(loanDetails.getNetDisbursalAmount());
        Emi emi = loanDetails.getEmi();
        offer.setEmi(emi.getAmount());
        offer.setLoanAmount(loanDetails.getLoanAmount());
        offer.setTenure(loanDetails.getTenure());
        offer.setRoi(loanDetails.getRoi());

        Map<Charge, ChargeDetails> chargeDetailsMap = loanDetails.getCharges();
        List<ChargeBreakup> charges = new ArrayList<>();
        for (Map.Entry<Charge, ChargeDetails> entity : chargeDetailsMap.entrySet()) {
            ChargeBreakup chargeBreakup = new ChargeBreakup();

            if (Charge.PROCESSING_FEE.equals(entity.getKey())) {
                ChargeDetails processingFeeCharge = chargeDetailsMap.get(Charge.PROCESSING_FEE);
                chargeBreakup.setChargeType(ChargeType.PROCESSING_FEE);
                chargeBreakup.setAmount(processingFeeCharge.getAmount());
                chargeBreakup.setGstAmount(processingFeeCharge.getGstAmount());
                chargeBreakup.setTotalAmount(processingFeeCharge.getTotalAmount());
            } else {
                ChargeDetails stampDutyCharge = chargeDetailsMap.get(Charge.STAMP_DUTY);
                chargeBreakup.setChargeType(ChargeType.STAMP_DUTY);
                chargeBreakup.setAmount(stampDutyCharge.getAmount());
                chargeBreakup.setGstAmount(stampDutyCharge.getGstAmount());
                chargeBreakup.setTotalAmount(stampDutyCharge.getTotalAmount());
            }
            charges.add(chargeBreakup);
        }
        offer.setCharges(charges);
        submittedOffer.add(offer);
        lenderDetails.setSubmittedOffer(submittedOffer);
        loanApplication.setLenderDetails(lenderDetails);
        UserDetails userDetails = Objects.isNull(loanApplication.getUserDetails()) ? new UserDetails() : loanApplication.getUserDetails();
        userDetails.setOfferConsentProvided((boolean) formData.get(PageConstant.PL_OFFER_SCREEN_FORM.OFFER_CONSENT));

        Map<String, Object> consentDetailsObj = (Map<String, Object>) formData.get(PinakaConstants.PLConstants.CONSENT_DETAILS);
        ConsentDetails consentDetails = objectMapper.convertValue(consentDetailsObj, new TypeReference<ConsentDetails>() {
        });
        List<ConsentDetails> consentDetailsList = Objects.isNull(loanApplication.getConsentDetailsList()) ? new ArrayList<>() : loanApplication.getConsentDetailsList();
        consentDetailsList.add(consentDetails);
        loanApplication.setConsentDetailsList(consentDetailsList);

        loanApplication.setUserDetails(userDetails);

        Map<String, Object> applicationDataMap = objectMapper.convertValue(loanApplication, Map.class);

        return applicationDataMap;
    }

    @Override
    public List<DataEnumResponse> constructPageResponse(List<DataEnum> dataEnumList, LoanApplication loanApplication, MerchantUser merchantUser, FormType formType) throws PinakaException {
        List<DataEnumResponse> dataEnumResponseList = new ArrayList<>();
        for (DataEnum dataEnum : dataEnumList) {
            DataEnumResponse dataEnumResponse;
            switch (dataEnum) {
                case FORM:
                    dataEnumResponse = constructFormDataWidget(loanApplication);
                    break;
                case HELP:
                    dataEnumResponse = PageConstant.constructHelpWidget();
                    break;
                case ANNOUNCEMENT:
                    dataEnumResponse = constructAnnouncementWidget();
                    break;
                default:
                    throw new PinakaException("the requested widget is not supported for OfferDetailsPage : " + dataEnum.name());
            }
            dataEnumResponseList.add(dataEnumResponse);
        }

        return dataEnumResponseList;
    }

    private AnnouncementDataEnumResponse constructAnnouncementWidget() {
        AnnouncementDataEnumResponse announcementDataEnumResponse
                = AnnouncementDataEnumResponse.builder()
                .dataEnum(DataEnum.ANNOUNCEMENT)
                .url(PageConstant.PL_OFFER_SCREEN_FORM.announcementImageURL)
                .aspectRatio(PageConstant.PL_OFFER_SCREEN_FORM.announcementImageAspectRatio)
                .build();

        return announcementDataEnumResponse;
    }

    private FormDataEnumResponse constructFormDataWidget(LoanApplication loanApplication) throws PinakaException {

        if (Objects.isNull(loanApplication.getLenderDetails().getGeneratedOffer().get(0))) {
            throw new PinakaException("Offer is missing, please generate and try again");
        }
        com.flipkart.fintech.pandora.api.model.response.plOnboarding.Offer offer = loanApplication.getLenderDetails().getGeneratedOffer().get(0);
        FormField loanAmountValue = FormUtils.getFormField(FormFieldContent.LOAN_AMOUNT);
        loanAmountValue.setMinValue(String.valueOf(offer.getLoanAmountDetail().getMin()));
        loanAmountValue.setMaxValue(String.valueOf(offer.getLoanAmountDetail().getMax()));
        loanAmountValue.setDefaultValue(String.valueOf(offer.getLoanAmountDetail().getMax()));
        loanAmountValue.setTitle(PageConstant.PL_OFFER_SCREEN_FORM.loanAmountTitle);

        FormField loanAmountRangeValue = FormUtils.getFormField(FormFieldContent.LOAN_AMOUNT_RANGE);
        loanAmountRangeValue.setMaxValue(String.valueOf(offer.getLoanAmountDetail().getMax()));
        loanAmountRangeValue.setMinValue(String.valueOf(offer.getLoanAmountDetail().getMin()));
        loanAmountRangeValue.setDefaultValue(String.valueOf(offer.getLoanAmountDetail().getMax()));

        FormField loanEmiValue = FormUtils.getFormField(FormFieldContent.LOAN_EMI);
        loanEmiValue.setMinValue(String.valueOf(offer.getTenureDetails().getMin()));
        loanEmiValue.setMaxValue(String.valueOf(offer.getTenureDetails().getMax()));
        loanEmiValue.setTenureUnit(TenureUnit.YEAR);
        loanEmiValue.setTitle(PageConstant.PL_OFFER_SCREEN_FORM.loanEmiTitle);
        loanEmiValue.setSubtitle(PageConstant.PL_OFFER_SCREEN_FORM.loanEmiSubTitle);
        loanEmiValue.setInterest(offer.getRoi());
        loanEmiValue.setNote(PageConstant.PL_OFFER_SCREEN_FORM.loanEmiNote);
        loanEmiValue.setTenureStepSize(offer.getTenureDetails().getStepSize());
        loanEmiValue.setRowCount(PinakaConstants.PLConstants.ROW_COUNT);
        loanEmiValue.setEmiThreshold(offer.getEmiThreshold());
        List<Charges> chargeList = new ArrayList<>();
        for (com.flipkart.fintech.pandora.api.model.response.plOnboarding.Charges charge : offer.getCharge()) {
            Charges charges = Charges.builder().chargeType(ChargeType.valueOf(charge.getChargeType().name()))
                    .type(RateType.valueOf(charge.getType().name()))
                    .gst(charge.getGst())
                    .value(charge.getValue())
                    .build();
            chargeList.add(charges);
        }
        Charges charges = Charges.builder().chargeType(ChargeType.FLIPKART_CHARGES)
                .type(RateType.FLAT)
                .value(PageConstant.PL_OFFER_SCREEN_FORM.flipkartChargesValue)
                .gst(PageConstant.PL_OFFER_SCREEN_FORM.flipkartChargesGST)
                .build();
        chargeList.add(charges);
        loanEmiValue.setCharges(chargeList);

        FormField kfsLaunchButton = FormUtils.getFormField(FormFieldContent.SUBMIT_DETAILS);
        kfsLaunchButton.setUrl(PageConstant.KfsUrl);
        kfsLaunchButton.setTitle(PageConstant.PL_OFFER_SCREEN_FORM.kfsButtonText);
        kfsLaunchButton.setActionType(Type.FETCH_DATA);

        FormField loanOfferSubmit = FormUtils.getFormField(FormFieldContent.SUBMIT_DETAILS);
        loanOfferSubmit.setActionType(Type.CALM_SUBMIT_BUTTON);
        loanOfferSubmit.setTitle(PageConstant.PL_OFFER_SCREEN_FORM.buttonText);

        if (Status.RETRY_WITHOUT_EDIT.equals(loanApplication.getCode())) {
            loanAmountValue.setDefaultValue(String.valueOf(loanApplication.getLenderDetails().getSubmittedOffer().get(0).getLoanAmount()));
            loanAmountValue.setDisabled(true);
            loanAmountRangeValue.setDefaultValue(String.valueOf(loanApplication.getLenderDetails().getSubmittedOffer().get(0).getLoanAmount()));
            loanAmountRangeValue.setDisabled(true);
            loanEmiValue.setDefaultValue(String.valueOf(loanApplication.getLenderDetails().getSubmittedOffer().get(0).getTenure().getValue()));
            loanEmiValue.setDisabled(true);
        }

        List<FormField> formFields = new ArrayList<>();
        formFields.add(loanAmountValue);
        formFields.add(loanAmountRangeValue);
        formFields.add(loanEmiValue);
        formFields.add(kfsLaunchButton);
        formFields.add(loanOfferSubmit);


        Form form = new Form();
        form.setFormType(FormType.PL_OFFER_SCREEN_FORM);
        form.setFormFields(formFields);

        return FormDataEnumResponse.builder()
                .dataEnum(DataEnum.FORM)
                .form(form)
                .formType(FormType.PL_OFFER_SCREEN_FORM)
                .build();
    }
}
