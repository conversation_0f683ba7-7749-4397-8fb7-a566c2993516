package com.flipkart.fintech.pinaka.service.widgettransformer.submitbuttonwidget;

import com.flipkart.fintech.pinaka.service.widgettransformer.ErrorScreenTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.KFSWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.RepeatLoanOfferPageTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.common.collect.Sets;
import java.util.Set;
import javax.annotation.Nullable;
import javax.inject.Inject;

public class SubmitButtonWidgetTransformerFactory {
  private final KFSWidgetTransformer kfsWidgetTransformer = new KFSWidgetTransformer();
  private final RepeatLoanOfferPageTransformer repeatLoanOfferPageTransformer;
  private final ErrorScreenTransformer errorScreenTransformer;
  public static final String REPEAT_LOAN_OFFER_PAGE = "REPEAT_LOAN_OFFER_PAGE";
  private final Set<String> ERROR_SCREEN_STATES;

  @Inject
  public SubmitButtonWidgetTransformerFactory(
      RepeatLoanOfferPageTransformer repeatLoanOfferPageTransformer,
      ErrorScreenTransformer errorScreenTransformer) {
    this.repeatLoanOfferPageTransformer = repeatLoanOfferPageTransformer;
    this.errorScreenTransformer = errorScreenTransformer;
    ERROR_SCREEN_STATES = Sets.newHashSet("REJECTED", "LENDER_FAILURE", "EXPIRED");
  }

  public SubmitButtonWidgetTransformer getSubmitButtonWidgetTransformer(@Nullable String contentId, ApplicationDataResponse applicationDataResponse) {
    if (REPEAT_LOAN_OFFER_PAGE.equals(contentId)) {
      return repeatLoanOfferPageTransformer;
    }
    else if (ERROR_SCREEN_STATES.contains(applicationDataResponse.getApplicationState().toUpperCase())) {
      return errorScreenTransformer;
    }
    return kfsWidgetTransformer;
  }

}
