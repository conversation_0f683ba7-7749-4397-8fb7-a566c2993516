package com.flipkart.fintech.pinaka.service.widgettransformer.widgets;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardCarouselWidgetDataV0;

public interface LenderCarouselTransformer {
    CardCarouselWidgetDataV0 buildWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaException, JsonProcessingException;
}
