package com.flipkart.fintech.pinaka.service.viesti;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.exception.ServiceErrorResponse;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.kafka.ConsumerUtil;
import com.flipkart.fintech.kafka.models.YakRelayerModel;
import com.flipkart.fintech.kafka.models.amsEvents.ApplicationEvent;
import com.flipkart.fintech.kafka.models.amsEvents.EventType;
import com.flipkart.fintech.kafka.models.amsEvents.YakEvent;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.viesti.PulsarClientConfig;
import com.flipkart.fintech.viesti.PulsarConsumerConfiguration;
import com.flipkart.fintech.viesti.ViestiConsumerThread;
import com.flipkart.fintech.viesti.config.ViestiConsumerConfiguration;
import com.flipkart.fintech.viesti.enums.ViestiConsumerName;
import com.flipkart.fintech.yak.core.YakBlobCodec;
import com.github.jknack.handlebars.internal.lang3.StringUtils;
import com.google.inject.Inject;
import lombok.CustomLog;
import org.apache.pulsar.client.api.*;

import javax.ws.rs.core.Response;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.List;
import java.util.Objects;

@CustomLog
public abstract class ViestiConsumer extends ViestiConsumerThread {

    private PulsarConsumerConfiguration pulsarConsumerConfiguration;
    private PulsarClientConfig pulsarClientConfig;

    @Inject
    public ViestiConsumer(ViestiConsumerConfiguration viestiConsumerConfiguration, ViestiConsumerName consumerName) {
        super(viestiConsumerConfiguration.getViestiConsumerProps().get(consumerName));
        this.pulsarConsumerConfiguration = viestiConsumerConfiguration.getViestiConsumerProps().get(consumerName).getPulsarConsumerConfiguration();
        this.pulsarClientConfig = viestiConsumerConfiguration.getViestiConsumerProps().get(consumerName).getPulsarClientConfig();
    }

    private ConsumerBuilder<byte[]> getViestiConsumerBuilder(PulsarClient pulsarClient, PulsarConsumerConfiguration pulsarConsumerConfiguration) {
        return pulsarClient.newConsumer()
                .topic(pulsarConsumerConfiguration.getTopic())
                .subscriptionName(pulsarConsumerConfiguration.getSubscriptionName())
                .subscriptionType(SubscriptionType.Shared)
                .subscriptionInitialPosition(SubscriptionInitialPosition.Earliest)
                .receiverQueueSize(1000)
                .messageListener((consumer, message) -> {
                    try {
                        yakAdaptation(message);
                        consumer.acknowledge(message);
                        log.debug("Acknowledging Pulsar event.");
                    } catch (Exception e) {
                        log.error(String.format("Negatively Acknowledging Pulsar event, Error while processing yak event: %s", e));
                        consumer.negativeAcknowledge(message);
                    }
                });
    }

    @Override
    public void yakAdaptation(Message<byte[]> message) {
        log.debug("Viesti Consumers are enabled.");
        YakRelayerModel yakRelayerModel = ConsumerUtil.toYakRelayerModel(message.getData(), false);
        try {
            log.debug("Viesti: yakRelayerModel: {}", yakRelayerModel);

            List<YakEvent> yakEventList = YakBlobCodec.deserialize(yakRelayerModel.getData(),
                    new TypeReference<List<YakEvent>>() {
                    }, false);

            for (YakEvent yakEvent : yakEventList) {
                ApplicationEvent applicationEvent = YakBlobCodec.deserialize(yakEvent.getPayload(), ApplicationEvent.class, false);
                String eventTypeString = yakEvent.getEventType();
                EventType eventType = null;
                if (StringUtils.isNotBlank(eventTypeString)) {
                    eventType = EventType.valueOf(eventTypeString);
                }
                log.debug("Viesti: Viesti event data for applicationId : {}", applicationEvent.getApplicationId());
                if (Objects.nonNull(applicationEvent)) {
                    Tenant tenant = Tenant.valueOf(applicationEvent.getTenant());
                    if (Tenant.CALM.equals(tenant)) {
                        handleEvent(eventType, applicationEvent);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Viesti: Inside viesti consumer event catch block");
            try {
                List<YakEvent> yakEventList = YakBlobCodec.deserialize(yakRelayerModel.getData(), new TypeReference<List<YakEvent>>() {
                }, false);
                YakEvent yakEvent = yakEventList.get(0);
                ApplicationEvent applicationEvent = YakBlobCodec.deserialize(yakEvent.getPayload(), ApplicationEvent.class, false);
                log.info("User:{}, Exception handling viesti event: type: {}, old state: {}, new state: {}, error_message: {}", applicationEvent.getExternalUserId(), applicationEvent.getApplicationType(), applicationEvent.getOldState(), applicationEvent.getApplicationState(), e.getMessage());
            } catch (IOException ex) {
                log.error("handled exception while deserializing in catch block ", ex);
            }
            throw new RuntimeException(e);
        }
    }

    public abstract void handleEvent(EventType eventType, ApplicationEvent applicationEvent) throws PinakaException;

    @Override
    protected void subscribeToTopic(String topicName, String consumerName) {
        try {
            getViestiConsumerBuilder(createPulsarClient(pulsarClientConfig), pulsarConsumerConfiguration)
                    .consumerName(consumerName)
                    .subscribe();
        } catch (PulsarClientException | MalformedURLException e) {
            log.error(String.format("Viesti: Error while creating consumers for topic: %s, exception: %s", topicName, e.getMessage()));
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(),
                    e.getMessage()));
        }
        log.debug(String.format("Viesti: Consumer %s is subscribed for topic: %s", consumerName, topicName));
    }

}
