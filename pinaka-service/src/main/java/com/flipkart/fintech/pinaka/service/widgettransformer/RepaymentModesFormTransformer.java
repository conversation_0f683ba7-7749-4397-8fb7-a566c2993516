package com.flipkart.fintech.pinaka.service.widgettransformer;

import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.RepaymentsModes.DEBIT_CARD;
import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.RepaymentsModes.DEBIT_CARD_IMG_URL;
import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.RepaymentsModes.IMAGE_VALUE_TYPE;
import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.RepaymentsModes.NET_BANKING;
import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.RepaymentsModes.NET_BANKING_IMG_URL;
import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.RepaymentsModes.imageHt;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pandora.api.model.pl.response.IfscResponse;
import com.flipkart.fintech.pinaka.service.response.RepaymentModesPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.value.ImageValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownFormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DropdownValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.ImageDropdownValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.DropdownType;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import java.util.ArrayList;
import java.util.List;

public class RepaymentModesFormTransformer {

    private static String genericFormjson;
    private RepaymentModesPageDataSourceResponse repaymentModesPageDataSourceResponse;

    static {
        genericFormjson = TransformerUtils.readFileasString("template/idfc/RepaymentModes.json");

    }

    public GenericFormWidgetData buildWidgetData(RepaymentModesPageDataSourceResponse pageDataSourceReponse)
            throws JsonProcessingException {
        GenericFormWidgetData genericFormWidgetData = ObjectMapperUtil.get().readValue(genericFormjson, GenericFormWidgetData.class);
        repaymentModesPageDataSourceResponse = pageDataSourceReponse;
        updateOptions(repaymentModesPageDataSourceResponse,genericFormWidgetData);
        updateSubmitButton(genericFormWidgetData.getSubmitButton(),repaymentModesPageDataSourceResponse);
        return updateOptions(repaymentModesPageDataSourceResponse,genericFormWidgetData);
    }

    private GenericFormWidgetData updateOptions(RepaymentModesPageDataSourceResponse repaymentModesPageDataSourceResponse, GenericFormWidgetData genericFormWidgetData) {
        DropdownFormFieldValue dropdownFormFieldValue= (DropdownFormFieldValue) genericFormWidgetData.getRenderableComponents().get(0).getValue();
        dropdownFormFieldValue.setOptions(getRepaymentOptions(repaymentModesPageDataSourceResponse.getIfscResponse()));
        genericFormWidgetData.getRenderableComponents().get(0).setValue(dropdownFormFieldValue);
        return genericFormWidgetData;
    }

    private List<DropdownValue> getRepaymentOptions(IfscResponse ifscResponse) {
        List<DropdownValue> dropdownValueList = new ArrayList<>();
        if(ifscResponse.getAvailableModes().getDebitCard()!=null)
        {
            ImageDropdownValue imageDropdownValue = new ImageDropdownValue();
            imageDropdownValue.setId(ifscResponse.getAvailableModes().getDebitCard());
            imageDropdownValue.setTitle(DEBIT_CARD);
            imageDropdownValue.setImage(getImageValue(DEBIT_CARD_IMG_URL));
            imageDropdownValue.setType(DropdownType.IMAGE);
            dropdownValueList.add(imageDropdownValue);

        }
        if(ifscResponse.getAvailableModes().getNetBanking()!=null)
        {
            ImageDropdownValue imageDropdownValue = new ImageDropdownValue();
            imageDropdownValue.setId(ifscResponse.getAvailableModes().getNetBanking());
            imageDropdownValue.setTitle(NET_BANKING);
            imageDropdownValue.setImage(getImageValue(NET_BANKING_IMG_URL));
            imageDropdownValue.setType(DropdownType.IMAGE);
            dropdownValueList.add(imageDropdownValue);
        } return dropdownValueList;
    }

    private ImageValue getImageValue(String url) {
        ImageValue imageValue = new ImageValue();
        imageValue.setDynamicImageUrl(url);
        imageValue.setType(IMAGE_VALUE_TYPE);
        imageValue.setWidth(imageHt);
        imageValue.setHeight(imageHt);
        return imageValue;
    }

    private static void updateSubmitButton(SubmitButtonValue submitButton,RepaymentModesPageDataSourceResponse repaymentModesPageDataSourceResponse) {
        Action action = submitButton.getButton().getAction();
        action.setParams(repaymentModesPageDataSourceResponse.getQueryParams());
        action.setEncryption(repaymentModesPageDataSourceResponse.getEncryptionData());
    }

}
