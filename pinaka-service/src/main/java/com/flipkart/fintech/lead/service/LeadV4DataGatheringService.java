package com.flipkart.fintech.lead.service;

import com.flipkart.fintech.lead.model.LeadV4DataGatheringResponse;
import com.flipkart.fintech.lead.model.Name;
import com.flipkart.fintech.lending.orchestrator.client.OfferServiceClient;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.helper.UserActionSubmitRequestHelper;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util;
import com.flipkart.fintech.profile.client.ProfileClient;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.profile.service.UpiUserService;
import com.flipkart.upi.user.service.api.models.base.v1.enums.GenericSearchType;
import com.flipkart.upi.user.service.api.models.base.v1.request.search_management.GenericSearchRequestDTO;
import com.flipkart.upi.user.service.api.models.base.v1.response.search_management.GenericSearchResponseDTO;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.flipkart.fintech.pinaka.api.model.LeadDetails.LeadState.LEAD_V4_LANDING_PAGE;

@CustomLog
public class LeadV4DataGatheringService {

    private final ProfileClient profileClient;
    private final OfferServiceClient offerServiceClient;
    private final Decrypter decrypter;
    private final UpiUserService upiUserService;
    private final InitialUserReviewDataSource initialUserReviewDataSource;

    @Inject
    public LeadV4DataGatheringService(ProfileClient profileClient, OfferServiceClient offerServiceClient, Decrypter decrypter, UpiUserService upiUserService, InitialUserReviewDataSource initialUserReviewDataSource) {
        this.profileClient = profileClient;
        this.offerServiceClient = offerServiceClient;
        this.decrypter = decrypter;
        this.upiUserService = upiUserService;
        this.initialUserReviewDataSource = initialUserReviewDataSource;
    }

    public LeadV4DataGatheringResponse gatherData(MerchantUser merchantUser) {
        return gatherData(merchantUser, LEAD_V4_LANDING_PAGE.name());
    }

    public LeadV4DataGatheringResponse gatherData(MerchantUser merchantUser, String applicationState) {
        LeadV4DataGatheringResponse.LeadV4DataGatheringResponseBuilder responseBuilder = LeadV4DataGatheringResponse.builder();

        // Store profile for reuse across methods
        ProfileDetailedResponse profile;

        // Gather data based on application state
        profile = gatherNameAndPhoneIfNeeded(merchantUser, applicationState, responseBuilder);
        if (LEAD_V4_LANDING_PAGE.name().equals(applicationState)) gatherPaOffer(merchantUser, responseBuilder);
        gatherCachedUserDataIfNeeded(merchantUser, applicationState, responseBuilder, profile);

        return responseBuilder.build();
    }

    public LeadV4DataGatheringResponse gatherData(MerchantUser merchantUser, String applicationState, Map<String, Object> existingApplicationData) {
        LeadV4DataGatheringResponse.LeadV4DataGatheringResponseBuilder responseBuilder = LeadV4DataGatheringResponse.builder();

        // Store profile for reuse across methods
        ProfileDetailedResponse profile;

        // Extract actual cached user data from applicationData to avoid nesting
        Map<String, Object> existingCachedData = null;
        if (existingApplicationData != null && existingApplicationData.containsKey(LV4Util.CACHED_USER_DATA_KEY)) {
            existingCachedData = (Map<String, Object>) existingApplicationData.get(LV4Util.CACHED_USER_DATA_KEY);
            log.info("Extracted existing cached data from applicationData for userId: {}", merchantUser.getSmUserId());
        }

        // Gather data based on application state
        profile = gatherNameAndPhoneIfNeeded(merchantUser, applicationState, responseBuilder);
        if (LEAD_V4_LANDING_PAGE.name().equals(applicationState)) gatherPaOffer(merchantUser, responseBuilder);
        gatherCachedUserDataIfNeeded(merchantUser, applicationState, responseBuilder, profile, existingCachedData);

        return responseBuilder.build();
    }

    private ProfileDetailedResponse gatherNameAndPhoneIfNeeded(MerchantUser merchantUser, String applicationState, LeadV4DataGatheringResponse.LeadV4DataGatheringResponseBuilder responseBuilder) {
        if (!LEAD_V4_LANDING_PAGE.name().equals(applicationState)) {
            log.debug("Skipping name/phone collection for applicationState: {} userId: {}", applicationState, merchantUser.getSmUserId());
            return null;
        }

        try {
            ProfileDetailedResponse profile = getProfileForUser(merchantUser);
            if (profile != null) {
                addPhoneNumberIfPresent(profile, responseBuilder, merchantUser.getSmUserId());
                addUserNameFromProfileOrUpi(profile, responseBuilder, merchantUser.getSmUserId());
            }
            return profile;
        } catch (Exception e) {
            log.warn("Failed to get user profile data for LEAD_V4_LANDING_PAGE {}: {}", merchantUser.getSmUserId(), e.getMessage());
            return null;
        }
    }

    private ProfileDetailedResponse getProfileForUser(MerchantUser merchantUser) {
        return profileClient.getProfile(merchantUser.getMerchantUserId(), merchantUser.getSmUserId(), false);
    }

    private void addPhoneNumberIfPresent(ProfileDetailedResponse profile, LeadV4DataGatheringResponse.LeadV4DataGatheringResponseBuilder responseBuilder, String userId) {
        String phoneNumber = profile.getPhoneNo();
        if (StringUtils.isNotBlank(phoneNumber)) {
            responseBuilder.phoneNumber(phoneNumber);
            log.info("Retrieved phone number for LEAD_V4_LANDING_PAGE userId: {}", userId);
        }
    }

    private void addUserNameFromProfileOrUpi(ProfileDetailedResponse profile, LeadV4DataGatheringResponse.LeadV4DataGatheringResponseBuilder responseBuilder, String userId) {
        if (profile != null && StringUtils.isNotBlank(profile.getFirstName())) {
            Name userName = getUserNameFromProfile(profile);
            responseBuilder.name(userName);
            log.info("Retrieved user profile data for LEAD_V4_LANDING_PAGE userId: {}", userId);
        } else {
            log.warn("Profile not found for LEAD_V4_LANDING_PAGE userId: {}, fetching name from UPI service", userId);
            Name nameFromUpi = getNameFromUpiService(profile);
            if (nameFromUpi != null) {
                responseBuilder.name(nameFromUpi);
            } else {
                log.debug("No name found in UPI service for LEAD_V4_LANDING_PAGE smUser: {}", userId);
            }
        }
    }

    private void gatherPaOffer(MerchantUser merchantUser, LeadV4DataGatheringResponse.LeadV4DataGatheringResponseBuilder responseBuilder) {
        try {
            Optional<LenderOfferEntity> paOfferOpt = getPaOffer(merchantUser);
            if (paOfferOpt.isPresent()) {
                responseBuilder.paOffer(paOfferOpt.get());
                log.info("Retrieved PA offer for userId: {}", merchantUser.getSmUserId());
            } else {
                log.info("No PA offer found for userId: {}", merchantUser.getSmUserId());
            }
        } catch (Exception e) {
            log.warn("Failed to get PA offer for {}: {}", merchantUser.getSmUserId(), e.getMessage());
        }
    }

    private void gatherCachedUserDataIfNeeded(MerchantUser merchantUser, String applicationState, LeadV4DataGatheringResponse.LeadV4DataGatheringResponseBuilder responseBuilder, ProfileDetailedResponse profile) {
        gatherCachedUserDataIfNeeded(merchantUser, applicationState, responseBuilder, profile, null);
    }

    private void gatherCachedUserDataIfNeeded(MerchantUser merchantUser, String applicationState, LeadV4DataGatheringResponse.LeadV4DataGatheringResponseBuilder responseBuilder, ProfileDetailedResponse profile, Map<String, Object> existingCachedData) {
        if (!shouldCacheUserData(applicationState)) {
            log.debug("Skipping user data caching for applicationState: {} userId: {}", applicationState, merchantUser.getSmUserId());
            return;
        }

        try {
            Map<String, Object> cachedUserData = gatherUserDataForCache(merchantUser, profile, existingCachedData);
            responseBuilder.cachedUserData(cachedUserData);
            log.info("Cached user data collected for {} userId: {}, data size: {}", applicationState, merchantUser.getSmUserId(), cachedUserData.size());
        } catch (Exception e) {
            log.warn("Failed to gather cached user data for {} {}: {}", applicationState, merchantUser.getSmUserId(), e.getMessage());
        }
    }

    private boolean shouldCacheUserData(String applicationState) {
        // Handle both cases:
        // 1. "leadV4LandingPage" from submit API (taskKey)
        // 2. "LEAD_V4_LANDING_PAGE" from apply-now API (applicationState)
        return UserActionSubmitRequestHelper.LEAD_V4_PAGE_1.equals(applicationState) || UserActionSubmitRequestHelper.LEAD_V4_LANDING_PAGE.equals(applicationState) || LEAD_V4_LANDING_PAGE.name().equals(applicationState);
    }

    private Name getUserNameFromProfile(ProfileDetailedResponse profile) {
        String name = LV4Util.getFullNameFromProfile(profile, true, decrypter);
        return StringUtils.isBlank(name) ? new Name(null, null, true) : new Name(profile.getFirstName(), profile.getLastName(), false);
    }

    private Name getNameFromUpiService(ProfileDetailedResponse profile) {
        String name;
        try {
            GenericSearchResponseDTO verifyVpaResponse = upiUserService.verifyVpa(GenericSearchRequestDTO.builder().genericPaymentAddress(profile.getPhoneNo()).possibleTypes(Collections.singletonList(GenericSearchType.MOBILE_NUMBER)).build());
            if (verifyVpaResponse != null && verifyVpaResponse.getPayeeDetails() != null) {
                name = verifyVpaResponse.getPayeeDetails().getPayeeName();
                if (StringUtils.isNotBlank(name)) {
                    return new Name(name, false);
                }
            }
        } catch (Exception upiException) {
            log.warn("Failed to get name from UPI service: {} for smUser: {}", upiException.getMessage(), profile.getSmUserId());
        }
        return null;
    }

    private Optional<LenderOfferEntity> getPaOffer(MerchantUser merchantUser) throws PinakaException {
        try {
            return offerServiceClient.getPreApprovedOffer(merchantUser, Optional.empty());
        } catch (Exception e) {
            log.error("Error retrieving PA offer for {}: {}", merchantUser.getSmUserId(), e.getMessage());
            throw new PinakaException("Failed to retrieve PA offer", e);
        }
    }

    private Map<String, Object> gatherUserDataForCache(MerchantUser merchantUser, ProfileDetailedResponse profile, Map<String, Object> existingCachedData) throws PinakaException {
        try {
            // Start with existing cached data or create new map
            Map<String, Object> cachedData = existingCachedData != null ? new HashMap<>(existingCachedData) : new HashMap<>();

            // Get or update ReviewUserDataSourceResponse
            ReviewUserDataSourceResponse reviewUserDataSourceResponse;

            if (cachedData.containsKey(LV4Util.CACHED_REVIEW_DATA_KEY)) {
                // Key is present - call getData to get fresh data
                Map<String, Object> emptyQueryParams = new HashMap<>();
                reviewUserDataSourceResponse = initialUserReviewDataSource.getData(merchantUser, emptyQueryParams, false);

                // Merge with existing cached data if it exists
                Object existingData = cachedData.get(LV4Util.CACHED_REVIEW_DATA_KEY);
                if (existingData != null) {
                    ReviewUserDataSourceResponse existingResponse = ObjectMapperUtil.get().convertValue(existingCachedData, ReviewUserDataSourceResponse.class);
                    if (existingResponse.getProfile() != null && reviewUserDataSourceResponse.getProfile() == null) {
                        // Preserve existing profile if new response doesn't have one
                        reviewUserDataSourceResponse.setProfile(existingResponse.getProfile());
                    }
                }
                log.info("Key present - fetched fresh data and merged with existing cached ReviewUserDataSourceResponse for userId: {}", merchantUser.getSmUserId());
            } else {
                // Key not present - create minimal response without calling getData
                reviewUserDataSourceResponse = new ReviewUserDataSourceResponse();
                log.info("Key not present - created minimal ReviewUserDataSourceResponse for userId: {}", merchantUser.getSmUserId());
            }

            // If profile is available, add it to the response
            if (profile != null) {
                reviewUserDataSourceResponse.setProfile(profile);
                log.info("Added profile to cached data for userId: {}", merchantUser.getSmUserId());
            }

            // Store the raw data source responses as encrypted data - let transformers handle decryption
            cachedData.put(LV4Util.CACHED_REVIEW_DATA_KEY, reviewUserDataSourceResponse);
            cachedData.put(LV4Util.CACHED_IS_ENCRYPTED_KEY, true); // Flag to indicate data is encrypted

            log.info("Stored encrypted user data for cache for userId: {}", merchantUser.getSmUserId());
            return cachedData;
        } catch (Exception e) {
            log.error("Error gathering user data for cache for userId: {}: {}", merchantUser.getSmUserId(), e.getMessage());
            throw new PinakaException("Failed to gather user data for cache", e);
        }
    }
}
