{"application_data": {"refreshAutoDisbursal": {"metadata": {"status": "SUCCESS", "message": "KFS details refreshed", "version": "v1", "time": "2023-11-30T10:17:45"}, "resourceData": [{"entityReqId": "SUMO51895A3624CE446A9CEA690331ACA928", "loanId": "*********", "kfsDetails": [{"agreementDate": "Refreshed date", "sanctLimit": "505000", "facilityAmt": "505000", "loanAmt": "505000", "totalInt": "39946", "processFee": "12625", "netDisbAmt": "492375", "totalAmtToBePaid": "544946", "appRoi": "14", "apr": "18.76", "tenor": "12", "repayFreq": "M", "numInstallment": "12", "amtInstall": "45413", "lspDetails": "SCAPIC INNOVATIONS PRIVATE LIMITED", "nameNodal": "Mr. <PERSON><PERSON><PERSON>", "designationNodal": "PNO", "addNodal": "IDFC FIRST Bank Ltd. Building No.2, Raheja Mindspace, MIDC Industrial Area, Shiravane, Juinagar, Nerul, Navi Mumbai 400706, Maharashtra, India. Email Id: <EMAIL>  / <EMAIL>", "phoneNodal": "022-******** /1800 209 9771"}], "charges": [{"chargeName": "PF RECD FRM CUST", "chargeAmount": "12625"}], "repaySchedule": [{"dueDate": "2023-02-05T00:00:00", "outstandingPrincipal": "505000", "principalComponent": "38735.77", "interestComponent": "6677.23", "installment": "45413", "loanId": "*********"}, {"dueDate": "2023-03-05T00:00:00", "outstandingPrincipal": "466264.23", "principalComponent": "39973.25", "interestComponent": "5439.75", "installment": "45413", "loanId": "*********"}, {"dueDate": "2023-04-05T00:00:00", "outstandingPrincipal": "426290.98", "principalComponent": "40439.6", "interestComponent": "4973.4", "installment": "45413", "loanId": "*********"}, {"dueDate": "2023-05-05T00:00:00", "outstandingPrincipal": "385851.38", "principalComponent": "40911.4", "interestComponent": "4501.6", "installment": "45413", "loanId": "*********"}, {"dueDate": "2023-06-05T00:00:00", "outstandingPrincipal": "344939.98", "principalComponent": "41388.7", "interestComponent": "4024.3", "installment": "45413", "loanId": "*********"}, {"dueDate": "2023-07-05T00:00:00", "outstandingPrincipal": "303551.28", "principalComponent": "41871.56", "interestComponent": "3541.44", "installment": "45413", "loanId": "*********"}, {"dueDate": "2023-08-05T00:00:00", "outstandingPrincipal": "261679.72", "principalComponent": "42360.06", "interestComponent": "3052.94", "installment": "45413", "loanId": "*********"}, {"dueDate": "2023-09-05T00:00:00", "outstandingPrincipal": "219319.66", "principalComponent": "42854.27", "interestComponent": "2558.73", "installment": "45413", "loanId": "*********"}, {"dueDate": "2023-10-05T00:00:00", "outstandingPrincipal": "176465.39", "principalComponent": "43354.23", "interestComponent": "2058.77", "installment": "45413", "loanId": "*********"}, {"dueDate": "2023-11-05T00:00:00", "outstandingPrincipal": "133111.16", "principalComponent": "43860.03", "interestComponent": "1552.97", "installment": "45413", "loanId": "*********"}, {"dueDate": "2023-12-05T00:00:00", "outstandingPrincipal": "89251.13", "principalComponent": "44371.73", "interestComponent": "1041.27", "installment": "45413", "loanId": "*********"}, {"dueDate": "2024-01-05T00:00:00", "outstandingPrincipal": "44879.4", "principalComponent": "44879.4", "interestComponent": "523.6", "installment": "45403", "loanId": "*********"}]}]}, "getPaymentDetails": {"status": "SUCCESS", "message": "GET_PMNT_DTL processed sucessfully", "version": "v1", "time": "2023-12-06T12:37:42", "loanUtilityResource": {"utrNo": "NA", "paymentStatus": "SUCCESS", "paymentAmount": "492375"}}, "getAadhaarDetailsEkyc": {"kyc": {"imageId": "fk-p-calm-kyc_image-docs/APP2311281449309342163855751733674315846", "name": "<PERSON><PERSON><PERSON>", "address": {"co": "<PERSON>", "country": "", "dist": "Jaipur", "house": "C12", "lm": "Mogli Colony", "loc": "Test Taluk", "pc": "302001", "po": "", "state": "Rajasthan", "street": "Select City Walk", "subdist": "", "vtc": "Jaipur", "pinType": "URBAN"}, "dob": "1965-01-10", "gender": "M"}}, "authAutoDisbursal": {"metadata": {"status": "SUCCESS", "message": "KFS authorized, loan onboarded successfully", "version": "v1", "time": "2023-11-24T15:28:36"}, "resourceData": [{"entityReqId": "SUMOF1563489083A43BBB6DA1D0F4A51276B", "loanId": "*********", "kfsDetails": [{"agreementDate": "24/11/2023 03:27:38", "sanctLimit": "505000", "facilityAmt": "505000", "loanAmt": "505000", "totalInt": "42248", "netDisbAmt": "492375", "totalAmtToBePaid": "547248", "appRoi": "14", "apr": "18.51", "tenor": "12", "repayFreq": "M", "numInstallment": "12", "amtInstall": "45604", "lspDetails": "SCAPIC INNOVATIONS PRIVATE LIMITED", "nameNodal": "Mr. <PERSON><PERSON><PERSON>", "designationNodal": "PNO", "addNodal": "IDFC FIRST Bank Ltd. Building No.2, Raheja Mindspace, MIDC Industrial Area, Shiravane, Juinagar, Nerul, Navi Mumbai 400706, Maharashtra, India. Email Id: <EMAIL>  / <EMAIL>", "phoneNodal": "022-******** /1800 209 9771"}], "charges": [{"chargeName": "PF RECD FRM CUST", "chargeAmount": "12625"}], "repaySchedule": [{"installmentNumber": "2", "dueDate": "2024-02-05T00:00:00", "outstandingPrincipal": "27564.5", "principalComponent": "2353.44", "interestComponent": "344.56", "installment": "2698", "loanId": "*********"}, {"installmentNumber": "3", "dueDate": "2024-03-05T00:00:00", "outstandingPrincipal": "25211.06", "principalComponent": "2382.86", "interestComponent": "315.14", "installment": "2698", "loanId": "*********"}, {"installmentNumber": "4", "dueDate": "2024-04-05T00:00:00", "outstandingPrincipal": "22828.2", "principalComponent": "2412.64", "interestComponent": "285.36", "installment": "2698", "loanId": "*********"}, {"installmentNumber": "1", "dueDate": "2024-01-05T00:00:00", "outstandingPrincipal": "30000", "principalComponent": "2435.5", "interestComponent": "262.5", "installment": "2698", "loanId": "*********"}, {"installmentNumber": "5", "dueDate": "2024-05-05T00:00:00", "outstandingPrincipal": "20415.56", "principalComponent": "2442.8", "interestComponent": "255.2", "installment": "2698", "loanId": "*********"}, {"installmentNumber": "6", "dueDate": "2024-06-05T00:00:00", "outstandingPrincipal": "17972.76", "principalComponent": "2473.34", "interestComponent": "224.66", "installment": "2698", "loanId": "*********"}, {"installmentNumber": "7", "dueDate": "2024-07-05T00:00:00", "outstandingPrincipal": "15499.42", "principalComponent": "2504.25", "interestComponent": "193.75", "installment": "2698", "loanId": "*********"}, {"installmentNumber": "8", "dueDate": "2024-08-05T00:00:00", "outstandingPrincipal": "12995.17", "principalComponent": "2535.56", "interestComponent": "162.44", "installment": "2698", "loanId": "*********"}, {"installmentNumber": "9", "dueDate": "2024-09-05T00:00:00", "outstandingPrincipal": "10459.61", "principalComponent": "2567.25", "interestComponent": "130.75", "installment": "2698", "loanId": "*********"}, {"installmentNumber": "10", "dueDate": "2024-10-05T00:00:00", "outstandingPrincipal": "7892.36", "principalComponent": "2599.34", "interestComponent": "98.66", "installment": "2698", "loanId": "*********"}, {"installmentNumber": "11", "dueDate": "2024-11-05T00:00:00", "outstandingPrincipal": "5293.02", "principalComponent": "2631.83", "interestComponent": "66.17", "installment": "2698", "loanId": "*********"}, {"installmentNumber": "12", "dueDate": "2024-12-05T00:00:00", "outstandingPrincipal": "2661.19", "principalComponent": "2661.19", "interestComponent": "33.81", "installment": "2695", "loanId": "*********"}]}]}, "onboardLoan": {"result": {"msg": "<PERSON>an onboarding completed succesfully.", "response": {"loanId": "*********", "entityReqId": "SUMOC3F21CAB943B42A9ABDCF620991EEE3C", "kfsDetails": [{"agreementDate": "16/11/2023 12:02:57", "bankName": "IDFC FIRST Bank Ltd.", "bankAdd": "KRM Tower, 7th Floor, No. 1, Harrington Road, Chetpet, Chennai - 600031", "sanctLimit": "505000", "facilityAmt": "505000", "loanAmt": "505000", "totalInt": "39528", "processFee": "12625", "netDisbAmt": "492375", "totalAmtToBePaid": "544528", "appRoi": "14", "apr": "18.81", "tenor": "12", "repayFreq": "M", "numInstallment": "12", "amtInstall": "45378", "bounceCharge": "NA", "latePayCharge": "NA", "foreclosureCharge": "NA", "overdueCharges": "NA", "coolOffPeriod": "3 days", "lspDetails": "SCAPIC INNOVATIONS PRIVATE LIMITED", "nameNodal": "Mr. <PERSON><PERSON><PERSON>", "designationNodal": "PNO", "addNodal": "IDFC FIRST Bank Ltd. Building No.2, Raheja Mindspace, MIDC Industrial Area, Shiravane, Juinagar, Nerul, Navi Mumbai 400706, Maharashtra, India. Email Id: <EMAIL>  / <EMAIL>", "phoneNodal": "022-******** /1800 209 9771", "preEmiCharges": "0", "bankDetails": "IDFC FIRST Bank Ltd., KRM Tower, 7th Floor, No. 1, Harrington Road, Chetpet, Chennai - 600031", "nodalDetails": "Mr. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IDFC FIRST Bank Ltd. Building No.2, Raheja Mindspace, MIDC Industrial Area, Shiravane, Juinagar, Nerul, Navi Mumbai 400706, Maharashtra, India. Email Id: <EMAIL>  / <EMAIL>, 022-******** /1800 209 9771"}], "repaySchedule": [{"dueDate": "2023-04-05", "outstandingPrincipal": "505000", "principalComponent": "39093.55", "interestComponent": "6284.45", "installment": "45378", "loanId": "*********"}, {"dueDate": "2023-05-05", "outstandingPrincipal": "465906.45", "principalComponent": "39942.42", "interestComponent": "5435.58", "installment": "45378", "loanId": "*********"}, {"dueDate": "2023-06-05", "outstandingPrincipal": "425964.03", "principalComponent": "40408.41", "interestComponent": "4969.59", "installment": "45378", "loanId": "*********"}, {"dueDate": "2023-07-05", "outstandingPrincipal": "385555.62", "principalComponent": "40879.85", "interestComponent": "4498.15", "installment": "45378", "loanId": "*********"}, {"dueDate": "2023-08-05", "outstandingPrincipal": "344675.77", "principalComponent": "41356.78", "interestComponent": "4021.22", "installment": "45378", "loanId": "*********"}, {"dueDate": "2023-09-05", "outstandingPrincipal": "303318.99", "principalComponent": "41839.27", "interestComponent": "3538.73", "installment": "45378", "loanId": "*********"}, {"dueDate": "2023-10-05", "outstandingPrincipal": "261479.72", "principalComponent": "42327.4", "interestComponent": "3050.6", "installment": "45378", "loanId": "*********"}, {"dueDate": "2023-11-05", "outstandingPrincipal": "219152.32", "principalComponent": "42821.22", "interestComponent": "2556.78", "installment": "45378", "loanId": "*********"}, {"dueDate": "2023-12-05", "outstandingPrincipal": "176331.1", "principalComponent": "43320.8", "interestComponent": "2057.2", "installment": "45378", "loanId": "*********"}, {"dueDate": "2024-01-05", "outstandingPrincipal": "133010.3", "principalComponent": "43826.21", "interestComponent": "1551.79", "installment": "45378", "loanId": "*********"}, {"dueDate": "2024-02-05", "outstandingPrincipal": "89184.09", "principalComponent": "44337.51", "interestComponent": "1040.49", "installment": "45378", "loanId": "*********"}, {"dueDate": "2024-03-05", "outstandingPrincipal": "44846.58", "principalComponent": "44846.58", "interestComponent": "523.42", "installment": "45370", "loanId": "*********"}]}}}, "code": null, "emandateRedirection": {"mandateResponseString": "jinY2U+Tm8kUFZktkL0V6nU4zZ2oU/OVL1Us64fVpkUe7XvUrd5bfbPlRySf0umGSoR58KXVShcv/w9NmypMLthPlFD9PLv9Z6QMuboM7ddp91Eoc/ner7lMnZ8QWNv/kUlow2Tclp3FzOKOrUIOAs/2xB3vYpOWtmWq+PHZn0a4i7MI3Mn/CjCyhjxX9IQxntqJE9tRParT9bfNfnjWqzUkwNpzXiOwGZ5JOwe5PaJrs/hsHa3k7OHc7huiLhrW9GRP3InIs4fD7Ej9r6riI+V7UrFtlGKWftRsokWztgzvm/+Pjhn+rkaYrtj21sDtGsGcgnuHKtKdU+RgS0QTwA=="}, "user_details": {"pan": null, "dob": null, "gender": null, "shipping_address_id": "CNTCT000B051A14EE43348E399EDBC", "bin_score": "301.382", "employment_type": null, "employer_id": null, "employer_name": null, "industry_id": null, "industry_name": null, "monthly_income": null, "annual_turn_over": null, "pan_consent_provided": false, "offer_consent_provided": false, "segment": null, "subsegment": null}, "getApprovalStatus": {"crn": "**********", "loanApplicationNo": "*********", "referenceId": "**********", "status": "APPROVE", "rejectReason": null, "customerEligibilityType": "Real-Time", "offers": [{"maxEligibleAmount": "1000000", "minEligibleAmount": "10000", "minTenure": "12", "maxTenure": "60", "processingFee": "2.5", "roi": "14"}, {"maxEligibleAmount": "1000000", "minEligibleAmount": "10000", "minTenure": "12", "maxTenure": "48", "processingFee": "2.5", "roi": "14"}, {"maxEligibleAmount": "1000000", "minEligibleAmount": "10000", "minTenure": "12", "maxTenure": "36", "processingFee": "2.5", "roi": "14"}]}, "searchDownloadCkyc": {"age": "25", "address": {"addressLine1": "TestAddr2India", "city": "PUNE", "state": "MH", "country": "IN", "pincode": "411001"}, "transactionId": "4565889", "transactionStatus": "CKYCSuccess", "positiveConfirmation": "Y", "kyc": {"personalDetails": [{"recordIdentifier": "123456789012", "applicationFormNo": "1", "branchCode": "11", "ckycNumber": "81695283462935", "ckycFullName": "<PERSON><PERSON><PERSON>", "ckycFatherFullName": "<PERSON>", "ckycGender": "M", "ckycDOB": "10-Feb-1998", "ckycCorAddPinType": "URBAN", "ckycKycVerificationDate": "18-02-2017", "ckycKycVerificationName": "<PERSON><PERSON><PERSON>", "ckycKycVerificationDesg": "Manager", "ckycKycVerificationBranch": "North", "ckycKycVerificationEmpcode": "4636464"}], "relatedPersonDetails": [], "images": [{"extension": "jpg", "type": "Photograph", "id": "fk-p-calm-kyc_image-docs/APP2311281449309342163855751733674315846", "sequence": "1"}]}}, "generateEmandateUrl": {"url": "https://uat.fmreporting.idfcfirstbank.com/IDFCEMandate/EMandateB2BPaynimmo.aspx", "payload": {"DATA": "jinY2U+Tm8kUFZktkL0V6nU4zZ2oU/OVL1Us64fVpkUe7XvUrd5bfbPlRySf0umGSoR58KXVShcv/w9NmypMLppgd9Auj2Aif3ybX+27Ee1WV8DlPnlQ9GkNwTU4/wCq3uaDRgF5nU4phCFe27ycSAmyz8Z4A46ba9L2ZaOnXUi9wnCmOjPabht2q37Q8IcrVfufw2XPam3tjjjPswEkDJ9TOPaLOORnIshfE1/oQ/eL0xYq4hBl6u7VYjqhcBw7mF6uBcD3ywRAFqBPoJVQ6OjTM9VtXXCrY/gCYYQQ2u10rHF69Z7G219TbfBuiyaTfTRKz7jf2E9ZDztQ1XHiQxBbbDiNdYscp19UY3FNU8Xk1DECLjjuQCYFL63jeYDtZYv46ZsDEO+kPniPvNZ3QmV7rjI6Jke2sTN6oemwzOPxGpIUNiBu59txXYFDjb2lN34MVF+OYEejTD3v18uKlQ==", "MERCHENTCODE": "SMONEY"}}, "verifyPan": {"status": "Success", "panStatus": "Existing and Valid PAN"}, "bankDetails": {"bankDetails": {"ifsc": "UTIB0000001", "bankAccount": "*********"}, "consentData": {"consentType": "CI_CONSENT", "userIP": "*************, *************", "deviceId": "clmfyyrnj1kk5072l1m3duhpkBR:.*************", "deviceInfo": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1 FKUA/msite/0.0.1/msite/Mobile", "currentTimeStamp": *************}, "bankAccType": "Saving"}, "ckycDetails": {"consentData": {"consentType": "CI_CONSENT", "userIP": "*************, *************", "deviceId": "clmfyyrnj1kk5072l1m3duhpkBR:.*************", "deviceInfo": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1 FKUA/msite/0.0.1/msite/Mobile", "currentTimeStamp": *************}}, "pollOffer": {"crn": "**********", "loanApplicationNo": "*********", "referenceId": "**********", "status": "APPROVE", "rejectReason": null, "customerEligibilityType": "Real-Time", "offers": [{"maxEligibleAmount": "279000", "minEligibleAmount": "10000", "minTenure": "12", "maxTenure": "49", "processingFee": "2.5", "roi": "14"}, {"maxEligibleAmount": "1000000", "minEligibleAmount": "10000", "minTenure": "12", "maxTenure": "48", "processingFee": "2.5", "roi": "14"}, {"maxEligibleAmount": "49000", "minEligibleAmount": "10000", "minTenure": "12", "maxTenure": "36", "processingFee": "2.5", "roi": "14"}]}, "lender_details": null, "generateSelfieToken": {"status": "success", "result": {"token": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************.tmnlxw_2RDl20MorIPf9avStmfnthkq0ymp847gZev2uzGGtN_rYhOhZzddi_HWvzuM2xKVALy2HItbHp8XmDLeT6Jrslujr15lhpO-ETF_ew7nDcSBXXYQ_PZnORQ5RkP8gqzH4Pn6wIl5YNhsHwsp92CD2WgQLhTKI6V66o7w"}}, "expiry": null, "error_message": null, "ifscDetails": {"bankDetails": {"city": "<PERSON><PERSON>n", "branch": "SHIAMGANJ", "name": "STATE BANK OF INDIA"}, "availableModes": {"debitCard": "5430", "netBanking": "5430"}}, "consent_details_list": null, "startLoan": {"status": "success"}, "application_created_at": "2023-11-28 14:49:30", "basicDetails": {"pan": "**********", "consentData": {"consentType": "CI_CONSENT", "userIP": "*************, *************", "deviceId": "clmfyyrnj1kk5072l1m3duhpkBR:.*************", "deviceInfo": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1 FKUA/msite/0.0.1/msite/Mobile", "currentTimeStamp": *************}, "dob": "11/08/1996"}, "generateOffer": {"status": "SUBMITTED"}, "repaymentModes": {"repaymentMode": "5430", "consentData": {"consentType": "CI_CONSENT", "userIP": "*************, *************", "deviceId": "clmfyyrnj1kk5072l1m3duhpkBR:.*************", "deviceInfo": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1 FKUA/msite/0.0.1/msite/Mobile", "currentTimeStamp": *************}}, "getEmandateStatus": {"status": "Success", "eMandateStatus": "success", "eMandateStatusCode": "0300", "message": "NA", "timestamp": "2023-11-28T09:22:05.895Z"}, "offer_id": null, "external_user_id": "ACC6722F15B35D14D36B0D46FA3C930AD8BO", "journey_context": "PERSONAL_LOAN_APPLICATION_JOURNEY", "offerScreen": {"bankOffer": {"amount": 505000, "tenure": "21"}, "consentData": {"consentType": "CI_CONSENT", "userIP": "*************, *************", "deviceId": "clmfyyrnj1kk5072l1m3duhpkBR:.*************", "deviceInfo": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1 FKUA/msite/0.0.1/msite/Mobile", "currentTimeStamp": *************}}, "product_type": "PERSONAL_LOAN", "form_details": null, "selfieScreen": {"documentId": "fk-p-calm-selfie_image-docs/APP2311281449309342163855751733674315846", "documentMeta": {"documentType": "SELFIE_IMAGE", "transactionId": "APP2311281449309342163855751733674315846", "status": "auto_approved", "livenessScore": 100}}, "financial_provider": "IDFC", "initiatePennyDrop": {"status": "S", "transactionId": "************", "errCode": "", "errMessage": "", "customerName": "<PERSON><PERSON>"}}, "sub_applications": {"sub_application_info_map": {}}, "external_user_id": "ACC6722F15B35D14D36B0D46FA3C930AD8BO", "application_type": "PERSONAL_LOAN_IDFC", "application_id": "APP2311281449309342163855751733674315846", "parent_application_id": null, "tenant": "CALM", "pending_task": [{"process_instance_id": "Tvw55_nJQtlNHBGe", "task_id": "B_GXm0nyfH3Gy6WV", "task_key": "repaymentSchedule", "form_key": null}], "application_state": "APPLICATION_STATUS", "merchant_id": "SHOPSY"}