<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="gaurav.nerwal (generated)" id="*************-1">
        <createTable tableName="BigfootIngestionAudit">
            <column name="entity_id" type="VARCHAR(150)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="entity_name" type="VARCHAR(50)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="trace_id" type="VARCHAR(256)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="mod_count" type="INT"/>
        </createTable>

        <createTable tableName="ConfigurableParameter">
            <column defaultValue="" name="id" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValue="" name="type" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="display_name" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="multi_input" type="VARCHAR(4)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="data_type" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="callback_uri" type="VARCHAR(512)"/>
            <column defaultValue="" name="searchable" type="VARCHAR(4)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="advanced" type="VARCHAR(4)">
                <constraints nullable="false"/>
            </column>
            <column name="enumerated_values" type="TEXT"/>
            <column name="stamp_created" type="timestamp"/>
            <column name="stamp_modified" type="timestamp"/>
            <column defaultValue="" name="configuration_context" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="Lender">
            <column defaultValue="" name="lender_id" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="lender_name" type="VARCHAR(11)"/>
            <column name="merchant_id" type="VARCHAR(11)"/>
            <column name="state" type="VARCHAR(11)"/>
            <column defaultValue="" name="lender_type" type="VARCHAR(11)">
                <constraints nullable="false"/>
            </column>
            <column name="stamp_created" type="timestamp"/>
            <column name="stamp_modified" type="timestamp"/>
            <column name="mod_count" type="TINYINT(3)"/>
        </createTable>

        <createTable tableName="LenderApproval">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="lender_id" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="applications_approved" type="INT"/>
            <column name="applications_rejected" type="INT"/>
            <column name="approval_weight" type="INT"/>
            <column name="created_at" type="timestamp"/>
            <column name="updated_at" type="timestamp"/>
        </createTable>

        <createTable tableName="mcc_description">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="created_at" type="timestamp"/>
            <column name="updated_at" type="timestamp"/>
            <column name="mcc_code" type="VARCHAR(5)"/>
            <column name="merchant_group" type="VARCHAR(50)"/>
            <column name="merchant_description" type="VARCHAR(50)"/>
        </createTable>

        <createTable tableName="LenderFeatures">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="lender_id" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="feature" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="source" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="weight" type="INT"/>
            <column name="value" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp"/>
            <column name="updated_at" type="timestamp"/>
        </createTable>

        <createTable tableName="REVINFO">
            <column autoIncrement="true" name="rev" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="revtstmp" type="BIGINT"/>
        </createTable>


        <createTable tableName="addresses">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="application_id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="address_line1" type="VARCHAR(255)"/>
            <column name="address_line2" type="VARCHAR(255)"/>
            <column name="city" type="VARCHAR(50)"/>
            <column name="postal_code" type="VARCHAR(20)"/>
            <column name="address_type" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="state" type="VARCHAR(50)"/>
        </createTable>


        <createTable tableName="application_forms">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="application_id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="email_id" type="VARCHAR(255)"/>
            <column name="first_name" type="VARCHAR(255)"/>
            <column name="phone" type="VARCHAR(20)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="pan_number" type="VARCHAR(50)"/>
            <column name="last_name" type="VARCHAR(255)"/>
            <column name="middle_name" type="VARCHAR(255)"/>
            <column name="gender" type="VARCHAR(10)"/>
            <column name="dob" type="date"/>
            <column name="pan_verified_name" type="VARCHAR(255)"/>
            <column name="highest_education_level" type="VARCHAR(50)"/>
            <column name="employment_status" type="VARCHAR(50)"/>
            <column name="occupation" type="VARCHAR(50)"/>
            <column name="job_title" type="VARCHAR(50)"/>
            <column name="employer_name" type="VARCHAR(50)"/>
            <column name="salary" type="DOUBLE"/>
        </createTable>

        <createTable tableName="application_id_map">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValue="" name="application_id" type="VARCHAR(255)">
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="external_id" type="VARCHAR(50)"/>
            <column name="external_ref_id" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="applications">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="borrower_id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="status" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="expiry_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="reject_reason" type="VARCHAR(255)"/>
            <column defaultValue="" name="aadhaar_number" type="VARCHAR(50)"/>
            <column defaultValue="" name="tracking_id" type="VARCHAR(50)">
                <constraints unique="true"/>
            </column>
            <column name="lender" type="VARCHAR(50)"/>
            <column name="masked_aadhaar" type="VARCHAR(50)"/>
            <column name="external_ref_id" type="VARCHAR(50)"/>
        </createTable>


        <createTable tableName="borrowers">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="true" name="enabled" type="BIT(1)"/>
            <column defaultValue="" name="external_id" type="VARCHAR(256)">
                <constraints nullable="false"/>
            </column>
            <column name="whitelist_id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="metadata" type="VARCHAR(150)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="communications">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="application_id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="display_id" type="VARCHAR(50)"/>
            <column name="type" type="VARCHAR(64)"/>
            <column name="message_id" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>


        <createTable tableName="journey">
            <column defaultValue="" name="journey_id" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="application_reference_id" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="journey_state" type="VARCHAR(50)"/>
            <column name="trigger_data" type="TEXT"/>
            <column defaultValueNumeric="0" name="version" type="INT"/>
        </createTable>

        <createTable tableName="journey_AUD">
            <column defaultValue="" name="journey_id" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="application_reference_id" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="journey_state" type="VARCHAR(50)"/>
            <column name="trigger_data" type="TEXT"/>
            <column name="rev" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueNumeric="0" name="revtype" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="version" type="INT"/>
        </createTable>

        <createTable tableName="locks">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="resource_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="key" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="lock_expiry_time" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="caller_key" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="locked" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="datetime"/>
            <column name="updated_at" type="datetime"/>
        </createTable>

        <createTable tableName="lcm">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="type" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="data" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="external_ref_id" type="VARCHAR(200)">
                <constraints nullable="true"/>
            </column>
            <column name="external_id" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="datetime"/>
            <column name="updated_at" type="datetime"/>
        </createTable>

        <createTable tableName="merchants">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="merchant_key" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="merchant_name" type="VARCHAR(255)"/>
            <column defaultValue="" name="status" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp"/>
            <column defaultValueNumeric="0" name="mod_count" type="TINYINT(3)"/>
        </createTable>

        <createTable tableName="nudges">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValue="" name="external_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="application_ref_id" type="VARCHAR(255)"/>
            <column name="product_type" type="VARCHAR(255)"/>
            <column name="type" type="VARCHAR(20)"/>
            <column name="status" type="VARCHAR(20)"/>
            <column name="request_data" type="VARCHAR(4096)"/>
            <column name="response_data" type="VARCHAR(4096)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="one_time_passwords">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="application_id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="reference_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="otp" type="VARCHAR(10)"/>
            <column name="mobile_number" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="attempt_count" type="TINYINT(3)"/>
            <column name="resend_count" type="TINYINT(3)"/>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="validation_token" type="VARCHAR(255)"/>
            <column name="device_id" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="expires_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="outbound_messages">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="message_id" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="relayed" type="BIT(1)"/>
            <column name="relayed_at" type="datetime"/>
            <column name="exchange_name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="message" type="TEXT"/>
            <column name="created_at" type="datetime"/>
            <column name="updated_at" type="datetime"/>
            <column name="inbound_message_id" type="INT"/>
            <column name="exchange_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="app_id" type="VARCHAR(50)"/>
            <column name="correlation_id" type="VARCHAR(100)"/>
            <column name="group_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="http_method" type="VARCHAR(10)"/>
            <column name="http_uri" type="VARCHAR(4096)"/>
            <column name="reply_to" type="VARCHAR(50)"/>
            <column name="reply_to_http_method" type="VARCHAR(10)"/>
            <column name="reply_to_http_uri" type="VARCHAR(255)"/>
            <column name="txn_id" type="VARCHAR(100)"/>
            <column name="routing_key" type="VARCHAR(100)"/>
            <column name="context" type="TEXT"/>
            <column name="destination_response_status" type="INT"/>
            <column name="relay_error" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="retries" type="INT"/>
            <column name="custom_headers" type="TEXT"/>
        </createTable>

        <createTable tableName="pincode">
            <column autoIncrement="true" name="id" type="INT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="lender" type="VARCHAR(50)"/>
            <column name="pincode" type="VARCHAR(10)"/>
            <column name="blacklisted" type="BIT(1)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp"/>
        </createTable>

        <createTable tableName="product_configs">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValue="" name="name" type="VARCHAR(256)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="product" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="lender" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="data" type="TEXT"/>
        </createTable>

        <createTable tableName="rule_attributes">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="group" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="true" name="active" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="VARCHAR(250)"/>
            <column name="source" type="VARCHAR(50)"/>
        </createTable>

        <createTable tableName="rule_configurations">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="name" type="VARCHAR(50)"/>
            <column defaultValue="" name="product_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="lender_id" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="rule_configurations_old">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="name" type="VARCHAR(50)"/>
            <column name="lender" type="VARCHAR(50)"/>
            <column defaultValue="" name="product_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="rules">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="name" type="VARCHAR(50)"/>
            <column defaultValueBoolean="true" name="active" type="BIT(1)"/>
            <column name="rule_sequence" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="rule_attr_grp" type="BIGINT UNSIGNED"/>
            <column name="expression" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="rule_configuration_id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="scheduler_idempotency">
            <column name="event_id" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true" unique="true"/>
            </column>
            <column name="application_reference_id" type="VARCHAR(50)"/>
            <column name="scheduler_status" type="VARCHAR(255)"/>
            <column name="timestamp" type="BIGINT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="journey_state" type="VARCHAR(50)"/>
            <column name="trigger_status" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="version" type="INT"/>
        </createTable>

        <createTable tableName="scheduler_idempotency_AUD">
            <column name="event_id" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="application_reference_id" type="VARCHAR(50)"/>
            <column name="scheduler_status" type="VARCHAR(255)"/>
            <column name="timestamp" type="BIGINT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="journey_state" type="VARCHAR(50)"/>
            <column name="trigger_status" type="VARCHAR(255)"/>
            <column name="rev" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueNumeric="0" name="revtype" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="version" type="INT"/>
        </createTable>

        <createTable tableName="sub_applications">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="application_id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="status" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="type" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="data" type="TEXT"/>
            <column name="external_ref_id" type="VARCHAR(200)"/>
        </createTable>

        <createTable tableName="tokens">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValue="" name="account_id" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="product_type" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="lender" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="token" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="device_id" type="VARCHAR(255)"/>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="expires_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="trigger_communication_configs">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="communication_mode" type="VARCHAR(20)"/>
            <column name="data" type="TEXT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="datetime"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="datetime"/>
            <column name="trigger_config_id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="trigger_configs">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="state" type="VARCHAR(255)"/>
            <column name="data" type="TEXT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="datetime"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="datetime"/>
            <column name="product_type" type="VARCHAR(20)"/>
            <column name="lender" type="VARCHAR(20)"/>
        </createTable>

        <createTable tableName="whitelist">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="whitelist_name" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="whitelist_desc" type="VARCHAR(255)"/>
            <column defaultValue="" name="product_type" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="merchant_id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp"/>
            <column name="meta_data" type="TEXT"/>
            <column name="rule_configs" type="TEXT"/>
            <column defaultValueBoolean="true" name="enabled" type="BIT(1)"/>
            <column defaultValue="" name="lender" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="workflows">
            <column autoIncrement="true" name="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValue="" name="status" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="correlation_id" type="VARCHAR(128)"/>
            <column name="application_id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-2">
        <addUniqueConstraint columnNames="journey_id, rev" constraintName="event_id_unique_contst" tableName="journey_AUD"/>
    </changeSet>


    <changeSet author="gaurav.nerwal (generated)" id="*************-3">
        <addUniqueConstraint columnNames="event_id, rev" constraintName="event_id_unique_const"
                             tableName="scheduler_idempotency_AUD"/>
    </changeSet>


    <changeSet author="gaurav.nerwal (generated)" id="*************-4">
        <addUniqueConstraint columnNames="group, name" constraintName="grp_name" tableName="rule_attributes"/>
    </changeSet>


    <changeSet author="gaurav.nerwal (generated)" id="*************-5">
        <addUniqueConstraint columnNames="resource_name, key" constraintName="idx_resource_key" tableName="locks"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-6">
        <addUniqueConstraint columnNames="name, product_type" constraintName="rule_config_uniques_const"
                             tableName="rule_configurations"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-7">
        <addUniqueConstraint columnNames="name, lender, product_type" constraintName="rule_config_unique_const"
                             tableName="rule_configurations_old"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-8">
        <addUniqueConstraint columnNames="whitelist_name, product_type" constraintName="unique_name_product"
                             tableName="whitelist"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-9">
        <createIndex indexName="application_idx" tableName="application_forms">
            <column name="application_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-10">
        <createIndex indexName="application_id_idx" tableName="addresses">
            <column name="application_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-11">
        <createIndex indexName="application_id_index" tableName="one_time_passwords">
            <column name="application_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-12">
        <createIndex indexName="borrower_idx" tableName="applications">
            <column name="borrower_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-13">
        <createIndex indexName="comm_application_idx" tableName="communications">
            <column name="application_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-14">
        <createIndex indexName="display_idx" tableName="communications">
            <column name="display_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-15">
        <createIndex indexName="exchange_name_index" tableName="outbound_messages">
            <column name="exchange_name"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-16">
        <createIndex indexName="external_id_idx" tableName="borrowers">
            <column name="external_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-18">
        <createIndex indexName="external_ref_id_idx" tableName="sub_applications">
            <column name="external_ref_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-19">
        <createIndex indexName="external_ref_id_index" tableName="applications">
            <column name="external_ref_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-20">
        <createIndex indexName="fk_trigger" tableName="trigger_communication_configs">
            <column name="trigger_config_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-21">
        <createIndex indexName="index_locks_on_caller_key" tableName="locks">
            <column name="caller_key"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-22">
        <createIndex indexName="index_outbound_messages_on_relayed" tableName="outbound_messages">
            <column name="relayed"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-23">
        <createIndex indexName="lender_id_idx" tableName="LenderApproval">
            <column name="lender_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-24">
        <createIndex indexName="lender_id_index" tableName="LenderFeatures">
            <column name="lender_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-25">
        <createIndex indexName="lender_id" tableName="rule_configurations">
            <column name="lender_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-28">
        <createIndex indexName="product_config_key" tableName="product_configs">
            <column name="name"/>
            <column name="product"/>
            <column name="lender"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-30">
        <createIndex indexName="product_lender_token" tableName="tokens">
            <column name="product_type"/>
            <column name="lender"/>
            <column name="token"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-31">
        <createIndex indexName="rule_config_idx" tableName="rules">
            <column name="rule_configuration_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-32">
        <createIndex indexName="sub_application_application_idx" tableName="sub_applications">
            <column name="application_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-33">
        <createIndex indexName="whitelist_id_idx" tableName="borrowers">
            <column name="whitelist_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-34">
        <createIndex indexName="workflow_application_idx" tableName="workflows">
            <column name="application_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-35">
        <addForeignKeyConstraint baseColumnNames="application_id" baseTableName="application_forms"
                                 constraintName="application" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="applications" validate="true"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-36">
        <addForeignKeyConstraint baseColumnNames="application_id" baseTableName="addresses"
                                 constraintName="application_id" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="applications" validate="true"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-37">
        <addForeignKeyConstraint baseColumnNames="application_id" baseTableName="communications"
                                 constraintName="comm_application" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="applications" validate="true"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-38">
        <addForeignKeyConstraint baseColumnNames="trigger_config_id" baseTableName="trigger_communication_configs"
                                 constraintName="fk_trigger" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="trigger_configs" validate="true"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-39">
        <addForeignKeyConstraint baseColumnNames="lender_id" baseTableName="LenderApproval"
                                 constraintName="lenderapproval_ibfk_1" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="lender_id"
                                 referencedTableName="Lender" validate="true"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-40">
        <addForeignKeyConstraint baseColumnNames="lender_id" baseTableName="LenderFeatures"
                                 constraintName="lenderfeatures_ibfk_1" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="lender_id"
                                 referencedTableName="Lender" validate="true"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-43">
        <addForeignKeyConstraint baseColumnNames="rule_configuration_id" baseTableName="rules"
                                 constraintName="rule_config" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="rule_configurations_old" validate="true"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-44">
        <addForeignKeyConstraint baseColumnNames="lender_id" baseTableName="rule_configurations"
                                 constraintName="rule_configurations_ibfk_1" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="lender_id" referencedTableName="Lender" validate="true"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-45">
        <addForeignKeyConstraint baseColumnNames="application_id" baseTableName="sub_applications"
                                 constraintName="sub_application_application" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="applications" validate="true"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-46">
        <addForeignKeyConstraint baseColumnNames="whitelist_id" baseTableName="borrowers" constraintName="whitelist_id"
                                 deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="whitelist" validate="true"/>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-47">
        <addForeignKeyConstraint baseColumnNames="application_id" baseTableName="workflows"
                                 constraintName="workflow_application" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="applications" validate="true"/>
    </changeSet>
    <changeSet author="gaurav.nerwal (generated)" id="*************-48">
        <insert tableName="merchants">
            <column defaultValueNumeric="0" name="mod_count" type="TINYINT(3)"/>
            <column name="id" value="1"/>
            <column name="merchant_key" value="mp_flipkart"/>
            <column name="status" value="ACTIVE"/>
            <column name="merchant_name" value="marketplace flipkart"/>
            <column name="mod_count" value="0"/>
        </insert>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-49">
        <insert tableName="whitelist">
            <column name="id" value="66"/>
            <column name="whitelist_name" value="test_whitelist"/>
            <column name="whitelist_desc" value="test description"/>
            <column name="product_type" value="CBC"/>
            <column name="merchant_id" value="1"/>
            <column name="enabled" value="1"/>
            <column name="meta_data" value='{"isFlipkartPlus":"true"}'/>
            <column name="lender" value="AXIS"/>
        </insert>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-50">
        <insert tableName="borrowers">
            <column name="id" value="898989"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST12345"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"NTB","display_id":"77909b39c33841e1"}'/>
        </insert>
        <insert tableName="borrowers">
            <column name="id" value="898988"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST10000"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"NTB","display_id":"77909b39c3384111"}'/>
        </insert>
        <insert tableName="borrowers">
            <column name="id" value="898987"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST10001"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"NTB","display_id":"77909b39c33841311"}'/>
        </insert>
        <insert tableName="borrowers">
            <column name="id" value="898986"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST10004"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"ETB_PRE_APPROVED","display_id":"77909b39c33841311"}'/>
        </insert>
        <insert tableName="borrowers">
            <column name="id" value="898985"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST10006"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"ETCC","display_id":"77909b39c33841311"}'/>
        </insert>
        <insert tableName="borrowers">
            <column name="id" value="898984"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST10005"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"NTB","display_id":"77909b39c33841311"}'/>
        </insert>
        <insert tableName="borrowers">
            <column name="id" value="898983"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST10007"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"NTB","display_id":"77909b31c33841311"}'/>
        </insert>
        <insert tableName="borrowers">
            <column name="id" value="898982"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST10008"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"NTB","display_id":"77909b31c33841311"}'/>
        </insert>
        <insert tableName="borrowers">
            <column name="id" value="898981"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST10009"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"ETB_NON_PRE_APPROVED","display_id":"77909b39c33841311"}'/>
        </insert>
        <insert tableName="borrowers">
            <column name="id" value="898111"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST10010"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"NTB","display_id":"77909b31c31841311"}'/>
        </insert>
        <insert tableName="borrowers">
            <column name="id" value="898112"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST10011"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"NTB","display_id":"77909b31c31141311"}'/>
        </insert>
        <insert tableName="borrowers">
            <column name="id" value="898113"/>
            <column name="enabled" value="1"/>
            <column name="external_id" value="ACCTEST10012"/>
            <column name="whitelist_id" value="66"/>
            <column name="metadata" value='{"cohort":"NTB","display_id":"77909b31c31141311"}'/>
        </insert>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-51">
        <update tableName="merchants"><column name="mod_count" value="0"/>
            <where>id=1</where></update>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-52">
        <insert tableName="lcm">
            <column name="type" value="LCM_DATA"/>
            <column name="data" value='{"approvedDate":1633289518389,"cardSerno":"19513","validationToken":null,"deviceId":"e193399f623f4e6d","applicationId":"APP1024","status":"APPROVED"}'/>
            <column name="external_ref_id" value="19513"/>
            <column name="external_id" value='ACCTEST10011'/>
        </insert>
        <insert tableName="lcm">
            <column name="type" value="VERIFY_OTP"/>
            <column name="data" value='{"baseResponse":{"info":null,"STATUS":"SUCCESS"},"otpDetails":{"otpReferenceId":null,"otp":null,"isOtpGenerated":false,"isOtpValidated":true},"lenderInfoSection":{"lenderApplicationReferenceNo":null,"lenderLeadReferenceNo":null,"lenderReferenceInfos":{"validationToken":"AAIkNjY5N2QwZTYtNzRkZi00NzY2LTgzZDgtNjc5NzdmMmExZDUw4Ak3GeATtssNxyYzw2GeDIU0z_bseH5WoxFZdidSG8kNZVaVVtcCJ8h-Y_AU2A6PrOZZmqy0UQVipv5A-kck3owxDYru5_ItUOzpUUzRpYpPwVIz2B__opMCvedhKVPNBR05MifFCnH3PwxQnxNXdZ9BoWUa5TbtZ1p8DhM24n0"}}}'/>
            <column name="external_id" value='ACCTEST10011'/>
        </insert>
    </changeSet>

    <changeSet author="gaurav.nerwal (generated)" id="*************-53">
        <insert tableName="tokens">
            <column name="id" value="1"/>
            <column name="account_id" value="ACCTEST10011"/>
            <column name="product_type" value="CBC"/>
            <column name="lender" value="AXIS"/>
            <column name="token" value="46c379d1-0d3c-412e-bce2-ba330c09220f"/>
            <column name="device_id" value="550e6ddfcf0cf2b61f358cbc2d8a0a31"/>
            <column name="status" value="CREATED"/>
            <column name="expires_at" value="2023-10-04 06:37:24.86"/>
        </insert>
        <insert tableName="tokens">
            <column name="id" value="2"/>
            <column name="account_id" value="ACCTEST10011"/>
            <column name="product_type" value="CBC"/>
            <column name="lender" value="AXIS"/>
            <column name="token" value="46c379d1-0d3c-412e-bce2-ba330c09220a"/>
            <column name="device_id" value="550e6ddfcf0cf2b61f358cbc2d8a0a31"/>
            <column name="status" value="CREATED"/>
            <column name="expires_at" value="2023-10-04 06:37:24.86"/>
        </insert>
    </changeSet>

<!--    <changeSet author="gaurav.nerwal (generated)" id="*************-56">-->
<!--        <insert tableName="applications">-->
<!--            <column name="id" value="1000"/>-->
<!--            <column name="borrower_id" value="898988"/>-->
<!--            <column name="status" value="APPROVED"/>-->
<!--            <column name="expiry_date" value="2020-04-21 22:44:03"/>-->
<!--            <column name="aadhaar_number" value="z8Jnoqfjhabhj=="/>-->
<!--            <column name="tracking_id" value="APL-8307823"/>-->
<!--            <column name="lender" value="IDFC"/>-->
<!--            <column name="masked_aadhaar" value="xxxxxxxx3748"/>-->
<!--        </insert>-->
<!--    </changeSet>-->

<!--    <changeSet author="gaurav.nerwal (generated)" id="*************-57">-->
<!--        <update tableName="applications">-->
<!--            <column name="external_ref_id" value="TESTEXTID"/>-->
<!--            <where>borrower_id=898989</where>-->
<!--        </update>-->
<!--    </changeSet>-->

<!--    <changeSet author="gaurav.nerwal (generated)" id="*************-58">-->
<!--        <insert tableName="borrowers">-->
<!--            <column name="enabled" value="1"/>-->
<!--            <column name="external_id" value="ACCTEST12020326"/>-->
<!--            <column name="whitelist_id" value="1"/>-->
<!--        </insert>-->
<!--    </changeSet>-->

<!--    <changeSet author="gaurav.nerwal (generated)" id="*************-63">-->
<!--        <insert tableName="sub_applications">-->
<!--            <column name="application_id" value="1000000"/>-->
<!--            <column name="status" value="COMPLETED"/>-->
<!--            <column name="type" value="PAYMENT_INSTRUMENT_GENERATION"/>-->
<!--            <column name="external_ref_id" value="103394"/>-->
<!--        </insert>-->
<!--    </changeSet>-->
</databaseChangeLog>
