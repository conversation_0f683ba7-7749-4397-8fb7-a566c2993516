{"REPEAT_LOAN_LP": [{"name": "repeatLoanForAxis1", "expression": "(application.disbursedLoanParams!=null && application.disbursedLoanParams.lender==\"AXIS\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForMV", "expression": "(application.disbursedLoanParams.lender==\"MONEYVIEW\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForMV2", "expression": "(application.disbursedLoanParams.lender==\"MONEYVIEWV2\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForMVOPENMKT", "expression": "(application.disbursedLoanParams.lender==\"MONEYVIEWOPENMKT\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForMVMFI", "expression": "(application.disbursedLoanParams.lender==\"MONEYVIEWMFI\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForDMI", "expression": "(application.disbursedLoanParams.lender==\"DMI\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForCS", "expression": "(application.disbursedLoanParams.lender==\"OMNI\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForCSV2", "expression": "(application.disbursedLoanParams.lender==\"OMNIV2\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForRING", "expression": "(application.disbursedLoanParams.lender==\"RING\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForSMARTCOIN", "expression": "(application.disbursedLoanParams.lender==\"SMARTCOIN\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForPFL", "expression": "(application.disbursedLoanParams.lender==\"PFL\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForABFL", "expression": "(application.disbursedLoanParams.lender==\"ABFL\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForPREFR", "expression": "(application.disbursedLoanParams.lender==\"PREFR\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForFIBE", "expression": "(application.disbursedLoanParams.lender==\"FIBE\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}, {"name": "repeatLoanForFIBEV2", "expression": "(application.disbursedLoanParams.lender==\"FIBEV2\" && application.disbursedLoanParams.daysPastDisbursal >= 0 && application.ongoingLoanParams==null)"}], "RESUME_JOURNEY_LOAN_LP": [{"name": "default", "expression": "true"}]}