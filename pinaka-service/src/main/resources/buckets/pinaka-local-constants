{"loader.default.title.style.color": "#212121", "FFB.device.alt-data.privacy-policy-sub-text": "privacyPolicySubText", "loader.default.CHECK_ELIGIBILITY.title.text": "Checking your eligibility..", "FFB.default.landing.url": "/rv/fintech/advanz/onboarding", "advanz.loader.url": "/rv/fintech/loader?context=%s&productType=%s", "loader.expired.KYC_COMPLETED.subtitle.text": "We will contact you once this is finished", "FFB.device.alt-data.pahe-headers": "Flipkart Pay Later", "loader.expired.subtitle.style.fontsize": 14, "loader.expired.LANDING_LOADER.title.text": "This seems to be taking some time", "FFB.device.alt-data.permission-details": "[{\r\n\"type\": \"SMS\",\r\n\"title\": \"SMS\",\r\n\"text\": \"We never read your personal messages\",\r\n\"icon\": \"https://rukminim1.flixcart.com/www/224/224/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q=100\",\r\n\"errorText\": \"error text\",\r\n\"errorIcon\": \"https://rukminim1.flixcart.com/www/224/224/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q=100\",\r\n\"mandatory\": true\r\n},\r\n{\r\n\"type\": \"CONTACT\",\r\n\"title\": \"Contact\",\r\n\"text\": \"We never read your personal messages\",\r\n\"icon\": \"rukmini icon url\",\r\n\"errorText\": \"error text\",\r\n\"errorIcon\": \"https://rukminim1.flixcart.com/www/224/224/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q=100\",\r\n\"mandatory\": false\r\n},\r\n{\r\n\"type\": \"LOCATION\",\r\n\"title\": \"Location\",\r\n\"text\": \"We never read your personal messages\",\r\n\"icon\": \"rukmini icon url\",\r\n\"errorText\": \"error text\",\r\n\"errorIcon\": \"https://rukminim1.flixcart.com/www/224/224/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q=100\",\r\n\"mandatory\": false\r\n},\r\n{\r\n\"type\": \"DEVICE\",\r\n\"title\": \"Device Information\",\r\n\"text\": \"We never read your personal messages\",\r\n\"icon\": \"rukmini icon url\",\r\n\"errorText\": \"error text\",\r\n\"errorIcon\": \"https://rukminim1.flixcart.com/www/224/224/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q=100\",\r\n\"mandatory\": false\r\n}\r\n]", "FFB.device.alt-data.mandatory-permissions": ["SMS"], "loader.body.layout.bgcolor": "#FFFFFF", "loader.expired.subtitle.style.color": "#212121", "FFB.device.alt-data.subtitle": "Please provide following permissions and help us to reprocess your application.", "kyc.store-page.banner.horizontal-banners": ["{\"alt\":\"\",\"url\":\"https://rukminim1.flixcart.com/www/78/78/promos/07/04/2020/faf54c04-6ffa-4232-ad71-e7a1f92652fe.png?q=100\",\"textContent\":\"Get a Instant Credit up to ?6,000\"}", "{\"alt\":\"\",\"url\":\"https://rukminim1.flixcart.com/www/78/78/promos/09/04/2020/fcb969a2-6ee5-43ea-a974-d3bf54a4101a.png?q=100\",\"textContent\":\"Pay next month at no extra cost\"}", "{\"alt\":\"\",\"url\":\"https://rukminim1.flixcart.com/www/78/78/promos/09/04/2020/61be33d3-ab53-4e5c-970f-25b6d97b1b8b.png?q=100\",\"textContent\":\"Get instant refunds\"}", "{\"alt\":\"\",\"url\":\"https://rukminim1.flixcart.com/www/78/78/promos/09/04/2020/84814e61-a097-4581-9082-76331f25e26f.png?q=100\",\"textContent\":\"Available on your favorite apps\",\"postTextImage\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/04/2020/8e703a08-a09c-48a7-9b35-1277c804c662.png?q={@quality}\"}"], "loader.default.title.style.lineHeight": 20, "secretKey": "Cd1/SXipT)So3=19", "FFB.continue.shopping.action": "{\r\n     \"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n     \"omnitureData\": \"deep_linking\",\r\n     \"params\": {\r\n          \"valid\": true,\r\n          \"screenName\": \"homepage\",\r\n          \"context\": \"{\\\"action\\\":{\\\"type\\\":\\\"NAVIGATION\\\",\\\"payload\\\":{\\\"url\\\":\\\"\\/\\\"}}}\"\r\n     },\r\n     \"screenType\": \"CALLBACK_TO_PARENT_APP\",\r\n\r\n     \"tracking\": {\r\n          \"omnitureData\": \"deep_linking\"\r\n     },\r\n     \"type\": \"NAVIGATION\"\r\n}", "loader.expired.title.style.lineHeight": 20, "pennyDrop.allowedAttempts": 5, "loader.LANDING_LOADER.loaderTimeout": 60, "loader.default.title.style.textAlign": "center", "FK_ADVANZ.store.page.landing.url": "/flipkart-pay-later-store", "product.page.utm.source": "PRODUCT_PAGE", "FFB.device.alt-data.tnc-url": "https://www.flipkart.com/pages/advanz-service-tnc", "device.alt.data.processing.ui.params": "{\n\t\"pageTitle\": \"Application Processing\",\n\t\"title\": \"Thank you for providing phone permissions\",\n\t\"subtitle\": \"We will reprocess your application and notify you within few minutes\",\n\t\"buttonText\": \"Continue Shopping\",\n\t\"icon\": \"rukmini url\"\n}", "loader.expired.subtitle.style.lineHeight": 20, "kyc.product-page-exp.banner.image.width": 100, "loader.default.title.style.style": "normal", "sms.permission.consent.context": "DEVICE-PERMISSIONS-CONSENT-V0", "loader.default.KYC_COMPLETED.title.text": "Completing your KYC..", "web.view.loader.pageId": "payla-categ-e8a4a", "loader.expired.title.style.style": "normal", "loader.expired.button.text": "Continue Shopping", "loader.default.subtitle.style.color": "#212121", "kyc.store-page.banner.image.url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d388836b-a541-4f36-95df-49818d720bb7.png?q={@quality}", "loader.default.title.style.fontsize": 16, "loader.body.layout.width": "100%", "loader.KYC_COMPLETED.loaderTimeout": 60, "FFB.device.alt-data.optional-permissions": ["CONTACT", "DEVICE"], "FFB.device.alt-data.tnc-text": "T&C", "FK_ADVANZ.continue.shopping.action": "{\r\n\t\"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n\t\"omnitureData\": \"deep_linking\",\r\n\t\"params\": {\r\n\t\t\"valid\": true,\r\n\t\t\"screenName\": \"homepage\"\r\n\t},\r\n\t\"screenType\": \"multiWidgetPage\",\r\n\r\n\t\"tracking\": {\r\n\t\t\"omnitureData\": \"deep_linking\"\r\n\t},\r\n\t\"type\": \"NAVIGATION\"\r\n\r\n\r\n}", "FFB.device.alt-data.page-headers": "Flipkart Pay Later", "loader.default.subtitle.style.textAlign": "center", "loader.expired.title.style.fontsize": 16, "advanz.store.page.url": "/pages/mw/cbc/loader?context=LANDING_LOADER&product=FLIPKART_ADVANZ", "kyc.product-page-exp.banner.image.height": 100, "loader.default.KYC_COMPLETED.subtitle.text": "This may take few seconds", "loader.default.CHECK_ELIGIBILITY.subtitle.text": "Just give us upto 60 seconds to help us calculate your credit limit", "FFB.device.alt-data.privacy-policy-text": "privacy-policy", "FFB.EKYC.landing.url": "/rv/fintech/advanz/onboarding", "loader.expired.subtitle.style.textAlign": "center", "kyc.product-page-exp.banner.image.url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d388836b-a541-4f36-95df-49818d720bb8.png?q={@quality}", "pinaka.store.page.ids": ["finte-categ-57ce6"], "communication.pn.flipkart.advanz.title": "<PERSON><PERSON><PERSON><PERSON>", "loader.CHECK_ELIGIBILITY.loaderTimeout": 30, "FFB.device.alt-data.privacy-policy-url": "https://www.flipkart.com/pages/fapl-privacy-policy", "loader.body.layout.height": "100%", "loader.expired.CHECK_ELIGIBILITY.subtitle.text": "We will contact you once this is finished", "loader.default.LANDING_LOADER.subtitle.text": "This may take few seconds", "communication.pn.flipkart.advanz.rejection.text": "Sorry. Based on the available data, we are unable to activate Flipkart Pay Later at this moment. Click here to shop on Flipkart for your favourite products", "loader.default.LANDING_LOADER.title.text": "Please Wait...", "pennyDrop.nameMatchThreshold": 70, "loader.body.layout.color": "#212121", "pennyDrop.applicationType": "penny_drop_v1", "loader.slot.layout.height": "100%", "loader.expired.title.style.textAlign": "center", "pennyDrop.dataTypePriorityList": ["SAVED_ACCOUNT", "BANK_ACCOUNT"], "loader.default.subtitle.style.fontsize": 14, "kyc.banner.experiment.name": "kycMethod", "loader.CHECK_ELIGIBILITY.refreshIntervalInSeconds": 3, "loader.KYC_COMPLETED.refreshIntervalInSeconds": 2, "loader.slot.layout.color": "#212121", "loader.default.subtitle.style.lineHeight": 20, "ffb.loader.url": "/rv/fintech/loader?context=%s&productType=%s&tenantId=FFB", "loader.slot.layout.bgcolor": "#FFFFFF", "advanz.store.page.mapi.screen.name": "multiWidgetPage", "kyc.store-page.banner.image.height": 100, "kyc.page.consent.context": "KYC-PAGE-CONSENT-V0", "ams.FFB.application-type": "ffb_async_cohort_v6", "ams.FK_CONSUMER_CREDIT.FLIPKART_ADVANZ.application-type": "advanz_onboarding_v1", "ams.FK_CONSUMER_CREDIT.CFA.application-type": "cfa_onboarding_v1", "FFB.kyc-upgrade-application-type": "kyc_upgrade_v3", "FK_CONSUMER_CREDIT.kyc-upgrade-application-type": "kyc_upgrade_v3", "FFB.underwriting.rule.name": "ffb_underwriting_rule_group_v1", "loader.default.subtitle.style.style": "normal", "loader.expired.subtitle.style.style": "normal", "loader.expired.LANDING_LOADER.subtitle.text": "We will contact you once this is finished", "kyc.store-page.banner.image.width": 100, "pennyDrop.IDFC.disclaimer": "By tapping on Confirm, your account details will be shared with our lender, IDFC First bank", "loader.slot.layout.width": "100%", "loader.LANDING_LOADER.refreshIntervalInSeconds": 3, "loader.expired.KYC_COMPLETED.title.text": "This seems to be taking some time", "FFB.device.alt-data.title": "Not enough Information", "loader.expired.title.style.color": "#212121", "loader.expired.CHECK_ELIGIBILITY.title.text": "This seems to be taking some time", "FFB.loader.url": "/rv/fintech/loader?context=%s&productType=%s&tenantId=FFB&aapiContext=true", "databaseConfigUrl": "**********************************", "databaseConfigUser": "root", "databaseConfigPassword": "", "databaseSlaveConfigUrl": "**********************************", "databaseSlaveConfigUser": "root", "databaseSlaveConfigPassword": "", "FFB.IDFC.FLIPKART_ADVANZ.valid.age.range": "21-65", "FK_CONSUMER_CREDIT.IDFC.FLIPKART_ADVANZ.valid.age.range": "18-100", "kycKey": "03A1CE01ED4D5FA1ABBDCD728C0BE2A8", "cbcKey": "59AC6C2B95DEFC3EC76C56CF232AF829", "default": "59AC6C2B95DEFC3EC76C56CF232AF829", "enableFKPGSavedAccountFetch": false, "enableFKPGSavedAccountTestAccounts": ["ACC798D3CC5AC4447869C1B1DD645A7128AZ"], "ebc.banner.url": "/dummy", "ebc.banner.height": "50", "ebc.banner.width": "100", "ebc.banner.landingUrl": "/dummy", "ebcEncryptionKey": "C1F)J@NcRfUjXn33", "ebc.notEligible.errorcode": "NOT_ELIGIBLE_FOR_EBC", "ebc.notEligible.errorTitle": "Something went wrong", "ebc.notEligible.errorMsg": "Not eligible for ebc", "ebc.loader.url": "/pages/mw/cbc/loader?context=%s&product=%s", "ebc.idfc.landing.url": "https://uat-capitalfirst.cs113.force.com/CustomerFlow/s/fkebc?crn=%s&token=%s", "ebc.notFullKyc.errorcode": "NOT_FULL_KYC_FOR_EBC", "ebc.notFullKyc.errorTitle": "Something went wrong", "ebc.notFullKyc.errorMsg": "Not Full KYC", "ebc.tokenGeneration.errorTitle": "Something went wrong", "ebc.tokenGeneration.errorcode": "TOKEN_GENERATION_ERROR_EBC", "ebc.encryption.failed.errorcode": "AES_ENCRYPTION_FAILED", "ebc.encryption.failed.errorTitle": "Something went wrong", "ebc.disableUpgradeKycFlow": true, "enableEbc": true, "pennyDrop.defaultBankIcon": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/11/08/2020/4113b595-1e31-406f-8f10-0989db25c167.png?q={@quality}", "FFB.kycUpgrade.approval.page.content": "{\"head\":{\"title\":{\"text\":\"Easy Credit\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/09/2020/d9f7686e-2501-4618-b44d-1dc44ce5727d.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Successfully upgraded to Full KYC!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Continue using Easy Credit without any restrictions!\"},{\"widget\":\"BUTTON\",\"text\":\"Continue Shopping\",\"action\":{\"params\":{\"context\":\"{\\\"action\\\":{\\\"type\\\":\\\"NAVIGATION\\\",\\\"payload\\\":{\\\"navigationType\\\":\\\"BACK\\\",\\\"url\\\":\\\"\\/\\\"}}}\"},\"screenType\":\"CALLBACK_TO_PARENT_APP\"}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.kycUpgrade.approval.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/09/2020/d9f7686e-2501-4618-b44d-1dc44ce5727d.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Successfully upgraded to Full KYC!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Continue using Flipkart Pay Later without any restrictions!\"},{\"widget\":\"BUTTON\",\"text\":\"Continue Shopping\",\"action\":{\r\n\t\"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n\t\"omnitureData\": \"deep_linking\",\r\n\t\"params\": {\r\n\t\t\"valid\": true,\r\n\t\t\"screenName\": \"homepage\"\r\n\t},\r\n\t\"screenType\": \"multiWidgetPage\",\r\n\r\n\t\"tracking\": {\r\n\t\t\"omnitureData\": \"deep_linking\"\r\n\t},\r\n\t\"type\": \"NAVIGATION\"\r\n\r\n\r\n}}]},\"footer\":{}}}", "FFB.CKYC.DEFAULT.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Easy Credit\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Something went wrong!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your KYC could not be completed due to some technical issues. You can try again using another method.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FFB.AADHAAR_XML_EKYC.DEFAULT.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Easy Credit\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Something went wrong!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"We couldn't complete the kyc due to some technical issue. Please try again or connect with our customer care if problem persists.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FFB.AADHAAR_XML_EKYC.PAN_NOT_FOUND.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Easy Credit\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"PAN Not Found!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"We couldn’t find any details linked with the entered PAN : %s. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FFB.CKYC.AADHAAR_AND_PAN_NAME_MISMATCH.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Easy Credit\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"PAN and Aadhaar Mismatch!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your Name as per PAN (%s) and <PERSON><PERSON><PERSON><PERSON> (%s) doesn't match. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FFB.AADHAAR_XML_EKYC.AADHAAR_AND_PAN_NAME_MISMATCH.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Easy Credit\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"PAN and Aadhaar Mismatch!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your Name as per PAN (%s) and <PERSON><PERSON><PERSON><PERSON> (%s) doesn't match. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://my.flipkart.com:3000/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "EXPIRED.daysLeftForExpiry.threshold": 0, "NEARING_EXPIRY.daysLeftForExpiry.threshold": 120, "kycUpgrade.disclaimer": "Use your bank details and upgrade in 60 secs", "IDFC.kycUpgrade.url": "/pages/mw/cbc/loader?context=KYC_UPGRADE&product=FLIPKART_ADVANZ", "independentPennyDropApplicationTypes": ["penny_drop_v1"], "FK_CONSUMER_CREDIT.AADHAAR_XML_EKYC.AADHAAR_AND_PAN_NAME_MISMATCH.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"<PERSON><PERSON> and Aadhaar Mismatch!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your Name as per PAN (%s) and <PERSON><PERSON><PERSON><PERSON> (%s) doesn't match. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.AADHAAR_XML_EKYC.AADHAAR_AND_PAN_NAME_MISMATCH.PAY_LATER.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"<PERSON><PERSON> and Aadhaar Mismatch!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your Name as per PAN (%s) and <PERSON><PERSON><PERSON><PERSON> (%s) doesn't match. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.AADHAAR_XML_EKYC.AADHAAR_AND_PAN_NAME_MISMATCH.FSUP.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Smart Upgrade\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"PA<PERSON> and Aadhaar Mismatch!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your Name as per PAN (%s) and <PERSON><PERSON><PERSON><PERSON> (%s) doesn't match. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.CKYC.DEFAULT.PAY_LATER.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Something went wrong!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your KYC could not be completed due to some technical issues. You can try again using another method.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.CKYC.DEFAULT.FSUP.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Smart Upgrade\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Something went wrong!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your KYC could not be completed due to some technical issues. You can try again using another method.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.CKYC.AADHAAR_AND_PAN_NAME_MISMATCH.PAY_LATER.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"<PERSON><PERSON> and Aadhaar Mismatch!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your Name as per PAN (%s) and <PERSON><PERSON><PERSON><PERSON> (%s) doesn't match. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.CKYC.AADHAAR_AND_PAN_NAME_MISMATCH.FSUP.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Smart Upgrade\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"PA<PERSON> and Aadhaar Mismatch!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your Name as per PAN (%s) and <PERSON><PERSON><PERSON><PERSON> (%s) doesn't match. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.CKYC.DEFAULT.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Something went wrong!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your KYC could not be completed due to some technical issues. You can try again using another method.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.CKYC.AADHAAR_AND_PAN_NAME_MISMATCH.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"<PERSON><PERSON> and Aadhaar Mismatch!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Your Name as per PAN (%s) and <PERSON><PERSON><PERSON><PERSON> (%s) doesn't match. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.AADHAAR_XML_EKYC.PAN_NOT_FOUND.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"PAN Not Found!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"We couldn’t find any details linked with the entered PAN : %s. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.AADHAAR_XML_EKYC.PAN_NOT_FOUND.PAY_LATER.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"PAN Not Found!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"We couldn’t find any details linked with the entered PAN : %s. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.AADHAAR_XML_EKYC.PAN_NOT_FOUND.FSUP.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Smart Upgrade\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"PAN Not Found!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"We couldn’t find any details linked with the entered PAN : %s. Please retry the KYC Process.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=null\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.AADHAAR_XML_EKYC.DEFAULT.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Something went wrong!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"We couldn't complete the kyc due to some technical issue. Please try again or connect with our customer care if problem persists.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.AADHAAR_XML_EKYC.DEFAULT.PAY_LATER.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Something went wrong!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"We couldn't complete the kyc due to some technical issue. Please try again or connect with our customer care if problem persists.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true\",\"validationMeta\":null}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.AADHAAR_XML_EKYC.DEFAULT.FSUP.kyc_reject.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Smart Upgrade\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/02/2020/c0351ada-c375-4042-ae1f-617d57c544c0.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Something went wrong!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"We couldn't complete the kyc due to some technical issue. Please try again or connect with our customer care if problem persists.\"},{\"widget\":\"BUTTON\",\"text\":\"Try Again\",\"action\":{\"constraints\":null,\"fallback\":null,\"loginType\":\"LOGIN_NOT_REQUIRED\",\"omnitureData\":null,\"originalUrl\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\",\"params\":{\"valid\":true,\"killCurrentPage\":true,\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\"},\"requiredPermissionType\":null,\"screenType\":\"webView\",\"tracking\":{},\"type\":\"NAVIGATION\",\"url\":\"http://rv-next2.flipkart.com/rv/fintech/loader?context=KYC_UPGRADE&productType=FLIPKART_ADVANZ&aapiContext=true&productSubType=FSUP\",\"validationMeta\":null}}]},\"footer\":{}}}", "validKycApplicationTypesForUSL": ["CKYC_v1", "XML_KYC_v1"], "ams.onboarding.test.kyc.method": "CKYC", "ams.onboarding.skipKyc": true, "ams.onboarding.fallback.kyc.method": "AADHAAR_XML_EKYC", "mockCeWithUnderwriting": true, "test.alt.data": "SMS", "activeKycMethods": ["CKYC", "EKYC", "AADHAAR_XML_EKYC"], "kyc.method.for.test.account": "CKYC", "IDFC.pre.loan.kyc.valid.days": 80, "activeLenders": ["IDFC", "AXIS"], "cfa.nudge.enabled": true, "FK_CONSUMER_CREDIT.loader.url": "/rv/fintech/loader?context=%s&productType=%s&tenantId=FK_CONSUMER_CREDIT&aapiContext=true", "FK_CONSUMER_CREDIT.default.landing.url": "/rv/fintech/advanz/onboarding?aapiContext=true&productContext=%s", "cfa.notEligible.errorCode": "NOT_ELIGIBLE", "cfa.notEligible.errorMsg": "Sorry, you are not eligible for credit", "cfa.notEligible.errorTitle": "Not Eligible", "cfa.IDFC.landing.url": "https://www.flipkart.com/flipkart-pay-later-store", "cfa.redirection.url": "https://www.flipkart.com/rv/fintech/webview-opener?url=%s", "cfa.IDFC.postOnboarding.approval.pageUrl": "/flipkart-pay-later-store", "cfa.IDFC.postOnboarding.rejection.pageUrl": "/flipkart-pay-later-store", "cfa.IDFC.preOnboarding.pageUrl": "/flipkart-pay-later-store", "FK_CONSUMER_CREDIT.continue.shopping.action": "{\r\n\t\"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n\t\"omnitureData\": \"deep_linking\",\r\n\t\"params\": {\r\n\t\t\"valid\": true,\r\n\t\t\"screenName\": \"homepage\"\r\n\t},\r\n\t\"screenType\": \"multiWidgetPage\",\r\n\r\n\t\"tracking\": {\r\n\t\t\"omnitureData\": \"deep_linking\"\r\n\t},\r\n\t\"type\": \"NAVIGATION\"\r\n\r\n\r\n}", "loader.PRE_ONBOARDING_LANDING_LOADER.loaderTimeout": 60, "loader.PRE_ONBOARDING_LANDING_LOADER.refreshIntervalInSeconds": 3, "loader.default.PRE_ONBOARDING_LANDING_LOADER.subtitle.text": "This may take few seconds", "loader.default.PRE_ONBOARDING_LANDING_LOADER.title.text": "Please Wait...", "cbc.live_cohorts": ["ETCC", "ETB_NON_PRE_APPROVED", "ETB_PRE_APPROVED", "NTB", "ETS", "OFFLINE", "CARD_CONSOLE"], "desktop.loader.url": "https://www.flipkart.com/pages/mw/cbc/apply-now/loader", "cfa.IDFC.cardIngestionEnabled": true, "cbc.banner.url": "fad", "cbc.banner.height": 10, "cbc.banner.width": 30, "cbc.banner.landingUrl.NTB": "NTB_URL", "cbc.banner.landingUrl.ETCC": "ETCC_URL", "cbc.banner.landingUrl.ETB_PRE_APPROVED": "ETB_PRE_APPROVED_URL", "cbc.banner.landingUrl.ETB_NON_PRE_APPROVED": "ETB_NON_PRE_APPROVED_URL", "cbc.banner.landingUrl.ETS": "ETS_URL", "cbc.advanz.discovery": true, "idd.enabled": true, "idd.enabled.accountIds": ["ACCB4DEB84ADC7F4558B066A1ED494509ECR"], "defaultKycMethod": "CKYC", "abEnabledForKyc": false, "abEnabledForPennyDrop": false, "NOT_ELIGIBLE_FOR_CFA.ui.response": "{\"head\":{\"title\":{\"text\":\"EMIs on Flipkart\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/10/2020/8f7b04ee-e36a-4f79-a568-65331e014b52.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Not eligible for EMIs on Flipkart \"},{\"widget\":\"SUB_TEXT\",\"text\":\"Dear customer, you are currently not eligible to apply for EMIs at Flipkart. We apologise for the inconvenience. \"},{\"widget\":\"BUTTON\",\"text\":\"Continue Shopping\",\"action\":{\r\n\t\"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n\t\"omnitureData\": \"deep_linking\",\r\n\t\"params\": {\r\n\t\t\"valid\": true,\r\n\t\t\"screenName\": \"homepage\"\r\n\t,\"url\":\"https://www.flipkart.com\"},\r\n\t\"screenType\": \"multiWidgetPage\",\r\n\r\n\t\"tracking\": {\r\n\t\t\"omnitureData\": \"deep_linking\"\r\n\t},\r\n\t\"type\": \"NAVIGATION\",\"url\":\"https://www.flipkart.com\"\r\n\r\n\r\n}}]},\"footer\":{}}}", "CFA.PHONE_NUMBER_NOT_VERIFIED.ui.response": "{\"head\":{\"title\":{\"text\":\"EMIs on Flipkart\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/10/2020/8f7b04ee-e36a-4f79-a568-65331e014b52.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Phone Number not verified! \"},{\"widget\":\"SUB_TEXT\",\"text\":\"We are sorry, we are not able to proceed with your application. This is because your Flipkart registered phone number is not currently verified. Don't worry, you can verify your phone number by logging in to Flipkart using OTP on registered phone number.\"},{\"widget\":\"BUTTON\",\"text\":\"Continue Shopping\",\"action\":{\r\n\t\"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n\t\"omnitureData\": \"deep_linking\",\r\n\t\"params\": {\r\n\t\t\"valid\": true,\r\n\t\t\"screenName\": \"homepage\"\r\n\t,\"url\":\"https://www.flipkart.com\"},\r\n\t\"screenType\": \"multiWidgetPage\",\r\n\r\n\t\"tracking\": {\r\n\t\t\"omnitureData\": \"deep_linking\"\r\n\t},\r\n\t\"type\": \"NAVIGATION\",\"url\":\"https://www.flipkart.com\"\r\n\r\n\r\n}}]},\"footer\":{}}}", "FSUP.PHONE_NUMBER_NOT_VERIFIED.ui.response": "{\"head\":{\"title\":{\"text\":\"Flipkart Smart Upgrade\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/10/2020/8f7b04ee-e36a-4f79-a568-65331e014b52.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Phone Number not verified! \"},{\"widget\":\"SUB_TEXT\",\"text\":\"We are sorry, we are not able to proceed with your application. This is because your Flipkart registered phone number is not currently verified. Don't worry, you can verify your phone number by logging in to Flipkart using OTP on registered phone number.\"},{\"widget\":\"BUTTON\",\"text\":\"Continue Shopping\",\"action\":{\r\n\t\"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n\t\"omnitureData\": \"deep_linking\",\r\n\t\"params\": {\r\n\t\t\"valid\": true,\r\n\t\t\"screenName\": \"homepage\"\r\n\t,\"url\":\"https://www.flipkart.com\"},\r\n\t\"screenType\": \"multiWidgetPage\",\r\n\r\n\t\"tracking\": {\r\n\t\t\"omnitureData\": \"deep_linking\"\r\n\t},\r\n\t\"type\": \"NAVIGATION\",\"url\":\"https://www.flipkart.com\"\r\n\r\n\r\n}}]},\"footer\":{}}}", "PAY_LATER.PHONE_NUMBER_NOT_VERIFIED.ui.response": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/10/2020/8f7b04ee-e36a-4f79-a568-65331e014b52.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Phone Number not verified! \"},{\"widget\":\"SUB_TEXT\",\"text\":\"We are sorry, we are not able to proceed with your application. This is because your Flipkart registered phone number is not currently verified. Don't worry, you can verify your phone number by logging in to Flipkart using OTP on registered phone number.\"},{\"widget\":\"BUTTON\",\"text\":\"Continue Shopping\",\"action\":{\r\n\t\"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n\t\"omnitureData\": \"deep_linking\",\r\n\t\"params\": {\r\n\t\t\"valid\": true,\r\n\t\t\"screenName\": \"homepage\"\r\n\t,\"url\":\"https://www.flipkart.com\"},\r\n\t\"screenType\": \"multiWidgetPage\",\r\n\r\n\t\"tracking\": {\r\n\t\t\"omnitureData\": \"deep_linking\"\r\n\t},\r\n\t\"type\": \"NAVIGATION\",\"url\":\"https://www.flipkart.com\"\r\n\r\n\r\n}}]},\"footer\":{}}}", "FK_CONSUMER_CREDIT.FLIPKART_ADVANZ.REJECTED.FRAUDULENT_USER.ui.response": "{\"head\":{\"title\":{\"text\":\"EMIs on Flipkart\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/10/2020/8f7b04ee-e36a-4f79-a568-65331e014b52.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Rejected! \"},{\"widget\":\"SUB_TEXT\",\"text\":\"We are sorry, we are not able to proceed with your application. This is because your PAN is currently registered with another Flipkart PayLater account.\"},{\"widget\":\"BUTTON\",\"text\":\"Continue Shopping\",\"action\":{\r\n\t\"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n\t\"omnitureData\": \"deep_linking\",\r\n\t\"params\": {\r\n\t\t\"valid\": true,\r\n\t\t\"screenName\": \"homepage\"\r\n\t,\"url\":\"https://www.flipkart.com\"},\r\n\t\"screenType\": \"multiWidgetPage\",\r\n\r\n\t\"tracking\": {\r\n\t\t\"omnitureData\": \"deep_linking\"\r\n\t},\r\n\t\"type\": \"NAVIGATION\",\"url\":\"https://www.flipkart.com\"\r\n\r\n\r\n}}]},\"footer\":{}}}", "cbcApplyNowBannerUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2021/a2479074-5e39-48f0-9374-d250de4f470b.jpg?q={@quality}", "cbcContinueApplicationBannerUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/07/07/2019/a9a79dfe-ad02-4241-925b-31331f619f46.jpg?q={@quality}", "cbcViewCardDetailsBannerUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2021/47f22256-5603-43fa-a0c9-d5914125df54.jpg?q={@quality}", "storemerge.accids": ["ACCF26148ECD4D44E4B8B2241C90E76FF08J", "ACXGN4LIBRS1DYHS6PNEDBHZNDZGJXPU"], "storemerge.enabledForAll": false, "cbcComingSoonBannerUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2021/501dfee2-4c83-45e1-a69e-8cacea9cfa20.jpg?q={@quality}", "displayPennyDropBanner": true, "idd.ab": false, "cbc.applyNow.disable": false, "cbc.applyNow.disable.cohorts": ["NTB", "ETS", "ETB_PRE_APPROVED", "ETB_NON_PRE_APPROVED"], "cbc.applyNow.disable.bypass.accountIds": ["AC2VQOUINNW3LLTVHVMIHCNVBKBKHR3R", "ACCB4DEB84ADC7F4558B066A1ED494509ECR", "ACCTEST12345", "ACCTEST10000", "ACCTEST10001", "ACCTEST10004", "ACCTEST10006", "ACCTEST10005", "ACCTEST10007", "ACCTEST10008", "ACCTEST10009", "ACCTEST10010", "ACCTEST10011", "ACCTEST10012"], "cbc.applyNow.disable.url": "/flipkart-axis-bank-credit-card-store", "cbc.product.type": "530", "plus.MEMBERSHIP_ACTIVATED.rewardSerno": "6014", "plus.MEMBERSHIP_EXPIRED.rewardSerno": "6252", "cbc.elite.product.type": "534", "cbc.lowline.product.type": "535", "cbc.supercoin.promo.plus.free": "ppnn", "cbc.supercoin.promo.plus": "ppf2", "cbc.supercoin.promo.free": "nppnn", "cbc.supercoin.promo": "nppf2", "cbc_display_card.front_background_key": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/07/2019/5b1f1a5b-478a-4328-8d1f-0c75fde4cedd.png?q={@quality}", "cbc_display_card.background_key": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/27/07/2020/30a3e876-41bb-4a54-8350-6d916585c0d6.png?q={@quality}", "cbc_display_card.details_background_key": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/27/07/2020/9e7e7975-4939-4142-9066-e303b3074571.png?q={@quality}", "cbc_display_card.back_background_key": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/07/2019/276e4a26-a958-4b57-803d-eea48966fef3.png?q={@quality}", "cfa.IDFC.plutusIngestionRequired": true, "cfa.BAJAJFIN.plutusIngestionRequired": false, "cfa.BAJAJFIN.criVersion": "v1", "ams.FK_CONSUMER_CREDIT.application-type": "advanz_onboarding_v1", "cbcAms": ["ACXGN4LIBRS1DYHS6PNEDBHZNDZGJXPU", "ACC41B6061C7171458380E843DFE2C1CA56D", "ACCD1FAFCA251E848B48031CB2D42EBD100H", "ACCF26148ECD4D44E4B8B2241C90E76FF08J", "ACC41B6061C7171458380E843DFE2C1CA56D", "ACCTEST12345", "ACCTEST10000", "ACCTEST10001", "ACCTEST10004", "ACCTEST10006", "ACCTEST10005", "ACCTEST10007", "ACCTEST10008", "ACCTEST10009", "ACCTEST10010", "ACCTEST10011", "ACCTEST10012"], "cbcDiscardTime": 60, "cbcIpaPolling": 60, "cbcNstpPolling": 60, "cbcKycTelecalling": 60, "cbcIddPolling": [120, 240, 360, 480, 600, 900, 1200], "FK_CONSUMER_CREDIT.PAY_LATER.kycUpgrade.approval.page.content": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/09/2020/d9f7686e-2501-4618-b44d-1dc44ce5727d.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Successfully upgraded to Full KYC!\"},{\"widget\":\"SUB_TEXT\",\"text\":\"Continue using Flipkart Pay Later without any restrictions!\"},{\"widget\":\"BUTTON\",\"text\":\"Continue Shopping\",\"action\":{\r\n\t\"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n\t\"omnitureData\": \"deep_linking\",\r\n\t\"params\": {\r\n\t\t\"valid\": true,\r\n\t\t\"screenName\": \"homepage\"\r\n\t},\r\n\t\"screenType\": \"multiWidgetPage\",\r\n\r\n\t\"tracking\": {\r\n\t\t\"omnitureData\": \"deep_linking\"\r\n\t},\r\n\t\"type\": \"NAVIGATION\"\r\n\r\n\r\n}}]},\"footer\":{}}}", "loader.FLIPKART_ADVANZ.FK_CONSUMER_CREDIT.PAY_LATER.metadata.title": "Flipkart Pay Later", "loader.FLIPKART_ADVANZ.FK_CONSUMER_CREDIT.FSUP.metadata.title": "Flipkart Smart Upgrade", "NOT_ELIGIBLE.FSUP.ui.response": "{\"head\":{\"title\":{\"text\":\"Flipkart Smart Upgrade\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/10/2020/8f7b04ee-e36a-4f79-a568-65331e014b52.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Not eligible for Flipkart Smart Upgrade \"},{\"widget\":\"SUB_TEXT\",\"text\":\"Dear customer, you are currently not eligible to apply for Flipkart Smart Upgrade. We apologise for the inconvenience. \"},{\"widget\":\"BUTTON\",\"text\":\"Continue Shopping\",\"action\":{\r\n\t\"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n\t\"omnitureData\": \"deep_linking\",\r\n\t\"params\": {\r\n\t\t\"valid\": true,\r\n\t\t\"screenName\": \"homepage\"\r\n\t,\"url\":\"https://www.flipkart.com\"},\r\n\t\"screenType\": \"multiWidgetPage\",\r\n\r\n\t\"tracking\": {\r\n\t\t\"omnitureData\": \"deep_linking\"\r\n\t},\r\n\t\"type\": \"NAVIGATION\",\"url\":\"https://www.flipkart.com\"\r\n\r\n\r\n}}]},\"footer\":{}}}", "NOT_ELIGIBLE.PAY_LATER.ui.response": "{\"head\":{\"title\":{\"text\":\"Flipkart Pay Later\"}},\"body\":{\"main\":{\"slots\":[{\"widget\":\"IMAGE\",\"url\":\"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/10/2020/8f7b04ee-e36a-4f79-a568-65331e014b52.png?q={@quality}\",\"width\":55,\"height\":55},{\"widget\":\"HEADER\",\"text\":\"Not eligible for Flipkart Pay Later \"},{\"widget\":\"SUB_TEXT\",\"text\":\"Dear customer, you are currently not eligible to apply for Flipkart Pay Later. We apologise for the inconvenience. \"},{\"widget\":\"BUTTON\",\"text\":\"Continue Shopping\",\"action\":{\r\n\t\"loginType\": \"LOGIN_NOT_REQUIRED\",\r\n\t\"omnitureData\": \"deep_linking\",\r\n\t\"params\": {\r\n\t\t\"valid\": true,\r\n\t\t\"screenName\": \"homepage\"\r\n\t,\"url\":\"https://www.flipkart.com\"},\r\n\t\"screenType\": \"multiWidgetPage\",\r\n\r\n\t\"tracking\": {\r\n\t\t\"omnitureData\": \"deep_linking\"\r\n\t},\r\n\t\"type\": \"NAVIGATION\",\"url\":\"https://www.flipkart.com\"\r\n\r\n\r\n}}]},\"footer\":{}}}", "landing_page.ui.metadata": "[{  \"name\": \"banner_section\",  \"order\": 1,  \"image_urls\": [    \"imageUrl1\",    \"imageUrl2\"  ],  \"video_urls\": []},{  \"name\": \"stepper_section\",  \"order\": 2,  \"image_urls\": [    \"imageUrl1\",    \"imageUrl2\"  ],  \"video_urls\": []},{  \"name\": \"image_callouts_section\",  \"order\": 3,  \"image_urls\": [    \"imageUrl1\",    \"imageUrl2\"  ],  \"video_urls\": []},{  \"name\": \"testimonies_section\",  \"order\": 4,  \"image_urls\": [    \"imageUrl1\",    \"imageUrl2\"  ],  \"video_urls\": []},{  \"name\": \"video_section\",  \"order\": 5,  \"image_urls\": [],  \"video_urls\": [    \"videoUrl1\",    \"videoUrl2\"  ]}]", "cbc.secure.image.url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/25/08/2021/74381bda-3b53-4b1b-93bc-4b58a4971c3a.png?q={@quality}", "enable.lms.loan.check": true, "cbcPnnEnabled": false, "cbcSkylerEnabled": false, "cbcOfferMailerTime": 5, "borrower.not_eligible.states": ["RISK_BLOCKED", "BLACKLISTED", "DEACTIVATED"], "ams.FK_CONSUMER_CREDIT.FLIPKART_ADVANZ.revalidation.application-type": "advanz_revalidation_v1", "cardConsolePageIds": ["flipk-categ-3f21a", "clone-categ-2a033", "flipk-categ-94c72"], "cardConsoleHomePageId": "flipk-categ-3f21a", "cbc.card-console.activate-card-banner.navigation-url": "/pages/Enable-online-usage-tnc", "cbc.card-console.activate-card-banner.aspect-ratio": "328:70", "idd.enabled_for_all": true, "RATE_LIMIT.PENNY_DROP_SUBMIT_DETAILS": 1000, "RATE_LIMIT.AADHAR_VERIFICATION": 1000, "RATE_LIMIT.GENERATE_OTP": 1000, "RATE_LIMIT.CREATE_APPLICATION": 1000, "RATE_LIMIT.FETCH_UI": 12.5, "RATE_LIMIT.PAN_SUBMIT": 1000, "RATE_LIMIT.FETCH_LOADER": 1000, "RATE_LIMIT.VERIFY_OTP": 1000, "e2eEnabledForAll": true, "e2e": [], "cbcWebView": true, "otaLiteEnabled": false, "ntbCugUserList": ["ACC4BBD119E3A394E2D99818EB471201D53A"], "emiBillingEnabled": true, "emiMinimumFees": 250, "ntbCug": ["ACC4BBD119E3A394E2D99818EB471201D53A"], "cbc.cbc_console_home.textList": ["What is cashback earned till date?", "What is the minimum amount due?", "How can I block my card?", "How can I contact Axis Bank customer care?"], "cbc.cbc_console_home.expandableTextList": ["This is the total cashback earned by you using your Flipkart Axis Bank Credit Card.", "The minimum amount that should be paid in order to avoid late fees. However an interest of 3.4% p.m. will be charged on the remaining outstanding amount in your next billing cycle.", "Please go to the Manage your card section on this page then you will find an option to 'Block your Card'. Post clicking on it you can block your card with an OTP authentication. You need to get in touch with Axis Bank at 1860 419 5555, 1860 500 5555 to get a replacement.", "You can reach Axis Bank "], "cbc.cbc_console_account_summary.textList": ["What is an Unbilled Outstanding Amount?", "When will I get my Cashback that is pending?", "Is there a way to change my billing cycle?"], "cbc.cbc_console_account_summary.expandableTextList": ["The unbilled outstanding amount is the amount that is due on your credit card but has not yet been billed to you in your credit card statement.", "For all transactions done in a billing cycle, transaction level cashback will be reflected in the credit card statement generated for that month, this is referred to as 'Cashback Earned', the cashback that is earned in one billing cycle will be credited to your Credit Card account in the next credit card statement and this will reflect as 'Cashback Credited'.", "No, change of billing cycle is not permitted by the bank."], "cbcNtbRevampValue": true, "enableNtbRevampForAll": false, "applicationRetryDuration": 500, "isMockEnabled": true, "nudgeThreadsCount": 20, "threadRunType": "invokeAll", "applicationSyncCutoffDate": "2022-12-23 20:00:00", "cardLinkAmsEnabled": true, "offlineAmsProductTypes": ["CBC_SC_ELITE"], "enable.advanz.ams.onboarding": true, "mockPhoneNumber": true, "consentServiceFlag": true, "mock.underwriting.response": "[{\"account_id\":\"ACC82ABCBBB4D5D4007B7DC5F35D12015541\",\"eligibility_response\":{\"ruleName\":\"test_rule\",\"eligible\":true,\"eligibleAmount\":60000.0,\"isEligible\":true,\"STATUS\":\"SUCCESS\",\"credit_config\":{\"credit_limit\":60000,\"sublimits\":[{\"sublimit_type\":\"PAY_LATER\",\"credit_limit\":20000,\"used_limit\":0,\"max_limit\":20000,\"status\":\"ACTIVE\",\"min_txn\":10,\"max_txn\":5000},{\"sublimit_type\":\"EMI\",\"credit_limit\":60000,\"used_limit\":null,\"max_limit\":60000,\"status\":\"ACTIVE\",\"emi_tenure_group_id\":\"220707141808UB71E55J\"}]},\"intent_score\":4.0,\"affordability_score\":2.0,\"reject_reason\":null,\"lms_config_id\":\"idfc_advanz\"}},{\"account_id\":\"********************************\",\"eligibility_response\":{\"ruleName\":\"test_rule\",\"eligible\":true,\"eligibleAmount\":60000.0,\"isEligible\":true,\"STATUS\":\"SUCCESS\",\"credit_config\":{\"credit_limit\":60000,\"sublimits\":[{\"sublimit_type\":\"PAY_LATER\",\"credit_limit\":20000,\"used_limit\":0,\"max_limit\":20000,\"status\":\"ACTIVE\",\"min_txn\":10,\"max_txn\":5000},{\"sublimit_type\":\"EMI\",\"credit_limit\":60000,\"used_limit\":null,\"max_limit\":60000,\"status\":\"ACTIVE\",\"emi_tenure_group_id\":\"220707141808UB71E55J\"}]},\"intent_score\":4.0,\"affordability_score\":2.0,\"reject_reason\":null,\"lms_config_id\":\"idfc_advanz\"}},{\"account_id\":\"ACC14118057686658287\",\"eligibility_response\":{\"ruleName\":\"test_rule\",\"eligible\":true,\"eligibleAmount\":60000.0,\"isEligible\":true,\"STATUS\":\"SUCCESS\",\"credit_config\":{\"credit_limit\":60000,\"sublimits\":[{\"sublimit_type\":\"PAY_LATER\",\"credit_limit\":20000,\"used_limit\":0,\"max_limit\":20000,\"status\":\"ACTIVE\",\"min_txn\":10,\"max_txn\":5000},{\"sublimit_type\":\"EMI\",\"credit_limit\":60000,\"used_limit\":null,\"max_limit\":60000,\"status\":\"ACTIVE\",\"emi_tenure_group_id\":\"220707141808UB71E55J\"}]},\"intent_score\":4.0,\"affordability_score\":2.0,\"reject_reason\":null,\"lms_config_id\":\"idfc_advanz\"}},{\"account_id\":\"ACC14118057678869888\",\"eligibility_response\":{\"ruleName\":\"test_rule\",\"eligible\":true,\"eligibleAmount\":60000.0,\"isEligible\":true,\"STATUS\":\"SUCCESS\",\"credit_config\":{\"credit_limit\":60000,\"sublimits\":[{\"sublimit_type\":\"PAY_LATER\",\"credit_limit\":20000,\"used_limit\":0,\"max_limit\":20000,\"status\":\"ACTIVE\",\"min_txn\":10,\"max_txn\":5000},{\"sublimit_type\":\"EMI\",\"credit_limit\":60000,\"used_limit\":null,\"max_limit\":60000,\"status\":\"ACTIVE\",\"emi_tenure_group_id\":\"220707141808UB71E55J\"}]},\"intent_score\":4.0,\"affordability_score\":2.0,\"reject_reason\":null,\"lms_config_id\":\"idfc_advanz\"}},{\"account_id\":\"ACC14118057680406463\",\"eligibility_response\":{\"ruleName\":\"test_rule\",\"eligible\":true,\"eligibleAmount\":60000.0,\"isEligible\":true,\"STATUS\":\"SUCCESS\",\"credit_config\":{\"credit_limit\":60000,\"sublimits\":[{\"sublimit_type\":\"PAY_LATER\",\"credit_limit\":20000,\"used_limit\":0,\"max_limit\":20000,\"status\":\"ACTIVE\",\"min_txn\":10,\"max_txn\":5000},{\"sublimit_type\":\"EMI\",\"credit_limit\":60000,\"used_limit\":null,\"max_limit\":60000,\"status\":\"ACTIVE\",\"emi_tenure_group_id\":\"220707141808UB71E55J\"}]},\"intent_score\":4.0,\"affordability_score\":2.0,\"reject_reason\":null,\"lms_config_id\":\"idfc_advanz\"}},{\"account_id\":\"ACC13535889460401557\",\"eligibility_response\":{\"ruleName\":\"test_rule\",\"eligible\":true,\"eligibleAmount\":60000.0,\"isEligible\":true,\"STATUS\":\"SUCCESS\",\"credit_config\":{\"credit_limit\":60000,\"sublimits\":[{\"sublimit_type\":\"PAY_LATER\",\"credit_limit\":20000,\"used_limit\":0,\"max_limit\":20000,\"status\":\"ACTIVE\",\"min_txn\":10,\"max_txn\":5000},{\"sublimit_type\":\"EMI\",\"credit_limit\":60000,\"used_limit\":null,\"max_limit\":60000,\"status\":\"ACTIVE\",\"emi_tenure_group_id\":\"220707141808UB71E55J\"}]},\"intent_score\":4.0,\"affordability_score\":2.0,\"reject_reason\":null,\"lms_config_id\":\"idfc_advanz\"}},{\"account_id\":\"ACCBB25111400374BE2BE6837A3089D2E8AY\",\"eligibility_response\":{\"ruleName\":\"test_rule\",\"eligible\":true,\"eligibleAmount\":60000.0,\"isEligible\":true,\"STATUS\":\"SUCCESS\",\"credit_config\":{\"credit_limit\":60000,\"sublimits\":[{\"sublimit_type\":\"PAY_LATER\",\"credit_limit\":20000,\"used_limit\":0,\"max_limit\":20000,\"status\":\"ACTIVE\",\"min_txn\":10,\"max_txn\":5000},{\"sublimit_type\":\"EMI\",\"credit_limit\":60000,\"used_limit\":null,\"max_limit\":60000,\"status\":\"ACTIVE\",\"emi_tenure_group_id\":\"220707141808UB71E55J\"}]},\"intent_score\":4.0,\"affordability_score\":2.0,\"reject_reason\":null,\"lms_config_id\":\"idfc_advanz\"}}]"}