{"application_data": {"error_message": null, "consent_details_list": null, "code": null, "application_created_at": "2023-11-16 15:39:34", "user_details": {"pan": null, "dob": null, "gender": null, "shipping_address_id": "CNTCT000B051A14EE43348E399EDBC", "bin_score": "301.382", "employment_type": null, "employer_id": null, "employer_name": null, "industry_id": null, "industry_name": null, "monthly_income": null, "annual_turn_over": null, "pan_consent_provided": false, "offer_consent_provided": false, "segment": null, "subsegment": null}, "basicDetails": {"pan": "Qdj+XMnTu1LAIoOntT1EpQ==", "consentData": {"consentType": "CI_CONSENT", "userIP": "***********, **************", "deviceId": "TI169156747080100013213510722851136872597748835719373369928454301440", "deviceInfo": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.1/msite/Mobile", "currentTimeStamp": 1700120258199}, "dob": "11/08/1996"}, "error": {"error": {"errorCode": "EXP_400", "errorType": "BAD_REQUEST", "errorDescription": "/panNumber string [02MNMsK0jOTjANwcQQbdmw==] does not match pattern ^[a-zA-Z0-9_.]+$", "correlationId": "39177257-5002-4746-8833-31d58470d1a3", "dateTime": "2023-11-16T10:24:19.23Z"}}, "offer_id": null, "external_user_id": "ACC6722F15B35D14D36B0D46FA3C930AD8BO", "journey_context": "PERSONAL_LOAN_APPLICATION_JOURNEY", "product_type": "PERSONAL_LOAN", "form_details": null, "lender_details": null, "financial_provider": "IDFC", "expiry": null}, "sub_applications": {"sub_application_info_map": {}}, "external_user_id": "ACC6722F15B35D14D36B0D46FA3C930AD8BO", "application_type": "PERSONAL_LOAN_IDFC", "application_id": "APP2311161539345285938556431494293763232", "parent_application_id": null, "tenant": "CALM", "pending_task": [{"process_instance_id": "CesggOJgwNUzCVjC", "task_id": "doVNX4MqCz3TGDs8", "task_key": "basicDetails", "form_key": null}], "application_state": "VERIFY_PAN", "merchant_id": "SHOPSY"}