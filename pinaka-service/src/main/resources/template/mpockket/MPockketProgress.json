{"data": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/%7B@width%7D/%7B@height%7D/promos/17/12/2024/6d7d78ae-5d34-4fae-8843-7e1feb779c6d.png?q={@quality}", "height": 52, "width": 52, "aspectRatio": "1:1", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Application still in process", "style": {"color": "#1c41d6", "fontSize": 20, "fontWeight": "bold", "textAlign": "center", "lineHeight": 33}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "You have ongoing application with our partner mPockket. Head over to their platform to complete the application", "style": {"color": "#1c41d6", "fontSize": 14, "fontWeight": "bold", "textAlign": "center", "lineHeight": 33}}}, "richButton": {"action": {"type": "MERCHANT_HOMEPAGE", "params": {}, "tracking": {}, "loginType": "LOGIN_NOT_REQUIRED"}, "value": {"type": "RichTextButtonValue", "title": "Okay, got it"}}, "type": "AnnouncementV2Value"}}}