{"formId": "CREDIT_LINE_OTP_FORM", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"customFieldData": {"customFieldType": "OTP_FIELD", "resendText": {"action": {"url": "/otp-resend"}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"text": "Resend OTP", "textColor": "1D2939", "fontSize": 18, "containerStyle": {"paddingTop": 30}}}, "length": 6}, "disabled": false, "formFieldType": "CUSTOM", "label": "", "name": "otp"}}], "submitButton": {"type": "SubmitButtonValue", "consent": {"consentType": "CHECKBOX", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "By giving consent, I agree that I’m an Indian citizen and  I authorize Flipkart Advanz Private Limited to share my details with Axis Bank and allow Axis bank to fetch my credit report from the Credit Bureau."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}, "button": {"action": {"type": "CALM__RESUME_ACTION", "url": "/api/sm/1/application/form", "screenType": "BottomSheet", "encryption": {"publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCLo3cxsTNbj2c6FB4EZK5gtO0PsjWzOw9b2vVCaDY9IVFGHLROz3MINr0pThJwfoBl+ahhHyUioXgILCUk6y/HVRfgYwitsHQ7Hv+JkIDnnw0qtclRRUnRlejhfDHQozypFiTZNLG3Y1/JBS2tSPE6RHEQe/w98sqRu86yBorzMwIDAQAB", "keyId": "ASD"}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "title": "Next"}}}, "resultStoreKey": null, "title": {"text": "Enter OTP", "textColor": "#4D43FE", "textSize": 32, "fontWeight": "bold", "lineHeight": 20, "containerStyle": {"paddingBottom": 24}}, "subTitle": {"text": "Please enter the OTP sent to +91 xxxxxx4126 to process your amount", "textColor": "#475467", "textSize": 16, "fontWeight": "bold", "lineHeight": 40}}