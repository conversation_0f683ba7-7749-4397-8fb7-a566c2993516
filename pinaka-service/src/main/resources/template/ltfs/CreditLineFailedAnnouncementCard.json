{"card": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/27/05/2025/713cb405-8ed7-4571-8918-ae6199217597.png?q={@quality}", "height": 200, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/27/05/2025/713cb405-8ed7-4571-8918-ae6199217597.png?q={@quality}", "width": 178, "aspectRatio": 0.881}, "iconBackgroundColor": null, "iconSize": null, "orientation": "PORTRAIT", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"padding": 8, "alignItems": "center", "minWidth": 312}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 32, "fontWeight": "bold", "lineHeight": 32, "marginTop": 8, "textAlign": "left", "alignItems": "start"}, "text": "Uh oh, validation failed", "textColor": "#1D2939", "containerStyle": {"display": "flex", "paddingTop": 65, "alignItems": "start", "textAlign": "left"}}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "textAlign": "center"}, "containerStyle": {"paddingTop": 12, "alignItems": "center"}, "text": "Unfortunately, your account validation with the bank wasn’t successful. <Dynamic reason>", "textColor": "#475467"}}, "viewType": "PRIMITIVE_CARD"}}}