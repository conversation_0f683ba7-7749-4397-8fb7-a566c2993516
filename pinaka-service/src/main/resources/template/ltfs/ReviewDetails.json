{"cardSummaryTitle": null, "clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formGroups": [{"formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "panNumber", "sendEdits": "true", "prefill": "true", "prefilledValue": "**********", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "pan", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "CLIENT__INLINE_NAVIGATION": true, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_1"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": {"widgetName": "REVIEW_PAGE_1_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "panNumber", "noOfErrors": 1, "label": "PAN Number", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter correct PAN", "interactionType": null, "validateOnSubmit": false, "regex": "^[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}$"}], "value": "pan", "tracking": {"sendImpression": true, "fieldName": "panNumber", "sendEdits": true, "prefill": true, "prefilledValue": "**********", "sendInlineError": true}, "suffix": {"tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"screenType": null, "type": "EDIT", "omnitureData": null, "originalUrl": null, "url": null, "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "minCharacters": 10, "placeholder": ""}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {"backgroundColor": "red"}, "cardSummaryTitle": null, "formId": "REVIEW_PAGE_1", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Edit details", "textColor": "#4D43FE", "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 20, "fontWeight": "bold", "lineHeight": 24}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Save", "buttonColor": "#B5EF85", "buttonSize": "LARGE", "buttonTextColor": "#000000", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "borderRadius": 8, "borderWidth": 0, "borderColor": "transparent"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 16, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 16}, "style": {"paddingBottom": 24, "paddingLeft": 12, "paddingRight": 0, "paddingTop": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "PAN", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "panNumber"}, "style": {}}], "formGroupDataStyle": {"display": "block", "backgroundColor": "#FFFFFF", "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "marginRight": 24, "paddingLeft": 24, "paddingRight": 24, "paddingBottom": 12, "paddingTop": 12}, "formGroupName": "Group1"}, {"formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "firstName", "sendEdits": "true", "prefill": "true", "prefilledValue": "**********", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "first and middle name", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "CLIENT_PARAMS": true, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_1"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": {"widgetName": "REVIEW_PAGE_1_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "firstName", "noOfErrors": 1, "label": "First and middle name", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter correct name", "interactionType": null, "validateOnSubmit": false, "regex": "^[A-Za-z]+( [A-Za-z]+)?$"}], "value": "first and middle name", "tracking": {"sendImpression": true, "fieldName": "firstName", "sendEdits": true, "prefill": true, "prefilledValue": "<PERSON>", "sendInlineError": true}, "suffix": {"tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"screenType": null, "type": "EDIT", "omnitureData": null, "originalUrl": null, "url": null, "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "minCharacters": 10, "placeholder": "eg. <PERSON>"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {"backgroundColor": "red"}, "cardSummaryTitle": null, "formId": "REVIEW_PAGE_1", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Edit details", "textColor": "#4D43FE", "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 20, "fontWeight": "bold", "lineHeight": 24}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Save", "buttonColor": "#B5EF85", "buttonSize": "LARGE", "buttonTextColor": "#000000", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "borderRadius": 8, "borderWidth": 0, "borderColor": "transparent"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 16, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 16}, "style": {"paddingBottom": 24, "paddingLeft": 12, "paddingRight": 0, "paddingTop": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "First and middle name", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "firstName"}, "style": {}}], "formGroupDataStyle": {"display": "block", "backgroundColor": "#FFFFFF", "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "marginRight": 24, "paddingLeft": 24, "paddingRight": 24, "paddingBottom": 12, "paddingTop": 12}, "formGroupName": "Group2"}, {"formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "lastName", "sendEdits": "true", "prefill": "true", "prefilledValue": "<PERSON><PERSON><PERSON>", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "last name", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "CLIENT_PARAMS": true, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_1"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": {"widgetName": "REVIEW_PAGE_1_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "lastName", "noOfErrors": 1, "label": "Last name", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter correct last name", "interactionType": null, "validateOnSubmit": false, "regex": "^[A-Za-z]+( [A-Za-z]+)?$"}], "value": "last name", "tracking": {"sendImpression": true, "fieldName": "lastName", "sendEdits": true, "prefill": true, "prefilledValue": "<PERSON><PERSON><PERSON>", "sendInlineError": true}, "suffix": {"tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"screenType": null, "type": "EDIT", "omnitureData": null, "originalUrl": null, "url": null, "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "minCharacters": 10, "placeholder": "<PERSON><PERSON><PERSON>"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {"backgroundColor": "red"}, "cardSummaryTitle": null, "formId": "REVIEW_PAGE_1", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Edit details", "textColor": "#4D43FE", "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 20, "fontWeight": "bold", "lineHeight": 24}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Save", "buttonColor": "#B5EF85", "buttonSize": "LARGE", "buttonTextColor": "#000000", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "borderRadius": 8, "borderWidth": 0, "borderColor": "transparent"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 16, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 16}, "style": {"paddingBottom": 24, "paddingLeft": 12, "paddingRight": 0, "paddingTop": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "Last name", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "lastName"}, "style": {}}], "formGroupDataStyle": {"display": "block", "backgroundColor": "#FFFFFF", "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "marginRight": 24, "paddingLeft": 24, "paddingRight": 24, "paddingBottom": 12, "paddingTop": 12}, "formGroupName": "Group3"}, {"formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "email", "sendEdits": "true", "prefill": "true", "prefilledValue": "<EMAIL>", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "email", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-761f2", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_1"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "email", "noOfErrors": 1, "label": "Email", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter correct Email", "interactionType": null, "validateOnSubmit": false, "regex": "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,6})$"}], "value": "email", "tracking": {"sendImpression": true, "fieldName": "email", "sendEdits": true, "prefill": true, "prefilledValue": "<EMAIL>", "sendInlineError": true}, "inputType": "EMAIL_ADDRESS", "warningValidation": {"type": "emailHostCheck", "condition": ["gmail.co.za", "gmail.com", "gmail.com.au", "gmail.com.br", "gmail.ru", "hotmail.be", "hotmail.ca", "hotmail.ch", "hotmail.co", "hotmail.co.il", "hotmail.co.jp", "hotmail.co.nz", "hotmail.co.uk", "hotmail.co.za", "hotmail.com", "hotmail.com.ar", "hotmail.com.au", "hotmail.com.br", "hotmail.com.mx", "hotmail.com.tr", "hotmail.de", "hotmail.es", "hotmail.fi", "hotmail.fr", "hotmail.it", "hotmail.kg", "hotmail.kz", "hotmail.my", "hotmail.nl", "hotmail.ro", "hotmail.roor", "hotmail.ru", "rediff.com", "rediffmail.com", "rediffmailpro.com", "yahoo.ae", "yahoo.at", "yahoo.be", "yahoo.ca", "yahoo.ch", "yahoo.cn", "yahoo.co", "yahoo.co.id", "yahoo.co.il", "yahoo.co.in", "yahoo.co.jp", "yahoo.co.kr", "yahoo.co.nz", "yahoo.co.th", "yahoo.co.uk", "yahoo.co.za", "yahoo.com", "yahoo.com.ar", "yahoo.com.au", "yahoo.com.br", "yahoo.com.cn", "yahoo.com.co", "yahoo.com.hk", "yahoo.com.is", "yahoo.com.mx", "yahoo.com.my", "yahoo.com.ph", "yahoo.com.ru", "yahoo.com.sg", "yahoo.com.tr", "yahoo.com.tw", "yahoo.com.vn", "yahoo.cz", "yahoo.de", "yahoo.dk", "yahoo.es", "yahoo.fi", "yahoo.fr", "yahoo.gr", "yahoo.hu", "yahoo.ie", "yahoo.in", "yahoo.it", "yahoo.jp", "yahoo.net", "yahoo.nl", "yahoo.no", "yahoo.pl", "yahoo.pt", "yahoo.ro", "yahoo.ru", "yahoo.se", "yahoofs.com", "yahoomail.com", "aol.com", "msn.com", "wanadoo.fr", "orange.fr", "comcast.net", "live.com", "free.fr", "gmx.de", "web.de", "yandex.ru", "ymail.com", "libero.it", "outlook.com", "uol.com.br", "bol.com.br", "mail.ru", "cox.net", "sbcglobal.net", "sfr.fr", "live.fr", "verizon.net", "live.co.uk", "googlemail.com", "ig.com.br", "live.nl", "bigpond.com", "terra.com.br", "neuf.fr", "alice.it", "rocketmail.com", "att.net", "laposte.net", "facebook.com", "bellsouth.net", "charter.net", "rambler.ru", "tiscali.it", "shaw.ca", "sky.com", "earthlink.net", "optonline.net", "freenet.de", "t-online.de", "aliceadsl.fr", "virgilio.it", "home.nl", "qq.com", "telenet.be", "me.com", "tiscali.co.uk", "voila.fr", "gmx.net", "mail.com", "planet.nl", "tin.it", "live.it", "ntlworld.com", "arcor.de", "frontiernet.net", "hetnet.nl", "live.com.au", "zonnet.nl", "club-internet.fr", "juno.com", "optusnet.com.au", "blueyonder.co.uk", "bluewin.ch", "skynet.be", "sympatico.ca", "windstream.net", "mac.com", "centurytel.net", "chello.nl", "live.ca", "aim.com", "bigpond.net.au", "super.money"], "warningMessage": "Please recheck correctness of your email address"}, "autoCapitalize": "none", "placeholder": "enter full email ID"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {"backgroundColor": "red"}, "cardSummaryTitle": null, "formId": "REVIEW_PAGE_1", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Edit details", "textColor": "#4D43FE", "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 20, "fontWeight": "bold", "lineHeight": 24}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Save", "buttonColor": "#B5EF85", "buttonSize": "LARGE", "buttonTextColor": "#000000", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "borderRadius": 8, "borderWidth": 0, "borderColor": "transparent"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 16, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 16}, "style": {"paddingBottom": 24, "paddingLeft": 12, "paddingRight": 0, "paddingTop": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "Email id", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "email"}, "style": {}}], "formGroupDataStyle": {"display": "block", "backgroundColor": "#FFFFFF", "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "marginRight": 24, "paddingLeft": 24, "paddingRight": 24, "paddingBottom": 12, "paddingTop": 12}, "formGroupName": "Group4"}, {"formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "dob", "sendEdits": "true", "prefill": "true", "prefilledValue": "27/09/1995", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "dob", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "CLIENT_PARAMS": true, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_1"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": {"widgetName": "REVIEW_PAGE_1_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "DATE", "name": "dob", "noOfErrors": 1, "label": "DOB", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "value": "dob", "tracking": {"sendImpression": true, "fieldName": "dob", "sendEdits": true, "prefill": true, "prefilledValue": "27/09/1995", "sendInlineError": true}, "minValue": "1965-03-18T14:22:41.155+05:30", "maxValue": "2007-03-18T14:22:41.155+05:30", "suffix": {"tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"screenType": null, "type": "EDIT", "omnitureData": null, "originalUrl": null, "url": null, "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {"backgroundColor": "red"}, "cardSummaryTitle": null, "formId": "REVIEW_PAGE_1", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Edit details", "textColor": "#4D43FE", "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 20, "fontWeight": "bold", "lineHeight": 24}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Save", "buttonColor": "#B5EF85", "buttonSize": "LARGE", "buttonTextColor": "#000000", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "borderRadius": 8, "borderWidth": 0, "borderColor": "transparent"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 16, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 16}, "style": {"paddingBottom": 24, "paddingLeft": 12, "paddingRight": 0, "paddingTop": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "DOB", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "dob"}, "style": {}}], "formGroupDataStyle": {"display": "block", "backgroundColor": "#FFFFFF", "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "marginRight": 24, "paddingLeft": 24, "paddingRight": 24, "paddingBottom": 12, "paddingTop": 12}, "formGroupName": "Group5"}, {"formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "gender", "sendEdits": "true", "prefill": "true", "prefilledValue": null, "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "gender", "title": "M", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "CLIENT_PARAMS": true, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_1"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": {"widgetName": "REVIEW_PAGE_1_FORM"}, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "formFieldType": "DROPDOWN", "name": "gender", "label": "Gender", "mandatory": true, "disabled": false, "tracking": {"sendImpression": true, "fieldName": "gender", "sendEdits": true, "prefill": true, "prefilledValue": "Male", "sendInlineError": true}, "options": [{"id": "M", "title": "Male", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "F", "title": "Female", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "O", "title": "Others", "type": "PLAIN_TEXT", "fullWidthPress": false}], "selectedOption": {"id": "", "title": "Male", "type": "PLAIN_TEXT", "fullWidthPress": false}, "displayType": "DROPDOWN", "stackType": "HORIZONTAL"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {"backgroundColor": "red"}, "cardSummaryTitle": null, "formId": "REVIEW_PAGE_1", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Edit details", "textColor": "#4D43FE", "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 20, "fontWeight": "bold", "lineHeight": 24}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Save", "buttonColor": "#B5EF85", "buttonSize": "LARGE", "buttonTextColor": "#000000", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "borderRadius": 8, "borderWidth": 0, "borderColor": "transparent"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 16, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 16}, "style": {"paddingBottom": 24, "paddingLeft": 12, "paddingRight": 0, "paddingTop": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "Gender", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "gender", "displayType": "KEY_VALUE_MAPPER", "displayMapper": {"M": "Male", "F": "Female", "O": "Others"}}, "style": {}}], "formGroupDataStyle": {"display": "block", "backgroundColor": "#FFFFFF", "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "marginRight": 24, "paddingLeft": 24, "paddingRight": 24, "paddingBottom": 12, "paddingTop": 12}, "formGroupName": "Group6"}, {"formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "panNumber", "sendEdits": "true", "prefill": "true", "prefilledValue": "**********", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "address line 1", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "CLIENT_PARAMS": true, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_1"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": {"widgetName": "REVIEW_PAGE_1_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "addressLineOne", "noOfErrors": 1, "label": "Line 1", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter correct address", "interactionType": null, "validateOnSubmit": false, "regex": "^[A-Za-z0-9\\s,\\.\\-/#]+$"}], "value": "address line 1", "tracking": {"sendImpression": true, "fieldName": "addressLineOne", "sendEdits": true, "prefill": true, "prefilledValue": "63, Apte Road, Pali Pathar, Khar", "sendInlineError": true}, "suffix": {"tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"screenType": null, "type": "EDIT", "omnitureData": null, "originalUrl": null, "url": null, "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "minCharacters": 10, "placeholder": ""}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {"backgroundColor": "red"}, "cardSummaryTitle": null, "formId": "REVIEW_PAGE_1", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Edit details", "textColor": "#4D43FE", "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 20, "fontWeight": "bold", "lineHeight": 24}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Save", "buttonColor": "#B5EF85", "buttonSize": "LARGE", "buttonTextColor": "#000000", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "borderRadius": 8, "borderWidth": 0, "borderColor": "transparent"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 16, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 16}, "style": {"paddingBottom": 24, "paddingLeft": 12, "paddingRight": 0, "paddingTop": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "Address Line 1", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "addressLineOne"}, "style": {}}], "formGroupDataStyle": {"display": "block", "backgroundColor": "#FFFFFF", "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "marginRight": 24, "paddingLeft": 24, "paddingRight": 24, "paddingBottom": 12, "paddingTop": 12}, "formGroupName": "Group8"}, {"formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "panNumber", "sendEdits": "true", "prefill": "true", "prefilledValue": "**********", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "address line 2", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "CLIENT_PARAMS": true, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_1"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": {"widgetName": "REVIEW_PAGE_1_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "addressLineTwo", "noOfErrors": 1, "label": "Line 2", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter correct address", "interactionType": null, "validateOnSubmit": false, "regex": "^[A-Za-z0-9\\s,\\.\\-/#]+$"}], "value": "address line 2", "tracking": {"sendImpression": true, "fieldName": "addressLineTwo", "sendEdits": true, "prefill": true, "prefilledValue": "Mumbai, Maharashtra", "sendInlineError": true}, "suffix": {"tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"screenType": null, "type": "EDIT", "omnitureData": null, "originalUrl": null, "url": null, "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "minCharacters": 10, "placeholder": ""}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {"backgroundColor": "red"}, "cardSummaryTitle": null, "formId": "REVIEW_PAGE_1", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Edit details", "textColor": "#4D43FE", "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 20, "fontWeight": "bold", "lineHeight": 24}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Save", "buttonColor": "#B5EF85", "buttonSize": "LARGE", "buttonTextColor": "#000000", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "borderRadius": 8, "borderWidth": 0, "borderColor": "transparent"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 16, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 16}, "style": {"paddingBottom": 24, "paddingLeft": 12, "paddingRight": 0, "paddingTop": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "Address Line 2", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "addressLineTwo"}, "style": {}}], "formGroupDataStyle": {"display": "block", "backgroundColor": "#FFFFFF", "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "marginRight": 24, "paddingLeft": 24, "paddingRight": 24, "paddingBottom": 12, "paddingTop": 12}, "formGroupName": "Group9"}, {"formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "pincodeDetails", "sendEdits": "true", "prefill": "true", "prefilledValue": "400052", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "pincode", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "CLIENT_PARAMS": true, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_1"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": {"widgetName": "REVIEW_PAGE_1_FORM"}, "trackingData": null, "value": {"type": "CustomFormFieldValue", "formFieldType": "CUSTOM", "name": "pincodeDetails", "noOfErrors": null, "label": null, "mandatory": true, "disabled": false, "containerStyle": null, "validationRuleList": null, "dependencyList": null, "outerContainerStyle": null, "inputStyle": null, "value": null, "tracking": {"sendImpression": true, "fieldName": "pincodeDetails", "sendEdits": true, "prefill": true, "prefilledValue": "400052", "sendInlineError": true}, "grids": null, "customFieldData": {"customFieldType": "PINCODE_V0", "pincode": {"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "pincode", "noOfErrors": 1, "label": "Pin Code", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter a valid pin code", "interactionType": null, "validateOnSubmit": false, "regex": "^(?!\\s*$)[1-9]{1}[0-9]{5}$"}], "value": "pincode", "tracking": {"sendImpression": true, "fieldName": "pincodeDetails", "sendEdits": true, "prefill": true, "prefilledValue": "400052", "sendInlineError": true}, "validationRegex": "^[1-9]{1}[0-9]{5}$", "minCharacters": 6, "maxCharacters": 6, "autoCapitalize": "characters", "placeholder": "eg. 400052"}, "action": {"screenType": null, "type": "NAVIGATION", "omnitureData": null, "originalUrl": null, "url": "/6/pincode/existence", "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "state": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "state", "label": "State", "mandatory": true, "disabled": true, "outerContainerStyle": {"display": "none"}, "autoCapitalize": "characters", "placeholder": ""}, "city": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "city", "label": "City", "mandatory": true, "disabled": true, "outerContainerStyle": {"display": "none"}, "autoCapitalize": "characters", "placeholder": ""}}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {"backgroundColor": "red"}, "cardSummaryTitle": null, "formId": "REVIEW_PAGE_1", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Edit details", "textColor": "#4D43FE", "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 20, "fontWeight": "bold", "lineHeight": 24}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Save", "buttonColor": "#B5EF85", "buttonSize": "LARGE", "buttonTextColor": "#000000", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "borderRadius": 8, "borderWidth": 0, "borderColor": "transparent"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 16, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 16}, "style": {"paddingBottom": 24, "paddingLeft": 12, "paddingRight": 0, "paddingTop": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "Pincode", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "pincode", "initialValue": {"pincodeDetails": {"pincode": "400052", "city": "Bengaluru", "state": "Karnataka"}}}, "style": {"gridRowStart": 1, "gridRowEnd": 2, "gridColumnStart": 1, "gridColumnEnd": 4}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "pincodeDetails", "sendEdits": "true", "prefill": "true", "prefilledValue": "400052", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontWeight": "bold", "lineHeight": 16}, "text": "bengaluru", "textColor": "#039855"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": null, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "city"}, "style": {"gridRowStart": 2, "gridRowEnd": 3, "gridColumnStart": 1, "gridColumnEnd": 2}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "pincodeDetails", "sendEdits": "true", "prefill": "true", "prefilledValue": "400052", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontWeight": "bold", "lineHeight": 16}, "text": ",", "textColor": "#039855"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": null, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": ""}, "style": {"gridRowStart": 2, "gridRowEnd": 3, "gridColumnStart": 2, "gridColumnEnd": 3, "marginRight": 6}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "pincodeDetails", "sendEdits": "true", "prefill": "true", "prefilledValue": "400052", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontWeight": "bold", "lineHeight": 16}, "text": "karnataka", "textColor": "#039855"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": null, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "state"}, "style": {"gridRowStart": 2, "gridRowEnd": 3, "gridColumnStart": 3, "gridColumnEnd": 4}}], "formGroupDataStyle": {"display": "grid", "gridTemplateColumns": "auto auto 1fr", "gridTemplateRows": "auto", "columnGap": "0px", "rowGap": "0px", "backgroundColor": "#FFFFFF", "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "marginRight": 24, "paddingLeft": 24, "paddingRight": 24, "paddingBottom": 12, "paddingTop": 12}, "formGroupName": "Group7"}, {"formGroupName": "Group10", "formGroupData": [{"style": {}, "data": {"type": "CardFormFieldValue", "formFieldType": "RICH_CARD", "name": "employmentType", "mandatory": false, "disabled": false, "card": {"type": "RichCard", "icon": null, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Employment type", "showClipboard": false, "textColor": "#667085", "style": {"color": "#667085", "fontSize": 14, "lineHeight": 22, "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": {"sendImpression": "true", "fieldName": "employmentType", "sendEdits": "true", "prefill": "true", "prefilledValue": "Salaried", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "text": "Employment Type", "showClipboard": false, "textColor": "#1D2939", "style": {"color": "#1D2939", "fontSize": 16, "lineHeight": 24, "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "tagValue": null, "overlayTagValue": null, "style": {}, "cardPressEnabled": null, "viewType": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null, "next": {"tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "image": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 16, "height": 16}, "buttonViewType": "SOLID", "buttonSize": "LARGE", "disabled": false, "buttonColor": "inherit", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "style": {"paddingBottom": 24, "paddingTop": 0, "paddingLeft": 12, "paddingRight": 0}}, "action": {"screenType": null, "type": "CLIENT__INLINE_NAVIGATION", "omnitureData": null, "originalUrl": null, "url": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "CLIENT_PARAMS": true, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": null, "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": null, "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "CREDIT_LINE_REVIEW_FORM"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": null, "trackingData": null, "value": {"type": "MultiDropdownFormFieldValue", "formFieldType": "MULTI_DROPDOWN", "name": "employmentType", "label": "Employment type", "mandatory": true, "disabled": false, "value": "SALARIED", "tracking": {"sendImpression": true, "fieldName": "employmentType", "sendEdits": true, "prefill": true, "prefilledValue": "SelfEmployed", "sendInlineError": true}, "viewType": "DROPDOWN", "options": [{"id": "SALARIED", "title": "Salari<PERSON> (Job)", "subtitle": "You get a monthly salary"}, {"id": "SELF_EMPLOYED", "title": "Self Employed (Own Business)", "subtitle": "You have a business or shop"}], "defaultValue": ""}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {"backgroundColor": "red"}, "cardSummaryTitle": null, "formId": "REVIEW_PAGE_1", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Edit details", "textColor": "#4D43FE", "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 20, "fontWeight": "bold", "lineHeight": 24}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Save", "buttonColor": "#B5EF85", "buttonSize": "LARGE", "buttonTextColor": "#000000", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "borderRadius": 8, "borderWidth": 0, "borderColor": "transparent"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "statusText": null, "subStatusText": null, "actionButtons": null}, "displayType": "KEY_VALUE_MAPPER", "displayMapper": {"SALARIED": "Salari<PERSON> (Job)", "SELF_EMPLOYED": "Self Employed (Own Business)"}}}], "formGroupDataStyle": {"backgroundColor": "#FFFFFF", "display": "block", "marginRight": 24, "paddingBottom": 12, "paddingTop": 12, "paddingLeft": 24, "paddingRight": 24, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "borderBottomRightRadius": 8, "borderBottomLeftRadius": 8}}], "formId": "REVIEW_PAGE_1", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": null, "resultStoreKey": null, "subTitle": {"value": "RichTextValue", "text": "Hope we got your info right", "style": {"color": "#475467", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "textAlign": "center"}}, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": {"content": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "AnnouncementV2Value", "image": {"type": "ImageValue", "aspectRatio": "262:185", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/04/04/2024/16390b7e-85b7-4d0a-b11c-f3c0e375ea04.png?q={@quality}", "height": 185, "width": 262}, "subTitle": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "lineHeight": 22, "textAlign": "center"}, "text": "Do not press back or switch apps."}}, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "lineHeight": 30, "textAlign": "center"}, "text": "Your application details are being processed..."}}}}, "type": "ANNOUNCEMENT_V2"}, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__RESUME_ACTION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Select your EMI", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "borderColor": "#B5EF85", "borderRadius": 8}}, "buttonOrientation": null, "consent": null, "consentList": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": {"value": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/06/2025/7c73f43c-e31a-4664-a5bf-278dc068e50f.png?q={@quality}", "height": 16, "width": 63}, "title": {"type": "RichTextValue", "style": {"color": "#98A2B3", "fontSize": "12", "fontWeight": "normal", "lineHeight": 18}, "text": "Powered by"}}, "secondaryButtons": null, "sticky": null, "viewType": null, "containerStyle": null}, "title": {"value": "RichTextValue", "text": "Review your details", "style": {"color": "#4D43FE", "fontSize": 32, "fontWeight": "bold", "lineHeight": 40, "textAlign": "center"}}}