{"subWidget": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "lenderLogo": null, "button": {"tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Continue", "borderRadius": 8, "borderColor": "#B5EF85", "buttonTextSize": 18, "buttonTextWeight": "bold", "style": {"backgroundColor": "#B5EF85", "borderRadius": 8, "borderWidth": 0, "color": "#204601", "lineHeight": 22, "fontSize": 18}}, "action": {"url": "/api/sm/1/application/form", "type": "CLIENT__INLINE_NAVIGATION", "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_1"}}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,32,24,12", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"colGap": null, "containerStyle": null, "failCard": null, "formId": "BASIC_DETAILS", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "resultStoreKey": "data", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "disabled": false, "formFieldType": "DROPDOWN", "label": null, "mandatory": true, "name": "mandateOption", "title": {"type": "RichTextValue", "text": "Complete your EMI auto-pay set up", "containerStyle": {"marginLeft": 16, "marginRight": 16, "display": "block"}, "style": {"color": "#4D43FE", "fontSize": 24, "fontWeight": "bold", "lineHeight": 28}}, "options": [{"id": "UPI", "title": null, "type": "IMAGE", "image": {"type": "ImageValue", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/05/2025/8e598da3-74fa-4498-a13a-8cff10027144.png?q={@quality}", "height": 32, "width": 32}, "titleContainerStyle": {"borderStyle": "none", "background": "none"}, "subtitle": {"type": "RichTextValue", "text": "UPI", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal"}}, "description": null}, {"id": "DEBIT_CARD", "title": null, "type": "IMAGE", "image": {"type": "ImageValue", "dynamicImageUrl": " https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/05/2025/4f11b6c1-28d8-40f0-9956-243ed63ffa9e.png?q={@quality}", "height": 32, "width": 32}, "titleContainerStyle": {"borderStyle": "none", "background": "none"}, "subtitle": {"type": "RichTextValue", "text": "Debit Card", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal"}}, "description": {"type": "RichTextValue", "text": "For better success rate", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal"}}}, {"id": "NETBANKING", "title": null, "type": "IMAGE", "image": {"type": "ImageValue", "dynamicImageUrl": " https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/05/2025/2b76b76f-cea0-4caf-8fa2-3f3c892ae3d1.png?q={@quality}", "height": 32, "width": 32}, "titleContainerStyle": {"borderStyle": "none", "background": "none"}, "subtitle": {"type": "RichTextValue", "text": "Net Banking", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal"}}, "description": null}], "displayType": "DROPDOWN", "stackType": "VERTICAL"}}], "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichButtonValue", "title": "Confirm", "style": {"backgroundColor": "#B5EF85", "borderRadius": 8, "borderWidth": 0, "color": "#1D2939", "lineHeight": 22, "fontSize": 18}}, "action": {"screenType": null, "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "type": "CALM__RESUME_ACTION", "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": {"value": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/06/2025/7c73f43c-e31a-4664-a5bf-278dc068e50f.png?q={@quality}", "height": 16, "width": 63}, "title": {"type": "RichTextValue", "style": {"color": "#98A2B3", "fontSize": "12", "fontWeight": "normal", "lineHeight": 18}, "text": "Powered by"}}, "containerStyle": {"padding": 20, "width": "calc(100% - 32px)", "position": "sticky"}}, "title": null}, "tracking": {}}}]}}, "loginType": "LOGIN_NOT_REQUIRED"}, "rcType": null, "metaData": null}, "containerStyle": {"position": "fixed", "bottom": 0, "left": 0, "right": 0, "padding": 16}, "buttonOrientation": null, "autoSubmit": false, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null}}