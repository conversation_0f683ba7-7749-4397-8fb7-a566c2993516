{"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "REVIEW_PAGE_1", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "title": {"text": "Add delivery address", "style": {"color": "#4D43FE", "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold", "fontSize": 32, "lineHeight": 50}}, "subTitle": {"text": "To deliver your superCard in your hands", "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal", "fontSize": 16, "marginTop": 32}}, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {"marginTop": 0}, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Line 1", "mandatory": true, "name": "line1", "noOfErrors": 1, "placeholder": "Line 1", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 32, "fontStyle": "normal", "fontWeight": "bold", "border": "none", "borderBottom": "2px solid #D0D5DD", "width": 100, "backgroundColor": "#F2F4F7"}, "inputBoxStyle": {"backgroundColor": "#F2F4F7", "border": "none", "paddingLeft": 0, "box-shadow": "none", "paddingTop": 0}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "marginTop": 32}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}, "value": null}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {}, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Line 2", "mandatory": true, "name": "line2", "noOfErrors": 1, "placeholder": "Line 2", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 32, "fontStyle": "normal", "fontWeight": "bold", "border": "none", "borderBottom": "2px solid #D0D5DD", "width": 100, "backgroundColor": "#F2F4F7"}, "inputBoxStyle": {"backgroundColor": "#F2F4F7", "border": "none", "paddingLeft": 0, "box-shadow": "none", "paddingTop": 0}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal"}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}, "value": null}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CustomFormFieldValue", "containerStyle": null, "customFieldData": {"city": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "City", "mandatory": true, "name": "city", "placeholder": "", "textboxStyles": {"inputStyle": {"fontFamily": "<PERSON><PERSON>", "fontSize": 32, "fontStyle": "normal", "fontWeight": "bold", "border": "none", "borderBottom": "2px solid #D0D5DD", "width": 100, "backgroundColor": "#F2F4F7"}, "inputBoxStyle": {"backgroundColor": "#F2F4F7", "border": "none", "paddingLeft": 0, "box-shadow": "none", "paddingTop": 0}, "labelStyle": {"fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal"}, "captionStyle": {"fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}}, "customFieldType": "PINCODE_V0", "pincode": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/6/pincode/existence", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Pin Code", "mandatory": true, "maxCharacters": 10, "minCharacters": 10, "name": "pincode", "noOfErrors": 1, "placeholder": "enter you pin code", "validationRegex": "^[1-9]{1}[0-9]{5}$", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter a valid pin code", "interactionType": null, "regex": "^(?!\\s*$)[1-9]{1}[0-9]{5}$", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 32, "fontStyle": "normal", "fontWeight": "bold", "border": "none", "borderBottom": "2px solid #D0D5DD", "width": 100, "backgroundColor": "#F2F4F7"}, "inputBoxStyle": {"backgroundColor": "#F2F4F7", "border": "none", "paddingLeft": 0, "box-shadow": "none", "paddingTop": 0}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal"}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}, "value": null}}, "state": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "State", "mandatory": true, "name": "state", "placeholder": "", "textboxStyles": {"inputStyle": {"fontFamily": "<PERSON><PERSON>", "fontSize": 32, "fontStyle": "normal", "fontWeight": "bold", "border": "none", "borderBottom": "2px solid #D0D5DD", "width": 100, "backgroundColor": "#F2F4F7"}, "inputBoxStyle": {"backgroundColor": "#F2F4F7", "border": "none", "paddingLeft": 0, "box-shadow": "none", "paddingTop": 0}, "labelStyle": {"fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal"}, "captionStyle": {"fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}}}, "disabled": true, "formFieldType": "CUSTOM", "grids": null, "inputStyle": null, "label": null, "mandatory": true, "name": "pincodeDetails", "noOfErrors": null, "outerContainerStyle": null, "tracking": null, "validationRuleList": null, "value": null}}], "resultStoreKey": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__RESUME_ACTION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "containerStyle": {"backgroundColor": "#B5EF85", "borderColor": "#B5EF85", "borderRadius": 8, "borderWidth": 1, "height": 48, "width": 100, "boxShadow": "none"}, "value": {"type": "RichTextButtonValue", "title": "Continue", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "borderRadius": 8, "text": "Continue", "fontWeight": 700}}, "buttonOrientation": null, "consent": null, "consentList": null, "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": false, "viewType": null}}