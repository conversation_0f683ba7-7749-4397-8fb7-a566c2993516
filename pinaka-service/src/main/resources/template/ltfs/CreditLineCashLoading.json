{"items": [{"title": {"type": "RichTextValue", "text": "Your cash is loading", "style": {"color": "#4D43FE", "fontSize": 32, "fontWeight": "bold"}, "containerStyle": {"display": "block", "marginBottom": 12}}, "subTitle": {"type": "RichTextValue", "text": "Hang tight, this might take a minute. Please don't go back or leave the app.", "style": {"color": "#475467", "fontSize": 16}}, "image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/13/05/2025/d14e2627-61ca-4ce5-bc35-5eb121e23f05.png?q={@quality}", "height": 300, "width": 300, "type": "ImageValue"}, "type": "ImageTextValue"}], "pollingContext": {"action": {"params": {}, "screenType": "multiWidgetPage", "tracking": {}, "type": "POLLING", "url": "/api/sm/1/application/state"}, "interval": 2000, "threshold": 300000}, "progressBarMetaData": {"progressBarAColor": "#B5EF85", "progressBarBColor": "#EAECF0"}, "fallbackValue": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/10/2023/554b30c9-4309-4815-ac8b-2ccb42c51a4a.png?q={@quality}", "height": 192, "width": 192, "aspectRatio": "1:1", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Something went wrong", "style": {"color": "#000000", "fontSize": 18, "fontWeight": "bold", "textAlign": "center", "lineHeight": 28}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Our team is working hard to fix it, please check back again later", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 22}}}, "richButton": {"action": {"type": "MERCHANT_HOMEPAGE", "params": {}, "tracking": {}, "loginType": "LOGIN_NOT_REQUIRED"}, "value": {"type": "RichTextButtonValue", "title": "Okay, got it"}}, "type": "AnnouncementV2Value"}}