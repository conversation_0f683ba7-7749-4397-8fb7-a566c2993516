{
  "clientPowered": false,
  "colGap": null,
  "containerStyle": null,
  "failCard": null,
  "formId": "REVIEW_PAGE_1",
  "gap": 0,
  "numGridColumns": 0,
  "orientation": "PORTRAIT",
  "paginationRequestContext": null,
  "persistFormData": false,
  "title": {
    "text": "Your personal details",
    "style": {
      "color": "#4D43FE",
      "fontFamily": "Anek Latin",
      "fontWeight": "bold",
      "fontSize": 32
    }
  },
  "subTitle": {
    "text": "To give credit where it's due",
    "style": {
      "color": "#475467",
      "fontFamily": "Anek Latin",
      "fontWeight": "normal",
      "fontSize": 16,
      "lineHeight": 32
    }
  },
  "renderableComponents": [
    {
      "action": null,
      "lenderLogo": null,
      "metaData": null,
      "rcType": null,
      "tracking": null,
      "trackingData": null,
      "style": {},
      "value": {
        "type": "TextBoxFormFieldValueV0",
        "autoCapitalize": "words",
        "disabled": false,
        "formFieldType": "TEXT_BOX_V0",
        "label": "First name and middle name",
        "mandatory": true,
        "name": "firstName",
        "noOfErrors": 1,
        "placeholder": "Enter your first name",
        "validationRuleList": [
          {
            "errorMessage": "Complete this field to proceed",
            "interactionType": null,
            "regex": "\\S",
            "ruleType": "REGEX",
            "validateOnSubmit": false
          }
        ],
        "textboxStyles": {
          "inputStyle": {
            "color": "#1D2939",
            "fontFamily": "Anek Latin",
            "fontSize": 32,
            "fontStyle": "normal",
            "fontWeight": "bold",
            "border": "none",
            "borderBottom": "2px solid #D0D5DD",
            "backgroundColor": "#F2F4F7"
          },
          "inputBoxStyle": {
            "backgroundColor": "#F2F4F7",
            "border": "none",
            "paddingLeft": 0,
            "boxShadow": "none",
            "paddingTop": 0
          },
          "labelStyle": {
            "color": "#344054",
            "fontFamily": "Anek Latin",
            "fontSize": 14,
            "fontStyle": "normal",
            "fontWeight": "normal",
            "letterSpacing": 0.28,
            "marginTop": 32
          },
          "captionStyle": {
            "color": "#1D2939",
            "fontFamily": "Anek Latin",
            "fontSize": 12,
            "fontStyle": "normal",
            "fontWeight": "normal",
            "letterSpacing": 0.24
          }
        },
        "value": null
      }
    },
    {
      "action": null,
      "lenderLogo": null,
      "metaData": null,
      "rcType": null,
      "tracking": null,
      "trackingData": null,
      "style": {},
      "value": {
        "type": "TextBoxFormFieldValueV0",
        "autoCapitalize": "words",
        "disabled": false,
        "formFieldType": "TEXT_BOX_V0",
        "label": "Last Name",
        "mandatory": true,
        "name": "lastName",
        "noOfErrors": 1,
        "placeholder": "Enter your last name",
        "validationRuleList": [
          {
            "errorMessage": "Complete this field to proceed",
            "interactionType": null,
            "regex": "\\S",
            "ruleType": "REGEX",
            "validateOnSubmit": false
          }
        ],
        "textboxStyles": {
          "inputStyle": {
            "color": "#1D2939",
            "fontFamily": "Anek Latin",
            "fontSize": 32,
            "fontStyle": "normal",
            "fontWeight": "bold",
            "border": "none",
            "borderBottom": "2px solid #D0D5DD",
            "backgroundColor": "#F2F4F7"
          },
          "inputBoxStyle": {
            "backgroundColor": "#F2F4F7",
            "border": "none",
            "paddingLeft": 0,
            "boxShadow": "none",
            "paddingTop": 0
          },
          "labelStyle": {
            "color": "#344054",
            "fontFamily": "Anek Latin",
            "fontSize": 14,
            "fontStyle": "normal",
            "fontWeight": "normal",
            "letterSpacing": 0.28
          },
          "captionStyle": {
            "color": "#1D2939",
            "fontFamily": "Anek Latin",
            "fontSize": 12,
            "fontStyle": "normal",
            "fontWeight": "normal",
            "letterSpacing": 0.24
          }
        },
        "value": null
      }
    },
    {
      "tracking": null,
      "trackingData": null,
      "value": {
        "type": "TextBoxFormFieldValueV0",
        "formFieldType": "TEXT_BOX_V0",
        "name": "email",
        "label": "Email Address",
        "mandatory": true,
        "disabled": false,
        "value": null,
        "inputType": "EMAIL_ADDRESS",
        "textboxStyles": {
          "inputStyle": {
            "color": "#1D2939",
            "fontFamily": "Anek Latin",
            "fontSize": 32,
            "fontStyle": "normal",
            "fontWeight": "bold",
            "border": "none",
            "borderBottom": "2px solid #D0D5DD",
            "backgroundColor": "#F2F4F7"
          },
          "inputBoxStyle": {
            "backgroundColor": "#F2F4F7",
            "border": "none",
            "paddingLeft": 0,
            "box-shadow": "none",
            "paddingTop": 0
          },
          "labelStyle": {
            "color": "#344054",
            "fontFamily": "Anek Latin",
            "fontSize": 14,
            "fontStyle": "normal",
            "fontWeight": "normal",
            "letterSpacing": 0.28
          },
          "captionStyle": {
            "color": "#1D2939",
            "fontFamily": "Anek Latin",
            "fontSize": 12,
            "fontStyle": "normal",
            "fontWeight": "normal",
            "letterSpacing": 0.24
          }
        },
        "validationRuleList": [
          {
            "ruleType": "REGEX",
            "errorMessage": "Please enter correct Email",
            "interactionType": null,
            "validateOnSubmit": false,
            "regex": "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,6})$"
          }
        ],
        "warningValidation": {
          "type": "emailHostCheck",
          "condition": [
            "gmail.co.za",
            "gmail.com",
            "gmail.com.au",
            "gmail.com.br",
            "gmail.ru",
            "hotmail.be",
            "hotmail.ca",
            "hotmail.ch",
            "hotmail.co",
            "hotmail.co.il",
            "hotmail.co.jp",
            "hotmail.co.nz",
            "hotmail.co.uk",
            "hotmail.co.za",
            "hotmail.com",
            "hotmail.com.ar",
            "hotmail.com.au",
            "hotmail.com.br",
            "hotmail.com.mx",
            "hotmail.com.tr",
            "hotmail.de",
            "hotmail.es",
            "hotmail.fi",
            "hotmail.fr",
            "hotmail.it",
            "hotmail.kg",
            "hotmail.kz",
            "hotmail.my",
            "hotmail.nl",
            "hotmail.ro",
            "hotmail.roor",
            "hotmail.ru",
            "rediff.com",
            "rediffmail.com",
            "rediffmailpro.com",
            "yahoo.ae",
            "yahoo.at",
            "yahoo.be",
            "yahoo.ca",
            "yahoo.ch",
            "yahoo.cn",
            "yahoo.co",
            "yahoo.co.id",
            "yahoo.co.il",
            "yahoo.co.in",
            "yahoo.co.jp",
            "yahoo.co.kr",
            "yahoo.co.nz",
            "yahoo.co.th",
            "yahoo.co.uk",
            "yahoo.co.za",
            "yahoo.com",
            "yahoo.com.ar",
            "yahoo.com.au",
            "yahoo.com.br",
            "yahoo.com.cn",
            "yahoo.com.co",
            "yahoo.com.hk",
            "yahoo.com.is",
            "yahoo.com.mx",
            "yahoo.com.my",
            "yahoo.com.ph",
            "yahoo.com.ru",
            "yahoo.com.sg",
            "yahoo.com.tr",
            "yahoo.com.tw",
            "yahoo.com.vn",
            "yahoo.cz",
            "yahoo.de",
            "yahoo.dk",
            "yahoo.es",
            "yahoo.fi",
            "yahoo.fr",
            "yahoo.gr",
            "yahoo.hu",
            "yahoo.ie",
            "yahoo.in",
            "yahoo.it",
            "yahoo.jp",
            "yahoo.net",
            "yahoo.nl",
            "yahoo.no",
            "yahoo.pl",
            "yahoo.pt",
            "yahoo.ro",
            "yahoo.ru",
            "yahoo.se",
            "yahoofs.com",
            "yahoomail.com",
            "aol.com",
            "msn.com",
            "wanadoo.fr",
            "orange.fr",
            "comcast.net",
            "live.com",
            "free.fr",
            "gmx.de",
            "web.de",
            "yandex.ru",
            "ymail.com",
            "libero.it",
            "outlook.com",
            "uol.com.br",
            "bol.com.br",
            "mail.ru",
            "cox.net",
            "sbcglobal.net",
            "sfr.fr",
            "live.fr",
            "verizon.net",
            "live.co.uk",
            "googlemail.com",
            "ig.com.br",
            "live.nl",
            "bigpond.com",
            "terra.com.br",
            "neuf.fr",
            "alice.it",
            "rocketmail.com",
            "att.net",
            "laposte.net",
            "facebook.com",
            "bellsouth.net",
            "charter.net",
            "rambler.ru",
            "tiscali.it",
            "shaw.ca",
            "sky.com",
            "earthlink.net",
            "optonline.net",
            "freenet.de",
            "t-online.de",
            "aliceadsl.fr",
            "virgilio.it",
            "home.nl",
            "qq.com",
            "telenet.be",
            "me.com",
            "tiscali.co.uk",
            "voila.fr",
            "gmx.net",
            "mail.com",
            "planet.nl",
            "tin.it",
            "live.it",
            "ntlworld.com",
            "arcor.de",
            "frontiernet.net",
            "hetnet.nl",
            "live.com.au",
            "zonnet.nl",
            "club-internet.fr",
            "juno.com",
            "optusnet.com.au",
            "blueyonder.co.uk",
            "bluewin.ch",
            "skynet.be",
            "sympatico.ca",
            "windstream.net",
            "mac.com",
            "centurytel.net",
            "chello.nl",
            "live.ca",
            "aim.com",
            "bigpond.net.au"
          ],
          "warningMessage": "Please recheck correctness of your email address"
        },
        "autoCapitalize": "none",
        "placeholder": "enter full email ID"
      },
      "action": null,
      "rcType": null,
      "metaData": null
    }
  ],
  "resultStoreKey": null,
  "subWidget": null,
  "submitButton": {
    "type": "SubmitButtonValue",
    "alwaysEnabled": true,
    "autoSubmit": false,
    "button": {
      "action": {
        "constraints": null,
        "customTrackingEvents": null,
        "encryption": null,
        "fallback": null,
        "loaderContent": null,
        "loginType": "LOGIN_NOT_REQUIRED",
        "nonWidgetizeRedirection": null,
        "omnitureData": null,
        "originalUrl": null,
        "params": {
          "processInstanceId": null,
          "taskKey": null,
          "applicationId": null,
          "taskId": null,
          "token": null
        },
        "requiredPermissionType": null,
        "requiredPermissionTypes": null,
        "screenType": null,
        "tracking": {},
        "triggerExtraStandardEvents": null,
        "type": "CALM__RESUME_ACTION",
        "url": "/api/sm/1/application/form",
        "validationMeta": null,
        "widgetTracking": null
      },
      "lenderLogo": null,
      "metaData": null,
      "rcType": null,
      "tracking": null,
      "trackingData": null,
      "value": {
        "type": "RichTextButtonValue",
        "title": "Continue",
        "borderRadius": 8,
        "borderColor": "#B5EF85",
        "buttonTextSize": 18,
        "buttonTextWeight": "bold"
      }
    }
  },
  "buttonOrientation": null,
  "consent": null,
  "consentList": null,
  "containerStyle": null,
  "footerValue": null,
  "headerValue": null,
  "hidden": false,
  "lenderLogo": null,
  "secondaryButtons": null,
  "sticky": null,
  "viewType": null
}
}