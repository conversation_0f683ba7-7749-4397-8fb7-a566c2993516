{"submitButton": {"type": "SubmitButtonValue", "consent": {"consentType": "CHECKBOX", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "By continuing I agree to the above displayed Key Fact Statement and the Terms and Conditions."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}, "button": {"action": {"url": "/api/sm/1/application/form", "encryption": {}, "params": {}, "type": "CALM__RESUME_ACTION", "tracking": {}, "loginType": "LOGIN_NOT_REQUIRED"}, "value": {"type": "RichTextButtonValue", "title": "Confirm with OTP"}}}}