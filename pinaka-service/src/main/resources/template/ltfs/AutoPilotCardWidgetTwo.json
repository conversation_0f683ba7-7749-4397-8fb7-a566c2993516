{"subWidget": null, "clientPowered": false, "card": {"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": null, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Initiating autopay of up to", "showClipboard": false, "textColor": "#667085", "style": {"color": "#1D2939", "fontSize": 18, "lineHeight": 22, "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold", "marginTop": 8}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "₹ 250000", "showClipboard": false, "textColor": "#1D2939", "style": {"color": "#475467", "fontSize": 40, "lineHeight": 48, "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "textContainerStyle": {"display": "flex", "flexDirection": "column", "alignItems": "flex-start"}, "tagValue": null, "overlayTagValue": null, "style": {"padding": 8, "alignItems": "flex-start", "minWidth": 312}, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "PORTRAIT", "requestContext": null, "cardType": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}}