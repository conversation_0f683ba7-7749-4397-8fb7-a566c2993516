{"card": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "requestContext": null, "statusText": null, "subStatusText": null, "superTitle": null, "icon": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/13/05/2025/d14e2627-61ca-4ce5-bc35-5eb121e23f05.png?q={@quality}", "height": 200, "width": 200, "type": "ImageValue"}, "viewType": "PRIMITIVE_CARD", "orientation": "PORTRAIT", "style": {"paddingBottom": 26, "paddingTop": 26}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#4D43FE", "fontFamily": "<PERSON><PERSON>", "fontSize": 40, "fontWeight": "bold", "lineHeight": 48}, "text": null}}, "textContainerStyle": {"alignItems": "center", "display": "flex"}, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 20, "fontWeight": "normal", "lineHeight": 24}, "containerStyle": {"marginTop": 16}, "text": null}}}}}