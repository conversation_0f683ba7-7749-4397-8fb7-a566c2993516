{"orientation": "LANDSCAPE", "gap": 0, "containerStyle": {"justifyContent": "space-between", "alignItems": "center", "width": "100%"}, "renderableComponents": [{"action": null, "value": {"type": "RichCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "gap": "12", "flexGrow": 0, "flexShrink": 0}, "subStatusText": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 16, "fontWeight": "bold", "lineHeight": 20}, "text": "Documents", "textColor": "#1D2939"}}, "description": null, "viewType": "RICH_CARD"}}, {"action": null, "value": {"type": "RichCard", "actionButtons": null, "next": {"action": {"loginType": "LOGIN_NOT_REQUIRED", "params": {"fileName": "statement.pdf"}, "screenType": "multiWidgetPage", "tracking": {}, "type": "DOWNLOAD_FILE", "url": ""}, "value": {"type": "RichButtonValue", "borderRadius": 5, "buttonColor": "#FFFFFF", "buttonTextColor": "#344054", "buttonTextSize": 12, "buttonTextWeight": "500", "style": {"border": "1px solid #D0D5DD", "padding": 12, "display": "flex", "alignItems": "center", "gap": "8", "whiteSpace": "nowrap"}, "title": "Loan agreement", "iconRight": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/07/2025/9410fd2b-2b89-4f21-91ce-44ba87f5aab9.png?q={@quality}", "height": 14, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/07/2025/9410fd2b-2b89-4f21-91ce-44ba87f5aab9.png?q={@quality}", "width": 14}}}, "orientation": "LANDSCAPE", "style": {"display": "flex", "flexDirection": "row", "gap": "12"}, "viewType": "RICH_CARD"}}, {"action": null, "value": {"type": "RichCard", "actionButtons": null, "next": {"action": {"loginType": "LOGIN_NOT_REQUIRED", "params": {"fileName": "statement.pdf"}, "screenType": "multiWidgetPage", "tracking": {}, "type": "DOWNLOAD_FILE", "url": ""}, "value": {"type": "RichButtonValue", "borderRadius": 5, "buttonColor": "#FFFFFF", "buttonTextColor": "#344054", "buttonTextSize": 12, "buttonTextWeight": "500", "style": {"border": "1px solid #D0D5DD", "padding": 12, "display": "flex", "alignItems": "center", "gap": "8"}, "title": "KFS", "iconRight": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/07/2025/9410fd2b-2b89-4f21-91ce-44ba87f5aab9.png?q={@quality}", "height": 14, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/07/2025/9410fd2b-2b89-4f21-91ce-44ba87f5aab9.png?q={@quality}", "width": 14}}}, "orientation": "LANDSCAPE", "style": {"display": "flex", "flexDirection": "row", "gap": "12"}, "viewType": "RICH_CARD"}}]}