{"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "LOAN_EMI_DETAILS", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CustomFormFieldValue", "containerStyle": null, "customFieldData": {"accountAggregatorAction": null, "amountRangeOffers": [{"charges": [{"calculatorType": "DEDUCTED_CHARGES", "chargeType": "PROCESSING_FEE", "gst": 18, "name": {"fontFamily": null, "fontSize": null, "fontStyle": null, "fontWeight": null, "padding": null, "text": "Processing fee", "textAlignment": null, "textColor": null}, "type": "PERCENTAGE", "value": 1}], "interest": {"value": null}, "max": null, "min": null, "repaymentDetails": {"endDate": null, "startDate": null, "tenure": {"unit": null, "value": null}}, "tag": {"type": "ImageValue", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/15/05/2025/4d5715fd-bb35-40f7-958d-ab8dc98ef2ad.png?q={@quality}", "height": 24, "width": 95}}, {"charges": [{"calculatorType": "DEDUCTED_CHARGES", "chargeType": "PROCESSING_FEE", "gst": 18, "name": {"fontFamily": null, "fontSize": null, "fontStyle": null, "fontWeight": null, "padding": null, "text": "Processing fee", "textAlignment": null, "textColor": null}, "type": "PERCENTAGE", "value": 3}], "interest": {"value": 15.75}, "max": null, "min": null, "repaymentDetails": {"endDate": "2030-04-05", "startDate": "2025-04-05", "tenure": {"unit": "MONTH", "value": 48}}}], "amountSlider": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RangeFormFieldValue", "defaultValue": null, "disabled": false, "formFieldType": "RANGE", "label": "Selected Amount is", "mandatory": true, "maxValue": null, "minValue": 50000, "name": "amountSlider", "stepperValue": {"type": "StepperValue", "stepSize": 1000, "stepperUnit": "RUPEE"}}}, "product": "CREDIT_LINE", "customFieldType": "BANK_OFFER_FIELD_V2"}, "disabled": false, "formFieldType": "CUSTOM", "grids": null, "inputStyle": null, "label": "", "mandatory": true, "name": "bankOffer", "noOfErrors": null, "outerContainerStyle": null, "tracking": null, "validationRuleList": null, "value": null}}], "resultStoreKey": null, "subTitle": {"type": "RichTextValue", "text": "Make sure there's no error!", "showClipboard": false, "style": {"color": "#475467", "fontSize": 16, "lineHeight": 20, "textAlign": "center", "paddingBottom": 16}}, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": null, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": {"content": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "AnnouncementV2Value", "image": {"type": "ImageValue", "aspectRatio": "262:185", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/04/04/2024/16390b7e-85b7-4d0a-b11c-f3c0e375ea04.png?q={@quality}", "height": 185, "width": 262}, "subTitle": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "lineHeight": 22, "textAlign": "center"}, "text": "Do not press back or switch apps."}}, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "lineHeight": 30, "textAlign": "center"}, "text": "Your offer is being saved... Please wait"}}}}, "type": "ANNOUNCEMENT_V2"}, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__RESUME_ACTION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Continue with KYC", "buttonColor": "#B5EF85", "buttonTextSize": 18, "buttonTextWeight": "bold", "buttonTextColor": "#1D2939", "style": {"paddingLeft": 20, "paddingRight": 20, "paddingTop": 16, "paddingBottom": 16, "borderColor": "#B5EF85", "borderRadius": 8, "borderWidth": 1}}}, "buttonOrientation": null, "consent": null, "consentList": null, "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": {"title": null, "value": {"type": "ImageValue", "alternateText": "Go to top", "dynamicImageUrl": "", "height": 25, "width": 312}}, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": {"type": "RichTextValue", "text": "Choose your EMI", "showClipboard": false, "style": {"color": "#4D43FE", "fontSize": 32, "lineHeight": 40, "fontWeight": "bold", "textAlign": "center", "paddingTop": 16}}}