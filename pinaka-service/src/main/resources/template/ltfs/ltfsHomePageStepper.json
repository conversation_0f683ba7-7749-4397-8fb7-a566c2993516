{"completedProgressColor": "#008C00", "incompleteProgressColor": "#D9D9D9", "selectedTextColor": "#111112", "completedIcon": {"type": "ImageValue", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/05/2025/f4d7406a-0f34-4c22-b8cb-cbdf79881b10.png?q={@quality}", "height": 16, "width": 16}, "incompleteIcon": {"type": "ImageValue", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/13/05/2025/bdcce686-05a7-48d7-8e01-d1240a7222cc.png?q={@quality}", "height": 16, "width": 16}, "inprogressIcon": {"type": "ImageValue", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/13/05/2025/eacb0224-560d-40db-a05b-0fa0332f7608.png?q={@quality}", "height": 16, "width": 16}, "stepperProgressStyle": "DASHED", "steps": [{"stepCard": {"type": "RichCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/13/05/2025/6c1a2fac-cc30-455b-8b18-5d250f19d7c3.png?q={@quality}", "height": 50, "source": "https://fastly.picsum.photos/id/237/200/300.jpg?hmac=TmmQSbShHz9CdQm0NkEjx1Dyh_Y984R9LpNrpvH2D_U", "width": 50}, "iconBackgroundColor": null, "iconSize": "MEDIUM", "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"padding": 16, "flex": 1, "borderRadius": 8, "lineHeight": 1, "backgroundColor": "#EAECF0", "position": "relative", "bottom": 10, "alignItems": "flex-start", "gap": 2}, "subStatusText": null, "superTitle": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 18}, "containerStyle": {"flex": 1}, "text": "Review your details", "textColor": "#1D2939"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontWeight": "normal", "lineHeight": 18}, "containerStyle": {"width": "130%", "marginTop": 8}, "text": "Verify your personal and work details for the bank.", "textColor": "#475467"}}, "actionButtons": [{"action": {"loginType": "LOGIN_NOT_REQUIRED", "params": {}, "screenType": "multiWidgetPage", "tracking": {}, "type": "CALM__VIEW_ACTION", "url": "/api/fpg/1/action/view"}, "value": {"type": "RichTextButtonValue", "title": "Get Started", "buttonColor": "#B5EF85", "buttonTextWeight": "bold", "buttonTextColor": "#204601", "borderStyle": "1px solid #D0D5DD", "borderColor": "#B5EF85", "style": {"width": "100%", "borderStyle": "solid", "borderWidth": 1, "borderRadius": 8, "paddingLeft": 20, "paddingRight": 20}}}], "viewType": "STEPPER_CARD", "tagValue": {"richText": {"textColor": "#475467", "textSize": 12, "containerStyle": {"backgroundColor": "#F2F4F7", "paddingRight": 8, "paddingLeft": 8, "paddingTop": 4, "paddingBottom": 4}, "text": "1 min", "title": "1 min"}}}, "showCompleted": false, "showSelected": false, "showIncompleted": false}, {"stepCard": {"type": "RichCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/07/07/2025/c0b3de7d-4a9d-4156-82e5-367ab85b2c0f.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/07/07/2025/c0b3de7d-4a9d-4156-82e5-367ab85b2c0f.png?q={@quality}", "width": 35}, "iconBackgroundColor": null, "iconSize": "MEDIUM", "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"padding": 16, "flex": 1, "borderRadius": 12, "lineHeight": 1, "backgroundColor": "#EAECF0", "position": "relative", "bottom": 10, "alignItems": "center", "gap": 2, "borderColor": "#EAECF0"}, "subStatusText": null, "superTitle": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 18}, "containerStyle": {"flex": 1}, "text": "Choose EMI Plan", "textColor": "#1D2939"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontWeight": "normal", "lineHeight": 18}, "containerStyle": {"width": "130%", "marginTop": 8}, "text": "Choose a EMI plan which is ideal for you", "textColor": "#475467"}}, "actionButtons": [{"action": {"loginType": "LOGIN_NOT_REQUIRED", "params": {}, "screenType": "multiWidgetPage", "tracking": {}, "type": "CALM__VIEW_ACTION", "url": "/api/fpg/1/action/view"}, "value": {"type": "RichTextButtonValue", "title": "Get Started", "buttonTextWeight": "bold", "buttonColor": "#B5EF85", "buttonTextColor": "#204601", "borderStyle": "1px solid #D0D5DD", "borderColor": "#B5EF85", "style": {"width": "100%", "borderStyle": "solid", "borderWidth": 1, "borderRadius": 8, "paddingLeft": 20, "paddingRight": 20}}}], "viewType": "STEPPER_CARD", "tagValue": {"richText": {"textColor": "#475467", "textSize": 12, "containerStyle": {"backgroundColor": "#F2F4F7", "paddingRight": 8, "paddingLeft": 8, "paddingTop": 4, "paddingBottom": 4}, "text": "1 min", "title": "1 min"}}}, "showCompleted": false, "showSelected": false, "showIncompleted": false}, {"stepCard": {"type": "RichCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/07/07/2025/6085cb78-75cc-43a1-8145-5d5856dd1d92.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/07/07/2025/6085cb78-75cc-43a1-8145-5d5856dd1d92.png?q={@quality}", "width": 35}, "iconBackgroundColor": null, "iconSize": "MEDIUM", "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"padding": 16, "flex": 1, "borderRadius": 12, "lineHeight": 1, "backgroundColor": "#EAECF0", "position": "relative", "bottom": 10, "alignItems": "center", "gap": 2, "borderColor": "#EAECF0"}, "subStatusText": null, "superTitle": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 18}, "containerStyle": {"flex": 1}, "text": "Set up bank details and KYC", "textColor": "#1D2939"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontWeight": "normal", "lineHeight": 18}, "containerStyle": {"width": "130%", "marginTop": 8}, "text": "Verify your bank details and review your KYC details.", "textColor": "#475467"}}, "actionButtons": [{"action": {"loginType": "LOGIN_NOT_REQUIRED", "params": {}, "screenType": "multiWidgetPage", "tracking": {}, "type": "CALM__VIEW_ACTION", "url": "/api/fpg/1/action/view"}, "value": {"type": "RichTextButtonValue", "title": "Get Started", "buttonTextWeight": "bold", "buttonColor": "#B5EF85", "buttonTextColor": "#204601", "borderStyle": "1px solid #D0D5DD", "borderColor": "#B5EF85", "style": {"width": "100%", "borderStyle": "solid", "borderWidth": 1, "borderRadius": 8, "paddingLeft": 20, "paddingRight": 20}}}], "viewType": "STEPPER_CARD", "tagValue": {"richText": {"textColor": "#475467", "textSize": 12, "containerStyle": {"backgroundColor": "#F2F4F7", "paddingRight": 8, "paddingLeft": 8, "paddingTop": 4, "paddingBottom": 4}, "text": "1 min", "title": "1 min"}}}, "showCompleted": false, "showSelected": false, "showIncompleted": false}], "unselectedTextColor": "#D9D9D9", "stepperContainerStyle": {"borderWidth": "0"}}