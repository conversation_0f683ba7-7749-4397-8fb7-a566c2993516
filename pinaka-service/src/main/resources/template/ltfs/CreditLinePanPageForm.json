{"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "PAN_PAGE", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "title": {"text": "Enter PAN", "style": {"color": "#4D43FE", "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold", "fontSize": 32}}, "subTitle": {"text": "To verify your details & process faster", "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal", "fontSize": 16}}, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {}, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Your PAN", "mandatory": true, "maxCharacters": 10, "minCharacters": 10, "name": "panNumber", "subText": "Your information is safe with us", "noOfErrors": 1, "placeholder": "enter your PAN", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter correct PAN", "interactionType": null, "regex": "^[A-Z]{5}[0-9]{4}[A-Z]{1}$", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 32, "fontStyle": "normal", "fontWeight": "bold", "border": "none", "borderBottom": "2px solid #D0D5DD", "backgroundColor": "#F2F4F7"}, "inputBoxStyle": {"backgroundColor": "#F2F4F7", "paddingLeft": 0, "boxShadow": "none", "borderRadius": 0, "borderWidth": 0, "borderBottom": "2px solid #D0D5DD", "paddingTop": 0}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "marginTop": 32}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}, "value": null}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "DATE", "label": "Date of birth", "mandatory": true, "maxValue": null, "minValue": null, "name": "dob", "noOfErrors": 1, "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": "14px", "fontStyle": "normal", "fontWeight": "400", "lineHeight": "18px", "letterSpacing": "0.28px"}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Enter a valid date", "interactionType": null, "regex": "^(0[1-9]|[12][0-9]|3[01])\\/(0[1-9]|1[0-2])\\/(19[2-9][1-9])|(200[0-6])$", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 32, "fontStyle": "normal", "fontWeight": "bold", "border": "none", "borderBottom": "2px solid #D0D5DD", "borderRadius": 0, "paddingLeft": 0, "backgroundColor": "#F2F4F7"}, "inputBoxStyle": {"backgroundColor": "#F2F4F7", "color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "paddingLeft": 0}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}, "value": null}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "MultiDropdownFormFieldValue", "disabled": false, "formFieldType": "MULTI_DROPDOWN", "label": "Gender", "mandatory": true, "name": "Gender", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "options": [{"id": "M", "title": "Male"}, {"id": "F", "title": "Female"}, {"id": "O", "title": "Other"}], "viewType": "RADIO_TYPE", "dropdownStyles": {"dropdownSuggestions": {"color": "#98A2B3", "fontFamily": "<PERSON><PERSON>", "fontSize": 32, "fontStyle": "normal", "fontWeight": "bold", "border": "none", "backgroundColor": "#F1F2F4"}, "dropdownTrigger": {"border": "none", "paddingLeft": 0, "color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 32, "fontStyle": "normal", "fontWeight": "bold", "borderRadius": 0, "borderBottom": "2px solid #D0D5DD", "backgroundColor": "#F2F4F7"}, "fieldLabel": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal"}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}}}], "resultStoreKey": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__RESUME_ACTION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Continue", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "borderRadius": 8, "richText": {"type": "RichTextValue", "style": {"color": "#1D2939", "fontSize": 18, "fontWeight": "bold", "fontFamily": "<PERSON><PERSON>"}}, "text": "Continue"}}, "buttonOrientation": null, "consent": null, "consentList": null, "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}}