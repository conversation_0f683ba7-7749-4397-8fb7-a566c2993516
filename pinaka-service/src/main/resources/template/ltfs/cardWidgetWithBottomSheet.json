{"card": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "requestContext": null, "statusText": null, "subStatusText": null, "title": {"action": {"type": "CLIENT__INLINE_NAVIGATION", "params": {"screenDetails": {"isBottomSheet": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-79371862", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": null}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-433355126", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "CARD_SUMMARY_LIST", "data": {"orientation": "PORTRAIT", "gap": 0, "listTitle": {"type": "RichTextAndImageValue", "title": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#4D43FE", "fontFamily": "<PERSON><PERSON>", "fontSize": 24, "fontWeight": "bold", "lineHeight": "18px"}, "text": "Details", "textColor": "#4D43FE"}}, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "requestContext": null, "statusText": null, "subStatusText": null, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 18}, "text": "27 February 2025 . 05:00 PM"}}, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 18}, "containerStyle": {"width": "100%", "display": "block"}, "text": "Date and Time"}}, "viewType": "PRIMITIVE_CARD", "orientation": "PORTRAIT", "style": {"alignItems": "baseline"}}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "requestContext": null, "statusText": null, "subStatusText": null, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 18}, "text": "ICICI 21803183018303"}}, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 18}, "containerStyle": {"width": "100%", "display": "block"}, "text": "Received Money from"}}, "viewType": "PRIMITIVE_CARD", "orientation": "PORTRAIT", "style": {"alignItems": "baseline"}}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichCard", "avatar": null, "iconBackgroundColor": null, "iconSize": null, "requestContext": null, "statusText": null, "subStatusText": null, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 18}, "text": "27 February 2025 . 05:00 PM"}}, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 18}, "containerStyle": {"width": "100%", "display": "block"}, "text": "Date and Time"}}, "viewType": "RICH_CARD", "orientation": "PORTRAIT", "style": {"alignItems": "baseline"}, "next": {"action": {"type": "COPY_TO_CLIPBOARD", "params": {"clipboardText": "qwertyuiopasdfghjkl;zxcvbnm,"}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "image": {"type": "ImageValue", "alternateText": ">", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/05/2025/f7338b51-9050-42ec-9710-18c3ede4d1d5.png?q={@quality}", "height": 16, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/05/2025/f7338b51-9050-42ec-9710-18c3ede4d1d5.png?q={@quality}", "width": 16}, "style": {"background": "none"}}}}}]}}}]}}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 18}, "containerStyle": {"marginTop": 16, "borderBottomColor": "#1D2939", "borderWidth": 1, "borderStyle": "SOLID", "borderTopColor": "#FFF", "borderRightColor": "#FFFF", "borderLeftColor": "#FFF"}, "text": "View Details"}}, "viewType": "PRIMITIVE_CARD", "orientation": "PORTRAIT", "style": {"paddingBottom": 26, "alignItems": "center"}}}}