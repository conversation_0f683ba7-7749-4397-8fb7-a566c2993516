{"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "LOAN_EMI_DETAILS", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CustomRangeFormFieldValue", "defaultValue": 351000, "disabled": false, "formFieldType": "CUSTOM_RANGE", "label": "Select cash to withdraw up to", "mandatory": true, "maxValue": 351000, "minValue": 50000, "name": "amountSlider", "stepperValue": {"type": "StepperValue", "stepSize": 1000, "stepperUnit": "RUPEE"}, "viewType": "CUSTOM", "activeTrack": {"background": "url('https://rukminim1.flixcart.com/www/1200/144/promos/14/05/2025/196354d1-fc13-4ab9-8c0b-3f258abf3026.png?q=90')", "borderTopLeftRadius": 8, "borderBottomLeftRadius": 8, "borderTopRightRadius": 8, "borderBottomRightRadius": 8, "height": "36"}, "inactiveTrack": {"background": "url('https://rukminim1.flixcart.com/www/1248/144/promos/14/05/2025/b79f58bf-7e4f-4e5b-b128-0d5872e9faa2.png?q=90')", "borderTopLeftRadius": 8, "borderBottomLeftRadius": 8, "borderTopRightRadius": 8, "borderBottomRightRadius": 8, "height": "36"}, "thumb": {"background": "url('https://rukminim1.flixcart.com/www/28/42/promos/16/05/2025/98b6d057-de73-4a6c-8ac1-2e0facd3256b.png?q=90') no-repeat", "borderTopRightRadius": 8, "borderBottomRightRadius": 8, "width": "28", "height": "42"}, "sliderThumbOffset": 24}}], "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "red", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-79371862", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": null}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,12", "widgetHeight": 40, "widgetWidth": 12}, "dataId": "-433355126", "elementId": "1-TEXT_V2", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "TEXT_V2", "viewType": "HEADING", "data": {"message": {"value": {"title": {"text": "Benefits of cash", "style": {"color": "#4D43FE", "fontFamily": "<PERSON><PERSON>", "fontSize": 24, "fontWeight": "bold", "lineHeight": 24, "textAlign": "left"}}}}}}}, {"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,12,24,12", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-433355126", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "CARD_SUMMARY_LIST", "data": {"orientation": "LANDSCAPE", "gap": 8, "containerStyle": {"justifyContent": "space-between"}, "renderableComponents": [{"action": null, "value": {"type": "RichCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": {"type": "ImageValue", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/18/07/2025/53f7cf7d-83c3-4f39-b3cc-323dd5f0a7da.png?q={@quality}", "width": 421, "height": 165}, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/97d11c27-6b20-497c-b826-ef87ba5ce81c.png?q={@quality}", "height": 52, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/97d11c27-6b20-497c-b826-ef87ba5ce81c.png?q={@quality}", "width": 52}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"padding": 4, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8, "backgroundColor": "#EFF8FF"}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "bold", "lineHeight": 12, "marginTop": 8}, "text": "12-60 months", "textColor": "#1D2939"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "normal", "lineHeight": 12, "paddingTop": 4}, "text": "Easy EMIs", "textColor": "#475467"}}, "textContainerStyle": {"paddingRight": 8}, "viewType": "RICH_CARD"}}, {"action": null, "value": {"type": "RichCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/d3b778f0-6f9c-4cfe-a12a-2d6cc8658ec7.png?q={@quality}", "height": 52, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/d3b778f0-6f9c-4cfe-a12a-2d6cc8658ec7.png?q={@quality}", "width": 52}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"backgroundColor": "#F7FFF0", "padding": 4, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "bold", "lineHeight": 12, "marginTop": 8}, "text": "12.5* lowest", "textColor": "#1D2939"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "normal", "lineHeight": 12, "paddingTop": 4}, "text": "Interest rate", "textColor": "#475467"}}, "textContainerStyle": {"paddingRight": 8}, "viewType": "RICH_CARD"}}]}}}, {"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,8,24,12", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-433355126", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "CARD_SUMMARY_LIST", "data": {"orientation": "PORTRAIT", "gap": 12, "renderableComponents": [{"action": null, "value": {"type": "RichCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/2c01508b-9604-46cb-a8e5-039cba703134.png?q={@quality}", "height": 32, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/2c01508b-9604-46cb-a8e5-039cba703134.png?q={@quality}", "width": 32}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"padding": 8, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "alignItems": "baseline"}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "bold", "lineHeight": 12, "marginTop": 8}, "text": "Borrow Flexibly", "textColor": "#1D2939"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "normal", "lineHeight": 12, "paddingTop": 8}, "text": "Use only what you need, pay interest only on the used amount", "textColor": "#475467"}}, "viewType": "RICH_CARD"}}, {"action": null, "value": {"type": "RichCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/175164a2-4097-4104-a1e8-4f5b7bcf373a.png?q={@quality}", "height": 32, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/175164a2-4097-4104-a1e8-4f5b7bcf373a.png?q={@quality}", "width": 32}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"padding": 8, "alignItems": "baseline"}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "bold", "lineHeight": 12, "marginTop": 8}, "text": "Quick Access", "textColor": "#1D2939"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "normal", "lineHeight": 12, "paddingTop": 8}, "text": "Instant funds for emergencies and planned expenses", "textColor": "#475467"}}, "viewType": "RICH_CARD"}}, {"action": null, "value": {"type": "RichCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/155785b1-2e12-4f91-83be-562421fc70c3.png?q={@quality}", "height": 32, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/155785b1-2e12-4f91-83be-562421fc70c3.png?q={@quality}", "width": 32}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"padding": 8, "alignItems": "baseline"}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "bold", "lineHeight": 12, "marginTop": 8}, "text": "Easy Repayments", "textColor": "#1D2939"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "normal", "lineHeight": 12, "paddingTop": 8}, "text": "Affordable EMI options to suit your buget", "textColor": "#475467"}}, "viewType": "RICH_CARD"}}, {"action": null, "value": {"type": "RichCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/155785b1-2e12-4f91-83be-562421fc70c3.png?q={@quality}", "height": 32, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/05/07/2025/155785b1-2e12-4f91-83be-562421fc70c3.png?q={@quality}", "width": 32}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"padding": 8, "alignItems": "baseline", "borderBottomLeftRadius": 8, "borderBottomRightRadius": 8}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "bold", "lineHeight": 12, "marginTop": 8}, "text": "No foreclosure charges", "textColor": "#1D2939"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "normal", "lineHeight": 12, "paddingTop": 8}, "text": "Affordable EMI options to suit your buget", "textColor": "#475467"}}, "viewType": "RICH_CARD"}}]}}}, {"slotType": "WIDGET", "id": 3, "parentId": 0, "layoutParams": {"orientation": "", "widgetHeight": 150, "widgetWidth": 24, "margin": "0,0,0,0", "padding": "24,12,24,12", "positionType": "absolute", "positionBottom": "0px", "positionLeft": "0px", "positionRight": "0px", "backgroundColor": "#fff"}, "dataId": "-476340040", "elementId": "3-ANNOUNCEMENT", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "SUBMIT_BUTTON_WIDGET", "data": {"subWidget": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "lenderLogo": null, "button": {"tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Okay got it", "style": {"backgroundColor": "#B5EF85", "borderRadius": 8, "borderWidth": 0, "color": "#000000"}}, "action": {"screenType": null, "omnitureData": null, "originalUrl": null, "type": "CALM__RESUME_ACTION", "url": "/api/sm/1/application/form", "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null}, "buttonOrientation": null, "autoSubmit": false, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"title": "Get cash now", "type": "RichTextButtonValue", "borderRadius": 8, "borderColor": "#B5EF85", "buttonTextSize": 18, "buttonTextWeight": "bold"}}, "buttonOrientation": null, "consent": {"checkboxField": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "I hereby grant express consent and authorize Scapic Innovations Pvt. Ltd (SIPL) to collect, process, record my personal data including personal information and sensitive personal information such as PAN, DOB, gender, contact details, income source (\"Personal Data\") during loan journey on the platform/application and share Personal Data with <a href='https://www.flipkart.com/pages/scapic-lp-tnc'>SIPL’s Partners</a> to complete Know Your Customer (KYC) requirements as required under applicable laws and for the purpose of processing my loan application (\"Purpose\"). I further consent to and authorize the <a href='https://www.flipkart.com/pages/scapic-lp-tnc'>SIPL’s Partners</a> to further share my Personal Data with their service providers and/or third parties for the Purpose including Tele-calling, SMS or WhatsApp. I also understand and authorize the <a href='https://www.flipkart.com/pages/scapic-lp-tnc'>SIPL’s Partners</a> to pull my credit report separately from any credit information companies' basis my Personal Data shared for the Purpose. I hereby consent to and expressly authorize SIPL to pull credit reports from credit information companies (Read Experian Specific terms <a href='https://www.flipkart.com/pages/experian-new-tnc'>here</a>, to evaluate my credit worthiness/eligibility/identity for financial services and products such as personal loans, credit cards, buy now pay later /or for my loan application respectively (End Use Purpose). SIPL does not share credit information received from credit information companies with third parties without your express consent. I hereby also confirm that I am an Indian citizen and accept that I have read the <a href='https://www.flipkart.com/pages/scapic-tnc'>terms and conditions</a> and <a href='https://www.flipkart.com/pages/scapic-privacy-policy'>privacy policy</a> of Scapic Innovations Pvt. Ltd."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "BUREAU_PULL", "consentId": "001", "consentType": "CHECKBOX"}, "containerStyle": {"zIndex": 100}, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "type": "SubmitButtonValue", "viewType": null}, "title": null}