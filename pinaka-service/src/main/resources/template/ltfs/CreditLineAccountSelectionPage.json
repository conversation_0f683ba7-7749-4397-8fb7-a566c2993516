{"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "ACCOUNT_SELECTION_FORM_PAGE", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "title": {"text": "Select account to deposit your money ", "style": {"color": "#4D43FE", "fontWeight": "bold", "fontSize": 32}}, "subTitle": {"text": "Your auto debit will be set on this account", "style": {"color": "#475467", "fontWeight": "normal", "fontSize": 16}}, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {}, "value": {"type": "TextBoxFormFieldValue", "disabled": false, "formFieldType": "TEXT_BOX", "label": "Account Number", "mandatory": true, "name": "account-number", "placeholder": "EX: ************", "noOfErrors": 1, "validationRuleList": [{"errorMessage": "Please enter correct Account Number", "interactionType": null, "regex": "^[0-9]{9,18}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontSize": 24, "fontStyle": "normal", "borderBottomColor": "#D0D5DD", "width": 100, "backgroundColor": "#FFF", "border": "none"}, "inputBoxStyle": {"backgroundColor": "#FFF", "border": "1px solid #D0D5DD", "borderRadius": 8}, "labelStyle": {"color": "#344054", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "marginTop": 32}, "captionStyle": {"color": "#1D2939", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}, "value": "**************"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {}, "value": {"type": "TextBoxFormFieldValue", "disabled": false, "formFieldType": "TEXT_BOX", "label": "IFSC Code", "mandatory": true, "name": "ifsc", "placeholder": "EX: SBIN0070001", "noOfErrors": 1, "validationRuleList": [{"errorMessage": "Please enter correct IFSC", "interactionType": null, "regex": "^[A-Z]{4}0[A-Z0-9]{6}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontSize": 24, "fontStyle": "normal", "borderBottomColor": "#D0D5DD", "width": 100, "backgroundColor": "#FFF", "border": "none"}, "inputBoxStyle": {"backgroundColor": "#FFF", "border": "1px solid #D0D5DD", "borderRadius": 8}, "labelStyle": {"color": "#344054", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal"}, "captionStyle": {"color": "#1D2939", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}, "value": "SBIN0070001"}}], "resultStoreKey": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": null, "taskKey": null, "applicationId": null, "taskId": null, "token": null}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__RESUME_ACTION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Set AutoPay", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "borderRadius": 8, "richText": {"type": "RichTextValue", "style": {"color": "#1D2939", "fontSize": 18, "fontWeight": "bold"}}, "text": "Continue"}}, "buttonOrientation": null, "consent": null, "consentList": null, "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": {"value": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/06/2025/7c73f43c-e31a-4664-a5bf-278dc068e50f.png?q={@quality}", "height": 16, "width": 63}, "title": {"type": "RichTextValue", "style": {"color": "#98A2B3", "fontSize": "12", "fontWeight": "normal", "lineHeight": 18}, "text": "Powered by"}}, "secondaryButtons": null, "sticky": null, "viewType": null}}