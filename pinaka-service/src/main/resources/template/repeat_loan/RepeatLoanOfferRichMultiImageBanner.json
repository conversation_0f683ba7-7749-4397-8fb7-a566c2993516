{"banner": {"value": {"title": {"value": {"type": "RichTextValue", "text": "Choose amount and get", "style": {"color": "#666666", "fontSize": 20, "fontWeight": "bold", "textAlign": "center", "lineHeight": 20}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Instant loan of", "style": {"color": "#212121", "fontSize": 40, "fontWeight": "bold", "textAlign": "center"}}}, "bannerImage": {"value": {"bannerImageType": "DYNAMIC_IMAGE", "image": {"type": "ImageValue", "width": 10, "height": 10, "source": "https://static-assets-web.flixcart.com/fk-p-linchpin-web/sm-personal-loan/images/PreApprovedAmountV1.svg"}, "keyValueList": {"value": {"type": "ListKeyValue", "values": [{"key": "preApprovedText", "value": "Pre approved for you"}, {"key": "approvedLimitAmount", "value": "₹5,00,000"}]}}}}, "images": [{"title": {"type": "RichTextValue", "text": "No income documents", "style": {"color": "#4A4C4F", "fontSize": 10, "lineHeight": 14, "fontWeight": "bold", "textAlign": "center"}}, "image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/19/04/2024/59e94e5b-fe30-4ecb-87f0-cb56f746930d.png?q={@quality}", "height": 40, "type": "ImageValue", "width": 40}, "type": "ImageTextValue"}, {"title": {"type": "RichTextValue", "text": "Get approved in 30 seconds", "style": {"color": "#4A4C4F", "fontSize": 10, "lineHeight": 14, "fontWeight": "bold", "textAlign": "center"}}, "image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/23/04/2024/7b08c68b-73e6-490a-8d9b-5eeaea953edd.png?q={@quality}", "height": 40, "type": "ImageValue", "width": 40}, "type": "ImageTextValue"}, {"title": {"type": "RichTextValue", "text": "Affordable interest rates", "style": {"color": "#4A4C4F", "fontSize": 10, "lineHeight": 14, "fontWeight": "bold", "textAlign": "center"}}, "image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/23/04/2024/6ea56116-0460-4bf5-a9b6-e75a9a5bc550.png?q={@quality}", "height": 40, "type": "ImageValue", "width": 40}, "type": "ImageTextValue"}], "type": "RichMultiImageBannerValue"}}}