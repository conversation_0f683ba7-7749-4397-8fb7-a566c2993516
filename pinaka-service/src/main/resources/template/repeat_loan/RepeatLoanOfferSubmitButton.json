{"submitButton": {"type": "SubmitButtonValue", "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "loaderContent": {"type": "ANNOUNCEMENT_V2", "content": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/04/2023/89ec64b7-c62a-4d92-ae9c-3686d9db4ad1.png?q={@quality}", "height": 185, "width": 262, "aspectRatio": "262:185", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Your application details are being processed...", "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "textAlign": "center", "lineHeight": 30}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Do not press back or switch apps.", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 22}}}, "type": "AnnouncementV2Value"}}}, "params": {"utmSource": "REPEAT_LOAN_LANDING_PAGE_BANNER"}, "requiredPermissionType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__STATUS_ACTION", "url": "/api/fpg/1/action/view", "validationMeta": null, "widgetTracking": null}, "metaData": null, "rcType": null, "tracking": {"buttonName": "SECOND_LOAN_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Continue Application"}}, "secondaryButtons": [{"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "", "taskKey": "", "applicationId": "", "taskId": "", "token": ""}, "requiredPermissionType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__STATUS_ACTION", "url": "/api/fpg/1/action/view", "validationMeta": null, "widgetTracking": null}, "metaData": null, "rcType": null, "tracking": {"buttonName": "PAST_LOAN_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "View loans", "buttonColor": "#FFFFFF", "borderColor": "transparent", "buttonTextColor": "var(--primary)"}}]}}