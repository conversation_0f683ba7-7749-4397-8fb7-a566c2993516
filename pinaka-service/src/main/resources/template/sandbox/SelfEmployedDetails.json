{"formId": "WORK_DETAILS", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "AutoSuggestFormFieldValue", "action": null, "data": [], "disabled": false, "formFieldType": "AUTO_SUGGEST_BOX", "label": "Choose your Industry.", "mandatory": true, "name": "industryName", "offline": false}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "data": [], "label": "Company name.", "mandatory": true, "name": "organization", "offline": false, "replaceInputList": [{"regex": "^[a-zA-Z0-9]+( [a-zA-Z0-9]+)* ?$", "replaceRegex": [{"regex": "^\\s", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PriceTextBoxFormFieldValueV0", "disabled": false, "formFieldType": "PRICE_TEXT_BOX_V0", "label": "Annual turnover", "mandatory": true, "name": "annualTurnOver", "placeholder": "Eg. 10,00,000", "autoCapitalize": "none", "inputType": "NUMERIC"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PriceTextBoxFormFieldValueV0", "disabled": false, "formFieldType": "PRICE_TEXT_BOX_V0", "label": "Monthly Income", "mandatory": true, "name": "income", "placeholder": "Eg. 100,000", "autoCapitalize": "none", "inputType": "NUMERIC"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "disabled": false, "formFieldType": "MULTI_DROPDOWN", "label": "Mode of income", "mandatory": true, "name": "incomeSource", "options": [{"id": "CHEQUE", "title": "CHEQUE", "type": "PLAIN_TEXT"}, {"id": "CASH", "title": "CASH", "type": "PLAIN_TEXT"}, {"id": "ONLINE", "title": "ONLINE", "type": "PLAIN_TEXT"}], "displayType": "DROPDOWN", "stackType": "HORIZONTAL"}}], "submitButton": {"type": "SubmitButtonValue", "consentList": [{"consentType": "CHECKBOX", "consentId": "${consentIdMFI}", "consentFor": "${consentForMFI}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentMFI}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}, {"consentType": "CHECKBOX", "consentId": "${consentIdNonPEP}", "consentFor": "${consentForNonPEP}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentNonPEP}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}, {"consentType": "CHECKBOX", "consentId": "${consentIdCKYC}", "consentFor": "${consentForCKYC}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentCKYC}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}], "button": {"action": {"url": "/api/sm/1/application/form", "encryption": {}, "params": {}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next"}}}, "resultStoreKey": null, "subTitle": null, "title": null}