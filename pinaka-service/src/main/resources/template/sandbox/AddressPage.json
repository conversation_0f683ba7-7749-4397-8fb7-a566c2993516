{"formId": "ADDRESS_DETAILS", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Pincode*", "mandatory": true, "name": "pincode", "placeholder": "Eg. 560034", "inputType": "NUMERIC", "maxCharacters": 6, "minCharacters": 6, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid pincode", "validateOnSubmit": false, "regex": "^[1-9]{1}[0-9]{5}$"}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "House no.,Building name*", "mandatory": true, "name": "houseNumber", "placeholder": "Eg. 97, <PERSON><PERSON><PERSON>", "autoCapitalize": "characters", "replaceInputList": [{"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "^[\\s\\W]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Road name, Area, Colony*", "mandatory": true, "name": "area", "placeholder": "Eg. 3rd Block Koramangala", "replaceInputList": [{"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "^[\\s\\W]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"name": "city", "label": "City*", "type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "mandatory": true, "placeholder": "Eg. Bengal<PERSON>"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"name": "state", "label": "State*", "type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "mandatory": true, "placeholder": "Eg. Karnataka"}}], "submitButton": {"type": "SubmitButtonValue", "button": {"action": {"url": "/api/sm/1/application/form", "encryption": {}, "params": {}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next"}}}, "resultStoreKey": null, "subTitle": null, "title": null}