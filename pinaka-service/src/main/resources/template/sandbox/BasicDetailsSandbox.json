{"formId": "BASIC_DETAILS", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "First name and Middle name", "mandatory": true, "name": "firstName", "placeholder": " ", "autoCapitalize": "characters", "replaceInput": {"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}, "replaceInputList": [{"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "^\\s", "replaceValue": ""}, {"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Last name", "mandatory": true, "name": "lastName", "placeholder": " ", "autoCapitalize": "characters", "replaceInput": {"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}, "replaceInputList": [{"regex": "^\\s?[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}, {"regex": "^\\s$", "replaceRegex": [{"regex": "^\\s+", "replaceValue": ""}]}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DateFormFieldValue", "disabled": false, "formFieldType": "DATE", "label": "Date of Birth", "subText": " ", "mandatory": true, "name": "dob"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "PAN Number", "mandatory": true, "name": "pan", "placeholder": "enter your PAN", "autoCapitalize": "characters", "maxCharacters": 10, "minCharacters": 10, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter correct PAN", "validateOnSubmit": false, "regex": "^[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}$"}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "disabled": false, "formFieldType": "MULTI_DROPDOWN", "label": "Employment Type", "mandatory": true, "name": "employmentType", "options": [{"id": "Salaried", "title": "Salaried", "type": "PLAIN_TEXT"}, {"id": "SelfEmployed", "title": "Self Employed", "type": "PLAIN_TEXT"}], "displayType": "DROPDOWN", "stackType": "HORIZONTAL"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "defaultValue": "M", "disabled": false, "formFieldType": "DROPDOWN", "label": "Gender", "mandatory": true, "name": "gender", "options": [{"id": "M", "title": "Male", "type": "PLAIN_TEXT"}, {"id": "F", "title": "Female", "type": "PLAIN_TEXT"}, {"id": "O", "title": "Others", "type": "PLAIN_TEXT"}], "displayType": "DROPDOWN", "stackType": "HORIZONTAL"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Pincode", "mandatory": true, "name": "pincode", "placeholder": "EX: 560103", "autoCapitalize": "none", "inputType": "PHONE_PAD", "maxCharacters": 6, "minCharacters": 6, "replaceInputList": [{"regex": "^[0-9]*$", "replaceRegex": [{"regex": "[^0-9]", "replaceValue": ""}]}], "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid pincode", "validateOnSubmit": false, "regex": "^[1-9]{1}[0-9]{5}$"}, {"ruleType": "TRIGGER_ACTION", "errorMessage": "Invalid pincode", "validateOnSubmit": false, "action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/6/pincode/existence", "validationMeta": null, "widgetTracking": null}}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Email", "mandatory": true, "name": "email", "placeholder": "enter full email ID", "autoCapitalize": "none", "inputType": "EMAIL_ADDRESS", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter correct Email", "validateOnSubmit": false, "regex": "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,6})$"}], "warningValidation": {"type": "emailHostCheck", "condition": ["gmail.co.za", "gmail.com", "gmail.com.au", "gmail.com.br", "gmail.ru", "hotmail.be", "hotmail.ca", "hotmail.ch", "hotmail.co", "hotmail.co.il", "hotmail.co.jp", "hotmail.co.nz", "hotmail.co.uk", "hotmail.co.za", "hotmail.com", "hotmail.com.ar", "hotmail.com.au", "hotmail.com.br", "hotmail.com.mx", "hotmail.com.tr", "hotmail.de", "hotmail.es", "hotmail.fi", "hotmail.fr", "hotmail.it", "hotmail.kg", "hotmail.kz", "hotmail.my", "hotmail.nl", "hotmail.ro", "hotmail.roor", "hotmail.ru", "rediff.com", "rediffmail.com", "rediffmailpro.com", "yahoo.ae", "yahoo.at", "yahoo.be", "yahoo.ca", "yahoo.ch", "yahoo.cn", "yahoo.co", "yahoo.co.id", "yahoo.co.il", "yahoo.co.in", "yahoo.co.jp", "yahoo.co.kr", "yahoo.co.nz", "yahoo.co.th", "yahoo.co.uk", "yahoo.co.za", "yahoo.com", "yahoo.com.ar", "yahoo.com.au", "yahoo.com.br", "yahoo.com.cn", "yahoo.com.co", "yahoo.com.hk", "yahoo.com.is", "yahoo.com.mx", "yahoo.com.my", "yahoo.com.ph", "yahoo.com.ru", "yahoo.com.sg", "yahoo.com.tr", "yahoo.com.tw", "yahoo.com.vn", "yahoo.cz", "yahoo.de", "yahoo.dk", "yahoo.es", "yahoo.fi", "yahoo.fr", "yahoo.gr", "yahoo.hu", "yahoo.ie", "yahoo.in", "yahoo.it", "yahoo.jp", "yahoo.net", "yahoo.nl", "yahoo.no", "yahoo.pl", "yahoo.pt", "yahoo.ro", "yahoo.ru", "yahoo.se", "yahoofs.com", "yahoomail.com", "aol.com", "msn.com", "wanadoo.fr", "orange.fr", "comcast.net", "live.com", "free.fr", "gmx.de", "web.de", "yandex.ru", "ymail.com", "libero.it", "outlook.com", "uol.com.br", "bol.com.br", "mail.ru", "cox.net", "sbcglobal.net", "sfr.fr", "live.fr", "verizon.net", "live.co.uk", "googlemail.com", "ig.com.br", "live.nl", "bigpond.com", "terra.com.br", "neuf.fr", "alice.it", "rocketmail.com", "att.net", "laposte.net", "facebook.com", "bellsouth.net", "charter.net", "rambler.ru", "tiscali.it", "shaw.ca", "sky.com", "earthlink.net", "optonline.net", "freenet.de", "t-online.de", "aliceadsl.fr", "virgilio.it", "home.nl", "qq.com", "telenet.be", "me.com", "tiscali.co.uk", "voila.fr", "gmx.net", "mail.com", "planet.nl", "tin.it", "live.it", "ntlworld.com", "arcor.de", "frontiernet.net", "hetnet.nl", "live.com.au", "zonnet.nl", "club-internet.fr", "juno.com", "optusnet.com.au", "blueyonder.co.uk", "bluewin.ch", "skynet.be", "sympatico.ca", "windstream.net", "mac.com", "centurytel.net", "chello.nl", "live.ca", "aim.com", "bigpond.net.au"], "warningMessage": "Please recheck correctness of your email address"}}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "MultiDropdownFormFieldValue", "formFieldType": "MULTI_DROPDOWN", "label": "Purpose of <PERSON>an", "mandatory": true, "name": "loanPurpose", "options": [{"id": "PERSONAL", "title": "Personal"}, {"id": "TRAVEL", "title": "Travel"}, {"id": "HOME_REPAIR", "title": "Home - repair"}, {"id": "MARRIAGE", "title": "Marriage"}, {"id": "MEDICAL", "title": "Medical"}, {"id": "EDUCATION", "title": "Education"}, {"id": "CONSUMER_DURABLES", "title": "Consumer - durables"}], "viewType": "DROPDOWN"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PriceTextBoxFormFieldValueV0", "disabled": false, "formFieldType": "PRICE_TEXT_BOX_V0", "label": "Net Monthly Income", "mandatory": true, "name": "income", "placeholder": "Eg. 100,000", "autoCapitalize": "none", "inputType": "NUMERIC"}}], "submitButton": {"type": "SubmitButtonValue", "consent": {"consentType": "CHECKBOX", "consentId": "${consentId}", "consentFor": "${consentFor}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consent}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}, "button": {"action": {"url": "/api/sm/1/application/form", "encryption": {}, "params": {}, "loaderContent": {"type": "ANNOUNCEMENT_V2", "content": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/04/04/2024/16390b7e-85b7-4d0a-b11c-f3c0e375ea04.png?q={@quality}", "height": 185, "width": 262, "aspectRatio": "262:185", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Your application details are being processed...", "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "textAlign": "center", "lineHeight": 30}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Do not press back or switch apps.", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 22}}}, "type": "AnnouncementV2Value"}}}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next"}}}, "resultStoreKey": null, "subTitle": null, "title": null}