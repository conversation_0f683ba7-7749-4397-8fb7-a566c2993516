{"data": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/13/03/2024/e039b614-a87b-4e68-89d2-621c29e67751.png?q={@quality}", "height": 134, "width": 132, "aspectRatio": "132:134", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "₹5,50,000", "style": {"color": "#2A55E5", "fontSize": 48, "fontWeight": "bold", "textAlign": "center", "lineHeight": 60}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Your approved limit", "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "textAlign": "center", "lineHeight": 30}}}, "richButton": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": {"keyId": "sumo-end-key", "publicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmCrV0vRs4d+9pufICdn/QVYfmu0+4ZHqh+qRxLXxPqOUwCMJnVq2yT27JYoEOAe8xPXyQVIR6NM8+XojcO/rKZxPOV7iVfB+mcoZ06EaMp/S4NPJs+TjbEnSoAMJ7OL5PPQKmFGiKwAYQLrcAj08Fu0AlLDBUXrtg2i/W/occxZQnJTq0dUPoRCRb6YYD7DwezWGrHaGZUffzzYvDaBB1e0tBgpPF8HZ7K359xT6w6haUuYw9bnDu4FqW3rv82upU3dexi11txOUbuE4Xwg/4uVb0kmsgCDRVwafKx090cjJWbuHJ3ZcMzekJc/ueuqUr1dx7PJX1ClMhDhcMnRs4QIDAQAB"}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__FORM_INTERACTION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "value": {"type": "RichTextButtonValue", "title": "Choose loan amount"}}, "type": "AnnouncementV2Value"}}}