{"data": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/04/2024/13f82c38-3fa6-4faf-82fe-9401e0e3b734.png?q={@quality}", "height": 68, "width": 68, "aspectRatio": "132:134", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "We need more info about you", "style": {"color": "#000000", "fontSize": 28, "fontWeight": "bold", "textAlign": "center", "lineHeight": 50}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Just one more step to find ideal offer for you", "style": {"color": "#5C5F63", "fontSize": 18, "fontWeight": "bold", "textAlign": "center", "lineHeight": 30}}}, "richButton": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": {"keyId": "sumo-end-key", "publicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmCrV0vRs4d+9pufICdn/QVYfmu0+4ZHqh+qRxLXxPqOUwCMJnVq2yT27JYoEOAe8xPXyQVIR6NM8+XojcO/rKZxPOV7iVfB+mcoZ06EaMp/S4NPJs+TjbEnSoAMJ7OL5PPQKmFGiKwAYQLrcAj08Fu0AlLDBUXrtg2i/W/occxZQnJTq0dUPoRCRb6YYD7DwezWGrHaGZUffzzYvDaBB1e0tBgpPF8HZ7K359xT6w6haUuYw9bnDu4FqW3rv82upU3dexi11txOUbuE4Xwg/4uVb0kmsgCDRVwafKx090cjJWbuHJ3ZcMzekJc/ueuqUr1dx7PJX1ClMhDhcMnRs4QIDAQAB"}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__FORM_INTERACTION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "value": {"type": "RichTextButtonValue", "title": "Continue"}}, "type": "AnnouncementV2Value"}}}