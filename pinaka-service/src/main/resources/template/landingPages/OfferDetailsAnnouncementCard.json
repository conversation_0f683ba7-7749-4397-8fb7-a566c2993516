{"announcementDetails": {"title": {"type": "RichTextValue", "style": {"color": "#475467", "fontSize": "20", "fontWeight": "bold", "lineHeight": 30}, "text": "Don’t like the offer you are seeing? Don’t worry"}, "subTitle": {"type": "RichTextValue", "style": {"color": "#475467", "fontSize": "16", "fontWeight": "medium", "lineHeight": 16}, "text": "From our multiple partners"}, "lenderLogos": [{"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/7229ac68-95d2-4e3b-a1ad-158949b08f65.png?q={@quality}", "height": 28, "width": 31}, {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/e8512fe0-801f-4d0e-af63-32ac537f70ea.png?q={@quality}", "height": 25, "width": 28}, {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/7885680c-7968-4bbd-be59-284974778df7.png?q={@quality}", "height": 28, "width": 34}, {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/bea7652b-a853-479a-b782-62f3f2b2af10.png?q={@quality}", "height": 28, "width": 37}, {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/16f3f972-8a83-4b11-a414-2d069491dc7f.png?q={@quality}", "height": 28, "width": 43}, {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/e14cc817-ee87-42b9-9b35-999dc587dc07.png?q={@quality}", "height": 24, "width": 37}], "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "screenType": "", "tracking": {}, "triggerExtraStandardEvents": null, "url": "/api/sm/1/application/try-another-lender", "type": "CALM__TRY_ANOTHER_LENDER", "validationMeta": null, "widgetTracking": null}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "title": "Apply to another lender"}}}, "refreshScoreAction": null}