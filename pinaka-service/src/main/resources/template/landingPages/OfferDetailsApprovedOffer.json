{"offerDetails": {"image": {"type": "ImageValue", "alternateText": "Offer Image", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/27/08/2024/8c99e0e1-de0e-460f-9838-b3c20fed1839.png?q={@quality}", "height": 98, "width": 98}, "recommendedText": {"type": "RichTextValue", "style": {"color": "#FFFFFF", "fontSize": "10", "fontWeight": "bold", "letterSpacing": 2, "lineHeight": 16}, "containerStyle": {"backgroundColor": "#2BAB3F", "paddingBottom": 4, "paddingTop": 4, "paddingLeft": 8, "paddingRight": 8, "borderRadius": 12, "marginTop": "8", "width": "max-content"}, "text": "Recommended"}, "title": [{"type": "RichTextValue", "style": {"color": "#475467", "fontSize": "20", "fontWeight": "medium", "lineHeight": 30}, "text": "Complete"}, {"type": "RichTextValue", "style": {"color": "#1D2939", "fontSize": "24", "fontWeight": "bold", "lineHeight": 30}, "text": "KYC & Bank details"}, {"type": "RichTextValue", "style": {"color": "#475467", "fontSize": "20", "fontWeight": "medium", "lineHeight": 30}, "text": "to get cash of"}], "offerAmount": [{"type": "RichTextValue", "style": {"color": "#475467", "fontSize": "24", "fontWeight": "normal"}, "text": "₹"}, {"type": "RichTextValue", "style": {"color": "#2A55E5", "fontSize": "44", "fontWeight": "bold", "lineHeight": 40}, "text": "5,00,000"}], "repaymentDetails": [{"title": {"type": "RichTextValue", "style": {"color": "#666666", "fontSize": "12", "fontWeight": "medium"}, "text": "EMI"}, "value": {"type": "RichTextValue", "style": {"color": "#212121", "fontSize": "14", "fontWeight": "medium"}, "text": "₹ 6,500"}}, {"title": {"type": "RichTextValue", "style": {"color": "#666666", "fontSize": "12", "fontWeight": "medium"}, "text": "TENURE"}, "value": {"type": "RichTextValue", "style": {"color": "#212121", "fontSize": "14", "fontWeight": "medium"}, "text": "36 months"}}, {"title": {"type": "RichTextValue", "style": {"color": "#666666", "fontSize": "12", "fontWeight": "medium"}, "text": "INTEREST RATE"}, "value": {"type": "RichTextValue", "style": {"color": "#212121", "fontSize": "14", "fontWeight": "medium"}, "text": "7.39%"}}], "button": {"action": {"constraints": null, "customTrackingEvents": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "screenType": "", "tracking": {}, "triggerExtraStandardEvents": null, "type": "CALM__STATUS_ACTION", "url": "/api/fpg/1/action/view", "validationMeta": null, "widgetTracking": null}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "title": "Get cash in my account"}}, "lenderLogo": {"value": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/23077cea-00d8-45de-a53c-252f852fd4dc.png?q={@quality}", "height": 16, "width": 63}, "title": {"type": "RichTextValue", "style": {"color": "#98A2B3", "fontSize": "12", "fontWeight": "normal", "lineHeight": 18}, "text": "Approved by"}}}, "refreshScoreAction": null}