{"items": [{"title": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 32, "fontWeight": "bold", "lineHeight": 32}, "containerStyle": {"marginTop": 32, "marginBottom": 8}, "text": "You’re almost there", "textColor": "#4D43FE"}, "subTitle": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 16, "fontWeight": "medium", "lineHeight": 16, "marginTop": 8}, "text": "Only 2 steps away to get cash in your account. Let's do your kyc next", "textColor": "#475467"}, "image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/19/05/2025/f3b72629-030c-41b7-b42e-4abe22ff0236.png?q={@quality}", "height": 200, "type": "ImageValue", "width": 238, "aspectRatio": 1.19}, "type": "ImageTextValue"}], "footerNote": null, "pollingContext": {"action": {"params": {}, "screenType": "multiWidgetPage", "tracking": {}, "type": "POLLING", "url": "/hasStateChanged"}, "interval": 2000, "threshold": 10000}, "progressBarMetaData": {"progressBarAColor": null, "progressBarBColor": null}, "fallback": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/10/2023/554b30c9-4309-4815-ac8b-2ccb42c51a4a.png?q={@quality}", "height": 192, "width": 192, "aspectRatio": "1:1", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Something went wrong", "style": {"color": "#000000", "fontSize": 18, "fontWeight": "bold", "textAlign": "center", "lineHeight": 28}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Our team is working hard to fix it, please check back again later", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 22}}}, "richButton": {"action": {"type": "MERCHANT_HOMEPAGE", "params": {}, "tracking": {}, "loginType": "LOGIN_NOT_REQUIRED"}, "value": {"type": "RichButtonValue", "title": "Okay, got it"}}, "type": "AnnouncementV2Value"}}