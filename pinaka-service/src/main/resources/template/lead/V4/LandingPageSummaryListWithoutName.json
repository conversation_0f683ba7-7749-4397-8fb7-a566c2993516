{"clientPowered": false, "formId": "LV4_LANDING_PAGE_SUMMARY_LIST_WITHOUT_NAME", "colGap": null, "containerStyle": null, "failCard": null, "gap": 0, "listTitle": null, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 28, "fontWeight": "normal", "lineHeight": 32}, "text": "Get instant loan of up to", "textColor": "#98A2B3"}}, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/25/07/2025/b1b39d43-bc7c-4a8f-848e-f0b65147c444.png?q={@quality}", "height": 92, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/25/07/2025/b1b39d43-bc7c-4a8f-848e-f0b65147c444.png?q={@quality}", "width": 92}, "iconBackgroundColor": null, "iconSize": null, "orientation": "PORTRAIT", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "style": {"alignItems": "flex-start", "borderRadius": 6, "padding": 8, "paddingBottom": 0}, "superTitle": null, "tagValue": null, "textContainerStyle": {"alignItems": "flex-start", "display": "flex", "flexDirection": "column"}, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 28, "fontWeight": "normal", "lineHeight": 32}, "text": "Wohoo!", "textColor": "#1D2939"}}, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 28, "fontWeight": "normal", "lineHeight": 32}, "text": "", "textColor": "#98A2B3"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "style": {"alignItems": "baseline", "borderRadius": 6, "padding": 8, "paddingTop": 0}, "superTitle": null, "tagValue": null, "textContainerStyle": {}, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "specialTextsMapper": {"coloredAmount": {"color": "#4D43FE"}, "*": {"color": "#C7CDFF", "fontFamily": "<PERSON><PERSON>", "fontSize": 24, "fontWeight": "bold", "marginTop": -15}}, "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 52, "fontWeight": "bold", "lineHeight": 60}, "text": "₹1,00,000 *", "textColor": "#4D43FE"}}, "viewType": "PRIMITIVE_CARD"}}], "subWidget": null}