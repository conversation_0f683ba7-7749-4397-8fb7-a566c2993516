{"clientPowered": false, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": null, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "#F9FAFB", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-29791372", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": null, "pageSubtitle": "", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {}}}, "pageMeta": {"baseImpressionId": "25b04894-2a9b-4b55-b6aa-6343b6654005", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#F9FAFB", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 0, "widgetWidth": 12}, "dataId": "-96053503", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "LV4_LANDING_PAGE_NAME_PAGE", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "title": {"text": " ", "icon": {"type": "ImageValue", "alternateText": "Sample PAN image", "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/00d3c9eb-4e80-4c6b-95f7-6d9c9fdbbcad.png?q={@quality}", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/00d3c9eb-4e80-4c6b-95f7-6d9c9fdbbcad.png?q={@quality}", "height": 98, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/00d3c9eb-4e80-4c6b-95f7-6d9c9fdbbcad.png?q={@quality}", "width": 312}}, "subTitle": {"text": "Make sure your name\nis as per PAN", "style": {"fontFamily": "<PERSON><PERSON>", "color": "#4D43FE", "fontSize": 20, "fontWeight": "bold", "width": 100}}, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {}, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Name", "mandatory": true, "name": "fullName", "noOfErrors": 1, "placeholder": "Enter your first name", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name cannot include numbers or special characters", "interactionType": null, "regex": "^[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name must be of 3 characters", "interactionType": null, "regex": "^[A-Za-z\\s]{3,}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name exceeds 26 characters", "interactionType": null, "regex": "^[A-Za-z\\s]{0,26}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Remove extra spaces in your name", "interactionType": null, "regex": "^(?!.*\\s{2,})[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Re-check your name – repeated letters may be a mistake", "interactionType": null, "regex": "^(?!.*(.)\\1{2,})[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal"}}, "value": "", "tracking": {"sendImpression": true, "fieldName": "fullName", "sendEdits": true, "prefill": false, "prefilledValue": "", "sendInlineError": true}}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValue", "disabled": true, "formFieldType": "TEXT_BOX", "label": "Phone no.", "mandatory": true, "name": "phoneNumber", "placeholder": "Enter mobile number", "inputType": "PHONE_PAD", "value": "", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid mobile number", "validateOnSubmit": false, "regex": "^[6-9]{1}[0-9]{9}$"}], "textboxStyles": {"captionStyle": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal"}, "containerStyle": {}, "inputBoxStyle": {"background-color": "#EAECF0"}, "inputStyle": {"background-color": "#EAECF0", "border": "none", "border-radius": 8, "box-shadow": "none", "color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal"}, "labelStyle": {"color": "#98A2B3", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal"}}, "tracking": {"sendImpression": true, "fieldName": "phoneNumber", "sendEdits": true, "prefill": false, "prefilledValue": "", "sendInlineError": true}}}], "resultStoreKey": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"tracking": {"fieldName": "LV4_LANDING_PAGE_NAME_PAGE_SUBMIT_BUTTON"}, "action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "AahvDs6p4C9TKcPC", "taskKey": "leadV4LandingPage", "applicationId": "APP2507021244032946916563947828235621639", "taskId": "Rc8NX9S5MdQHFsnS", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmCPLnJ05MRfKKYcqLnUmbdI2EQbOXCyFDjIxrBqWfEPhea+0Og8emCaAJ7UJuLpgrabhc5eNzDSYpzVIZDHeIS0"}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {"fieldName": "LV4_LANDING_PAGE_NAME_PAGE_SUBMIT_BUTTON_CLICK"}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Confirm", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18}}, "buttonOrientation": null, "consent": {"checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "${consent}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "${consentFor}", "consentId": "${consentId}", "consentType": "CHECKBOX"}, "consentList": null, "containerStyle": {"background": "#FFFFFF", "padding": 24, "width": "calc(100% - 48px)"}, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": "NO_SHADOW"}}, "tracking": {"widgetName": "LV4_LANDING_PAGE_NAME_PAGE"}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {"widgetName": "LV4_LANDING_PAGE_UNLOCK_OFFER_SUBMIT_BUTTON_CLICK"}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": "6/pl/apply-now", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV4_LANDING_PAGE_UNLOCK_OFFER_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18, "title": "Apply now"}}, "buttonOrientation": null, "consent": null, "consentList": null, "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}}