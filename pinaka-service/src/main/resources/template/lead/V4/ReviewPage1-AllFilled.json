{"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formGroups": [{"formGroupContainerStyle": {"background": "linear-gradient(to left, #EBEFFF, #CBCEFF)", "border": "2px solid white", "borderBottomLeftRadius": 16, "borderBottomRightRadius": 16, "borderTopLeftRadius": 16, "borderTopRightRadius": 16, "marginBottom": 16}, "formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"borderTopLeftRadius": 16, "borderTopRightRadius": 16}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "PAN (Permanent Account Number)", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "panNumber"}, "style": {}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "DOB", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "dob"}, "style": {"flex": 1}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "M", "textColor": "#1D2939", "title": "M"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "Gender", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "displayMapper": {"M": "Male", "F": "Female", "O": "Others"}, "displayType": "KEY_VALUE_MAPPER", "formFieldType": "RICH_CARD", "mandatory": false, "name": "gender", "tracking": {}}, "style": {}}], "formGroupDataStyle": {"backgroundColor": "#FFF", "borderBottomColor": "#C7D7FE", "borderBottomLeftRadius": 16, "borderBottomRightRadius": 16, "borderTopLeftRadius": 16, "borderTopRightRadius": 16, "borderWidth": "4", "marginLeft": 4, "marginRight": 4, "marginBottom": 4, "padding": 24}, "formGroupHeader": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": {"type": "ImageValue", "alternateText": "Personal Details image icon", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/06/2025/db326f7e-ffd8-4753-bd03-745ac6984dcd.png?q={@quality}", "height": 36, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/06/2025/db326f7e-ffd8-4753-bd03-745ac6984dcd.png?q={@quality}", "width": 36}, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-79371862", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "540837788", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "panNumber", "noOfErrors": 1, "label": "PAN Number", "placeholder": "eg. **********", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter correct PAN", "interactionType": null, "validateOnSubmit": false, "regex": "^[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}$"}], "value": "", "tracking": null, "suffix": null, "minCharacters": 10, "maxCharacters": 10, "autoCapitalize": "characters"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "DateFormFieldValueV0", "formFieldType": "DATE_V0", "name": "dob", "noOfErrors": 1, "label": "DOB", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "value": "11/04/1998", "tracking": null, "minValue": "1965-03-18T14:22:41.155+05:30", "maxValue": "2007-03-18T14:22:41.155+05:30", "suffix": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "formFieldType": "DROPDOWN", "name": "gender", "label": "Gender", "mandatory": true, "disabled": false, "containerStyle": {"display": "flex", "justifyContent": "space-around", "gap": 8}, "outerContainerStyle": {"fontSize": 14}, "value": {"id": "M", "title": "Male", "type": "PLAIN_TEXT", "fullWidthPress": false}, "tracking": null, "options": [{"id": "M", "title": "Male", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "F", "title": "Female", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "O", "title": "Others", "type": "PLAIN_TEXT", "fullWidthPress": false}], "selectedOption": {"id": "M", "title": "Male", "type": "PLAIN_TEXT", "fullWidthPress": false}, "displayType": "DROPDOWN", "stackType": "HORIZONTAL", "inputType": "CUSTOM_RADIO"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {}, "listTitle": null, "formId": "LV4_ALL_DETAILS_FILLED_PERSONAL_DETAILS_BOTTOM_SHEET_FORM_ID", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Personal details", "showClipboard": false, "containerStyle": {"marginBottom": 20}, "style": {"color": "#4D43FE", "fontSize": 17, "lineHeight": 24, "fontWeight": "bold"}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "LV4_ALL_DETAILS_FILLED_PERSONAL_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18, "borderColor": "#B5EF85"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {}, "validationMeta": null, "tracking": {"widgetName": "LV4_ALL_DETAILS_FILLED_PERSONAL_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON_CLICK"}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"background": "#FFFFFF", "padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "Edit details button", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 24, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 24}, "style": {"padding": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"backgroundColor": "", "borderTopLeftRadius": 16, "borderTopRightRadius": 16, "left": 0, "paddingBottom": 12, "paddingLeft": 16, "paddingRight": 16, "paddingTop": 12, "position": "sticky"}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold"}, "text": "Personal details", "textColor": "#1D2939"}}, "viewType": null}, "formGroupName": "LV4 Page 2 Personal Details Form Group"}, {"formGroupContainerStyle": {"background": "linear-gradient(to left, #FFF1FC, #f1b9f1)", "border": "2px solid white", "borderBottomLeftRadius": 16, "borderBottomRightRadius": 16, "borderTopLeftRadius": 16, "borderTopRightRadius": 16, "marginBottom": 16}, "formGroupData": [{"data": {"type": "DropdownFormFieldValueV0", "dehydratedOption": {"description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontWeight": "normal"}, "text": "${houseNumber}, ${area}, ${pincodeDetails__city}, ${pincodeDetails__state} ${pincodeDetails__pincode} India", "textColor": "#667085", "title": "${houseNumber}, ${area}, ${pincodeDetails__city}, ${pincodeDetails__state} ${pincodeDetails__pincode} India"}}, "fullWidthPress": false, "id": "${ID}", "title": "Address ${INDEX}", "titleStyle": {"color": "#1D2939", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "type": "PLAIN_TEXT"}, "disabled": false, "displayType": "DROPDOWN", "formFieldType": "DROPDOWN_V0", "initialOptionsDataMap": [], "initialValue": {"address": ""}, "mandatory": true, "name": "address", "options": [], "outerContainerStyle": {"background": "#FFF", "borderBottomLeftRadius": 16, "borderBottomRightRadius": 16, "borderTopLeftRadius": 16, "borderTopRightRadius": 16, "display": "block", "width": "100%", "minWidth": 212, "padding": 24, "paddingBottom": 28}, "stackType": "HORIZONTAL", "title": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontWeight": "normal"}, "text": "Communication Address", "textColor": "#1D2939", "title": "Communication Address"}, "titleDescContainerStyle": {"alignItems": "baseline"}}, "style": {}}], "formGroupDataStyle": {"marginLeft": 4, "marginRight": 4, "marginBottom": 4}, "formGroupHeader": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": {"type": "ImageValue", "alternateText": "Current Address icon", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/06/2025/35381e73-ef8e-48b2-bb75-cb1ea2688d04.png?q={@quality}", "height": 36, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/06/2025/35381e73-ef8e-48b2-bb75-cb1ea2688d04.png?q={@quality}", "width": 36}, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"doNotBroadcastToNestedForm": true, "screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-79371862", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "540837788", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "houseNumber", "noOfErrors": 1, "label": "House/Flat/Floor number", "placeholder": "House/Flat/Floor number", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "value": "", "tracking": {}, "multiline": true, "numberOfLines": 4}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "area", "noOfErrors": 1, "label": "Apartment/Area/Locality", "placeholder": "Apartment/Area/Locality", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "value": "", "tracking": {}, "multiline": true, "numberOfLines": 4}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "CustomFormFieldValue", "formFieldType": "CUSTOM", "name": "pincodeDetails", "noOfErrors": null, "label": null, "subLabel": null, "mandatory": true, "disabled": false, "containerStyle": null, "validationRuleList": null, "dependencyList": null, "outerContainerStyle": null, "inputStyle": null, "value": null, "tracking": null, "grids": null, "customFieldData": {"customFieldType": "PINCODE_V0", "pincode": {"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "pincode", "noOfErrors": 1, "label": "Pin Code", "placeholder": "eg. 560103", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter a valid pin code", "interactionType": null, "validateOnSubmit": false, "regex": "^(?!\\s*$)[1-9]{1}[0-9]{5}$"}], "value": "", "tracking": {}, "inputType": "NUMERIC", "validationRegex": "^[1-9]{1}[0-9]{5}$", "minCharacters": 6, "maxCharacters": 6}, "action": {"screenType": null, "type": "NAVIGATION", "omnitureData": null, "originalUrl": null, "url": "/6/pincode/existence", "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "state": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "state", "label": "State", "placeholder": "", "mandatory": true, "disabled": true, "outerContainerStyle": {"display": "none"}}, "city": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "city", "label": "City", "placeholder": "", "mandatory": true, "disabled": true, "outerContainerStyle": {"display": "none"}}}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {}, "listTitle": null, "formId": "LV4_ALL_DETAILS_FILLED_ADDRESS_DETAILS_BOTTOM_SHEET_FORM_ID", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Add new address", "showClipboard": false, "containerStyle": {"marginBottom": 20}, "style": {"color": "#4D43FE", "fontSize": 17, "lineHeight": 24, "fontWeight": "bold"}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "LV4_ALL_DETAILS_FILLED_ADDRESS_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18, "borderColor": "#B5EF85"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {}, "validationMeta": null, "tracking": {"widgetName": "LV4_ALL_DETAILS_FILLED_ADDRESS_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON_CLICK"}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"background": "#FFFFFF", "padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "Add new Address button", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/b2ebdef2-c87d-47d2-8573-05aa1782edbd.png?q=100", "height": 24, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/b2ebdef2-c87d-47d2-8573-05aa1782edbd.png?q=100", "width": 24}, "style": {"padding": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"borderTopLeftRadius": 12, "borderTopRightRadius": 12, "left": 0, "paddingBottom": 12, "paddingLeft": 16, "paddingRight": 16, "paddingTop": 12, "position": "sticky"}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold"}, "text": "Current Address", "textColor": "#1D2939"}}, "viewType": null}, "formGroupName": "LV4 Page 2 Address Details Form Group"}, {"formGroupContainerStyle": {"background": "linear-gradient(to left, #E7F9E7 , #b5ffde)", "border": "2px solid white", "borderBottomLeftRadius": 16, "borderBottomRightRadius": 16, "borderTopLeftRadius": 16, "borderTopRightRadius": 16}, "formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 18, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "Company name", "textColor": "#667085"}}, "viewType": null}, "dependencyList": [{"depKey": "employmentType", "depValue": {"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\bSalaried\b", "ruleType": "REGEX", "validateOnSubmit": false}}], "disabled": false, "displayType": "SHOW_BY_TITLE", "formFieldType": "RICH_CARD", "initialValue": {"organization": ""}, "mandatory": false, "name": "organization"}, "style": {"width": "100%"}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "bold", "lineHeight": 20}, "text": "Business name", "textColor": "#667085"}}, "viewType": null}, "dependencyList": [{"depKey": "employmentType", "depValue": {"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\bSelfEmployed\b", "ruleType": "REGEX", "validateOnSubmit": false}}], "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "businessName"}, "style": {}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24, "flexBasis": 100}, "text": "", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "bold", "lineHeight": 20}, "text": "Industry type", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "displayType": "SHOW_BY_TITLE", "formFieldType": "RICH_CARD", "mandatory": false, "name": "industryName"}, "style": {}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "Monthly\n income", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "displayType": "CURRENCY", "formFieldType": "RICH_CARD", "mandatory": false, "name": "income"}, "style": {}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "ONLINE", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "How you get\n your income", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "displayMapper": {"ONLINE": "Bank Transfer", "CASH": "Cash", "CHEQUE": "Cheque"}, "displayType": "KEY_VALUE_MAPPER", "formFieldType": "RICH_CARD", "mandatory": false, "name": "incomeSource"}, "style": {}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "Extra monthly income (bonus, rent, interest, etc.)", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "displayType": "CURRENCY", "formFieldType": "RICH_CARD", "mandatory": false, "name": "bonusIncome"}, "style": {}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "Employment Type", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "displayMapper": {"Salaried": "Salaried", "SelfEmployed": "Self Employed"}, "displayType": "KEY_VALUE_MAPPER", "formFieldType": "RICH_CARD", "mandatory": false, "name": "employmentType"}, "style": {}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "PERSONAL", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "Purpose of loan", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "displayMapper": {"PERSONAL": "Personal", "TRAVEL": "Travel", "HOME_REPAIR": "Home - repair", "MARRIAGE": "Marriage", "MEDICAL": "Medical", "EDUCATION": "Education", "CONSUMER_DURABLES": "Consumer - durables"}, "displayType": "KEY_VALUE_MAPPER", "formFieldType": "RICH_CARD", "initialValue": {"loanPurpose": ""}, "mandatory": false, "name": "loanPurpose"}, "style": {}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "Official email", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "email"}, "style": {}}], "formGroupDataStyle": {"backgroundColor": "#FFF", "borderBottomLeftRadius": 16, "borderBottomRightRadius": 16, "borderTopLeftRadius": 16, "borderTopRightRadius": 16, "marginLeft": 4, "marginRight": 4, "marginBottom": 4, "padding": 24}, "formGroupHeader": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": {"type": "ImageValue", "alternateText": "Work Details Icon", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/06/2025/3bb19e48-09e9-4249-be4c-229eb42d761f.png?q={@quality}", "height": 36, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/06/2025/3bb19e48-09e9-4249-be4c-229eb42d761f.png?q={@quality}", "width": 36}, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-79371862", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "540837788", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "email", "noOfErrors": 1, "label": "Official Email", "placeholder": "Enter your email", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter correct Email", "interactionType": null, "validateOnSubmit": false, "regex": "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,6})$"}], "value": "", "tracking": {}, "inputType": "EMAIL_ADDRESS", "warningValidation": {"type": "emailHostCheck", "condition": ["gmail.co.za", "gmail.com", "gmail.com.au", "gmail.com.br", "gmail.ru", "hotmail.be", "hotmail.ca", "hotmail.ch", "hotmail.co", "hotmail.co.il", "hotmail.co.jp", "hotmail.co.nz", "hotmail.co.uk", "hotmail.co.za", "hotmail.com", "hotmail.com.ar", "hotmail.com.au", "hotmail.com.br", "hotmail.com.mx", "hotmail.com.tr", "hotmail.de", "hotmail.es", "hotmail.fi", "hotmail.fr", "hotmail.it", "hotmail.kg", "hotmail.kz", "hotmail.my", "hotmail.nl", "hotmail.ro", "hotmail.roor", "hotmail.ru", "rediff.com", "rediffmail.com", "rediffmailpro.com", "yahoo.ae", "yahoo.at", "yahoo.be", "yahoo.ca", "yahoo.ch", "yahoo.cn", "yahoo.co", "yahoo.co.id", "yahoo.co.il", "yahoo.co.in", "yahoo.co.jp", "yahoo.co.kr", "yahoo.co.nz", "yahoo.co.th", "yahoo.co.uk", "yahoo.co.za", "yahoo.com", "yahoo.com.ar", "yahoo.com.au", "yahoo.com.br", "yahoo.com.cn", "yahoo.com.co", "yahoo.com.hk", "yahoo.com.is", "yahoo.com.mx", "yahoo.com.my", "yahoo.com.ph", "yahoo.com.ru", "yahoo.com.sg", "yahoo.com.tr", "yahoo.com.tw", "yahoo.com.vn", "yahoo.cz", "yahoo.de", "yahoo.dk", "yahoo.es", "yahoo.fi", "yahoo.fr", "yahoo.gr", "yahoo.hu", "yahoo.ie", "yahoo.in", "yahoo.it", "yahoo.jp", "yahoo.net", "yahoo.nl", "yahoo.no", "yahoo.pl", "yahoo.pt", "yahoo.ro", "yahoo.ru", "yahoo.se", "yahoofs.com", "yahoomail.com", "aol.com", "msn.com", "wanadoo.fr", "orange.fr", "comcast.net", "live.com", "free.fr", "gmx.de", "web.de", "yandex.ru", "ymail.com", "libero.it", "outlook.com", "uol.com.br", "bol.com.br", "mail.ru", "cox.net", "sbcglobal.net", "sfr.fr", "live.fr", "verizon.net", "live.co.uk", "googlemail.com", "ig.com.br", "live.nl", "bigpond.com", "terra.com.br", "neuf.fr", "alice.it", "rocketmail.com", "att.net", "laposte.net", "facebook.com", "bellsouth.net", "charter.net", "rambler.ru", "tiscali.it", "shaw.ca", "sky.com", "earthlink.net", "optonline.net", "freenet.de", "t-online.de", "aliceadsl.fr", "virgilio.it", "home.nl", "qq.com", "telenet.be", "me.com", "tiscali.co.uk", "voila.fr", "gmx.net", "mail.com", "planet.nl", "tin.it", "live.it", "ntlworld.com", "arcor.de", "frontiernet.net", "hetnet.nl", "live.com.au", "zonnet.nl", "club-internet.fr", "juno.com", "optusnet.com.au", "blueyonder.co.uk", "bluewin.ch", "skynet.be", "sympatico.ca", "windstream.net", "mac.com", "centurytel.net", "chello.nl", "live.ca", "aim.com", "bigpond.net.au", "super.money"], "warningMessage": "Please recheck correctness of your email address"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "MultiDropdownFormFieldValue", "formFieldType": "MULTI_DROPDOWN", "name": "loanPurpose", "label": "Purpose of <PERSON>an", "mandatory": true, "disabled": false, "value": "PERSONAL", "viewType": "DROPDOWN", "options": [{"id": "PERSONAL", "title": "Personal"}, {"id": "TRAVEL", "title": "Travel"}, {"id": "HOME_REPAIR", "title": "Home - repair"}, {"id": "MARRIAGE", "title": "Marriage"}, {"id": "MEDICAL", "title": "Medical"}, {"id": "EDUCATION", "title": "Education"}, {"id": "CONSUMER_DURABLES", "title": "Consumer - durables"}], "defaultValue": "PERSONAL"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "MultiDropdownFormFieldValue", "formFieldType": "MULTI_DROPDOWN", "name": "employmentType", "label": "Employment type", "placeholder": "Select", "mandatory": true, "disabled": false, "tracking": {}, "viewType": "DROPDOWN", "options": [{"id": "Salaried", "title": "Salari<PERSON> (Job)", "subtitle": "You get a monthly salary"}, {"id": "SelfEmployed", "title": "Self Employed (Own Business)", "subtitle": "You have a business or shop"}], "defaultValue": ""}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"trackingData": null, "value": {"type": "AutoSuggestFormFieldValue", "formFieldType": "AUTO_SUGGEST_BOX", "name": "industryName", "label": "Industry type", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "dependencyList": [{"depKey": "employmentType", "depValue": {"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\bSelfEmployed\\b"}}], "tracking": {}, "data": [{"id": "1", "title": "ACTIVITIES OF TRADE UNIONS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "2", "title": "AIR TRAVEL AGENTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "3", "title": "AVIATION", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "4", "title": "BUILDERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "5", "title": "CABLE OPERATORS / VIDEO LIBRARY OWNERS / VIDEO PARLOURS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "6", "title": "CHARITABLE TRUST", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "7", "title": "CYBERCAFES/  INTERNET COMPANIES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "8", "title": "DSA'S / VERIFICATION AGENCIES / COLLECTION AGENCIES / REPOSSESSION AGENCIES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "9", "title": "EDUCATION AND TRAINING", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "10", "title": "SALARIED - OTHERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "11", "title": "SHIPPING", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "12", "title": "STANDALONE BAR", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "13", "title": "STD / PCO OUTLETS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "14", "title": "STOCK BROKERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "15", "title": "TAX CONSULTANTS (NON QUALIFIED)", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "16", "title": "TAX CONSULTANTS (QUALIFIED)", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "17", "title": "TELECOMMUNICATION AND TELECOM SERVICES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "18", "title": "TRANSPORT OPERATORS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "19", "title": "FINANCE COMPANIES/ PRIVATE MONEY LENDERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "20", "title": "Practicing Doctor - Homeopathic/Ayurvedic/<PERSON>ni", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "21", "title": "ENTERTAINMENT AND MEDIA", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "22", "title": "FILM PERSONALITIES - RADIO / TV AND FILM INDUSTRY (ACTORS / ARTISTS / PRODUCERS / DIRECTORS) / FILM DISTRIBUTORS.", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "23", "title": "Finance companies / Financial Intermediaries", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "24", "title": "Firms/Companies Dealing In Plantation / Aquaculture / Chit Funds / Nidhis / money lending /Money Exchangers", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "25", "title": "HOTELS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "26", "title": "Builders / Infrastructure Construction / Real Estate Developer", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "27", "title": "IT AND ITES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "28", "title": "JOURNALISTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "29", "title": "LAWYER/TAX CONSULTANTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "30", "title": "LOTTERY BUSINESS / ANY GAMBLING BUSINESS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "31", "title": "MANAGEMENT CONSULTANTS -NON PROFESSIONAL CONSULTANTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "32", "title": "MANPOWER CONSULTANTS / PLACEMENT AGENCIES / LABOUR CONTRACTORS/ SECURITY AGENCY", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "33", "title": "MEDICAL AND HEALTH SERVICES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "34", "title": "MOTOR TRAINING SCHOOLS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "35", "title": "MULTI- LEVEL / NETWORK MARKETING BUSINESS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "36", "title": "NBFC", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "37", "title": "NON - MNC COURIER COMPANIES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "38", "title": "OIL AND GAS RELATED SERVICES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "39", "title": "Politicians / linked to Politics", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "40", "title": "REAL ESTATE AGENTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "41", "title": "ENGINEERING", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "42", "title": "POWER - TRANSMISSION", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "44", "title": "CABLE OPERATORS / VIDEO LIBRARY OWNERS / VIDEO PARLOURS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "45", "title": "DSA'S / VERIFICATION AGENCIES / COLLECTION AGENCIES / REPOSSESSION AGENCIES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "46", "title": "EDUCATION AND TRAINING", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "48", "title": "ENTERTAINMENT AND MEDIA", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "49", "title": "FILM PERSONALITIES - RADIO / TV AND FILM INDUSTRY (ACTORS / ARTISTS / PRODUCERS / DIRECTORS) / FILM DISTRIBUTORS.", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "50", "title": "Finance companies / Financial Intermediaries", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "51", "title": "Firms/Companies Dealing In Plantation / Aquaculture / Chit Funds / Nidhis / money lending /Money Exchangers", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "52", "title": "HOTELS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "53", "title": "Builders / Infrastructure Construction / Real Estate Developer", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "54", "title": "IT AND ITES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "55", "title": "JOURNALISTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "56", "title": "LAWYER/TAX CONSULTANTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "57", "title": "LOTTERY BUSINESS / ANY GAMBLING BUSINESS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "58", "title": "MANAGEMENT CONSULTANTS -NON PROFESSIONAL CONSULTANTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "59", "title": "MANAGEMENT CONSULTANTS -PROFESSIONAL CONSULTANTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "60", "title": "MANPOWER CONSULTANTS / PLACEMENT AGENCIES / LABOUR CONTRACTORS/ SECURITY AGENCY", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "61", "title": "MEDICAL AND HEALTH SERVICES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "62", "title": "MNC COURIER COMPANIES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "63", "title": "MOTOR TRAINING SCHOOLS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "64", "title": "MULTI- LEVEL / NETWORK MARKETING BUSINESS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "65", "title": "NBFC", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "66", "title": "NON - MNC COURIER COMPANIES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "67", "title": "OIL AND GAS RELATED SERVICES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "68", "title": "OTHER SERVICES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "69", "title": "Politicians / linked to Politics", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "70", "title": "POWER - TRANSMISSION", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "71", "title": "REAL ESTATE AGENTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "72", "title": "Salaried", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "73", "title": "STOCK/COMMODITY BROKERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "74", "title": "TELECOMMUNICATION AND TELECOM SERVICES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "75", "title": "TRANSPORT OPERATORS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "76", "title": "PRIVATE MONEY LENDERS /DEALER OF VIRTUAL CURRENCY", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "77", "title": "Practicing Doctor - Homeopathic/Ayurvedic/<PERSON>ni", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "78", "title": "TOBACCO AND TOBACCO PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "79", "title": "EDIBLE OILS AND VANASPATI", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "80", "title": "FOOD PROCESSING", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "81", "title": "BEVERAGES (EXCLUDING TEA AND COFFEE)", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "82", "title": "TEA", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "83", "title": "AUTO ANCILLARIES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "84", "title": "AUTOMOBILES-MANUFACTURING", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "85", "title": "CEMENT AND CEMENT PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "86", "title": "CHEMICALS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "87", "title": "PAINTS AND VARNISHES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "88", "title": "DYES AND PIGMENTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "89", "title": "DRUGS AND PHARMACEUTICALS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "91", "title": "ELECTRONICS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "92", "title": "FERTILIZERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "93", "title": "GEMS AND JEWELLERY", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "94", "title": "POWER GENERATION", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "95", "title": "POWER - TRANSMISSION", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "96", "title": "IRON AND STEEL - MANUFACTURING", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "97", "title": "OTHER METAL AND METAL PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "98", "title": "PETROCHEMICALS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "99", "title": "TEXTILES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "100", "title": "PAPER AND PAPER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "101", "title": "WOOD AND WOOD PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "102", "title": "LEATHER AND LEATHER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "103", "title": "RUBBER AND RUBBER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "104", "title": "ENVIRONMENTALLY UNFRIENDLY PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "105", "title": "HATCHERIES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "106", "title": "COAL PRODUCT", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "107", "title": "COFFEE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "108", "title": "CONSUMER DURABLES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "109", "title": "GLASS AND GLASSWARE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "110", "title": "MINING OF IRON ORES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "111", "title": "Other Manufacturing", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "112", "title": "SMALL / MEDIUM SCALE UNITS ENGAGED IN THE MANUFACTURE OF AEROSAL UNITS USING CFC (CHLOROFLUOROCARBONS)", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "113", "title": "SUGAR MILL", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "114", "title": "PLASTIC AND PLASTIC PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "115", "title": "SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP)", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "117", "title": "AUTOMOBILES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "119", "title": "CEMENT AND CEMENT PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "121", "title": "COAL PRODUCT", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "122", "title": "COFFEE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "123", "title": "CONSUMER DURABLES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "124", "title": "DRUGS AND PHARMACEUTICALS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "125", "title": "DYES AND PIGMENTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "126", "title": "EDIBLE OILS AND VANASPATI", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "127", "title": "ELECTRONICS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "129", "title": "ENVIRONMENTALLY UNFRIENDLY PRODUCTS/OFFENSIVE/WARFARE PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "130", "title": "FERTILIZERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "131", "title": "FOOD PROCESSING", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "132", "title": "GEMS AND JEWELLERY", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "133", "title": "GLASS AND GLASSWARE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "134", "title": "HATCHERIES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "135", "title": "IRON AND STEEL - MANUFACTURING", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "136", "title": "LEATHER AND LEATHER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "137", "title": "MINING OF IRON ORES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "138", "title": "OTHER METAL AND METAL PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "139", "title": "Other Manufacturing", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "140", "title": "PAINTS AND VARNISHES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "141", "title": "PAPER AND PAPER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "142", "title": "PETROCHEMICALS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "143", "title": "PLASTIC AND PLASTIC PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "144", "title": "POWER GENERATION", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "145", "title": "RUBBER AND RUBBER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "146", "title": "SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "147", "title": "SMALL / MEDIUM SCALE UNITS ENGAGED IN THE MANUFACTURE OF AEROSAL UNITS USING CFC (CHLOROFLUOROCARBONS)", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "148", "title": "SUGAR MILL", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "149", "title": "TEA", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "150", "title": "TEXTILES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "151", "title": "TOBACCO AND TOBACCO PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "152", "title": "WOOD AND WOOD PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "153", "title": "PETROCHEMICALS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "154", "title": "PLASTIC AND PLASTIC PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "155", "title": "RUBBER AND RUBBER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "156", "title": "SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP, ETC).", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "157", "title": "SUGAR", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "158", "title": "TOBACCO AND TOBACCO PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "159", "title": "TRADE-RETAIL", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "160", "title": "TRADE-WHOLESALE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "161", "title": "WOOD AND WOOD PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "162", "title": "ENVIRONMENTALLY UNFRIENDLY PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "163", "title": "WASTE/SCRAP MERCHANTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "164", "title": "Silver Bullion / Precious stones/ precious metal dealer / Importer of Gold", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "165", "title": "ANTIQUE DEALERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "166", "title": "AUTOMOBILES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "167", "title": "COAL PRODUCT", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "168", "title": "COFFEE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "169", "title": "CONSUMER DURABLES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "170", "title": "GEMS AND JEWELLERY-TRADE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "171", "title": "GLASS AND GLASSWARE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "172", "title": "IRON AND STEEL-TRADE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "173", "title": "LIQUOR/WINE STORES/Standalone Bar", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "174", "title": "MOBILE PRE-PAID CARD DEALERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "177", "title": "CEMENT AND CEMENT PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "179", "title": "DRUGS AND PHARMACEUTICALS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "180", "title": "DYES AND PIGMENTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "181", "title": "EDIBLE OILS AND VANASPATI", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "182", "title": "ELECTRONICS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "183", "title": "FERTILIZERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "184", "title": "LEATHER AND LEATHER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "185", "title": "OTHER METAL AND METAL PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "186", "title": "PAINTS AND VARNISHES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "187", "title": "PAPER AND PAPER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "188", "title": "TEA", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "189", "title": "TEXTILES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "190", "title": "ANTIQUE DEALERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "192", "title": "AUTOMOBILES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "194", "title": "CEMENT AND CEMENT PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "196", "title": "COAL PRODUCT", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "197", "title": "COFFEE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "198", "title": "CONSUMER DURABLES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "199", "title": "DRUGS AND PHARMACEUTICALS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "200", "title": "DYES AND PIGMENTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "201", "title": "EDIBLE OILS AND VANASPATI", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "202", "title": "ELECTRONICS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "203", "title": "ENVIRONMENTALLY UNFRIENDLY PRODUCTS/OFFENSIVE/WARFARE PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "204", "title": "FERTILIZERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "205", "title": "GEMS AND JEWELLERY-TRADE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "206", "title": "GLASS AND GLASSWARE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "207", "title": "IRON AND STEEL-TRADE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "208", "title": "LEATHER AND LEATHER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "209", "title": "LIQUOR/WINE STORES/Standalone Bar", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "210", "title": "MOBILE PRE-PAID CARD DEALERS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "211", "title": "OTHER METAL AND METAL PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "212", "title": "PAINTS AND VARNISHES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "213", "title": "PAPER AND PAPER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "214", "title": "PETROCHEMICALS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "215", "title": "PLASTIC AND PLASTIC PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "216", "title": "RUBBER AND RUBBER PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "217", "title": "SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "218", "title": "SUGAR", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "219", "title": "TEA", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "220", "title": "TEXTILES", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "221", "title": "TOBACCO AND TOBACCO PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "222", "title": "TRADE-RETAIL", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "223", "title": "TRADE-WHOLESALE", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "224", "title": "WASTE/SCRAP MERCHANTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "225", "title": "WOOD AND WOOD PRODUCTS", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "226", "title": "Silver Bullion / Precious stones/ precious metal dealer / Importer of Gold", "type": "PLAIN_TEXT", "fullWidthPress": false}], "offline": false}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "AutoSuggestFormFieldValue", "formFieldType": "AUTO_SUGGEST_BOX", "name": "organization", "label": "Company", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "dependencyList": [{"depKey": "employmentType", "depValue": {"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\bSalaried\\b"}}], "tracking": {}, "data": [], "offline": false, "action": {"screenType": null, "type": "SEARCH", "omnitureData": null, "originalUrl": null, "url": "/1/fintech/calm/search", "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "freeFlow": true}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "businessName", "label": "Business Name", "placeholder": "Type here", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "dependencyList": [{"depKey": "employmentType", "depValue": {"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\bSelfEmployed\\b"}}], "value": "", "replaceInput": {"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"type": null, "condition": null, "warningMessage": null, "regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"type": null, "condition": null, "warningMessage": null, "regex": "^\\S(.)*$", "replaceValue": " "}]}, "replaceInputList": [{"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"type": null, "condition": null, "warningMessage": null, "regex": "^\\s", "replaceValue": ""}, {"type": null, "condition": null, "warningMessage": null, "regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"type": null, "condition": null, "warningMessage": null, "regex": "\\s+", "replaceValue": " "}]}]}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "PriceTextBoxFormFieldValueV0", "formFieldType": "PRICE_TEXT_BOX_V0", "name": "income", "label": "Net Monthly Income", "placeholder": "Eg. 100,000", "mandatory": true, "maxCharacters": 9, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Minimum income should be 13,500", "interactionType": null, "validateOnSubmit": false, "regex": "^(13[5-9]\\d{2}|1[4-9]\\d{3}|[2-9]\\d{4}|[1-9]\\d{5,})$"}], "value": "", "tracking": {}, "inputType": "NUMERIC"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": {}, "trackingData": null, "value": {"type": "PriceTextBoxFormFieldValueV0", "formFieldType": "PRICE_TEXT_BOX_V0", "name": "bonusIncome", "label": "Extra monthly income (bonus, rent, interest, etc.)", "placeholder": "Eg. 10,000", "mandatory": false, "disabled": false, "value": "", "tracking": {}, "inputType": "NUMERIC", "maxCharacters": 9}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "formFieldType": "DROPDOWN", "name": "incomeSource", "label": "Select how you get your income", "mandatory": true, "disabled": false, "outerContainerStyle": {"fontSize": 14}, "containerStyle": {"gap": 8}, "tracking": {}, "options": [{"id": "ONLINE", "title": "Bank transfer", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "CHEQUE", "title": "Cheque", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "CASH", "title": "Cash", "type": "PLAIN_TEXT", "fullWidthPress": false}], "selectedOption": {"id": "ONLINE", "title": "Bank transfer", "type": "PLAIN_TEXT", "fullWidthPress": false}, "displayType": "DROPDOWN", "stackType": "HORIZONTAL", "inputType": "CUSTOM_RADIO"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {}, "listTitle": null, "formId": "LV4_ALL_DETAILS_FILLED_WORK_DETAILS_BOTTOM_SHEET_FORM_ID", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Work details", "showClipboard": false, "containerStyle": {"marginBottom": 20}, "style": {"color": "#4D43FE", "fontSize": 17, "lineHeight": 24, "fontWeight": "bold"}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "LV4_ALL_DETAILS_FILLED_WORK_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "borderColor": "#B5EF85", "buttonTextWeight": "medium", "buttonTextSize": 18}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {}, "validationMeta": null, "tracking": {"widgetName": "LV4_ALL_DETAILS_FILLED_WORK_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON_CLICK"}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"background": "#FFFFFF", "padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "<PERSON>", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 24, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 24}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"borderTopLeftRadius": 16, "borderTopRightRadius": 16, "left": 0, "paddingBottom": 12, "paddingLeft": 16, "paddingRight": 8, "paddingTop": 12, "position": "sticky"}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold"}, "text": "Work details", "textColor": "#1D2939"}}, "viewType": null}, "formGroupName": "LV4 Page 2 Work Details Form Group"}], "formId": "LV4_ALL_DETAILS_FILLED", "gap": 0, "listTitle": null, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": null, "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": {"content": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "AnnouncementV2Value", "image": {"type": "ImageValue", "aspectRatio": "262:185", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/04/04/2024/16390b7e-85b7-4d0a-b11c-f3c0e375ea04.png?q={@quality}", "height": 185, "width": 262}, "subTitle": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "lineHeight": 22, "textAlign": "center"}, "text": "Do not press back or switch apps."}}, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "lineHeight": 30, "textAlign": "center"}, "text": "Your application details are being processed..."}}}}, "type": "ANNOUNCEMENT_V2"}, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {"widgetName": "LV4_ALL_DETAILS_FILLED_SUBMIT_BUTTON_CLICK"}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV4_ALL_DETAILS_FILLED_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18, "title": "Confirm & Continue"}}, "buttonOrientation": null, "consent": null, "consentList": [{"consentType": "CHECKBOX", "consentId": "${consentIdMFI}", "consentFor": "${consentForMFI}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentMFI}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentMFI"}}}, {"consentType": "CHECKBOX", "consentId": "${consentIdNonPEP}", "consentFor": "${consentForNonPEP}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentNonPEP}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentNonPep"}}}, {"consentType": "CHECKBOX", "consentId": "${consentIdCKYC}", "consentFor": "${consentForCKYC}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentCKYC}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentCKYC"}}}], "containerStyle": {}, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": null}