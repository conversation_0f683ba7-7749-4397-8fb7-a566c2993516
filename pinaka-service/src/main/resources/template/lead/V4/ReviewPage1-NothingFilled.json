{"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formGroups": [{"formGroupData": [{"data": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "autoFocus": true, "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "PAN Number", "mandatory": true, "maxCharacters": 10, "minCharacters": 10, "name": "panNumber", "noOfErrors": 1, "placeholder": "eg. **********", "suffix": null, "tracking": {}, "initialValue": {"panNumber": ""}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter correct PAN", "interactionType": null, "regex": "^[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}$", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": "normal", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "marginTop": 10}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}, "value": ""}, "style": {"flex": 1}}, {"data": {"type": "DateFormFieldValueV0", "disabled": false, "formFieldType": "DATE_V0", "initialValue": {"dob": ""}, "label": "DOB", "mandatory": true, "name": "dob", "noOfErrors": 1, "suffix": null, "tracking": {}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": "normal", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}}, "style": {"flex": 1}}, {"data": {"type": "DropdownFormFieldValueV0", "disabled": false, "formFieldType": "DROPDOWN_V0", "label": "Gender", "mandatory": true, "name": "gender", "options": [{"fullWidthPress": false, "id": "M", "title": "Male", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "F", "title": "Female", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "O", "title": "Others", "type": "PLAIN_TEXT"}], "displayType": "DROPDOWN", "stackType": "HORIZONTAL", "inputType": "CUSTOM_RADIO", "containerStyle": {"display": "flex", "justifyContent": "space-around", "gap": 8}, "outerContainerStyle": {"fontSize": 14}, "selectedOption": {"fullWidthPress": false, "id": "M", "title": "Male", "type": "PLAIN_TEXT"}, "tracking": {}}, "style": {"flex": 1}}], "formGroupDataStyle": {"gap": 12, "marginRight": 24, "marginTop": 12}, "formGroupName": "LV4 Page 1 Without Personal Details Without Address Details : Personal Details"}, {"formGroupData": [{"data": {"type": "DropdownFormFieldValueV0", "dehydratedOption": {"description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontWeight": "normal"}, "text": "${houseNumber}, ${area}, ${pincodeDetails__city}, ${pincodeDetails__state} ${pincodeDetails__pincode} India", "textColor": "#667085", "title": "${houseNumber}, ${area}, ${pincodeDetails__city}, ${pincodeDetails__state} ${pincodeDetails__pincode} India"}}, "fullWidthPress": false, "id": "${ID}", "title": "Address ${INDEX}", "titleStyle": {"color": "#1D2939", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "type": "PLAIN_TEXT"}, "disabled": false, "displayType": "DROPDOWN", "formFieldType": "DROPDOWN_V0", "initialValue": {"address": ""}, "inputType": null, "mandatory": true, "name": "address", "options": [{"type": "IMAGE", "disabled": true, "id": "FALLBACK_NO_OPTION", "image": {"type": "ImageValue", "alternateText": "No address found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/753b009e-0bb7-4cbe-9658-a20e6cec8298.png?q={@quality}", "height": 154, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/753b009e-0bb7-4cbe-9658-a20e6cec8298.png?q={@quality}", "width": 300}, "action": {"type": "CLIENT__INLINE_NAVIGATION", "params": {"doNotBroadcastToNestedForm": true, "screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-79371862", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "540837788", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": {}, "failCard": null, "formId": "LV4_WITHOUT_PERSONAL_WITHOUT_ADDRESS_DETAILS_FILLED_ADDRESS_OPTION_FORM_ID", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV4_WITHOUT_PERSONAL_WITHOUT_ADDRESS_DETAILS_FILLED_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "numberOfLines": 4, "formFieldType": "TEXT_BOX_V0", "label": "House/Flat/Floor number", "mandatory": true, "multiline": true, "name": "houseNumber", "noOfErrors": 1, "placeholder": "House/Flat/Floor number", "tracking": {}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV4_WITHOUT_PERSONAL_WITHOUT_ADDRESS_DETAILS_FILLED_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "numberOfLines": 4, "formFieldType": "TEXT_BOX_V0", "label": "Apartment/Area/Locality", "mandatory": true, "multiline": true, "name": "area", "noOfErrors": 1, "placeholder": "Apartment/Area/Locality", "tracking": {}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CustomFormFieldValue", "containerStyle": null, "customFieldData": {"city": {"type": "TextBoxFormFieldValueV0", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "City", "mandatory": true, "name": "city", "placeholder": "", "outerContainerStyle": {"display": "none"}}, "customFieldType": "PINCODE_V0", "pincode": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/6/pincode/existence", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Pin Code", "mandatory": true, "maxCharacters": 6, "minCharacters": 6, "name": "pincode", "inputType": "NUMERIC", "noOfErrors": 1, "placeholder": "eg. 560103", "tracking": {}, "validationRegex": "^[1-9]{1}[0-9]{5}$", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter a valid pin code", "interactionType": null, "regex": "^(?!\\s*$)[1-9]{1}[0-9]{5}$", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, "state": {"type": "TextBoxFormFieldValueV0", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "State", "mandatory": true, "name": "state", "placeholder": "", "outerContainerStyle": {"display": "none"}}}, "disabled": false, "formFieldType": "CUSTOM", "grids": null, "inputStyle": null, "label": null, "mandatory": true, "name": "pincodeDetails", "noOfErrors": null, "outerContainerStyle": null, "tracking": null, "validationRuleList": null, "value": null}}], "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {"widgetName": "LV4_WITHOUT_PERSONAL_WITHOUT_ADDRESS_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON_CLICK"}, "triggerExtraStandardEvents": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV4_WITHOUT_PERSONAL_WITHOUT_ADDRESS_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "borderRadius": 8, "title": "Enter Work Details", "buttonColor": "#B5EF85", "borderColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18}}, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}, "buttonOrientation": null, "consent": null, "consentList": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": null}, "tracking": {}}}]}}}, "value": {"type": "RichButtonValue", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "buttonColor": "inherit"}}], "outerContainerStyle": {"background": "#FFF", "borderColor": "#D0D5DD", "borderRadius": 12, "borderStyle": "SOLID", "borderWidth": "1", "display": "block", "marginRight": 24, "minWidth": 212, "padding": 24, "paddingBottom": 20, "paddingTop": 16}, "stackType": "HORIZONTAL", "title": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontWeight": "normal"}, "text": "Communication Address", "textColor": "#1D2939", "title": "Communication Address"}, "titleDescContainerStyle": {"marginBottom": 8}}, "style": {}}], "formGroupDataStyle": {}, "formGroupHeader": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"doNotBroadcastToNestedForm": true, "screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-79371862", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "540837788", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "houseNumber", "noOfErrors": 1, "label": "House/Flat/Floor number", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "value": "", "tracking": {}, "multiline": true, "numberOfLines": 4, "placeholder": "House/Flat/Floor number"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "area", "noOfErrors": 1, "label": "Apartment/Area/Locality", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "value": "", "tracking": {}, "multiline": true, "numberOfLines": 4, "placeholder": "Apartment/Area/Locality"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "CustomFormFieldValue", "formFieldType": "CUSTOM", "name": "pincodeDetails", "noOfErrors": null, "label": null, "subLabel": null, "mandatory": true, "disabled": false, "cursorColor": null, "containerStyle": null, "validationRuleList": null, "dependencyList": null, "outerContainerStyle": null, "inputStyle": null, "value": null, "tracking": null, "grids": null, "customFieldData": {"customFieldType": "PINCODE_V0", "pincode": {"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "pincode", "noOfErrors": 1, "label": "Pin Code", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter a valid pin code", "interactionType": null, "validateOnSubmit": false, "regex": "^(?!\\s*$)[1-9]{1}[0-9]{5}$"}], "value": "", "tracking": {}, "validationRegex": "^[1-9]{1}[0-9]{5}$", "minCharacters": 6, "maxCharacters": 6, "placeholder": "eg. 560101", "inputType": "NUMERIC"}, "action": {"screenType": null, "type": "NAVIGATION", "omnitureData": null, "originalUrl": null, "url": "/6/pincode/existence", "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "state": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "state", "label": "State", "mandatory": true, "disabled": true, "outerContainerStyle": {"display": "none"}, "placeholder": ""}, "city": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "city", "label": "City", "mandatory": true, "disabled": true, "outerContainerStyle": {"display": "none"}, "placeholder": ""}}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {}, "listTitle": null, "formId": "LV4_WITHOUT_PERSONAL_WITHOUT_ADDRESS_DETAILS_BOTTOM_SHEET_FORM_ID", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Add new address", "showClipboard": false, "containerStyle": {"marginBottom": 20}, "style": {"color": "#4D43FE", "fontSize": 17, "lineHeight": 24, "fontWeight": "bold"}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "LV4_WITHOUT_PERSONAL_WITHOUT_ADDRESS_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Done", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {}, "validationMeta": null, "tracking": {"widgetName": "LV4_WITHOUT_PERSONAL_WITHOUT_ADDRESS_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON_CLICK"}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#2A55E5", "buttonTextWeight": "bold", "buttonViewType": "SOLID", "disabled": false, "style": {"justifyContent": "end", "padding": 0, "width": "100%"}, "title": "+ Add new"}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"left": 0, "marginBottom": 4, "marginRight": 24, "paddingBottom": 12, "position": "sticky"}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold"}, "text": "Current address", "textColor": "#344054"}}, "viewType": null}, "formGroupName": "LV4 Page 1 Personal Details Filled Without Address Details : Address Details"}], "formId": "LV4_WITHOUT_PERSONAL_WITHOUT_ADDRESS_DETAILS", "gap": 0, "listTitle": null, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": null, "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {"widgetName": "LV4_WITHOUT_PERSONAL_WITHOUT_ADDRESS_DETAILS_SUBMIT_BUTTON_CLICK"}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV4_WITHOUT_PERSONAL_WITHOUT_ADDRESS_DETAILS_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "borderRadius": 8, "title": "Enter Work Details", "buttonColor": "#B5EF85", "borderColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18}}, "buttonOrientation": null, "consent": null, "consentList": null, "containerStyle": {"background": "#FFFFFF"}, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": null}