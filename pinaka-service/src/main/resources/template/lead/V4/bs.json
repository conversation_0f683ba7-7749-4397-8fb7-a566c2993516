{"CACHE_INVALIDATION_TTL": "0", "META_INFO": {"abContext": {"abWrapper": []}, "appConfigHash": null, "clientConfigHashMap": null, "dcInfo": null, "omnitureInfo": null}, "REQUEST": null, "REQUEST-ID": "d98d7081-eabf-42fb-8a66-54d9ca6986c4", "RESPONSE": {"actionSuccess": false, "marketPlaceTrackingDataMap": {}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"background": "linear-gradient(135deg, #F8E6FA 0%, #F9FAFB 60%, #E6F0FA 100%)", "inBottomBar": true, "orientation": "", "theme": "light"}, "pageHash": "**********", "pageLevelSlots": {}, "pageNudges": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": "superCash", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACC14029865817626988", "pageType": "DYNAMIC", "pageId": "super-dynam-5ab74", "applicationId": "APP2507191225378364193554437427744887551", "loginStatus": "login:<PERSON><PERSON><PERSON>"}}}, "pageMeta": {"baseImpressionId": "d98d7081-eabf-42fb-8a66-54d9ca6986c4", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,0", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-*********", "elementId": "1-CARD_SUMMARY_LIST", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "CARD_SUMMARY_LIST", "data": {"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "gap": 0, "listTitle": null, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 28, "fontWeight": "normal", "lineHeight": 32}, "text": "Get instant loan of up to", "textColor": "#98A2B3"}}, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/25/07/2025/b1b39d43-bc7c-4a8f-848e-f0b65147c444.png?q={@quality}", "height": 92, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/25/07/2025/b1b39d43-bc7c-4a8f-848e-f0b65147c444.png?q={@quality}", "width": 92}, "iconBackgroundColor": null, "iconSize": null, "orientation": "PORTRAIT", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "style": {"alignItems": "flex-start", "borderRadius": 6, "padding": 8, "paddingBottom": 0}, "superTitle": null, "tagValue": null, "textContainerStyle": {"alignItems": "flex-start", "display": "flex", "flexDirection": "column"}, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "userName", "sendEdits": "true", "prefill": "true", "prefilledValue": "Vardaan", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "specialTextsMapper": {"Vardaan,": {"color": "#1D2939", "fontWeight": "bold"}}, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 28, "fontWeight": "normal", "lineHeight": 32}, "suffix": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "#F9FAFB", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": null, "pageSubtitle": "", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACC14029865817626988", "applicationId": "APP2507191225378364193554437427744887551"}}}, "pageMeta": {"baseImpressionId": "25b04894-2a9b-4b55-b6aa-6343b6654005", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#F9FAFB", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 0, "widgetWidth": 12}, "dataId": "-********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "LV4_LANDING_PAGE_NAME_PAGE_FROM_EDIT", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "title": {"text": " ", "icon": {"type": "ImageValue", "alternateText": "Sample PAN image", "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/00d3c9eb-4e80-4c6b-95f7-6d9c9fdbbcad.png?q={@quality}", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/00d3c9eb-4e80-4c6b-95f7-6d9c9fdbbcad.png?q={@quality}", "height": 98, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/00d3c9eb-4e80-4c6b-95f7-6d9c9fdbbcad.png?q={@quality}", "width": 312}}, "subTitle": {"text": "Make sure your name\nis as per PAN", "style": {"fontFamily": "<PERSON><PERSON>", "color": "#4D43FE", "fontSize": 20, "fontWeight": "bold", "width": 100}}, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {}, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Name", "mandatory": true, "name": "fullName", "noOfErrors": 1, "placeholder": "Enter your first name", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name cannot include numbers or special characters", "interactionType": null, "regex": "^[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name must be of 3 characters", "interactionType": null, "regex": "^[A-Za-z\\s]{3,}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name exceeds 26 characters", "interactionType": null, "regex": "^[A-Za-z\\s]{0,26}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Remove extra spaces in your name", "interactionType": null, "regex": "^(?!.*\\s{2,})[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Re-check your name – repeated letters may be a mistake", "interactionType": null, "regex": "^(?!.*(.)\\1{2,})[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal"}}, "value": "VARDAAN AGARWAL", "tracking": {"sendImpression": true, "fieldName": "fullName", "sendEdits": true, "prefill": true, "prefilledValue": "VARDAAN AGARWAL", "sendInlineError": true}}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValue", "disabled": true, "formFieldType": "TEXT_BOX", "label": "Phone no.", "mandatory": true, "name": "phoneNumber", "placeholder": "Enter mobile number", "inputType": "PHONE_PAD", "value": "8527872100", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid mobile number", "validateOnSubmit": false, "regex": "^[6-9]{1}[0-9]{9}$"}], "textboxStyles": {"captionStyle": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal"}, "containerStyle": {}, "inputBoxStyle": {"background-color": "#EAECF0"}, "inputStyle": {"background-color": "#EAECF0", "border": "none", "border-radius": 8, "box-shadow": "none", "color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal"}, "labelStyle": {"color": "#98A2B3", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal"}}, "tracking": {"sendImpression": true, "fieldName": "phoneNumber", "sendEdits": true, "prefill": true, "prefilledValue": "8527872100", "sendInlineError": true}}}], "resultStoreKey": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"tracking": {"fieldName": "LV4_LANDING_PAGE_NAME_PAGE_EDIT_SHEET_SUBMIT_BUTTON"}, "action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "SG_Q4G1waB2QLo7Y", "taskKey": "leadV4LandingPage", "applicationId": "APP2507191225378364193554437427744887551", "taskId": "BXDViPMYD0T_mTVZ", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmCN/wE4wQkXAUKkuZ5eb/d8F6pJyZEoTbCH7DoprlPdg2sL41S2dJa/UiDI25UvVrCii0XWDuUHF5Sw6iHILBcq"}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {"fieldName": "LV4_LANDING_PAGE_NAME_PAGE_SUBMIT_BUTTON_CLICK"}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Confirm", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18}}, "buttonOrientation": null, "consent": {"checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "I hereby grant express consent and authorize Scapic Innovations Pvt. Ltd (SIPL) to collect, process, record my personal data including personal information and sensitive personal information such as PAN, DOB, gender, contact details, income source (\"Personal Data\") during loan journey on the platform/application and share Personal Data with <a href='https://super.money/lending-partners'>SIPL's Partners</a> to complete Know Your Customer (KYC) requirements as required under applicable laws and for the purpose of processing my loan application (\"Purpose\"). I further consent to and authorize the <a href='https://super.money/lending-partners'>SIPL's Partners</a> to further share my Personal Data with their service providers and/or third parties for the Purpose including Tele-calling, SMS or WhatsApp. I also understand and authorize the <a href='https://super.money/lending-partners'>SIPL's Partners</a> to pull my credit report separately from any credit information companies' basis my Personal Data shared for the Purpose. I hereby consent to and expressly authorize SIPL to pull credit reports from credit information companies (Read Experian Specific terms <a href='https://super.money/scapic-compliance-tnc/scapic-tnc'>here</a>, to evaluate my credit worthiness/eligibility/identity for financial services and products such as personal loans, credit cards, buy now pay later /or for my loan application respectively (End Use Purpose). SIPL does not share credit information received from credit information companies with third parties without your express consent. I hereby also confirm that I am an Indian citizen and accept that I have read the <a href='https://super.money/scapic-compliance-tnc/scapic-tnc'>terms and conditions</a> and <a href='https://super.money/scapic-compliance-tnc/scapic-privacy-policy'>privacy policy</a> of Scapic Innovations Pvt. Ltd."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "BUREAU_PULL", "consentId": "001", "consentType": "CHECKBOX"}, "consentList": null, "containerStyle": {"background": "#FFFFFF", "padding": 24, "width": "calc(100% - 48px)"}, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": "NO_SHADOW"}}, "tracking": {"widgetName": "LV4_LANDING_PAGE_NAME_PAGE_FROM_EDIT_BUTTON"}}}]}, "processInstanceId": "SG_Q4G1waB2QLo7Y", "taskKey": "leadV4LandingPage", "applicationId": "APP2507191225378364193554437427744887551", "taskId": "BXDViPMYD0T_mTVZ", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmCN/wE4wQkXAUKkuZ5eb/d8F6pJyZEoTbCH7DoprlPdg2sL41S2dJa/UiDI25UvVrCii0XWDuUHF5Sw6iHILBcq"}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {"widgetName": "LV4_LANDING_PAGE_NAME_PAGE_EDIT_OPTION_CLICK"}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": "6/pl/apply-now", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV4_LANDING_PAGE_NAME_PAGE_EDIT_OPTION"}, "trackingData": null, "value": {"type": "ImageValue", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/18/07/2025/aab9b6f1-7b9d-4b25-9472-44807888e826.png?q={@quality}", "height": 18, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/18/07/2025/aab9b6f1-7b9d-4b25-9472-44807888e826.png?q={@quality}", "width": 18}}, "text": "<PERSON><PERSON><PERSON>,", "textColor": "#98A2B3"}}, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 28, "fontWeight": "normal", "lineHeight": 32}, "text": "", "textColor": "#98A2B3"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "style": {"alignItems": "baseline", "borderRadius": 6, "padding": 8, "paddingTop": 0}, "superTitle": null, "tagValue": null, "textContainerStyle": {}, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "offerAmount", "sendEdits": "true", "prefill": "true", "prefilledValue": "", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "specialTextsMapper": {"*": {"color": "#C7CDFF", "fontFamily": "<PERSON><PERSON>", "fontSize": 24, "fontWeight": "bold", "marginTop": -15.0}, "₹10,00,000": {"color": "#4D43FE"}}, "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": 52, "fontWeight": "bold", "lineHeight": 60}, "text": "₹10,00,000 *", "textColor": "#4D43FE"}}, "viewType": "PRIMITIVE_CARD"}}], "subWidget": null}, "tracking": {}}}, {"slotType": "WIDGET", "id": 2, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,0,24,0", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "549149135", "elementId": "2-CARD_SUMMARY_LIST", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "CARD_SUMMARY_LIST", "data": {"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "gap": 0, "listTitle": null, "numGridColumns": 0, "orientation": "VERTICAL_CARD_ANIMATION", "paginationRequestContext": null, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontWeight": "normal", "lineHeight": 16, "marginTop": 8.0, "whiteSpace": "nowrap"}, "text": "<PERSON><PERSON> Mela is Live! (22nd - 25th July)", "textColor": "#344054"}}, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/21/07/2025/6bb29426-8cb6-4347-9800-fdca4dbe1545.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/21/07/2025/6bb29426-8cb6-4347-9800-fdca4dbe1545.png?q={@quality}", "width": 40}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "style": {"alignItems": "center", "background": "linear-gradient(to right, #FFF6D3 0%, #F9FAFB 100%)", "borderRadius": 8, "display": "flex", "flexDirection": "row", "gap": 12, "padding": 16}, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 20, "marginTop": 8.0, "whiteSpace": "nowrap"}, "text": "FREE ₹250 Flipkart Voucher! ", "textColor": "#344054"}}, "viewType": "PRIMITIVE_CARD"}}], "subWidget": null}, "tracking": {}}}, {"slotType": "WIDGET", "id": 3, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,0,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "207635288", "elementId": "3-CARD_CAROUSEL", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "CARD_CAROUSEL", "data": {"autoPlay": true, "clientPowered": false, "colGap": null, "containerStyle": {"paddingBottom": 80}, "failCard": null, "gap": 0, "listTitle": null, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "aspectRatio": "1.8", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/6a5db249-6dc4-4786-ae46-9507e1e33c78.png?q={@quality}", "height": 15, "width": 48}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "style": {"paddingRight": 0}, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "aspectRatio": "1", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/37a843d7-8017-48bd-a7ab-78ea6de3bac5.png?q={@quality}", "height": 15, "width": 48}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "style": {"paddingRight": 0}, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "aspectRatio": "1.8", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/a3eb5d97-a3d2-43be-b1c2-821078a3676f.png?q={@quality}", "height": 15, "width": 48}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "style": {"paddingRight": 0}, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "aspectRatio": "2.5", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/f7f43184-a3a8-4e8b-84eb-776b901b88d8.png?q={@quality}", "height": 15, "width": 48}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "style": {"paddingRight": 0}, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "aspectRatio": "2.5", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/8aba4b2d-d228-4061-8891-9fb807aed55a.png?q={@quality}", "height": 15, "width": 48}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "style": {"paddingRight": 0}, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "aspectRatio": "1.2", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/21f2138e-f89a-416b-9e7b-788fc1c3d5e1.png?q={@quality}", "height": 15, "width": 48}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "style": {"paddingRight": 0}, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": null, "viewType": "PRIMITIVE_CARD"}}], "speed": 22000, "subWidget": null}, "tracking": {}}}, {"slotType": "WIDGET", "id": 5, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,0,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "480945067", "elementId": "5-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "LV4_LANDING_PAGE_NAME_PAGE", "gap": 0, "listTitle": null, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Name", "mandatory": true, "name": "fullName", "noOfErrors": 1, "placeholder": "Enter your first name", "textboxStyles": {"captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal"}, "containerStyle": null, "inputBoxStyle": null, "inputStyle": {"border": "none", "color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "marginTop": 20}, "prefixStyle": null}, "tracking": {"sendImpression": true, "fieldName": "fullName", "sendEdits": true, "prefill": true, "prefilledValue": "VARDAAN AGARWAL", "sendInlineError": true}, "validationRuleList": [{"compareWithValue": null, "errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"compareWithValue": null, "errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"compareWithValue": null, "errorMessage": "Name cannot include numbers or special characters", "interactionType": null, "regex": "^[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}, {"compareWithValue": null, "errorMessage": "Name must be of 3 characters", "interactionType": null, "regex": "^[A-Za-z\\s]{3,}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"compareWithValue": null, "errorMessage": "Name exceeds 26 characters", "interactionType": null, "regex": "^[A-Za-z\\s]{0,26}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"compareWithValue": null, "errorMessage": "Remove extra spaces in your name", "interactionType": null, "regex": "^(?!.*\\s{2,})[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}, {"compareWithValue": null, "errorMessage": "Re-check your name – repeated letters may be a mistake", "interactionType": null, "regex": "^(?!.*(.)\\1{2,})[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "VARDAAN AGARWAL"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValue", "disabled": true, "formFieldType": "TEXT_BOX", "inputType": "PHONE_PAD", "label": "Phone no.", "mandatory": true, "name": "phoneNumber", "placeholder": "Enter mobile number", "tracking": {"sendImpression": true, "fieldName": "phoneNumber", "sendEdits": true, "prefill": true, "prefilledValue": "8527872100", "sendInlineError": true}, "validationRuleList": [{"compareWithValue": null, "errorMessage": "Please enter valid mobile number", "interactionType": null, "regex": "^[6-9]{1}[0-9]{9}$", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "8527872100"}}], "resultStoreKey": null, "shouldHideConsent": true, "shouldHideFields": true, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "SG_Q4G1waB2QLo7Y", "taskKey": "leadV4LandingPage", "applicationId": "APP2507191225378364193554437427744887551", "taskId": "BXDViPMYD0T_mTVZ", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmCN/wE4wQkXAUKkuZ5eb/d8F6pJyZEoTbCH7DoprlPdg2sL41S2dJa/UiDI25UvVrCii0XWDuUHF5Sw6iHILBcq"}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {"fieldName": "LV4_LANDING_PAGE_NAME_PAGE_HIDDEN_FORM_SUBMIT_BUTTON_CLICK"}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"fieldName": "LV4_LANDING_PAGE_NAME_PAGE_HIDDEN_FORM_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextSize": 18, "buttonTextWeight": "medium", "title": "Apply now"}}, "buttonOrientation": null, "consent": {"checkboxField": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "I, <PERSON><PERSON><PERSON>, hereby grant express consent and authorize Scapic Innovations Pvt. Ltd (SIPL) to collect, process, record my personal data including personal information and sensitive personal information such as PAN, DOB, gender, contact details, income source (\"Personal Data\") during loan journey on the platform/application and share Personal Data with <a href='https://super.money/lending-partners'>SIPL's Partners</a> to complete Know Your Customer (KYC) requirements as required under applicable laws and for the purpose of processing my loan application (\"Purpose\"). I further consent to and authorize the <a href='https://super.money/lending-partners'>SIPL's Partners</a> to further share my Personal Data with their service providers and/or third parties for the Purpose including Tele-calling, SMS or WhatsApp. I also understand and authorize the <a href='https://super.money/lending-partners'>SIPL's Partners</a> to pull my credit report separately from any credit information companies' basis my Personal Data shared for the Purpose. I hereby consent to and expressly authorize SIPL to pull credit reports from credit information companies (Read Experian Specific terms <a href='https://super.money/scapic-compliance-tnc/scapic-tnc'>here</a>, to evaluate my credit worthiness/eligibility/identity for financial services and products such as personal loans, credit cards, buy now pay later /or for my loan application respectively (End Use Purpose). SIPL does not share credit information received from credit information companies with third parties without your express consent. I hereby also confirm that I am an Indian citizen and accept that I have read the <a href='https://super.money/scapic-compliance-tnc/scapic-tnc'>terms and conditions</a> and <a href='https://super.money/scapic-compliance-tnc/scapic-privacy-policy'>privacy policy</a> of Scapic Innovations Pvt. Ltd."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "BUREAU_PULL", "consentId": "001", "consentType": "CHECKBOX"}, "consentList": null, "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": null}, "tracking": {}}}]}}, "SESSION": {"accountId": "ACC14029865817626988", "asn": null, "at": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjhlM2ZhMGE3LTJmZDMtNGNiMi05MWRjLTZlNTMxOGU1YTkxZiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-CgMc4QWrvXe-kkggYwN99YtMPQ-PxjNC2-OdRnQeGI", "email": "<EMAIL>", "firstName": "Vardaan", "flipkartFirstUser": false, "isLoggedIn": true, "isUPISessionActive": false, "kaction": null, "lastName": "<PERSON><PERSON><PERSON>", "mobileNumber": null, "nsid": "3.VIFF0F7BB095054A1BA0A8CC2E8EBC2A80.*************.VIFF0F7BB095054A1BA0A8CC2E8EBC2A80", "rt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjhlM2ZhMGE3LTJmZDMtNGNiMi05MWRjLTZlNTMxOGU1YTkxZiJ9.****************************************************************************************************************************************************************************************************************************************************************************************.G343zTt8SetNbdmk6KQkrXlQiaLUy84ZlBZcMnn_F2k", "secureToken": "VIFF0F7BB095054A1BA0A8CC2E8EBC2A80:VIFF0F7BB095054A1BA0A8CC2E8EBC2A80", "sn": "VIFF0F7BB095054A1BA0A8CC2E8EBC2A80.TOKA68818F0E03F45C68E89046848045DCC.1753424030.LI", "tracking": null, "ts": 0, "twoFa": false, "upiSessionPolicy": null, "vid": "VIFF0F7BB095054A1BA0A8CC2E8EBC2A80"}, "STATUS_CODE": 200}