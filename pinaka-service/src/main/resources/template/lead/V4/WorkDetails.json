{"clientPowered": false, "colGap": null, "containerStyle": {}, "failCard": null, "formId": "LV4_WORK_DETAILS", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "inputType": "EMAIL_ADDRESS", "label": "Official Email", "mandatory": true, "name": "email", "noOfErrors": 1, "placeholder": "Enter your email", "tracking": {}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter correct Email", "interactionType": null, "regex": "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,6})$", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "", "warningValidation": {"condition": ["gmail.co.za", "gmail.com", "gmail.com.au", "gmail.com.br", "gmail.ru", "hotmail.be", "hotmail.ca", "hotmail.ch", "hotmail.co", "hotmail.co.il", "hotmail.co.jp", "hotmail.co.nz", "hotmail.co.uk", "hotmail.co.za", "hotmail.com", "hotmail.com.ar", "hotmail.com.au", "hotmail.com.br", "hotmail.com.mx", "hotmail.com.tr", "hotmail.de", "hotmail.es", "hotmail.fi", "hotmail.fr", "hotmail.it", "hotmail.kg", "hotmail.kz", "hotmail.my", "hotmail.nl", "hotmail.ro", "hotmail.roor", "hotmail.ru", "rediff.com", "rediffmail.com", "rediffmailpro.com", "yahoo.ae", "yahoo.at", "yahoo.be", "yahoo.ca", "yahoo.ch", "yahoo.cn", "yahoo.co", "yahoo.co.id", "yahoo.co.il", "yahoo.co.in", "yahoo.co.jp", "yahoo.co.kr", "yahoo.co.nz", "yahoo.co.th", "yahoo.co.uk", "yahoo.co.za", "yahoo.com", "yahoo.com.ar", "yahoo.com.au", "yahoo.com.br", "yahoo.com.cn", "yahoo.com.co", "yahoo.com.hk", "yahoo.com.is", "yahoo.com.mx", "yahoo.com.my", "yahoo.com.ph", "yahoo.com.ru", "yahoo.com.sg", "yahoo.com.tr", "yahoo.com.tw", "yahoo.com.vn", "yahoo.cz", "yahoo.de", "yahoo.dk", "yahoo.es", "yahoo.fi", "yahoo.fr", "yahoo.gr", "yahoo.hu", "yahoo.ie", "yahoo.in", "yahoo.it", "yahoo.jp", "yahoo.net", "yahoo.nl", "yahoo.no", "yahoo.pl", "yahoo.pt", "yahoo.ro", "yahoo.ru", "yahoo.se", "yahoofs.com", "yahoomail.com", "aol.com", "msn.com", "wanadoo.fr", "orange.fr", "comcast.net", "live.com", "free.fr", "gmx.de", "web.de", "yandex.ru", "ymail.com", "libero.it", "outlook.com", "uol.com.br", "bol.com.br", "mail.ru", "cox.net", "sbcglobal.net", "sfr.fr", "live.fr", "verizon.net", "live.co.uk", "googlemail.com", "ig.com.br", "live.nl", "bigpond.com", "terra.com.br", "neuf.fr", "alice.it", "rocketmail.com", "att.net", "laposte.net", "facebook.com", "bellsouth.net", "charter.net", "rambler.ru", "tiscali.it", "shaw.ca", "sky.com", "earthlink.net", "optonline.net", "freenet.de", "t-online.de", "aliceadsl.fr", "virgilio.it", "home.nl", "qq.com", "telenet.be", "me.com", "tiscali.co.uk", "voila.fr", "gmx.net", "mail.com", "planet.nl", "tin.it", "live.it", "ntlworld.com", "arcor.de", "frontiernet.net", "hetnet.nl", "live.com.au", "zonnet.nl", "club-internet.fr", "juno.com", "optusnet.com.au", "blueyonder.co.uk", "bluewin.ch", "skynet.be", "sympatico.ca", "windstream.net", "mac.com", "centurytel.net", "chello.nl", "live.ca", "aim.com", "bigpond.net.au", "super.money"], "type": "emailHostCheck", "warningMessage": "Please recheck correctness of your email address"}, "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": "normal", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "marginTop": 10}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}, "containerStyle": {"minHeight": 56}}}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "MultiDropdownFormFieldValue", "defaultValue": "Personal", "disabled": false, "formFieldType": "MULTI_DROPDOWN", "label": "Purpose of <PERSON>an", "mandatory": true, "name": "loanPurpose", "options": [{"id": "PERSONAL", "title": "Personal"}, {"id": "TRAVEL", "title": "Travel"}, {"id": "HOME_REPAIR", "title": "Home - repair"}, {"id": "MARRIAGE", "title": "Marriage"}, {"id": "MEDICAL", "title": "Medical"}, {"id": "EDUCATION", "title": "Education"}, {"id": "CONSUMER_DURABLES", "title": "Consumer - durables"}], "value": "PERSONAL", "viewType": "DROPDOWN", "dropdownStyles": {"textBox": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": "normal", "border-radius": 8, "border": "none", "box-shadow": "none", "height": 56}, "fieldLabel": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "marginTop": 20}, "body2": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "MultiDropdownFormFieldValue", "defaultValue": "", "disabled": false, "formFieldType": "MULTI_DROPDOWN", "label": "Employment type", "placeholder": "Select", "mandatory": true, "name": "employmentType", "options": [{"id": "Salaried", "title": "Salari<PERSON> (Job)", "subtitle": "You work for a company and get a fixed monthly salary"}, {"id": "SelfEmployed", "title": "Self Employed (Own Business)", "subtitle": "You run your own business or work independently"}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": "normal", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}, "value": "", "viewType": "DROPDOWN", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}]}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV4_WORK_DETAILS_FORM"}, "trackingData": null, "value": {"type": "AutoSuggestFormFieldValue", "data": [{"fullWidthPress": false, "id": "1", "title": "ACTIVITIES OF TRADE UNIONS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "2", "title": "AIR TRAVEL AGENTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "3", "title": "AVIATION", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "4", "title": "BUILDERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "5", "title": "CABLE OPERATORS / VIDEO LIBRARY OWNERS / VIDEO PARLOURS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "6", "title": "CHARITABLE TRUST", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "7", "title": "CYBERCAFES/  INTERNET COMPANIES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "8", "title": "DSA'S / VERIFICATION AGENCIES / COLLECTION AGENCIES / REPOSSESSION AGENCIES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "11", "title": "SHIPPING", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "12", "title": "STANDALONE BAR", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "13", "title": "STD / PCO OUTLETS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "14", "title": "STOCK BROKERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "15", "title": "TAX CONSULTANTS (NON QUALIFIED)", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "16", "title": "TAX CONSULTANTS (QUALIFIED)", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "17", "title": "TELECOMMUNICATION AND TELECOM SERVICES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "18", "title": "TRANSPORT OPERATORS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "19", "title": "FINANCE COMPANIES/ PRIVATE MONEY LENDERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "20", "title": "Practicing Doctor - Homeopathic/Ayurvedic/<PERSON>ni", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "21", "title": "ENTERTAINMENT AND MEDIA", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "22", "title": "FILM PERSONALITIES - RADIO / TV AND FILM INDUSTRY (ACTORS / ARTISTS / PRODUCERS / DIRECTORS) / FILM DISTRIBUTORS.", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "23", "title": "Finance companies / Financial Intermediaries", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "24", "title": "Firms/Companies Dealing In Plantation / Aquaculture / Chit Funds / Nidhis / money lending /Money Exchangers", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "25", "title": "HOTELS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "26", "title": "Builders / Infrastructure Construction / Real Estate Developer", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "27", "title": "IT AND ITES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "28", "title": "JOURNALISTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "29", "title": "LAWYER/TAX CONSULTANTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "30", "title": "LOTTERY BUSINESS / ANY GAMBLING BUSINESS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "31", "title": "MANAGEMENT CONSULTANTS -NON PROFESSIONAL CONSULTANTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "32", "title": "MANPOWER CONSULTANTS / PLACEMENT AGENCIES / LABOUR CONTRACTORS/ SECURITY AGENCY", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "33", "title": "MEDICAL AND HEALTH SERVICES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "34", "title": "MOTOR TRAINING SCHOOLS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "35", "title": "MULTI- LEVEL / NETWORK MARKETING BUSINESS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "36", "title": "NBFC", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "37", "title": "NON - MNC COURIER COMPANIES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "38", "title": "OIL AND GAS RELATED SERVICES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "39", "title": "Politicians / linked to Politics", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "40", "title": "REAL ESTATE AGENTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "41", "title": "ENGINEERING", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "42", "title": "POWER - TRANSMISSION", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "46", "title": "EDUCATION AND TRAINING", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "49", "title": "FILM PERSONALITIES - RADIO / TV AND FILM INDUSTRY (ACTORS / ARTISTS / PRODUCERS / DIRECTORS) / FILM DISTRIBUTORS.", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "50", "title": "Finance companies / Financial Intermediaries", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "51", "title": "Firms/Companies Dealing In Plantation / Aquaculture / Chit Funds / Nidhis / money lending /Money Exchangers", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "52", "title": "HOTELS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "53", "title": "Builders / Infrastructure Construction / Real Estate Developer", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "54", "title": "IT AND ITES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "55", "title": "JOURNALISTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "56", "title": "LAWYER/TAX CONSULTANTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "57", "title": "LOTTERY BUSINESS / ANY GAMBLING BUSINESS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "58", "title": "MANAGEMENT CONSULTANTS -NON PROFESSIONAL CONSULTANTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "59", "title": "MANAGEMENT CONSULTANTS -PROFESSIONAL CONSULTANTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "60", "title": "MANPOWER CONSULTANTS / PLACEMENT AGENCIES / LABOUR CONTRACTORS/ SECURITY AGENCY", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "61", "title": "MEDICAL AND HEALTH SERVICES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "62", "title": "MNC COURIER COMPANIES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "63", "title": "MOTOR TRAINING SCHOOLS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "64", "title": "MULTI- LEVEL / NETWORK MARKETING BUSINESS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "65", "title": "NBFC", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "66", "title": "NON - MNC COURIER COMPANIES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "67", "title": "OIL AND GAS RELATED SERVICES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "68", "title": "OTHER SERVICES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "69", "title": "Politicians / linked to Politics", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "70", "title": "POWER - TRANSMISSION", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "71", "title": "REAL ESTATE AGENTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "72", "title": "Salaried", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "73", "title": "STOCK/COMMODITY BROKERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "74", "title": "TELECOMMUNICATION AND TELECOM SERVICES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "75", "title": "TRANSPORT OPERATORS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "76", "title": "PRIVATE MONEY LENDERS /DEALER OF VIRTUAL CURRENCY", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "77", "title": "Practicing Doctor - Homeopathic/Ayurvedic/<PERSON>ni", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "78", "title": "TOBACCO AND TOBACCO PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "79", "title": "EDIBLE OILS AND VANASPATI", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "80", "title": "FOOD PROCESSING", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "81", "title": "BEVERAGES (EXCLUDING TEA AND COFFEE)", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "82", "title": "TEA", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "84", "title": "AUTOMOBILES-MANUFACTURING", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "85", "title": "CEMENT AND CEMENT PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "86", "title": "CHEMICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "87", "title": "PAINTS AND VARNISHES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "88", "title": "DYES AND PIGMENTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "89", "title": "DRUGS AND PHARMACEUTICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "91", "title": "ELECTRONICS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "92", "title": "FERTILIZERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "93", "title": "GEMS AND JEWELLERY", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "94", "title": "POWER GENERATION", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "95", "title": "POWER - TRANSMISSION", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "96", "title": "IRON AND STEEL - MANUFACTURING", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "97", "title": "OTHER METAL AND METAL PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "98", "title": "PETROCHEMICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "99", "title": "TEXTILES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "100", "title": "PAPER AND PAPER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "101", "title": "WOOD AND WOOD PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "102", "title": "LEATHER AND LEATHER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "103", "title": "RUBBER AND RUBBER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "104", "title": "ENVIRONMENTALLY UNFRIENDLY PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "105", "title": "HATCHERIES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "106", "title": "COAL PRODUCT", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "107", "title": "COFFEE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "108", "title": "CONSUMER DURABLES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "109", "title": "GLASS AND GLASSWARE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "110", "title": "MINING OF IRON ORES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "111", "title": "Other Manufacturing", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "112", "title": "SMALL / MEDIUM SCALE UNITS ENGAGED IN THE MANUFACTURE OF AEROSAL UNITS USING CFC (CHLOROFLUOROCARBONS)", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "113", "title": "SUGAR MILL", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "114", "title": "PLASTIC AND PLASTIC PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "115", "title": "SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP)", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "117", "title": "AUTOMOBILES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "119", "title": "CEMENT AND CEMENT PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "120", "title": "CHEMICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "121", "title": "COAL PRODUCT", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "122", "title": "COFFEE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "123", "title": "CONSUMER DURABLES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "124", "title": "DRUGS AND PHARMACEUTICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "125", "title": "DYES AND PIGMENTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "126", "title": "EDIBLE OILS AND VANASPATI", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "127", "title": "ELECTRONICS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "129", "title": "ENVIRONMENTALLY UNFRIENDLY PRODUCTS/OFFENSIVE/WARFARE PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "130", "title": "FERTILIZERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "131", "title": "FOOD PROCESSING", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "132", "title": "GEMS AND JEWELLERY", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "133", "title": "GLASS AND GLASSWARE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "134", "title": "HATCHERIES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "135", "title": "IRON AND STEEL - MANUFACTURING", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "136", "title": "LEATHER AND LEATHER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "137", "title": "MINING OF IRON ORES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "138", "title": "OTHER METAL AND METAL PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "139", "title": "Other Manufacturing", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "140", "title": "PAINTS AND VARNISHES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "141", "title": "PAPER AND PAPER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "142", "title": "PETROCHEMICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "143", "title": "PLASTIC AND PLASTIC PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "144", "title": "POWER GENERATION", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "145", "title": "RUBBER AND RUBBER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "146", "title": "SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "147", "title": "SMALL / MEDIUM SCALE UNITS ENGAGED IN THE MANUFACTURE OF AEROSAL UNITS USING CFC (CHLOROFLUOROCARBONS)", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "148", "title": "SUGAR MILL", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "149", "title": "TEA", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "150", "title": "TEXTILES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "151", "title": "TOBACCO AND TOBACCO PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "152", "title": "WOOD AND WOOD PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "153", "title": "PETROCHEMICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "154", "title": "PLASTIC AND PLASTIC PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "155", "title": "RUBBER AND RUBBER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "156", "title": "SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP, ETC).", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "157", "title": "SUGAR", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "158", "title": "TOBACCO AND TOBACCO PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "159", "title": "TRADE-RETAIL", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "160", "title": "TRADE-WHOLESALE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "161", "title": "WOOD AND WOOD PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "162", "title": "ENVIRONMENTALLY UNFRIENDLY PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "163", "title": "WASTE/SCRAP MERCHANTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "164", "title": "Silver Bullion / Precious stones/ precious metal dealer / Importer of Gold", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "165", "title": "ANTIQUE DEALERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "166", "title": "AUTOMOBILES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "167", "title": "COAL PRODUCT", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "168", "title": "COFFEE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "169", "title": "CONSUMER DURABLES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "170", "title": "GEMS AND JEWELLERY-TRADE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "171", "title": "GLASS AND GLASSWARE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "172", "title": "IRON AND STEEL-TRADE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "173", "title": "LIQUOR/WINE STORES/Standalone Bar", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "174", "title": "MOBILE PRE-PAID CARD DEALERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "175", "title": "AUTO ANCILLARIES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "177", "title": "CEMENT AND CEMENT PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "178", "title": "CHEMICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "179", "title": "DRUGS AND PHARMACEUTICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "180", "title": "DYES AND PIGMENTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "181", "title": "EDIBLE OILS AND VANASPATI", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "182", "title": "ELECTRONICS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "183", "title": "FERTILIZERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "184", "title": "LEATHER AND LEATHER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "185", "title": "OTHER METAL AND METAL PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "186", "title": "PAINTS AND VARNISHES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "187", "title": "PAPER AND PAPER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "188", "title": "TEA", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "189", "title": "TEXTILES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "190", "title": "ANTIQUE DEALERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "192", "title": "AUTOMOBILES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "194", "title": "CEMENT AND CEMENT PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "195", "title": "CHEMICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "196", "title": "COAL PRODUCT", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "197", "title": "COFFEE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "198", "title": "CONSUMER DURABLES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "199", "title": "DRUGS AND PHARMACEUTICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "200", "title": "DYES AND PIGMENTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "201", "title": "EDIBLE OILS AND VANASPATI", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "202", "title": "ELECTRONICS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "203", "title": "ENVIRONMENTALLY UNFRIENDLY PRODUCTS/OFFENSIVE/WARFARE PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "204", "title": "FERTILIZERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "205", "title": "GEMS AND JEWELLERY-TRADE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "206", "title": "GLASS AND GLASSWARE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "207", "title": "IRON AND STEEL-TRADE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "208", "title": "LEATHER AND LEATHER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "209", "title": "LIQUOR/WINE STORES/Standalone Bar", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "210", "title": "MOBILE PRE-PAID CARD DEALERS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "211", "title": "OTHER METAL AND METAL PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "212", "title": "PAINTS AND VARNISHES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "213", "title": "PAPER AND PAPER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "214", "title": "PETROCHEMICALS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "215", "title": "PLASTIC AND PLASTIC PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "216", "title": "RUBBER AND RUBBER PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "217", "title": "SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "218", "title": "SUGAR", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "219", "title": "TEA", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "220", "title": "TEXTILES", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "221", "title": "TOBACCO AND TOBACCO PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "222", "title": "TRADE-RETAIL", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "223", "title": "TRADE-WHOLESALE", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "224", "title": "WASTE/SCRAP MERCHANTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "225", "title": "WOOD AND WOOD PRODUCTS", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "226", "title": "Silver Bullion / Precious stones/ precious metal dealer / Importer of Gold", "type": "PLAIN_TEXT"}], "disabled": false, "formFieldType": "AUTO_SUGGEST_BOX", "label": "Industry type", "mandatory": true, "name": "industryName", "offline": false, "autoSuggestIcon": {"type": "ImageValue", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/06/2025/1afe2587-f33d-49f6-8cf0-ddb091c83648.png?q={@quality}", "width": 20, "height": 20}, "dependencyList": [{"depKey": "employmentType", "depValue": {"ruleType": "REGEX", "regex": "\\bSelfEmployed\\b", "errorMessage": "Complete this field to proceed"}}], "tracking": {}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}]}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "SEARCH", "url": "/1/fintech/calm/search", "validationMeta": null, "widgetTracking": null}, "data": [], "disabled": false, "formFieldType": "AUTO_SUGGEST_BOX", "freeFlow": true, "label": "Company Name", "mandatory": true, "name": "organization", "offline": false, "type": "AutoSuggestFormFieldValue", "dependencyList": [{"depKey": "employmentType", "depValue": {"ruleType": "REGEX", "regex": "\\bSalaried\\b", "errorMessage": "Complete this field to proceed"}}], "autoSuggestIcon": {"type": "ImageValue", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/06/2025/1afe2587-f33d-49f6-8cf0-ddb091c83648.png?q={@quality}", "width": 20, "height": 20}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}]}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Business Name", "mandatory": true, "name": "businessName", "placeholder": "Type here", "dependencyList": [{"depKey": "employmentType", "depValue": {"ruleType": "REGEX", "regex": "\\bSelfEmployed\\b", "errorMessage": "Complete this field to proceed"}}], "replaceInput": {"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"condition": null, "regex": "[^a-zA-Z\\s]", "replaceValue": "", "type": null, "warningMessage": null}, {"condition": null, "regex": "^\\S(.)*$", "replaceValue": " ", "type": null, "warningMessage": null}]}, "replaceInputList": [{"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"condition": null, "regex": "^\\s", "replaceValue": "", "type": null, "warningMessage": null}, {"condition": null, "regex": "[^a-zA-Z\\s]", "replaceValue": "", "type": null, "warningMessage": null}, {"condition": null, "regex": "\\s+", "replaceValue": " ", "type": null, "warningMessage": null}]}], "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "", "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": "normal", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"disabled": false, "formFieldType": "PRICE_TEXT_BOX_V0", "inputType": "NUMERIC", "label": "Net Monthly Income", "mandatory": true, "name": "income", "value": "", "placeholder": "Eg. 100,000", "maxCharacters": 9, "type": "PriceTextBoxFormFieldValueV0", "dependencyList": [{"depKey": "employmentType", "depValue": {"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\b(SelfEmployed|Salaried)\\b", "ruleType": "REGEX", "validateOnSubmit": false}}], "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"ruleType": "REGEX", "errorMessage": "Minimum income should be 13,500", "interactionType": null, "validateOnSubmit": false, "regex": "^(13[5-9]\\d{2}|1[4-9]\\d{3}|[2-9]\\d{4}|[1-9]\\d{5,})$"}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": "normal", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"formFieldType": "PRICE_TEXT_BOX_V0", "inputType": "NUMERIC", "label": "Extra monthly income (bonus, rent, interest, etc.)", "name": "bonusIncome", "placeholder": "Eg. 10,000", "maxCharacters": 9, "type": "PriceTextBoxFormFieldValueV0", "dependencyList": [{"depKey": "employmentType", "depValue": {"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\b(SelfEmployed|Salaried)\\b", "ruleType": "REGEX", "validateOnSubmit": false}}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": "normal", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": "normal", "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": "normal"}}}}, {"tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "formFieldType": "DROPDOWN", "name": "incomeSource", "label": "Select how you get your income", "mandatory": true, "disabled": false, "tracking": {}, "options": [{"id": "ONLINE", "title": "Bank transfer", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "CHEQUE", "title": "Cheque", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "CASH", "title": "CASH", "type": "PLAIN_TEXT", "fullWidthPress": false}], "selectedOption": {"id": "ONLINE", "title": "Bank transfer", "type": "PLAIN_TEXT", "fullWidthPress": false}, "displayType": "DROPDOWN", "stackType": "HORIZONTAL", "inputType": "CUSTOM_RADIO", "outerContainerStyle": {"fontSize": 14}, "containerStyle": {"gap": 8}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": {"content": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "AnnouncementV2Value", "image": {"type": "ImageValue", "aspectRatio": "262:185", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/04/04/2024/16390b7e-85b7-4d0a-b11c-f3c0e375ea04.png?q={@quality}", "height": 185, "width": 262}, "subTitle": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "lineHeight": 22, "textAlign": "center"}, "text": "Do not press back or switch apps."}}, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "lineHeight": 30, "textAlign": "center"}, "text": "Your application details are being processed..."}}}}, "type": "ANNOUNCEMENT_V2"}, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {"widgetName": "LV4_WORK_DETAILS_SUBMIT_BUTTON_CLICK"}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV4_WORK_DETAILS_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18, "title": "Confirm"}}, "buttonOrientation": null, "consent": null, "consentList": [{"consentType": "CHECKBOX", "consentId": "${consentIdMFI}", "consentFor": "${consentForMFI}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentMFI}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentMFI"}}}, {"consentType": "CHECKBOX", "consentId": "${consentIdNonPEP}", "consentFor": "${consentForNonPEP}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentNonPEP}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentNonPep"}}}, {"consentType": "CHECKBOX", "consentId": "${consentIdCKYC}", "consentFor": "${consentForCKYC}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentCKYC}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentCKYC"}}}], "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null, "containerStyle": {"padding": 24}}, "title": null}