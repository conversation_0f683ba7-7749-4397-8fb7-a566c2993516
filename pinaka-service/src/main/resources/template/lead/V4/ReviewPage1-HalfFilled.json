{"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formGroups": [{"formGroupContainerStyle": {"background": "linear-gradient(to left, #EBEFFF, #CBCEFF)", "border": "2px solid white", "borderBottomLeftRadius": 16, "borderBottomRightRadius": 16, "borderTopLeftRadius": 16, "borderTopRightRadius": 16, "marginBottom": 16}, "formGroupData": [{"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"borderTopLeftRadius": 16, "borderTopRightRadius": 16}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "PAN (Permanent Account Number)", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "panNumber"}, "style": {}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {}, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "DOB", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "formFieldType": "RICH_CARD", "mandatory": false, "name": "dob"}, "style": {"flex": 1}}, {"data": {"type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939", "title": "M"}}, "icon": null, "iconBackgroundColor": null, "iconSize": null, "next": null, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontWeight": "normal", "lineHeight": 20}, "text": "Gender", "textColor": "#667085"}}, "viewType": null}, "disabled": false, "displayMapper": {"M": "Male", "F": "Female", "O": "Others"}, "displayType": "KEY_VALUE_MAPPER", "formFieldType": "RICH_CARD", "mandatory": false, "name": "gender", "tracking": {}}, "style": {}}], "formGroupDataStyle": {"backgroundColor": "#FFF", "borderBottomColor": "#C7D7FE", "borderBottomLeftRadius": 16, "borderBottomRightRadius": 16, "borderTopLeftRadius": 16, "borderTopRightRadius": 16, "borderWidth": "4", "marginLeft": 4, "marginRight": 4, "marginBottom": 4, "padding": 24}, "formGroupHeader": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": {"type": "ImageValue", "alternateText": "Personal Details image icon", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/06/2025/db326f7e-ffd8-4753-bd03-745ac6984dcd.png?q={@quality}", "height": 36, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/06/2025/db326f7e-ffd8-4753-bd03-745ac6984dcd.png?q={@quality}", "width": 36}, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-79371862", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "540837788", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "panNumber", "noOfErrors": 1, "label": "PAN Number", "placeholder": "eg. **********", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter correct PAN", "interactionType": null, "validateOnSubmit": false, "regex": "^[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}$"}], "value": "", "tracking": {}, "suffix": null, "minCharacters": 10, "maxCharacters": 10, "autoCapitalize": "characters"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "DateFormFieldValueV0", "formFieldType": "DATE_V0", "name": "dob", "noOfErrors": 1, "label": "DOB", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "value": "", "tracking": {}, "suffix": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "formFieldType": "DROPDOWN", "name": "gender", "label": "Gender", "mandatory": true, "disabled": false, "containerStyle": {"display": "flex", "justifyContent": "space-around", "gap": 8}, "outerContainerStyle": {"fontSize": 14}, "tracking": {}, "options": [{"id": "M", "title": "Male", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "F", "title": "Female", "type": "PLAIN_TEXT", "fullWidthPress": false}, {"id": "O", "title": "Others", "type": "PLAIN_TEXT", "fullWidthPress": false}], "selectedOption": {"id": "M", "title": "Male", "type": "PLAIN_TEXT", "fullWidthPress": false}, "displayType": "DROPDOWN", "stackType": "HORIZONTAL", "inputType": "CUSTOM_RADIO"}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {}, "listTitle": null, "formId": "LV4_HALF_DETAILS_FILLED_PERSONAL_DETAILS_WIDGET_FORM_ID", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Personal details", "showClipboard": false, "containerStyle": {"marginBottom": 20}, "style": {"color": "#4D43FE", "fontSize": 17, "lineHeight": 24, "fontWeight": "bold"}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "LV4_HALF_DETAILS_FILLED_PERSONAL_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18, "borderColor": "#B5EF85"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {}, "validationMeta": null, "tracking": {"widgetName": "LV4_HALF_DETAILS_FILLED_PERSONAL_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON_CLICK"}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"background": "#FFFFFF", "padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "Edit details button", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 24, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 24}, "style": {"padding": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"backgroundColor": "", "borderTopLeftRadius": 16, "borderTopRightRadius": 16, "left": 0, "paddingBottom": 12, "paddingLeft": 16, "paddingRight": 16, "paddingTop": 12, "position": "sticky"}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold"}, "text": "Personal details", "textColor": "#1D2939"}}, "viewType": null}, "formGroupName": "LV4 Page 2 Personal Details Form Group"}, {"formGroupContainerStyle": {"background": "linear-gradient(to left, #FFF1FC, #f1b9f1)", "border": "2px solid white", "borderBottomLeftRadius": 16, "borderBottomRightRadius": 16, "borderTopLeftRadius": 16, "borderTopRightRadius": 16, "marginBottom": 16}, "formGroupData": [{"data": {"type": "DropdownFormFieldValueV0", "dehydratedOption": {"description": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontWeight": "normal"}, "text": "${houseNumber}, ${area}, ${pincodeDetails__city}, ${pincodeDetails__state} ${pincodeDetails__pincode} India", "textColor": "#667085", "title": "${houseNumber}, ${area}, ${pincodeDetails__city}, ${pincodeDetails__state} ${pincodeDetails__pincode} India"}}, "fullWidthPress": false, "id": "${ID}", "title": "Address ${INDEX}", "titleStyle": {"color": "#1D2939", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "type": "PLAIN_TEXT"}, "disabled": false, "displayType": "DROPDOWN", "formFieldType": "DROPDOWN_V0", "initialOptionsDataMap": [], "initialValue": {"address": ""}, "mandatory": true, "name": "address", "options": [{"type": "IMAGE", "disabled": true, "id": "FALLBACK_NO_OPTION", "image": {"type": "ImageValue", "alternateText": "No address found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/27/06/2025/136ea9f9-c340-47d7-8eeb-67fb6c668cba.png?q={@quality}", "height": 154, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/27/06/2025/136ea9f9-c340-47d7-8eeb-67fb6c668cba.png?q={@quality}", "width": 300}, "action": {"type": "CLIENT__INLINE_NAVIGATION", "params": {"doNotBroadcastToNestedForm": true, "screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-79371862", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "540837788", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": {}, "failCard": null, "formId": "LV4_HALF_DETAILS_FILLED_NEW_ADDRESS_FORM_ID", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "numberOfLines": 4, "formFieldType": "TEXT_BOX_V0", "label": "House/Flat/Floor number", "mandatory": true, "multiline": true, "name": "houseNumber", "noOfErrors": 1, "placeholder": "House/Flat/Floor number", "tracking": {}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "numberOfLines": 4, "formFieldType": "TEXT_BOX_V0", "label": "Apartment/Area/Locality", "mandatory": true, "multiline": true, "name": "area", "noOfErrors": 1, "placeholder": "Apartment/Area/Locality", "tracking": {}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CustomFormFieldValue", "containerStyle": null, "customFieldData": {"city": {"type": "TextBoxFormFieldValueV0", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "City", "mandatory": true, "name": "city", "placeholder": "", "outerContainerStyle": {"display": "none"}}, "customFieldType": "PINCODE_V0", "pincode": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/6/pincode/existence", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Pin Code", "mandatory": true, "maxCharacters": 6, "minCharacters": 6, "name": "pincode", "inputType": "NUMERIC", "noOfErrors": 1, "placeholder": "eg. 560103", "tracking": {}, "validationRegex": "^[1-9]{1}[0-9]{5}$", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter a valid pin code", "interactionType": null, "regex": "^(?!\\s*$)[1-9]{1}[0-9]{5}$", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, "state": {"type": "TextBoxFormFieldValueV0", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "State", "mandatory": true, "name": "state", "placeholder": "", "outerContainerStyle": {"display": "none"}}}, "disabled": false, "formFieldType": "CUSTOM", "grids": null, "inputStyle": null, "label": null, "mandatory": true, "name": "pincodeDetails", "noOfErrors": null, "outerContainerStyle": null, "tracking": null, "validationRuleList": null, "value": null}}], "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "LV4_HALF_DETAILS_FILLED_NEW_ADDRESS_BOTTOM_SHEET_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18, "borderColor": "#B5EF85"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {}, "validationMeta": null, "tracking": {"widgetName": "LV4_HALF_DETAILS_FILLED_NEW_ADDRESS_BOTTOM_SHEET_SUBMIT_BUTTON_CLICK"}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"background": "#FFFFFF", "padding": 24, "width": "calc(100% - 48px)"}}, "title": null}, "tracking": {}}}]}}}, "value": {"type": "RichButtonValue", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "buttonColor": "inherit"}}], "outerContainerStyle": {"background": "#FFF", "borderBottomLeftRadius": 16, "borderBottomRightRadius": 16, "borderTopLeftRadius": 16, "borderTopRightRadius": 16, "display": "block", "minWidth": 212, "padding": 24, "paddingBottom": 28}, "stackType": "HORIZONTAL", "title": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontWeight": "normal"}, "text": "Communication Address", "textColor": "#1D2939", "title": "Communication Address"}, "titleDescContainerStyle": {"alignItems": "baseline"}}, "style": {}}], "formGroupDataStyle": {"marginLeft": 4, "marginRight": 4, "marginBottom": 4}, "formGroupHeader": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "cardType": null, "description": null, "icon": {"type": "ImageValue", "alternateText": "Current Address icon", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/06/2025/35381e73-ef8e-48b2-bb75-cb1ea2688d04.png?q={@quality}", "height": 36, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/06/2025/35381e73-ef8e-48b2-bb75-cb1ea2688d04.png?q={@quality}", "width": 36}, "iconBackgroundColor": null, "iconSize": null, "next": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"doNotBroadcastToNestedForm": true, "screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-79371862", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "540837788", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "houseNumber", "noOfErrors": 1, "label": "House/Flat/Floor number", "placeholder": "House/Flat/Floor number", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "value": "", "tracking": {}, "multiline": true, "numberOfLines": 4}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": {"widgetName": "LV4_HALF_DETAILS_FILLED_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "area", "noOfErrors": 1, "label": "Apartment/Area/Locality", "placeholder": "Apartment/Area/Locality", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "value": "", "tracking": {}, "multiline": true, "numberOfLines": 4}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "CustomFormFieldValue", "formFieldType": "CUSTOM", "name": "pincodeDetails", "noOfErrors": null, "label": null, "subLabel": null, "placeholder": null, "mandatory": true, "disabled": false, "cursorColor": null, "containerStyle": null, "validationRuleList": null, "dependencyList": null, "outerContainerStyle": null, "inputStyle": null, "value": null, "tracking": null, "grids": null, "customFieldData": {"customFieldType": "PINCODE_V0", "pincode": {"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "pincode", "noOfErrors": 1, "label": "Pin Code", "placeholder": "eg. 560103", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter a valid pin code", "interactionType": null, "validateOnSubmit": false, "regex": "^(?!\\s*$)[1-9]{1}[0-9]{5}$"}], "value": "", "tracking": {}, "inputType": "NUMERIC", "validationRegex": "^[1-9]{1}[0-9]{5}$", "minCharacters": 6, "maxCharacters": 6}, "action": {"screenType": null, "type": "NAVIGATION", "omnitureData": null, "originalUrl": null, "url": "/6/pincode/existence", "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "state": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "state", "label": "State", "placeholder": "", "mandatory": true, "disabled": true, "outerContainerStyle": {"display": "none"}}, "city": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "city", "label": "City", "placeholder": "", "mandatory": true, "disabled": true, "outerContainerStyle": {"display": "none"}}}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": {}, "listTitle": null, "formId": "LV4_HALF_DETAILS_FILLED_ADDRESS_DETAILS_BOTTOM_SHEET", "persistFormData": false, "resultStoreKey": null, "title": {"type": "RichTextValue", "text": "Add new address", "showClipboard": false, "containerStyle": {"marginBottom": 20}, "style": {"color": "#4D43FE", "fontSize": 17, "lineHeight": 24, "fontWeight": "bold"}}, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": {"widgetName": "LV4_HALF_DETAILS_FILLED_ADDRESS_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18, "borderColor": "#B5EF85"}, "action": {"screenType": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {}, "tracking": {"widgetName": "LV4_HALF_DETAILS_FILLED_ADDRESS_DETAILS_BOTTOM_SHEET_SUBMIT_BUTTON_CLICK"}, "validationMeta": null, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": {"background": "#FFFFFF", "padding": 24, "width": "calc(100% - 48px)"}}}, "tracking": {}}}]}}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__INLINE_NAVIGATION", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "buttonColor": "inherit", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "image": {"type": "ImageValue", "alternateText": "Add new Address button", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/b2ebdef2-c87d-47d2-8573-05aa1782edbd.png?q=100", "height": 24, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/b2ebdef2-c87d-47d2-8573-05aa1782edbd.png?q=100", "width": 24}, "style": {"padding": 0}}}, "orientation": "LANDSCAPE", "overlayTagValue": null, "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"borderTopLeftRadius": 12, "borderTopRightRadius": 12, "left": 0, "paddingBottom": 12, "paddingLeft": 16, "paddingRight": 16, "paddingTop": 12, "position": "sticky"}, "subStatusText": null, "superTitle": null, "tagValue": null, "textContainerStyle": null, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "bold"}, "text": "Current Address", "textColor": "#1D2939"}}, "viewType": null}, "formGroupName": "LV4 Page 2 Address Details Form Group"}], "formId": "LV4_ALL_DETAILS_FILLED", "gap": 0, "listTitle": null, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": null, "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"tracking": {"widgetName": "LV4_HALF_DETAILS_FILLED_SUBMIT_BUTTON"}, "action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {"widgetName": "LV4_HALF_DETAILS_FILLED_SUBMIT_BUTTON_CLICK"}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18, "title": "Confirm & Continue"}}, "buttonOrientation": null, "consent": null, "consentList": [], "containerStyle": {}, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": null}