{"subWidget": null, "clientPowered": false, "renderableComponents": null, "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": null, "cardSummaryTitle": null, "formId": "LV3_NAME_PAGE", "persistFormData": false, "resultStoreKey": null, "title": null, "subTitle": null, "formGroups": [{"formGroupName": "LV3 Name Page Basic Details Group", "formGroupHeader": {"type": "RichCard", "icon": null, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Basic Details", "showClipboard": false, "textColor": "#1D2939", "style": {"color": "#1D2939", "fontSize": 16, "fontFamily": "Inter", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": null, "tagValue": null, "overlayTagValue": null, "style": {"paddingBottom": 12, "paddingTop": 12, "borderTopLeftRadius": 12, "borderTopRightRadius": 12, "position": "sticky", "left": 0, "flex": 1}, "cardPressEnabled": null, "viewType": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null, "next": {"tracking": null, "trackingData": null, "value": {"type": "RichButtonValue", "image": {"type": "ImageValue", "alternateText": "Edit Personal Details <PERSON>", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 20, "height": 20}, "buttonViewType": "SOLID", "buttonSize": "LARGE", "disabled": false, "buttonColor": "inherit", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "style": {"padding": 0}}, "action": {"screenType": null, "type": "CLIENT__INLINE_NAVIGATION", "omnitureData": null, "originalUrl": null, "url": null, "params": {"screenDetails": {"isBottomSheet": false}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "#F1F2F4", "orientation": "", "theme": "light"}, "pageHash": "*********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": "Personal Loan", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-311d7", "applicationId": "APP2502281559521932836675212131828233617", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "LV3_NAME_PAGE"}}}, "pageMeta": {"baseImpressionId": "5dabdcaf-9246-44ae-9e87-a45d6fcbb631", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"orientation": "", "padding": "0,0,0,0", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-**********", "elementId": "1-BANNER", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "BANNER", "viewType": "BANK_LOGO", "data": {"bannerListViewType": "REGULAR", "clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "ImageValue", "aspectRatio": "412:256", "dynamicImageUrl": "${BANNER_URL}", "height": 0, "width": 0}}], "subWidget": null}, "tracking": {}}}, {"slotType": "WIDGET", "id": 2, "parentId": 0, "layoutParams": {"backgroundColor": "#F9FAFB", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,0", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "********", "elementId": "2-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "fullName", "label": "Full Name (as per PAN)", "mandatory": true, "disabled": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "interactionType": null, "validateOnSubmit": false, "regex": "\\S"}], "value": "", "tracking": {"sendImpression": true, "fieldName": "fullName", "sendEdits": true, "prefill": true, "prefilledValue": "", "sendInlineError": true}, "suffix": {"tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"screenType": null, "type": "EDIT", "omnitureData": null, "originalUrl": null, "url": null, "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "replaceInput": {"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"type": null, "condition": null, "warningMessage": null, "regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"type": null, "condition": null, "warningMessage": null, "regex": "^\\S(.)*$", "replaceValue": " "}]}, "replaceInputList": [{"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"type": null, "condition": null, "warningMessage": null, "regex": "^\\s", "replaceValue": ""}, {"type": null, "condition": null, "warningMessage": null, "regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"type": null, "condition": null, "warningMessage": null, "regex": "\\s+", "replaceValue": " "}]}], "placeholder": ""}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "phoneNumber", "label": "Phone Number", "mandatory": true, "disabled": true, "value": "", "tracking": {"sendImpression": true, "fieldName": "phoneNumber", "sendEdits": true, "prefill": true, "prefilledValue": "", "sendInlineError": true}, "minCharacters": 10, "maxCharacters": 13}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": null, "cardSummaryTitle": null, "formId": "LV3_NAME_PAGE", "persistFormData": false, "resultStoreKey": null, "title": null, "subTitle": null, "submitButton": {"type": "SubmitButtonValue", "consent": {"consentType": "CHECKBOX", "consentId": "${consentId}", "consentFor": "${consentFor}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": {"widgetName": "LV3_NAME_PAGE_SUBMIT_BUTTON"}, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consent}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}, "consentList": null, "button": {"tracking": null, "trackingData": null, "value": {"iconRight": {"alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/05/2025/d1d3abea-1f8f-4445-b04e-741962b924ac.png?q={@quality}", "height": 20, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/05/2025/d1d3abea-1f8f-4445-b04e-741962b924ac.png?q={@quality}", "type": "ImageValue", "width": 20}, "type": "RichTextButtonValue", "title": "Review Details"}, "action": {"screenType": null, "type": "NAVIGATION", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": null}}, "tracking": {}}}]}}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "statusText": null, "subStatusText": null, "actionButtons": null}, "formGroupData": [{"style": {"flex": 1, "alignItems": "flex-start"}, "data": {"type": "CardFormFieldValue", "formFieldType": "RICH_CARD", "name": "fullName", "mandatory": false, "disabled": false, "card": {"type": "RichCard", "icon": {"type": "ImageValue", "alternateText": "Full Name Icon", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/d1c0f669-123b-45f3-9b98-de272f4b4ee0.png?q=100", "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/d1c0f669-123b-45f3-9b98-de272f4b4ee0.png?q=100", "width": 40, "height": 40}, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Full name (as per PAN)", "showClipboard": false, "textColor": "#667085", "style": {"color": "#667085", "fontSize": 14, "lineHeight": 22, "fontFamily": "Inter", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": {"sendImpression": true, "fieldName": "fullName", "sendEdits": true, "prefill": true, "prefilledValue": "", "sendInlineError": true}, "trackingData": null, "value": {"type": "RichTextValue", "text": "", "showClipboard": false, "textColor": "#1D2939", "style": {"color": "#1D2939", "fontSize": 16, "lineHeight": 24, "fontFamily": "Inter", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "tagValue": null, "overlayTagValue": null, "style": {"backgroundColor": "#F2F4F7", "padding": 16, "borderTopLeftRadius": 8, "borderTopRightRadius": 8, "paddingBottom": 12, "paddingTop": 12}, "cardPressEnabled": null, "initialValue": {"fullName": ""}, "viewType": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null, "next": null, "statusText": null, "subStatusText": null, "actionButtons": null}}}, {"style": {"flex": 1}, "data": {"type": "CardFormFieldValue", "formFieldType": "RICH_CARD", "name": "phoneNumber", "mandatory": false, "disabled": false, "card": {"type": "RichCard", "icon": {"type": "ImageValue", "alternateText": "Phone Number Icon", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/bd661f52-79ab-4f92-9207-62057cbd5855.png?q=100", "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/bd661f52-79ab-4f92-9207-62057cbd5855.png?q=100", "width": 40, "height": 40}, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Phone no.", "showClipboard": false, "textColor": "#667085", "style": {"color": "#667085", "fontSize": 14, "lineHeight": 22, "fontFamily": "Inter", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": {"sendImpression": true, "fieldName": "phoneNumber", "sendEdits": true, "prefill": true, "prefilledValue": "", "sendInlineError": true}, "trackingData": null, "value": {"type": "RichTextValue", "text": "", "showClipboard": false, "textColor": "#667085", "style": {"color": "#1D2939", "fontSize": 16, "lineHeight": 24, "fontFamily": "Inter", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "tagValue": null, "overlayTagValue": null, "style": {"backgroundColor": "#F2F4F7", "padding": 16, "borderBottomRightRadius": 8, "borderBottomLeftRadius": 8, "paddingBottom": 12, "paddingTop": 12}, "initialValue": {"phoneNumber": ""}, "cardPressEnabled": null, "viewType": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null, "next": null, "statusText": null, "subStatusText": null, "actionButtons": null}}}], "formGroupDataStyle": {"gap": 2, "flexDirection": "column"}}], "submitButton": {"type": "SubmitButtonValue", "consent": {"consentType": "CHECKBOX", "consentId": "${consentId}", "consentFor": "${consentFor}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": {"widgetName": "LV3_NAME_PAGE_SUBMIT_BUTTON"}, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consent}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}, "consentList": null, "button": {"tracking": null, "trackingData": null, "value": {"iconRight": {"alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/05/2025/d1d3abea-1f8f-4445-b04e-741962b924ac.png?q={@quality}", "height": 20, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/05/2025/d1d3abea-1f8f-4445-b04e-741962b924ac.png?q={@quality}", "type": "ImageValue", "width": 20}, "type": "RichTextButtonValue", "title": "Review details"}, "action": {"screenType": null, "type": "NAVIGATION", "omnitureData": null, "originalUrl": null, "url": "/api/sm/1/application/form", "params": {}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": null, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": true, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": null}}