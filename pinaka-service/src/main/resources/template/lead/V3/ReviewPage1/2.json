{"CACHE_INVALIDATION_TTL": "0", "META_INFO": {"abContext": {"abWrapper": []}, "appConfigHash": null, "clientConfigHashMap": null, "dcInfo": null, "omnitureInfo": null}, "REQUEST": null, "REQUEST-ID": "d13d6762-818d-40d8-9e1f-9750c2b628ed", "RESPONSE": {"actionSuccess": false, "marketPlaceTrackingDataMap": {}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "#FFFFFF", "orientation": "", "theme": "light"}, "pageHash": "*********", "pageLevelSlots": {}, "pageNudges": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": "Review Page (1/2)", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCE47ACFD48746449697FBE83F59F78BAFZ", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2507022010027459691295942378953898499", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_1"}}}, "pageMeta": {"baseImpressionId": "d13d6762-818d-40d8-9e1f-9750c2b628ed", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-**********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "REVIEW_PAGE_1", "gap": 0, "listTitle": null, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "REVIEW_PAGE_1_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "PAN Number", "mandatory": true, "maxCharacters": 10, "minCharacters": 10, "name": "panNumber", "noOfErrors": 1, "placeholder": "eg. **********", "suffix": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "EDIT", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "height": 16, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16}}}, "tracking": {"sendImpression": true, "fieldName": "panNumber", "sendEdits": true, "prefill": true, "prefilledValue": "**********", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter correct PAN", "interactionType": null, "regex": "^[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}$", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "**********"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "DATE", "label": "DOB", "mandatory": true, "maxValue": "2007-07-02T20:10:05.725+05:30", "minValue": "1965-07-02T20:10:05.725+05:30", "name": "dob", "noOfErrors": 1, "placeholder": "enter your Date of Birth", "suffix": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "EDIT", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "height": 16, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16}}}, "tracking": {"sendImpression": true, "fieldName": "dob", "sendEdits": true, "prefill": true, "prefilledValue": "12/07/1996", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "12/07/1996"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "disabled": false, "displayType": "DROPDOWN", "formFieldType": "DROPDOWN", "label": "Gender", "mandatory": true, "name": "gender", "options": [{"fullWidthPress": false, "id": "M", "title": "Male", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "F", "title": "Female", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "O", "title": "Others", "type": "PLAIN_TEXT"}], "selectedOption": {"fullWidthPress": false, "id": "M", "title": "Male", "type": "PLAIN_TEXT"}, "stackType": "HORIZONTAL", "tracking": {"sendImpression": true, "fieldName": "gender", "sendEdits": true, "prefill": true, "prefilledValue": "M", "sendInlineError": true}}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "email", "prefill": "true", "prefilledValue": "abc", "sendEdits": "true", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "none", "disabled": false, "formFieldType": "TEXT_BOX_V0", "inputType": "EMAIL_ADDRESS", "label": "Official Email ID", "mandatory": true, "name": "email", "noOfErrors": 1, "placeholder": "enter full email ID", "suffix": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "EDIT", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "height": 16, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16}}}, "tracking": {"sendImpression": true, "fieldName": "email", "sendEdits": true, "prefill": true, "prefilledValue": "<EMAIL>", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter correct Email", "interactionType": null, "regex": "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,6})$", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "<EMAIL>", "warningValidation": {"condition": ["gmail.co.za", "gmail.com", "gmail.com.au", "gmail.com.br", "gmail.ru", "hotmail.be", "hotmail.ca", "hotmail.ch", "hotmail.co", "hotmail.co.il", "hotmail.co.jp", "hotmail.co.nz", "hotmail.co.uk", "hotmail.co.za", "hotmail.com", "hotmail.com.ar", "hotmail.com.au", "hotmail.com.br", "hotmail.com.mx", "hotmail.com.tr", "hotmail.de", "hotmail.es", "hotmail.fi", "hotmail.fr", "hotmail.it", "hotmail.kg", "hotmail.kz", "hotmail.my", "hotmail.nl", "hotmail.ro", "hotmail.roor", "hotmail.ru", "rediff.com", "rediffmail.com", "rediffmailpro.com", "yahoo.ae", "yahoo.at", "yahoo.be", "yahoo.ca", "yahoo.ch", "yahoo.cn", "yahoo.co", "yahoo.co.id", "yahoo.co.il", "yahoo.co.in", "yahoo.co.jp", "yahoo.co.kr", "yahoo.co.nz", "yahoo.co.th", "yahoo.co.uk", "yahoo.co.za", "yahoo.com", "yahoo.com.ar", "yahoo.com.au", "yahoo.com.br", "yahoo.com.cn", "yahoo.com.co", "yahoo.com.hk", "yahoo.com.is", "yahoo.com.mx", "yahoo.com.my", "yahoo.com.ph", "yahoo.com.ru", "yahoo.com.sg", "yahoo.com.tr", "yahoo.com.tw", "yahoo.com.vn", "yahoo.cz", "yahoo.de", "yahoo.dk", "yahoo.es", "yahoo.fi", "yahoo.fr", "yahoo.gr", "yahoo.hu", "yahoo.ie", "yahoo.in", "yahoo.it", "yahoo.jp", "yahoo.net", "yahoo.nl", "yahoo.no", "yahoo.pl", "yahoo.pt", "yahoo.ro", "yahoo.ru", "yahoo.se", "yahoofs.com", "yahoomail.com", "aol.com", "msn.com", "wanadoo.fr", "orange.fr", "comcast.net", "live.com", "free.fr", "gmx.de", "web.de", "yandex.ru", "ymail.com", "libero.it", "outlook.com", "uol.com.br", "bol.com.br", "mail.ru", "cox.net", "sbcglobal.net", "sfr.fr", "live.fr", "verizon.net", "live.co.uk", "googlemail.com", "ig.com.br", "live.nl", "bigpond.com", "terra.com.br", "neuf.fr", "alice.it", "rocketmail.com", "att.net", "laposte.net", "facebook.com", "bellsouth.net", "charter.net", "rambler.ru", "tiscali.it", "shaw.ca", "sky.com", "earthlink.net", "optonline.net", "freenet.de", "t-online.de", "aliceadsl.fr", "virgilio.it", "home.nl", "qq.com", "telenet.be", "me.com", "tiscali.co.uk", "voila.fr", "gmx.net", "mail.com", "planet.nl", "tin.it", "live.it", "ntlworld.com", "arcor.de", "frontiernet.net", "hetnet.nl", "live.com.au", "zonnet.nl", "club-internet.fr", "juno.com", "optusnet.com.au", "blueyonder.co.uk", "bluewin.ch", "skynet.be", "sympatico.ca", "windstream.net", "mac.com", "centurytel.net", "chello.nl", "live.ca", "aim.com", "bigpond.net.au", "super.money"], "type": "emailHostCheck", "warningMessage": "Please recheck correctness of your email address"}}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "MultiDropdownFormFieldValue", "defaultValue": "Salaried", "disabled": false, "formFieldType": "MULTI_DROPDOWN", "label": "Select employment type", "mandatory": true, "name": "employmentType", "options": [{"id": "Salaried", "subtitle": "You get a monthly salary", "title": "Salari<PERSON> (Job)"}, {"id": "SelfEmployed", "subtitle": "You have a business or shop", "title": "Self Employed (Own Business)"}], "tracking": {"sendImpression": true, "fieldName": "employmentType", "sendEdits": true, "prefill": true, "prefilledValue": "Salaried", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Select employment type to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "Salaried", "viewType": "DROPDOWN"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "MultiDropdownFormFieldValue", "defaultValue": "Personal", "disabled": false, "formFieldType": "MULTI_DROPDOWN", "label": "Purpose of <PERSON>an", "mandatory": true, "name": "loanPurpose", "options": [{"id": "PERSONAL", "title": "Personal"}, {"id": "TRAVEL", "title": "Travel"}, {"id": "HOME_REPAIR", "title": "Home - repair"}, {"id": "MARRIAGE", "title": "Marriage"}, {"id": "MEDICAL", "title": "Medical"}, {"id": "EDUCATION", "title": "Education"}, {"id": "CONSUMER_DURABLES", "title": "Consumer - durables"}], "value": "PERSONAL", "viewType": "DROPDOWN"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "House/Flat/Floor number", "mandatory": true, "maxCharacters": 250, "minCharacters": 3, "name": "houseNumber", "noOfErrors": 1, "placeholder": "House/Flat/Floor number", "suffix": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "EDIT", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "height": 16, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16}}}, "tracking": {"sendImpression": true, "fieldName": "houseNumber", "sendEdits": true, "prefill": true, "prefilledValue": "HSR", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "HSR"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Apartment/Area/Locality", "mandatory": true, "maxCharacters": 250, "minCharacters": 3, "name": "area", "noOfErrors": 1, "placeholder": "Apartment/Area/Locality", "suffix": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "EDIT", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "height": 16, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16}}}, "tracking": {"sendImpression": true, "fieldName": "area", "sendEdits": true, "prefill": true, "prefilledValue": "ASD", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "ASD"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CustomFormFieldValue", "containerStyle": null, "cursorColor": null, "customFieldData": {"city": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "City", "mandatory": true, "name": "city", "placeholder": ""}, "customFieldType": "PINCODE_V0", "pincode": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/6/pincode/existence", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Pin Code", "mandatory": true, "maxCharacters": 6, "minCharacters": 6, "name": "pincode", "noOfErrors": 1, "placeholder": "eg. 560103", "suffix": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "EDIT", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "height": 16, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16}}}, "tracking": {"sendImpression": true, "fieldName": "pincode", "sendEdits": true, "prefill": true, "prefilledValue": "560102", "sendInlineError": true}, "validationRegex": "^[1-9]{1}[0-9]{5}$", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter a valid pin code", "interactionType": null, "regex": "^(?!\\s*$)[1-9]{1}[0-9]{5}$", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "560102"}}, "state": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "State", "mandatory": true, "name": "state", "placeholder": ""}}, "dependencyList": null, "disabled": false, "formFieldType": "CUSTOM", "grids": null, "inputStyle": null, "label": null, "mandatory": true, "name": "pincodeDetails", "noOfErrors": null, "outerContainerStyle": null, "placeholder": null, "subLabel": null, "tracking": null, "validationRuleList": null, "value": null}}], "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "VEehtz64cHnmkHlj", "taskKey": "reviewPage1", "applicationId": "APP2507022010027459691295942378953898499", "taskId": "LMuQF_5QkONwhKNP", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmCPLnJ05MRfKKYcqLnUmbdITHdzkyMMjiRplI1uhE97G7a9IYEdgVTzuE/mtiGzbvAnVz5rZla5JMuE816TKOT1"}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next"}}, "buttonOrientation": null, "consent": null, "consentList": null, "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": null}, "tracking": {}}}]}}, "SESSION": {"accountId": "ACCE47ACFD48746449697FBE83F59F78BAFZ", "asn": null, "at": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjhlM2ZhMGE3LTJmZDMtNGNiMi05MWRjLTZlNTMxOGU1YTkxZiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KMlZQE_ZGJWubsMPrBIdPRGXL89OCo87qgHfjPChV5M", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "flipkartFirstUser": false, "isLoggedIn": true, "isUPISessionActive": false, "kaction": null, "lastName": "<PERSON><PERSON><PERSON>", "mobileNumber": null, "nsid": "3.**********************************.*************.**********************************", "rt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjhlM2ZhMGE3LTJmZDMtNGNiMi05MWRjLTZlNTMxOGU1YTkxZiJ9.****************************************************************************************************************************************************************************************************************************************************************************************.VuoWw4K98V013JemXT1NgbIZtb0egNHzMsjXPqMAt24", "secureToken": "**********************************:**********************************", "sn": "**********************************.TOKD2BADCB4E7144A689908C7EF4B819DED.1751467205.LI", "tracking": null, "ts": 0, "twoFa": false, "upiSessionPolicy": null, "vid": "**********************************"}, "STATUS_CODE": 200}