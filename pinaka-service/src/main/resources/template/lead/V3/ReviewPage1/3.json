{"CACHE_INVALIDATION_TTL": "0", "META_INFO": {"abContext": {"abWrapper": []}, "appConfigHash": null, "clientConfigHashMap": null, "dcInfo": null, "omnitureInfo": null}, "REQUEST": null, "REQUEST-ID": "493149ba-38b5-4361-989c-6a48b0e294e0", "RESPONSE": {"actionSuccess": false, "marketPlaceTrackingDataMap": {}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "#FFFFFF", "orientation": "", "theme": "light"}, "pageHash": "**********", "pageLevelSlots": {}, "pageNudges": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": "Review Page (2/2)", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCE47ACFD48746449697FBE83F59F78BAFZ", "pageType": "DYNAMIC", "pageId": "perso-dynam-fa189", "applicationId": "APP2507022010027459691295942378953898499", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "REVIEW_PAGE_2"}}}, "pageMeta": {"baseImpressionId": "493149ba-38b5-4361-989c-6a48b0e294e0", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-**********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "WORK_DETAILS", "gap": 0, "listTitle": null, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "REVIEW_PAGE_2_FORM"}, "trackingData": null, "value": {"type": "AutoSuggestFormFieldValue", "action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "SEARCH", "url": "/1/fintech/calm/search", "validationMeta": null, "widgetTracking": null}, "data": [], "disabled": false, "formFieldType": "AUTO_SUGGEST_BOX", "freeFlow": true, "label": "Company", "mandatory": true, "name": "organization", "offline": false, "tracking": {"sendImpression": true, "fieldName": "organization", "sendEdits": true, "prefill": true, "prefilledValue": "SCAPIC INNOVATIONS PRIVATE LIMITED", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": {"fullWidthPress": false, "id": "2669733", "title": "SCAPIC INNOVATIONS PRIVATE LIMITED", "type": "PLAIN_TEXT"}}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PriceTextBoxFormFieldValueV0", "autoCapitalize": "none", "disabled": false, "formFieldType": "PRICE_TEXT_BOX_V0", "inputType": "NUMERIC", "label": "Net Monthly Income", "mandatory": true, "name": "income", "noOfErrors": 1, "placeholder": "Eg. 100,000", "tracking": {"sendImpression": true, "fieldName": "income", "sendEdits": true, "prefill": true, "prefilledValue": "1000001", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "1000001"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"sendImpression": "true", "fieldName": "bonusIncome", "prefill": "true", "prefilledValue": "", "sendEdits": "true", "sendInlineError": "true"}, "trackingData": null, "value": {"type": "PriceTextBoxFormFieldValueV0", "autoCapitalize": "none", "disabled": false, "formFieldType": "PRICE_TEXT_BOX_V0", "inputType": "NUMERIC", "label": "Extra monthly income (bonus, rent, interest, etc.)", "mandatory": false, "maxCharacters": 9, "name": "bonusIncome", "placeholder": "Eg. 10,000", "tracking": {"sendImpression": true, "fieldName": "bonusIncome", "sendEdits": true, "prefill": true, "prefilledValue": "100001", "sendInlineError": true}, "value": "100001"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "disabled": false, "formFieldType": "MULTI_DROPDOWN", "label": "Select how you get your income", "mandatory": true, "name": "incomeSource", "options": [{"id": "CHEQUE", "title": "CHEQUE"}, {"id": "CASH", "title": "CASH"}, {"id": "ONLINE", "title": "ONLINE"}], "tracking": {"sendImpression": true, "fieldName": "incomeSource", "sendEdits": true, "prefill": true, "prefilledValue": "ONLINE", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": "ONLINE"}}], "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": {"content": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "AnnouncementV2Value", "image": {"type": "ImageValue", "aspectRatio": "262:185", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/04/04/2024/16390b7e-85b7-4d0a-b11c-f3c0e375ea04.png?q={@quality}", "height": 185, "width": 262}, "subTitle": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "lineHeight": 22, "textAlign": "center"}, "text": "Do not press back or switch apps."}}, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "lineHeight": 30, "textAlign": "center"}, "text": "Your application details are being processed..."}}}}, "type": "ANNOUNCEMENT_V2"}, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "VEehtz64cHnmkHlj", "taskKey": "reviewPage2", "applicationId": "APP2507022010027459691295942378953898499", "taskId": "VW4zxvL3DftUV2FF", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmCPLnJ05MRfKKYcqLnUmbdITHdzkyMMjiRplI1uhE97G7a9IYEdgVTzuE/mtiGzbvAnVz5rZla5JMuE816TKOT1"}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": {"fdpTracking": null, "omnitureTracking": null}, "value": {"type": "RichTextButtonValue", "title": "Confirm"}}, "buttonOrientation": null, "consent": null, "consentList": [{"checkboxField": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "I confirm that my household income is above INR 3,00,000 per annum."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "MFI", "consentId": "003", "consentType": "CHECKBOX"}, {"checkboxField": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "I confirm that I am a major (above 18 years of age), resident of India and am currently residing in India. I also confirm that I am not a tax resident of any other country and am not a politically exposed person"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "NON_PEP", "consentId": "002", "consentType": "CHECKBOX"}, {"checkboxField": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "I authorise <a href='https://www.flipkart.com/pages/scapic-lp-tnc'>SIPL’s Partners</a> to fetch my records from CKYCR (CERSAI) for processing of my loan application."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "CKYC", "consentId": "004", "consentType": "CHECKBOX"}], "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": null}, "tracking": {}}}]}}, "SESSION": {"accountId": "ACCE47ACFD48746449697FBE83F59F78BAFZ", "asn": null, "at": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjNhNzdlZTgxLTRjNWYtNGU5Ni04ZmRlLWM3YWMyYjVlOTA1NSJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cWLpH9Fx5RVx0vRPUWudJYRM3xXne1hEa7edzYOYEYI", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "flipkartFirstUser": false, "isLoggedIn": true, "isUPISessionActive": false, "kaction": null, "lastName": "<PERSON><PERSON><PERSON>", "mobileNumber": null, "nsid": "3.**********************************.*************.**********************************", "rt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjNhNzdlZTgxLTRjNWYtNGU5Ni04ZmRlLWM3YWMyYjVlOTA1NSJ9.****************************************************************************************************************************************************************************************************************************************************************************************.VhhWBkCB3IV4MmH7aZRkMKPwGAaOwm-AyvdoQDzp08k", "secureToken": "**********************************:**********************************", "sn": "**********************************.TOKD2BADCB4E7144A689908C7EF4B819DED.1751467243.LI", "tracking": null, "ts": 0, "twoFa": false, "upiSessionPolicy": null, "vid": "**********************************"}, "STATUS_CODE": 200}