{"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "formGroups": [{"formGroupName": "LV3 Page 1 Personal Details Filled Without Address Details : Personal Details", "formGroupHeader": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Personal Details image icon", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/6698d25b-71a2-48b0-ad09-8520b565ff64.png?q=100", "height": 36, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/6698d25b-71a2-48b0-ad09-8520b565ff64.png?q=100", "width": 36}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"backgroundColor": "#C7D7FE", "borderTopLeftRadius": 12, "borderTopRightRadius": 12, "paddingBottom": 12, "paddingTop": 12, "paddingLeft": 16, "paddingRight": 16, "position": "sticky", "left": 0, "marginRight": 24}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "Inter", "fontSize": 16, "fontWeight": "bold"}, "text": "Personal details", "textColor": "#1D2939"}}, "viewType": null, "next": {"action": {"type": "CLIENT__INLINE_NAVIGATION", "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": {}, "failCard": null, "formId": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "PAN Number", "mandatory": true, "maxCharacters": 10, "minCharacters": 10, "name": "panNumber", "noOfErrors": 1, "placeholder": "eg. **********", "suffix": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "EDIT", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "height": 16, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16}}}, "tracking": {"sendImpression": true, "fieldName": "panNumber", "sendEdits": true, "prefill": true, "prefilledValue": "", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter correct PAN", "interactionType": null, "regex": "^[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}$", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DateFormFieldValue", "disabled": false, "formFieldType": "DATE", "label": "DOB", "mandatory": true, "maxValue": "2007-03-18T14:22:41.155+05:30", "minValue": "1965-03-18T14:22:41.155+05:30", "name": "dob", "noOfErrors": 1, "suffix": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "EDIT", "url": null, "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "height": 16, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16}}}, "tracking": {"sendImpression": true, "fieldName": "dob", "sendEdits": true, "prefill": true, "prefilledValue": "", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "disabled": false, "displayType": "DROPDOWN", "formFieldType": "DROPDOWN", "containerStyle": {"display": "flex", "gap": 20, "justifyContent": "initial"}, "label": "Gender", "mandatory": true, "name": "gender", "options": [{"fullWidthPress": false, "id": "M", "title": "Male", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "F", "title": "Female", "type": "PLAIN_TEXT"}, {"fullWidthPress": false, "id": "O", "title": "Others", "type": "PLAIN_TEXT"}], "selectedOption": {"fullWidthPress": false, "id": "M", "title": "Male", "type": "PLAIN_TEXT"}, "stackType": "HORIZONTAL"}}], "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Done"}}, "buttonOrientation": null, "consent": null, "consentList": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": {"text": "Personal details", "style": {"color": "#212121", "lineHeight": 24, "fontSize": 17, "fontWeight": "bold"}, "containerStyle": {"marginBottom": 20}}}, "tracking": {}}}]}}}, "value": {"type": "RichButtonValue", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "buttonColor": "inherit", "image": {"type": "ImageValue", "alternateText": "Edit button", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "height": 20, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/3b0868be-1914-4142-9946-0a511e92eb64.png?q=100", "width": 20}}}}, "formGroupData": [{"style": {}, "data": {"formFieldType": "RICH_CARD", "name": "panNumber", "type": "CardFormFieldValue", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "Inter", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "PAN (Permanent Account Number)", "textColor": "#667085"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "Inter", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "viewType": null, "initialValue": {"panNumber": ""}}}}, {"style": {"flex": 1}, "data": {"formFieldType": "RICH_CARD", "name": "dob", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "Inter", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "DOB", "textColor": "#667085"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "Inter", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "", "textColor": "#1D2939"}}, "viewType": null, "initialValue": {"dob": ""}}, "type": "CardFormFieldValue"}}, {"style": {}, "data": {"formFieldType": "RICH_CARD", "name": "gender", "card": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#667085", "fontFamily": "Inter", "fontSize": 14, "fontWeight": "bold", "lineHeight": 22}, "text": "Gender", "textColor": "#667085"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "Inter", "fontSize": 16, "fontWeight": "bold", "lineHeight": 24}, "text": "M", "title": "M", "textColor": "#1D2939"}}, "viewType": null}, "displayType": "KEY_VALUE_MAPPER", "type": "CardFormFieldValue", "displayMapper": {"M": "Male", "F": "Female", "O": "Others"}}}], "formGroupDataStyle": {"backgroundColor": "#E0EAFF", "borderBottomLeftRadius": 12, "borderBottomRightRadius": 12, "padding": 24, "marginRight": 24}}, {"formGroupName": "LV3 Page 1 Personal Details Filled Without Address Details : Address Details", "formGroupHeader": {"type": "RichCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Current Address image icon", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/5d132373-f89c-49d8-b191-277876ad8ffc.png?q=100", "height": 36, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/5d132373-f89c-49d8-b191-277876ad8ffc.png?q=100", "width": 36}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"backgroundColor": "#D0D5DD", "borderTopLeftRadius": 12, "borderTopRightRadius": 12, "paddingBottom": 12, "paddingTop": 12, "paddingLeft": 16, "paddingRight": 16, "position": "sticky", "left": 0, "marginBottom": 4, "marginRight": 24}, "subStatusText": null, "superTitle": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "Inter", "fontSize": 16, "fontWeight": "bold"}, "text": "Current Address", "textColor": "#1D2939"}}, "viewType": null, "next": {"action": {"type": "CLIENT__INLINE_NAVIGATION", "params": {"doNotBroadcastToNestedForm": true, "screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": {}, "failCard": null, "formId": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "numberOfLines": 4, "formFieldType": "TEXT_BOX_V0", "label": "House/Flat/Floor number", "mandatory": true, "multiline": true, "name": "houseNumber", "noOfErrors": 1, "placeholder": "House/Flat/Floor number", "tracking": {"sendImpression": true, "fieldName": "houseNumber", "sendEdits": true, "prefill": true, "prefilledValue": "", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "numberOfLines": 4, "formFieldType": "TEXT_BOX_V0", "label": "Apartment/Area/Locality", "mandatory": true, "multiline": true, "name": "area", "noOfErrors": 1, "placeholder": "Apartment/Area/Locality", "tracking": {"sendImpression": true, "fieldName": "area", "sendEdits": true, "prefill": true, "prefilledValue": "", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CustomFormFieldValue", "containerStyle": null, "customFieldData": {"city": {"type": "TextBoxFormFieldValueV0", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "City", "mandatory": true, "name": "city", "placeholder": "", "outerContainerStyle": {"display": "none"}}, "customFieldType": "PINCODE_V0", "pincode": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/6/pincode/existence", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Pin Code", "mandatory": true, "maxCharacters": 6, "minCharacters": 6, "name": "pincode", "inputType": "NUMERIC", "noOfErrors": 1, "placeholder": "eg. 560103", "tracking": {"sendImpression": true, "fieldName": "pincode", "sendEdits": true, "prefill": true, "prefilledValue": "560075", "sendInlineError": true}, "validationRegex": "^[1-9]{1}[0-9]{5}$", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter a valid pin code", "interactionType": null, "regex": "^(?!\\s*$)[1-9]{1}[0-9]{5}$", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, "state": {"type": "TextBoxFormFieldValueV0", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "State", "mandatory": true, "name": "state", "placeholder": "", "outerContainerStyle": {"display": "none"}}}, "disabled": false, "formFieldType": "CUSTOM", "grids": null, "inputStyle": null, "label": null, "mandatory": true, "name": "pincodeDetails", "noOfErrors": null, "outerContainerStyle": null, "tracking": null, "validationRuleList": null, "value": null}}], "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Done"}}, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}, "buttonOrientation": null, "consent": null, "consentList": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": {"text": "Add new address", "style": {"color": "#212121", "lineHeight": 24, "fontSize": 17, "fontWeight": "bold"}, "containerStyle": {"marginBottom": 20}}}, "tracking": {}}}]}}}, "value": {"type": "RichButtonValue", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "buttonColor": "inherit", "image": {"type": "ImageValue", "alternateText": "Add new address image icon", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/b2ebdef2-c87d-47d2-8573-05aa1782edbd.png?q=100", "height": 20, "source": "https://rukminim1.flixcart.com/www/120/120/promos/07/05/2025/b2ebdef2-c87d-47d2-8573-05aa1782edbd.png?q=100", "width": 20}}}}, "formGroupData": [{"style": {}, "data": {"type": "DropdownFormFieldValueV0", "formFieldType": "DROPDOWN_V0", "name": "address", "title": {"text": "Communication Address", "title": "Communication Address", "textColor": "#1D2939", "style": {"color": "#1D2939", "fontWeight": "normal"}}, "description": {"text": "to deliver your card", "title": "to deliver your card", "textColor": "#1D2939", "style": {"color": "#1D2939", "fontWeight": "normal"}}, "mandatory": true, "disabled": false, "selectedOption": {"id": "TWO", "title": "Address 1", "type": "PLAIN_TEXT"}, "dehydratedOption": {"title": "Address ${INDEX}", "titleStyle": {"fontWeight": "bold", "color": "#1D2939", "fontSize": 16, "lineHeight": 24}, "description": {"action": null, "value": {"text": "${houseNumber}, ${area}, ${pincodeDetails__city}, ${pincodeDetails__state} ${pincodeDetails__pincode} India", "title": "${houseNumber}, ${area}, ${pincodeDetails__city}, ${pincodeDetails__state} ${pincodeDetails__pincode} India", "textColor": "#667085", "style": {"color": "#667085", "fontWeight": "normal"}}}, "id": "${ID}", "type": "PLAIN_TEXT"}, "initialOptionsDataMap": [], "initialValue": {"address": ""}, "options": [{"type": "IMAGE", "disabled": true, "id": "FALLBACK_NO_OPTION", "image": {"type": "ImageValue", "alternateText": "No address found", "aspectRatio": "312:194", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/08/05/2025/b5196b7b-ff86-4a38-99c2-f360645983d7.png?q={@quality}", "height": 194, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/08/05/2025/b5196b7b-ff86-4a38-99c2-f360645983d7.png?q={@quality}", "width": 312}, "action": {"type": "CLIENT__INLINE_NAVIGATION", "params": {"doNotBroadcastToNestedForm": true, "screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": **********, "hasMorePages": false, "infinitePage": false, "layoutParams": {"orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2503181237221946624878265441683791458", "loginStatus": "login:<PERSON><PERSON><PERSON>", "pageName": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS"}}}, "pageMeta": {"baseImpressionId": "fbc96422-a6d3-4b45-a60c-ead49f5126dc", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#FFFFFF", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": {}, "failCard": null, "formId": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "numberOfLines": 4, "formFieldType": "TEXT_BOX_V0", "label": "House/Flat/Floor number", "mandatory": true, "multiline": true, "name": "houseNumber", "noOfErrors": 1, "placeholder": "House/Flat/Floor number", "tracking": {"sendImpression": true, "fieldName": "houseNumber", "sendEdits": true, "prefill": true, "prefilledValue": "", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "numberOfLines": 4, "formFieldType": "TEXT_BOX_V0", "label": "Apartment/Area/Locality", "mandatory": true, "multiline": true, "name": "area", "noOfErrors": 1, "placeholder": "Apartment/Area/Locality", "tracking": {"sendImpression": true, "fieldName": "area", "sendEdits": true, "prefill": true, "prefilledValue": "", "sendInlineError": true}, "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CustomFormFieldValue", "containerStyle": null, "customFieldData": {"city": {"type": "TextBoxFormFieldValueV0", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "City", "mandatory": true, "name": "city", "placeholder": "", "outerContainerStyle": {"display": "none"}}, "customFieldType": "PINCODE_V0", "pincode": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/6/pincode/existence", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Pin Code", "mandatory": true, "maxCharacters": 6, "minCharacters": 6, "name": "pincode", "noOfErrors": 1, "placeholder": "eg. 560103", "tracking": {"sendImpression": true, "fieldName": "pincode", "sendEdits": true, "prefill": true, "prefilledValue": "560075", "sendInlineError": true}, "validationRegex": "^[1-9]{1}[0-9]{5}$", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Please enter a valid pin code", "interactionType": null, "regex": "^(?!\\s*$)[1-9]{1}[0-9]{5}$", "ruleType": "REGEX", "validateOnSubmit": false}], "value": ""}}, "state": {"type": "TextBoxFormFieldValueV0", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "State", "mandatory": true, "name": "state", "placeholder": "", "outerContainerStyle": {"display": "none"}}}, "disabled": false, "formFieldType": "CUSTOM", "grids": null, "inputStyle": null, "label": null, "mandatory": true, "name": "pincodeDetails", "noOfErrors": null, "outerContainerStyle": null, "tracking": null, "validationRuleList": null, "value": null}}], "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "CLIENT__CLOSE_EDITED_GROUPED_FORM", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "LV3_PERSONAL_DETAILS_FILLED_WITHOUT_ADDRESS_DETAILS_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Done"}}, "containerStyle": {"padding": 24, "width": "calc(100% - 48px)"}, "buttonOrientation": null, "consent": null, "consentList": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": null}, "tracking": {}}}]}}}, "value": {"type": "RichButtonValue", "buttonSize": "LARGE", "buttonTextColor": "#B5EF85", "buttonTextWeight": "semibold", "buttonViewType": "SOLID", "disabled": false, "buttonColor": "inherit", "image": {"type": "ImageValue", "alternateText": "Not visible", "dynamicImageUrl": "https://fastly.picsum.photos/id/237/200/300.jpg?hmac=TmmQSbShHz9CdQm0NkEjx1Dyh_Y984R9LpNrpvH2D_U", "height": 36, "source": "https://fastly.picsum.photos/id/237/200/300.jpg?hmac=TmmQSbShHz9CdQm0NkEjx1Dyh_Y984R9LpNrpvH2D_U", "width": 36}}}], "stackType": "HORIZONTAL", "displayType": "DROPDOWN", "inputType": null, "outerContainerStyle": {"background": "#EAECF0", "padding": 24, "minWidth": 212, "marginRight": 24, "borderBottomLeftRadius": 12, "borderBottomRightRadius": 12, "paddingBottom": 28, "display": "block"}}}], "formGroupDataStyle": {}}], "persistFormData": false, "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": {}, "fallback": null, "loaderContent": {"content": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"image": {"aspectRatio": "262:185", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/04/04/2024/16390b7e-85b7-4d0a-b11c-f3c0e375ea04.png?q={@quality}", "height": 185, "type": "ImageValue", "width": 262}, "subTitle": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"showClipboard": false, "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "lineHeight": 22, "textAlign": "center"}, "text": "Do not press back or switch apps.", "type": "RichTextValue"}}, "title": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"showClipboard": false, "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "lineHeight": 30, "textAlign": "center"}, "text": "Your application details are being processed...", "type": "RichTextValue"}}, "type": "AnnouncementV2Value"}}, "type": "ANNOUNCEMENT_V2"}, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"title": "Enter work details", "type": "RichTextButtonValue"}}, "buttonOrientation": null, "consent": null, "consentList": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "type": "SubmitButtonValue", "viewType": null}}