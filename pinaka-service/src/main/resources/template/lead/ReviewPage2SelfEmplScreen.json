{"formId": "WORK_DETAILS", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": {"widgetName": "REVIEW_PAGE_2_FORM"}, "trackingData": null, "value": {"type": "AutoSuggestFormFieldValue", "action": null, "data": [], "disabled": false, "formFieldType": "AUTO_SUGGEST_BOX", "label": "Choose your Industry", "mandatory": true, "name": "industryName", "offline": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "data": [], "label": "Company name", "mandatory": true, "name": "organization", "offline": false, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}], "replaceInputList": [{"regex": "^[a-zA-Z0-9]+( [a-zA-Z0-9]+)* ?$", "replaceRegex": [{"regex": "^\\s", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PriceTextBoxFormFieldValueV0", "disabled": false, "formFieldType": "PRICE_TEXT_BOX_V0", "label": "Annual turnover", "noOfErrors": 1, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "^[1-9]\\d*(?:,\\d+)*$"}], "mandatory": true, "name": "annualTurnOver", "placeholder": "Eg. 10,00,000", "autoCapitalize": "none", "inputType": "NUMERIC"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "PriceTextBoxFormFieldValueV0", "disabled": false, "formFieldType": "PRICE_TEXT_BOX_V0", "label": "Monthly Income", "noOfErrors": 1, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "^[1-9]\\d*(?:,\\d+)*$"}], "mandatory": true, "name": "income", "placeholder": "Eg. 100,000", "autoCapitalize": "none", "inputType": "NUMERIC"}}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "trackingData": null, "tracking": {"sendImpression": true, "fieldName": "bonusIncome", "prefill": true, "prefilledValue": "", "sendEdits": true, "sendInlineError": true}, "value": {"autoCapitalize": "none", "disabled": false, "formFieldType": "PRICE_TEXT_BOX_V0", "inputType": "NUMERIC", "label": "Extra monthly income (bonus, rent, interest, etc.)", "mandatory": false, "name": "bonusIncome", "placeholder": "Eg. 10,000", "maxCharacters": 9, "type": "PriceTextBoxFormFieldValueV0"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "disabled": false, "formFieldType": "MULTI_DROPDOWN", "label": "Mode of income", "mandatory": true, "name": "incomeSource", "options": [{"id": "CHEQUE", "title": "CHEQUE", "type": "PLAIN_TEXT"}, {"id": "CASH", "title": "CASH", "type": "PLAIN_TEXT"}, {"id": "ONLINE", "title": "ONLINE", "type": "PLAIN_TEXT"}], "displayType": "DROPDOWN", "stackType": "HORIZONTAL"}}], "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "consentList": [{"consentType": "CHECKBOX", "consentId": "${consentIdMFI}", "consentFor": "${consentForMFI}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentMFI}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}, {"consentType": "CHECKBOX", "consentId": "${consentIdNonPEP}", "consentFor": "${consentForNonPEP}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentNonPEP}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}, {"consentType": "CHECKBOX", "consentId": "${consentIdCKYC}", "consentFor": "${consentForCKYC}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consentCKYC}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}], "button": {"action": {"url": "/api/sm/1/application/form", "encryption": {}, "params": {}, "loaderContent": {"type": "ANNOUNCEMENT_V2", "content": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/04/04/2024/16390b7e-85b7-4d0a-b11c-f3c0e375ea04.png?q={@quality}", "height": 185, "width": 262, "aspectRatio": "262:185", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Your application details are being processed...", "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "textAlign": "center", "lineHeight": 30}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Do not press back or switch apps.", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 22}}}, "type": "AnnouncementV2Value"}}}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": {"widgetName": "REVIEW_PAGE_2_SUBMIT_BUTTON"}, "value": {"type": "RichTextButtonValue", "title": "Next"}}}, "resultStoreKey": null, "subTitle": null, "title": null}