{"formId": "NAME_PAGE", "persistFormData": false, "tracking": {"widgetName": "NAME_PAGE_FORM"}, "renderableComponents": [{"tracking": {"widgetName": "NAME_PAGE_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "phoneNumber", "label": "Phone Number", "mandatory": true, "disabled": true, "value": "", "minCharacters": 10, "maxCharacters": 13, "autoCapitalize": "characters"}, "action": null, "rcType": null, "metaData": null}, {"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "firstName", "label": "First and Middle name as per PAN", "mandatory": true, "disabled": false, "value": "", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}], "suffix": {"value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"type": "EDIT"}}, "autoCapitalize": "characters", "placeholder": "First and Middle Name", "replaceInput": {"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}, "replaceInputList": [{"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "^\\s", "replaceValue": ""}, {"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}]}, "action": null, "rcType": null, "metaData": null}, {"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "lastName", "label": "Last name as per PAN", "mandatory": true, "disabled": false, "value": "", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}], "suffix": {"value": {"image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"type": "EDIT"}}, "autoCapitalize": "characters", "placeholder": "Last Name", "replaceInput": {"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}, "replaceInputList": [{"regex": "^\\s?[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}, {"regex": "^\\s$", "replaceRegex": [{"regex": "^\\s+", "replaceValue": ""}]}]}, "action": null, "rcType": null, "metaData": null}], "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "consent": {"consentType": "CHECKBOX", "consentId": "${consentId}", "consentFor": "${consentFor}", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": {"widgetName": "NAME_PAGE_SUBMIT_BUTTON"}, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consent}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}, "button": {"action": {"url": "/api/sm/1/application/form", "encryption": {}, "params": {}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next"}}}, "resultStoreKey": null, "subTitle": null, "title": null}