{"formId": "REVIEW_PAGE_1", "persistFormData": false, "renderableComponents": [{"tracking": {"widgetName": "REVIEW_PAGE_1_FORM"}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "panNumber", "label": "PAN Number", "mandatory": true, "value": null, "minCharacters": 10, "maxCharacters": 10, "autoCapitalize": "characters", "noOfErrors": 1, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter correct PAN", "validateOnSubmit": false, "regex": "^[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}$"}], "placeholder": "eg. **********", "suffix": {"value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"type": "EDIT"}}}, "action": null, "rcType": null, "metaData": null}, {"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "DATE", "name": "dob", "label": "DOB", "mandatory": true, "value": null, "minCharacters": 10, "maxCharacters": 10, "maxValue": "2004-02-19T15:48:30.406+05:30", "minValue": "1965-02-19T15:48:30.200+05:30", "autoCapitalize": "characters", "placeholder": "enter your Date of Birth", "noOfErrors": 1, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}], "suffix": {"value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"type": "EDIT"}}}, "action": null, "rcType": null, "metaData": null}, {"tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "formFieldType": "DROPDOWN", "name": "gender", "label": "Gender", "mandatory": true, "disabled": false, "selectedOption": {"id": "M", "title": "Male", "type": "PLAIN_TEXT"}, "options": [{"id": "M", "title": "Male", "type": "PLAIN_TEXT"}, {"id": "F", "title": "Female", "type": "PLAIN_TEXT"}, {"id": "O", "title": "Others", "type": "PLAIN_TEXT"}], "displayType": "DROPDOWN", "stackType": "HORIZONTAL"}, "action": null, "rcType": null, "metaData": null}, {"tracking": {"sendImpression": true, "fieldName": "email", "prefill": true, "prefilledValue": "abc", "sendEdits": true, "sendInlineError": true}, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "email", "label": "Official Email ID", "mandatory": true, "disabled": false, "value": "", "suffix": {"value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"type": "EDIT"}}, "inputType": "EMAIL_ADDRESS", "noOfErrors": 1, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter correct Email", "interactionType": null, "validateOnSubmit": false, "regex": "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,6})$"}], "warningValidation": {"type": "emailHostCheck", "condition": ["gmail.co.za", "gmail.com", "gmail.com.au", "gmail.com.br", "gmail.ru", "hotmail.be", "hotmail.ca", "hotmail.ch", "hotmail.co", "hotmail.co.il", "hotmail.co.jp", "hotmail.co.nz", "hotmail.co.uk", "hotmail.co.za", "hotmail.com", "hotmail.com.ar", "hotmail.com.au", "hotmail.com.br", "hotmail.com.mx", "hotmail.com.tr", "hotmail.de", "hotmail.es", "hotmail.fi", "hotmail.fr", "hotmail.it", "hotmail.kg", "hotmail.kz", "hotmail.my", "hotmail.nl", "hotmail.ro", "hotmail.roor", "hotmail.ru", "rediff.com", "rediffmail.com", "rediffmailpro.com", "yahoo.ae", "yahoo.at", "yahoo.be", "yahoo.ca", "yahoo.ch", "yahoo.cn", "yahoo.co", "yahoo.co.id", "yahoo.co.il", "yahoo.co.in", "yahoo.co.jp", "yahoo.co.kr", "yahoo.co.nz", "yahoo.co.th", "yahoo.co.uk", "yahoo.co.za", "yahoo.com", "yahoo.com.ar", "yahoo.com.au", "yahoo.com.br", "yahoo.com.cn", "yahoo.com.co", "yahoo.com.hk", "yahoo.com.is", "yahoo.com.mx", "yahoo.com.my", "yahoo.com.ph", "yahoo.com.ru", "yahoo.com.sg", "yahoo.com.tr", "yahoo.com.tw", "yahoo.com.vn", "yahoo.cz", "yahoo.de", "yahoo.dk", "yahoo.es", "yahoo.fi", "yahoo.fr", "yahoo.gr", "yahoo.hu", "yahoo.ie", "yahoo.in", "yahoo.it", "yahoo.jp", "yahoo.net", "yahoo.nl", "yahoo.no", "yahoo.pl", "yahoo.pt", "yahoo.ro", "yahoo.ru", "yahoo.se", "yahoofs.com", "yahoomail.com", "aol.com", "msn.com", "wanadoo.fr", "orange.fr", "comcast.net", "live.com", "free.fr", "gmx.de", "web.de", "yandex.ru", "ymail.com", "libero.it", "outlook.com", "uol.com.br", "bol.com.br", "mail.ru", "cox.net", "sbcglobal.net", "sfr.fr", "live.fr", "verizon.net", "live.co.uk", "googlemail.com", "ig.com.br", "live.nl", "bigpond.com", "terra.com.br", "neuf.fr", "alice.it", "rocketmail.com", "att.net", "laposte.net", "facebook.com", "bellsouth.net", "charter.net", "rambler.ru", "tiscali.it", "shaw.ca", "sky.com", "earthlink.net", "optonline.net", "freenet.de", "t-online.de", "aliceadsl.fr", "virgilio.it", "home.nl", "qq.com", "telenet.be", "me.com", "tiscali.co.uk", "voila.fr", "gmx.net", "mail.com", "planet.nl", "tin.it", "live.it", "ntlworld.com", "arcor.de", "frontiernet.net", "hetnet.nl", "live.com.au", "zonnet.nl", "club-internet.fr", "juno.com", "optusnet.com.au", "blueyonder.co.uk", "bluewin.ch", "skynet.be", "sympatico.ca", "windstream.net", "mac.com", "centurytel.net", "chello.nl", "live.ca", "aim.com", "bigpond.net.au", "super.money"], "warningMessage": "Please recheck correctness of your email address"}, "autoCapitalize": "none", "placeholder": "enter full email ID"}, "action": null, "rcType": null, "metaData": null}, {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "MultiDropdownFormFieldValue", "defaultValue": "Salaried", "disabled": false, "formFieldType": "MULTI_DROPDOWN", "label": "Select employment type", "mandatory": true, "name": "employmentType", "options": [{"id": "Salaried", "title": "Salari<PERSON> (Job)", "subtitle": "You work for a company and get a fixed monthly salary"}, {"id": "SelfEmployed", "title": "Self Employed (Own Business)", "subtitle": "You run your own business or work independently"}], "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Select employment type to proceed", "validateOnSubmit": false, "regex": "\\S"}], "value": "", "viewType": "DROPDOWN"}}, {"tracking": null, "trackingData": null, "value": {"type": "MultiDropdownFormFieldValue", "formFieldType": "MULTI_DROPDOWN", "name": "loanPurpose", "label": "Purpose of <PERSON>an", "mandatory": true, "disabled": false, "defaultValue": "Personal", "value": "PERSONAL", "viewType": "DROPDOWN", "options": [{"id": "PERSONAL", "title": "Personal"}, {"id": "TRAVEL", "title": "Travel"}, {"id": "HOME_REPAIR", "title": "Home - repair"}, {"id": "MARRIAGE", "title": "Marriage"}, {"id": "MEDICAL", "title": "Medical"}, {"id": "EDUCATION", "title": "Education"}, {"id": "CONSUMER_DURABLES", "title": "Consumer - durables"}]}, "action": null, "rcType": null, "metaData": null}, {"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "houseNumber", "label": "House/Flat/Floor number", "mandatory": true, "value": "", "autoCapitalize": "characters", "placeholder": "House/Flat/Floor number", "maxCharacters": 250, "minCharacters": 3, "noOfErrors": 1, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}], "suffix": {"value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"type": "EDIT"}}}, "action": null, "rcType": null, "metaData": null}, {"tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "area", "label": "Apartment/Area/Locality", "mandatory": true, "value": "", "autoCapitalize": "characters", "placeholder": "Apartment/Area/Locality", "maxCharacters": 250, "minCharacters": 3, "noOfErrors": 1, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}], "suffix": {"value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"type": "EDIT"}}}, "action": null, "rcType": null, "metaData": null}, {"tracking": null, "trackingData": null, "value": {"type": "CustomFormFieldValue", "formFieldType": "CUSTOM", "name": "pincodeDetails", "mandatory": true, "customFieldData": {"customFieldType": "PINCODE_V0", "pincode": {"action": {"url": "/6/pincode/existence"}, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "pincode", "value": "", "validationRegex": "^[1-9]{1}[0-9]{5}$", "noOfErrors": 1, "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Complete this field to proceed", "validateOnSubmit": false, "regex": "\\S"}, {"ruleType": "REGEX", "errorMessage": "Please enter a valid pin code", "interactionType": null, "validateOnSubmit": false, "regex": "^(?!\\s*$)[1-9]{1}[0-9]{5}$"}], "label": "Pin Code", "mandatory": true, "minCharacters": 6, "maxCharacters": 6, "autoCapitalize": "characters", "placeholder": "eg. 560103", "suffix": {"value": {"type": "RichTextButtonValue", "image": {"type": "ImageValue", "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/06/01/2025/56b68473-d06b-4092-87ab-26ce4c8b7f57.png?q={@quality}", "width": 16, "height": 16}}, "action": {"type": "EDIT"}}}, "rcType": null, "metaData": null}, "city": {"tracking": null, "trackingData": null, "type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "city", "label": "City", "mandatory": true, "value": null, "autoCapitalize": "characters", "placeholder": "", "disabled": "true"}, "state": {"tracking": null, "trackingData": null, "type": "TextBoxFormFieldValueV0", "formFieldType": "TEXT_BOX_V0", "name": "state", "label": "State", "mandatory": true, "value": null, "autoCapitalize": "characters", "placeholder": "", "disabled": "true", "action": null, "rcType": null, "metaData": null}}}, "action": null, "rcType": null, "metaData": null}], "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"url": "/api/sm/1/application/form", "encryption": {}, "params": {}}, "metaData": null, "rcType": null, "tracking": {"widgetName": "REVIEW_PAGE_1_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next"}}}, "resultStoreKey": null, "subTitle": null, "title": null}