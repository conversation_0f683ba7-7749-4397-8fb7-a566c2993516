{"renderableComponents": [{"action": {"loginType": "LEGACY_LOGIN", "params": {}, "screenType": "BottomSheet", "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/creditscore/checkscore/ontimepayments", "validationMeta": null, "widgetTracking": null}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichActionCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "buttonListOrientationType": null, "buttons": null, "cardPressEnabled": null, "cardType": null, "footerCTA": null, "orientation": "PORTRAIT", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"backgroundColor": "#fff", "padding": 16, "paddingRight": 32, "borderRadius": 12, "elevation": 2, "minWidth": 148, "minHeight": 142}, "subStatusText": null, "tagValue": {"text": "High", "textColor": "#039855", "backgroundColor": "#ECFDF3", "image": {"type": "ImageValue", "alternateText": "", "dynamicImageUrl": "https://static-assets-web.flixcart.com/sm-payments/images/credit_score/arrow-upgreen.png", "height": 16, "source": "https://static-assets-web.flixcart.com/sm-payments/images/credit_score/arrow-upgreen.png", "style": {"marginRight": 4}, "width": 16}}, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "AnekLatin-Bold", "fontSize": 20, "fontWeight": "bold"}, "containerStyle": {"marginTop": 0, "paddingTop": 0, "paddingBottom": 0, "marginBottom": 12}, "text": "On-time\npayments"}}, "superTitle": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#4D43FE", "fontSize": 24, "lineHeight": 28, "fontWeight": "bold"}, "containerStyle": {"marginBottom": 4}, "text": "100%"}, "viewType": null}}, {"action": {"loginType": "LEGACY_LOGIN", "params": {}, "screenType": "BottomSheet", "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/creditscore/checkscore/creditutilisation", "validationMeta": null, "widgetTracking": null}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichActionCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "buttonListOrientationType": null, "buttons": null, "cardPressEnabled": null, "cardType": null, "footerCTA": null, "orientation": "PORTRAIT", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"backgroundColor": "#fff", "padding": 16, "paddingRight": 32, "borderRadius": 12, "elevation": 2, "minWidth": 148, "minHeight": 142}, "subStatusText": null, "tagValue": {"text": "High", "textColor": "#039855", "backgroundColor": "#ECFDF3", "image": {"type": "ImageValue", "alternateText": "", "dynamicImageUrl": "https://static-assets-web.flixcart.com/sm-payments/images/credit_score/arrow-upgreen.png", "height": 16, "source": "https://static-assets-web.flixcart.com/sm-payments/images/credit_score/arrow-upgreen.png", "style": {"marginRight": 4}, "width": 16}}, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "AnekLatin-Bold", "fontSize": 20, "fontWeight": "bold"}, "containerStyle": {"marginTop": 0, "paddingTop": 0, "paddingBottom": 0, "marginBottom": 12}, "text": "Credit\nUtilization"}}, "superTitle": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#FF34FF", "fontSize": 24, "lineHeight": 28, "fontWeight": "bold"}, "containerStyle": {"marginBottom": 4}, "text": "100%"}, "viewType": null}}, {"action": {"loginType": "LEGACY_LOGIN", "params": {}, "screenType": "BottomSheet", "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/creditscore/checkscore/creditage", "validationMeta": null, "widgetTracking": null}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichActionCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "buttonListOrientationType": null, "buttons": null, "cardPressEnabled": null, "cardType": null, "footerCTA": null, "orientation": "PORTRAIT", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"backgroundColor": "#fff", "padding": 16, "paddingRight": 32, "borderRadius": 12, "elevation": 2, "minWidth": 148, "minHeight": 142}, "subStatusText": null, "tagValue": {"text": "High", "textColor": "#039855", "backgroundColor": "#ECFDF3", "image": {"type": "ImageValue", "alternateText": "", "dynamicImageUrl": "https://static-assets-web.flixcart.com/sm-payments/images/credit_score/arrow-upgreen.png", "height": 16, "source": "https://static-assets-web.flixcart.com/sm-payments/images/credit_score/arrow-upgreen.png", "style": {"marginRight": 4}, "width": 16}}, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "AnekLatin-Bold", "fontSize": 20, "fontWeight": "bold"}, "containerStyle": {"marginTop": 0, "paddingTop": 0, "paddingBottom": 0, "marginBottom": 12}, "text": "Credit\nAge"}}, "superTitle": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#4D43FE", "fontSize": 24, "lineHeight": 28, "fontWeight": "bold"}, "containerStyle": {"marginBottom": 4}, "text": "12 y 5 mo"}, "viewType": null}}, {"action": {"loginType": "LEGACY_LOGIN", "params": {}, "screenType": "BottomSheet", "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/creditscore/checkscore/creditenquires", "validationMeta": null, "widgetTracking": null}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichActionCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "buttonListOrientationType": null, "buttons": null, "cardPressEnabled": null, "cardType": null, "footerCTA": null, "orientation": "PORTRAIT", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"backgroundColor": "#fff", "padding": 16, "paddingRight": 32, "borderRadius": 12, "elevation": 2, "minWidth": 148, "minHeight": 142}, "subStatusText": null, "tagValue": {"text": "Medium", "textColor": "#EC4A0A", "backgroundColor": "#FFF6ED", "image": {"type": "ImageValue", "alternateText": "", "dynamicImageUrl": "https://static-assets-web.flixcart.com/sm-payments/images/credit_score/arrow-uporange.png", "height": 16, "source": "https://static-assets-web.flixcart.com/sm-payments/images/credit_score/arrow-uporange.png", "style": {"marginRight": 4}, "width": 16}}, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "AnekLatin-Bold", "fontSize": 20, "fontWeight": "bold"}, "containerStyle": {"marginTop": 0, "paddingTop": 0, "paddingBottom": 0, "marginBottom": 12}, "text": "Credit\nEnquiries"}}, "superTitle": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#FF34FF", "fontSize": 24, "lineHeight": 28, "fontWeight": "bold"}, "containerStyle": {"marginBottom": 4}, "text": "0"}, "viewType": null}}, {"action": {"loginType": "LEGACY_LOGIN", "params": {}, "screenType": "BottomSheet", "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/creditscore/checkscore/creditmix", "validationMeta": null, "widgetTracking": null}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichActionCard", "actionButtons": null, "avatar": null, "backgroundImage": null, "buttonListOrientationType": null, "buttons": null, "cardPressEnabled": null, "cardType": null, "footerCTA": null, "orientation": "PORTRAIT", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"backgroundColor": "#fff", "padding": 16, "paddingRight": 32, "borderRadius": 12, "elevation": 2, "minWidth": 148, "minHeight": 142}, "subStatusText": null, "tagValue": {"text": "Low", "textColor": "#DC1001", "backgroundColor": "#FFF2F3", "image": {"type": "ImageValue", "alternateText": "", "dynamicImageUrl": "https://static-assets-web.flixcart.com/sm-payments/images/credit_score/arrow-narrow-downred.png", "height": 16, "source": "https://static-assets-web.flixcart.com/sm-payments/images/credit_score/arrow-narrow-downred.png", "style": {"marginRight": 4}, "width": 16}}, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#475467", "fontFamily": "AnekLatin-Bold", "fontSize": 20, "fontWeight": "bold"}, "containerStyle": {"marginTop": 0, "paddingTop": 0, "paddingBottom": 0, "marginBottom": 12}, "text": "Credit\nMix"}}, "superTitle": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#4D43FE", "fontSize": 24, "lineHeight": 28, "fontWeight": "bold"}, "containerStyle": {"marginBottom": 4}, "text": "100%"}, "viewType": null}}], "orientation": "LANDSCAPE", "containerStyle": {"paddingEnd": 24, "paddingStart": 24, "paddingBottom": 12}, "gap": 12, "numGridColumns": 0}