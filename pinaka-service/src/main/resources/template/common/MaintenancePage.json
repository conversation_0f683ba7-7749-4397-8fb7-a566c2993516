{"data": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/18/07/2024/56378278-4c49-45c8-8310-454eb2f8ae71.png?q={@quality}", "height": 48, "width": 48, "aspectRatio": "1:1", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "We are tidying up!", "style": {"color": "#1C41D6", "fontSize": 18, "fontWeight": "bold", "textAlign": "center", "lineHeight": 28}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Sorry for the inconvenience. We are updating our service to improve your experience. Please check back in some time.", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 22}}}, "richButton": {"action": {"type": "MERCHANT_HOMEPAGE", "params": {}, "tracking": {}, "loginType": "LOGIN_NOT_REQUIRED"}, "value": {"type": "RichTextButtonValue", "title": "Okay, got it"}}, "type": "AnnouncementV2Value"}}}