{"formId": "BASIC_DETAILS", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "PAN Number", "mandatory": true, "name": "pan", "placeholder": "EX: **********", "autoCapitalize": "characters", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter correct PAN", "validateOnSubmit": false, "regex": "[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}"}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DateFormFieldValue", "disabled": false, "formFieldType": "DATE", "label": "Date of Birth", "subText": "Applicant’s age should lie between ${minAge} to ${maxAge} years", "mandatory": true, "name": "dob"}}], "submitButton": {"type": "SubmitButtonValue", "consent": {"consentType": "DEFAULT", "text": {"text": "${consent}"}}, "button": {"action": {"url": "/api/sm/1/application/form", "encryption": {}, "params": {}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next"}}}, "resultStoreKey": null, "subTitle": null, "title": null}