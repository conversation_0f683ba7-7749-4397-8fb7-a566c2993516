{"data": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/10/2023/0d215068-18f4-48b0-b663-d1e9b62252d7.png?q={@quality}", "height": 48, "width": 48, "aspectRatio": "1:1", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Face match failed", "style": {"color": "#D92D20", "fontSize": 18, "fontWeight": "bold", "textAlign": "center", "lineHeight": 28}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Sorry we can’t proceed at this moment, we are working hard to add other forms of KYC", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 22}}}, "richButton": {"action": {"type": "MERCHANT_HOMEPAGE", "params": {}, "tracking": {}, "loginType": "LOGIN_NOT_REQUIRED"}, "value": {"type": "RichTextButtonValue", "title": "Okay, got it"}}, "type": "AnnouncementV2Value"}}}