{"formId": "BASIC_DETAILS", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "PAN Number", "mandatory": true, "name": "pan", "placeholder": "EX: **********", "autoCapitalize": "characters", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter correct PAN", "validateOnSubmit": false, "regex": "^[A-Z]{5}[0-9]{4}[A-Z]{1}$"}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "First name and Middle name", "mandatory": true, "name": "firstName", "placeholder": "EX: <PERSON><PERSON>", "autoCapitalize": "none"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Last name", "mandatory": true, "name": "lastName", "placeholder": "EX: <PERSON>", "autoCapitalize": "none"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DateFormFieldValue", "disabled": false, "formFieldType": "DATE", "label": "Date of Birth", "subText": "Date of birth as per PAN", "mandatory": true, "name": "dob"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "First name and Middle name as per PAN", "mandatory": true, "name": "firstName", "placeholder": "EX: <PERSON><PERSON>", "autoCapitalize": "characters"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Last name as per PAN", "mandatory": true, "name": "lastName", "placeholder": "EX: <PERSON>", "autoCapitalize": "characters"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "disabled": false, "formFieldType": "DROPDOWN", "label": "Choose your employment type", "mandatory": true, "name": "employmentType", "options": [{"id": "Salaried", "title": "Salaried", "type": "IMAGE", "image": {"type": "ImageValue", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/02/2024/01eced73-5cd7-4791-9ce6-f0bf903ddd3e.png?q={@quality}", "height": 32, "width": 32}}, {"id": "SelfEmployed", "title": "Self Employed", "type": "IMAGE", "image": {"type": "ImageValue", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/02/2024/d6a89df9-5844-4fc6-859e-3c400940178f.png?q={@quality}", "height": 32, "width": 32}}], "displayType": "DROPDOWN", "stackType": "VERTICAL"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "defaultValue": "M", "disabled": false, "formFieldType": "DROPDOWN", "label": "Gender", "mandatory": true, "name": "gender", "options": [{"id": "M", "title": "Male", "type": "PLAIN_TEXT"}, {"id": "F", "title": "Female", "type": "PLAIN_TEXT"}, {"id": "O", "title": "Others", "type": "PLAIN_TEXT"}], "displayType": "DROPDOWN", "stackType": "HORIZONTAL"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Pincode", "mandatory": true, "name": "pincode", "placeholder": "EX: 560103", "autoCapitalize": "none", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter correct Pincode", "validateOnSubmit": false, "regex": "^[1-9]{1}[0-9]{5}$"}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Email", "mandatory": true, "name": "email", "placeholder": "EX: <EMAIL>", "autoCapitalize": "none", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter correct Email", "validateOnSubmit": false, "regex": "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,6})$"}]}}], "submitButton": {"type": "SubmitButtonValue", "consent": {"consentType": "CHECKBOX", "checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "${consent}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}}, "button": {"action": {"url": "/api/sm/1/application/form", "encryption": {}, "params": {}, "loaderContent": {"type": "ANNOUNCEMENT_V2", "content": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/04/2023/89ec64b7-c62a-4d92-ae9c-3686d9db4ad1.png?q={@quality}", "height": 185, "width": 262, "aspectRatio": "262:185", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Your application details are being processed...", "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "textAlign": "center", "lineHeight": 30}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Do not press back or switch apps.", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 22}}}, "type": "AnnouncementV2Value"}}}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next"}}}, "resultStoreKey": null, "subTitle": null, "title": null}