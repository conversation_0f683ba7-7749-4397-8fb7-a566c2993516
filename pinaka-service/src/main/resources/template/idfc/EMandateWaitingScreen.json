{"data": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/08/12/2023/14b3ae13-3353-4a21-9c5d-3d11d669aad4.png?q={@quality}", "height": 195, "width": 256, "aspectRatio": "1024:781", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Your request is being processed by the banking systems.", "style": {"color": "#111112", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 20}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "This can take up to 10 minutes.", "style": {"color": "#111112", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 20}}}, "descriptions": [{"value": {"data": [{"type": "FormattedRichTextData", "value": {"type": "RichTextValue", "text": "Sorry for the inconvenience!", "style": {"color": "#111112", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 20}}}], "type": "FormattedMessageValue"}}], "richButton": {"action": {"type": "MERCHANT_HOMEPAGE", "params": {}, "tracking": {}, "loginType": "LOGIN_NOT_REQUIRED"}, "value": {"type": "RichTextButtonValue", "title": "Okay, got it"}}, "type": "AnnouncementV2Value"}}}