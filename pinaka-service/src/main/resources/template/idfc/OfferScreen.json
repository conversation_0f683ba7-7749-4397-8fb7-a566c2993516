{"formId": "BANK_OFFER_FORM_V1", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"customFieldData": {"customFieldType": "BANK_OFFER_FIELD", "amountSlider": {"value": {"type": "RangeFormFieldValue", "defaultValue": 350000, "disabled": false, "formFieldType": "RANGE", "label": "How much do you need?", "mandatory": true, "minValue": 10000, "maxValue": 500000, "name": "amountSlider", "stepperValue": {"stepperUnit": "RUPEE", "stepSize": 5000, "type": "StepperValue"}}}, "amountTextInput": {"value": {"type": "TextBoxFormFieldValueV0", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "<PERSON><PERSON><PERSON>, How much do you need?", "mandatory": true, "name": "amountTextInput", "placeholder": "Eg: ₹2,00,000"}}, "tenureSlider": {"value": {"type": "RangeFormFieldValue", "defaultValue": 6, "disabled": false, "formFieldType": "RANGE", "label": "Select EMI plan", "mandatory": true, "minValue": 3, "maxValue": 12, "name": "amountSlider", "stepperValue": {"stepperUnit": "MONTH", "stepSize": 3, "type": "StepperValue"}}}, "rangeMap": {"value": {"type": "RangeFormFieldValueMap", "formFieldType": "RANGE_MAP", "name": null, "label": null, "mandatory": false, "disabled": false, "containerStyle": null, "outerContainerStyle": null, "value": null, "tracking": null, "grids": null, "rangeMap": null}}, "tenureTextInput": {"value": {"type": "TextBoxFormFieldValueV0", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "Select EMI plan", "mandatory": true, "name": "amountTextInput", "placeholder": "12 months"}}, "interest": {"value": 10.49}, "emiCalculator": {"type": "DEDUCTED_CHARGES", "charges": [{"chargeType": "PROCESSING_FEE", "name": {"text": "Processing fee"}, "gst": 0, "type": "PERCENTAGE", "value": 2.5}], "emiSchedules": [{"tenure": {}, "unitLevelEmiSchedule": {"principalUnitAmount": 1000, "emiSchedule": []}}]}}, "disabled": false, "formFieldType": "CUSTOM", "label": "", "name": "bankOffer", "mandatory": true}}], "submitButton": {"type": "SubmitButtonValue", "button": {"action": {"url": "/api/sm/1/application/form", "encryption": {}, "params": {}}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Next"}}}, "resultStoreKey": null, "subTitle": null, "title": null}