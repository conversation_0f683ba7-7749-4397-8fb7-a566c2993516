{"formId": "BANK_DETAILS", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"customFieldData": {"customFieldType": "BANK_DETAILS", "ifsc": {"action": {"url": "/api/sm/1/ifsc/search"}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Enter IFSC Code", "mandatory": true, "name": "ifsc", "placeholder": "EX: SBIN000000", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter correct IFSC", "validateOnSubmit": false, "regex": "^[A-Z]{4}0[A-Z0-9]{6}$"}]}}, "bankAccount": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Enter Account Number", "mandatory": true, "name": "account-number", "placeholder": "EX: ************", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter correct Account Number", "validateOnSubmit": false, "regex": "^[0-9]{9,18}$"}]}}, "reEnterBankAccount": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Re-Enter Account Number", "mandatory": true, "name": "re-account-number", "placeholder": "EX: ************", "validationRuleList": [{"ruleType": "FIELD_COMPARE", "errorMessage": "Account number does not match", "validateOnSubmit": false}]}}, "repaymentMode": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "disabled": false, "formFieldType": "DROPDOWN", "label": "Repayment using", "mandatory": true, "name": "employmentType", "options": []}}}, "disabled": false, "formFieldType": "CUSTOM", "label": "", "name": "bankDetails", "mandatory": true}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DropdownFormFieldValue", "disabled": false, "formFieldType": "DROPDOWN", "label": "Choose your bank account type.", "mandatory": true, "name": "bankAccType", "options": [{"id": "Saving", "title": "Saving", "type": "PLAIN_TEXT"}, {"id": "Current", "title": "Current", "type": "PLAIN_TEXT"}], "displayType": "DROPDOWN", "stackType": "HORIZONTAL"}}], "submitButton": {"type": "SubmitButtonValue", "consent": {"consentType": "DEFAULT", "text": {"text": "By continuing I consent to bank account verification and auto debit set up."}}, "button": {"action": {"url": "/api/sm/1/application/form", "encryption": {}, "params": {}, "screenType": "BottomSheet"}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Submit"}}}, "resultStoreKey": null, "subTitle": null, "title": null}