{"renderableComponents": [{"value": {"type": "AccordionItemValue", "title": {"type": "RichTitleValue", "text": {"type": "RichTextValue", "text": "<PERSON>an <PERSON>"}}, "contentType": "RICH_KEY_VALUE_LIST", "content": {"type": "ListRichKeyValue", "values": [{"type": "RichKey<PERSON><PERSON>ue", "key": {"type": "RichTextValue", "text": "Loan amount", "style": {"color": "#1D2939"}}, "value": {"type": "RichTextValue", "style": {"color": "#1D2939"}}}, {"type": "RichKey<PERSON><PERSON>ue", "key": {"type": "RichTextValue", "text": "Processing fee", "style": {"color": "#667085"}}, "value": {"type": "RichTextValue", "style": {"color": "#667085"}}}, {"type": "RichKey<PERSON><PERSON>ue", "key": {"type": "RichTextValue", "text": "Net disbursal amount", "style": {"color": "#1D2939"}}, "value": {"type": "RichTextValue", "style": {"color": "#1D2939"}}}]}}}, {"value": {"type": "AccordionItemValue", "title": {"type": "RichTitleValue", "text": {"type": "RichTextValue", "text": "Repayment details"}}, "contentType": "RICH_KEY_VALUE_LIST", "content": {"type": "ListRichKeyValue", "values": [{"type": "RichKey<PERSON><PERSON>ue", "key": {"type": "RichTextValue", "text": "Monthly EMI", "style": {"color": "#1D2939"}}, "value": {"type": "RichTextValue", "style": {"color": "#1D2939"}}}, {"type": "RichKey<PERSON><PERSON>ue", "key": {"type": "RichTextValue", "text": "Loan Period", "style": {"color": "#1D2939"}}, "value": {"type": "RichTextValue", "style": {"color": "#1D2939"}}}, {"type": "RichKey<PERSON><PERSON>ue", "key": {"type": "RichTextValue", "text": "First EMI Date", "style": {"color": "#1D2939"}}, "value": {"type": "RichTextValue", "style": {"color": "#1D2939"}}}, {"type": "RichKey<PERSON><PERSON>ue", "key": {"type": "RichTextValue", "text": "Last EMI Date", "style": {"color": "#1D2939"}}, "value": {"type": "RichTextValue", "style": {"color": "#1D2939"}}}], "button": {"action": {"url": "/api/fpg/1/action/view", "encryption": {}, "screenType": "BottomSheet", "params": {}, "type": "CALM__STATUS_ACTION", "tracking": {}, "loginType": "LOGIN_NOT_REQUIRED"}, "value": {"type": "RichTextButtonValue", "buttonColor": "transparent", "buttonTextColor": "#1570EF", "borderColor": "transparent", "richText": {"type": "RichTextValue", "text": "Repayment schedule", "style": {"color": "#2A55E5", "fontSize": 14, "fontWeight": "bold"}}}}}}}]}