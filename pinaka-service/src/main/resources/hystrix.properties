hystrix.threadpool.USER_SERVICE.coreSize=100
hystrix.threadpool.USER_SERVICE.queueSizeRejectionThreshold=10
hystrix.threadpool.USER_SERVICE.maxQueueSize=10





hystrix.command.GET_ADDRESS_DETAILS.execution.isolation.thread.timeoutInMilliseconds=1000




hystrix.command.GET_USER_PROFILE.execution.isolation.thread.timeoutInMilliseconds=1000




hystrix.command.GET_NON_VERIFIED_USER_PROFILE.execution.isolation.thread.timeoutInMilliseconds=1000




hystrix.command.GET_ADDRESS_FROM_ACCOUNT_ID.execution.isolation.thread.timeoutInMilliseconds=1000




hystrix.command.GET_ALTERNATE_NUMBERS.execution.isolation.thread.timeoutInMilliseconds=10000



hystrix.threadpool.LOGIN_SERVICE.coreSize=100
hystrix.threadpool.LOGIN_SERVICE.queueSizeRejectionThreshold=10
hystrix.threadpool.LOGIN_SERVICE.maxQueueSize=10





hystrix.command.GENERATE_OTP_USING_ACCOUNT_ID.execution.isolation.thread.timeoutInMilliseconds=1000




hystrix.command.VERIFY_OTP_USING_ACCOUNT_ID_AND_LOGIN_ID.execution.isolation.thread.timeoutInMilliseconds=1000




hystrix.command.GET_VERIFIED_USER_RESPONSE.execution.isolation.thread.timeoutInMilliseconds=1000




hystrix.command.GET_USER_BY_LOGIN_ID.execution.isolation.thread.timeoutInMilliseconds=1000



hystrix.command.GET_CRI_DECISION.execution.isolation.thread.timeoutInMilliseconds=500



hystrix.command.FETCH_USER_COHORT.execution.isolation.thread.timeoutInMilliseconds=200

hystrix.threadpool.AB_SERVICE.coreSize=200
hystrix.threadpool.AB_SERVICE.queueSizeRejectionThreshold=10
hystrix.threadpool.AB_SERVICE.maxQueueSize=200
hystrix.command.GET_AB_VARIABLES.execution.isolation.thread.timeoutInMilliseconds=500