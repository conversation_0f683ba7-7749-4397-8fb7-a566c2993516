#!/bin/bash -e

# Force the JVM to use IPv4 stack
JAVA_OPTS="${JAVA_OPTS} -Djava.net.preferIPv4Stack=true"

# add AOP type
JAVA_OPTS="${JAVA_OPTS} -DaopType=GUICE"

# Set language and region
JAVA_OPTS="${JAVA_OPTS} -Duser.language=en -Duser.region=CA"

# Flight Recorder
JAVA_OPTS="${JAVA_OPTS} -XX:+UnlockCommercialFeatures"
JAVA_OPTS="${JAVA_OPTS} -XX:+FlightRecorder"

# GC Options
JAVA_OPTS="${JAVA_OPTS} -XX:+UseG1GC"
JAVA_OPTS="${JAVA_OPTS} -verbose:gc  -XX:+PrintTenuringDistribution"

# Setup GC logs
JAVA_OPTS="${JAVA_OPTS} -Xloggc:/var/log/${PACKAGE}/gc.$(date +%Y-%m-%d).log"
JAVA_OPTS="${JAVA_OPTS} -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+PrintGCDetails"

# Setup remote JMX monitoring
JAVA_OPTS="${JAVA_OPTS} -Dcom.sun.management.jmxremote"
JAVA_OPTS="${JAVA_OPTS} -Dcom.sun.management.jmxremote.authenticate=false"
JAVA_OPTS="${JAVA_OPTS} -Dcom.sun.management.jmxremote.ssl=false"
JAVA_OPTS="${JAVA_OPTS} -Dcom.sun.management.jmxremote.port=4444"

case "${ENVNAME}" in
    test)
        JAVA_OPTS="${JAVA_OPTS} -javaagent:/usr/share/pinaka/jacocoagent.jar=destfile=/usr/share/pinaka/jacoco.exec,append=false,output=tcpserver,address=*,port=36320 -XX:-HeapDumpOnOutOfMemoryError"
    ;;
esac
