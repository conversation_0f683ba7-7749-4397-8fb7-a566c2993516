#!/bin/bash

set -e

PACKAGE=_PACKAGE_
USER=_USER_
JAR_FILE_NAME=_JAR_FILE_NAME_
CONFIG_FILE_NAME=_CONFIG_FILE_NAME_

INSTALL_DIR="/usr/share/${PACKAGE}"
CONFIG_DIR="/etc/${PACKAGE}"

source ./prepare_runtime

JAR_FILE="${INSTALL_DIR}/${JAR_FILE_NAME}"
CONF_FILE="${CONFIG_DIR}/${CONFIG_FILE_NAME}"

# Generate fresh configurations for our service
/etc/init.d/fk-config-service-confd init

# Run our service
exec 2>&1
exec setuidgid ${USER} java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=1900 ${JAVA_OPTS} -jar ${JAR_FILE} server ${CONF_FILE}
