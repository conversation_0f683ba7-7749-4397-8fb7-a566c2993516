#!/bin/bash

PACKAGE=_PACKAGE_
HEALTH_CHECK_PORT=_HEALTH_CHECK_PORT_
HEALTH_CHECK_URI=_HEALTH_CHECK_URI_

SERVICE_DIR="/etc/service/${PACKAGE}"

case "$1" in
  start)
    svc -u ${SERVICE_DIR}
    ;;
  stop)
    svc -d ${SERVICE_DIR}
    ;;
  restart)
    svc -d ${SERVICE_DIR}
    sleep 5
    svc -u ${SERVICE_DIR}
    ;;
  status)
    svstat ${SERVICE_DIR};
    ;;
  http-status)
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:${HEALTH_CHECK_PORT}/${HEALTH_CHECK_URI}")
    echo "${HTTP_STATUS}"
    ;;
  *)
    echo "Usage: ${PACKAGE} <start|stop|restart|status|http-status>";;
esac
