[template]
src = "pinaka-config.tmpl"
dest = "/etc/_PACKAGE_/_CONFIG_FILE_NAME_"
uid = 0
gid = 0
mode = "0644"
keys = [
  "/loglevel",
  "/sqlloglevel",
  "/databaseUrl",
  "/databaseUser",
  "/databasePassword",
  "/pinakaUrl",
  "/ardourUrl",
  "/userServiceUrl",
  "/authNClientConfig.url",
  "/authNClientConfig.clientId",
  "/authNClientConfig.clientSecret",
  "/cryptexClientConfig.maxConnections",
  "/cryptexClientConfig.connectTimeOut",
  "/cryptexClientConfig.readTimeOut",
  "/dynamicBucketConfig.bucketName",
  "/dynamicBucketConfig.localDynamicConfigBucketEnabled",
  "/oAuthUrl",
  "/oAuthClientId",
  "/oAuthClientSecret",
  "/oAuthCachedAccessTokenTTL",
  "/oAuthTargetClientID",
  "/lockin.oAuthUrl",
  "/lockin.oAuthClientId",
  "/lockin.oAuthTargetClientID",
  "/lockin.oAuthClientSecret",
  "/lockin.oAuthCachedAccessTokenTTL",
  "/coinManager.url",
  "/coinManager.clientId",
  "/coinManager.merchantId",
  "/coinManager.requestId",
  "/logInServiceUrl",
  "/bigFootUrl",
  "/bigFootExchangeName",
  "/fluxUrl",
  "/robinHoodUrl",
  "/robinhoodExchangeName",
  "/pandoraUrl",
  "/pandoraLiteUrl",
  "/fluxRuntimeUrl",
  "/fluxConnectionTimeout",
  "/fluxSocketTimeout",
  "/onboardingUrl",
  "/gibraltarTrustStorePath",
  "/gibraltarTrustStorePass",
  "/gibraltarKeyStorePath",
  "/gibraltarKeyStorePass",
  "/gibraltarUrl",
  "/gibraltarGenerateKeysOnStartUp",
  "/cremoUrl",
  "/cremoEnabled",
  "/testUsers",
  "/secretKey",
  "/cbcTelesaleSecretKey",
  "/underwritingServiceUrl",
  "/efaCeEnabled",
  "/efaCeTestUsers",
  "/efaCitiConsentKey",
  "/efaCitiInterestRate",
  "/efaCitiPostApprovalTncKey",
  "/efaCitiPreApprovalTncKey",
  "/efaCitiLateFee",
  "/efaKisshtConsentKey",
  "/efaKisshtInterestRate",
  "/efaKisshtPostApprovalTncKey",
  "/efaKisshtPreApprovalTncKey",
  "/efaKisshtLateFee",
  "/citiEnabled",
  "/upgradationEnabled",
  "/tijoriUrl",
  "/pageServiceHost",
  "/pageServicePort",
  "/kisshtApprovalCallouts",
  "/kisshtUnderwritingCallouts",
  "/indiaBullsUnderwritingCallouts",
  "/indiaBullsApprovalCallouts",
  "/testAccountPhones",
  "/cbcEtbApprovalCallouts",
  "/cbcNtbApprovalCallouts",
  "/cbcFkpayDisplayCardConsoleUrl",
  "/cbcFkpayDisplayCardEtccUrl",
  "/cbcFkpayDisplayCardEtbUrl",
  "/cbcFkpayTriggerOtpUrl",
  "/collectionServiceUrl",
  "/collectionServicePort",
  "/pandoraLiteUrl",
  "/EfaIbNudgingIsEnabledForEveryoneFlag",
  "/EfaIbNudgingWhitelistedAccountIds",
  "/schedulerHost",
  "/schedulerPort",
  "/schedulerPoolSize",
  "/schedulerClientId",
  "/cbcComingSoonBannerUrl",
  "/cbcApplyNowBannerUrl",
  "/cbcContinueApplicationBannerUrl",
  "/cbcViewCardDetailsBannerUrl",
  "/idfcBnplEkycNameMatchThreshold",
  "/panRetryCacheMaximumSize",
  "/panRetryCacheDuration",
  "/panRetryCacheDurationUnit",
  "/cbcApplicationEtccBuzzBannerUrl",
  "/cbcApplicationEtccNonBuzzBannerUrl",
  "/cbcApplicationEtccBuzzBannerAspectRatio",
  "/cbcApplicationEtccNonBuzzBannerAspectRatio",
  "/idfcBnplEkycNameValidationEnabled",
  "/isIdfcPanValidationRequired",
  "/CbcAxisNudgingIsEnabledForEveryoneFlag",
  "/CbcAxisNudgingWhitelistedAccountIds",
  "/proxyRewriteHostName",
  "/proxyRewritePortNumber",
  "/proxyEnable",
  "/proxyMaxConnections",
  "/proxyMaxConnectionsPerHost",
  "/proxyPooledConnectionIdleTimeoutInMS",
  "/proxyExecutorThreadsCount",
  "/proxyExecutionIsolationSemaphoreMaxConcurrentRequests",
  "/proxyExecutionTimeoutInMS",
  "/proxyHystrixCommandKey",
  "/proxyHystrixGroupKey",
  "/proxyRewriteProtocal",
  "/enableFldg",
  "/abUrl",
  "/xmlKycAndroidAppVersion",
  "/xmlKycChannels",
  "/bnplStorePageUrl",
  "/advanzStorePageUrl",
  "/alfredClientUrl",
  "/alfredClientSecretKey",
  "/idfcEkycNameMatchThreshold",
  "/idfcEkycNameValidationEnabled",
  "/idfcAdvanzMinimumAgeRequired",
  "/axisThinCremoScoreThreshold",
  "/autoRejectThinEnabled",
  "/onboardingGenericFlowEnabled",
  "/onboardingGenericFlowAccountIds",
  "/bnplOnboardingQueue",
  "/advanzOnboardingQueue",
  "/accountsEnabledForCbcSplit",
  "/accountsEnabledForCbcNtbSplitForm",
  "/channelsEnabledForCbcNtbSplitForm",
  "/whitelistIdsEnabledForCbcNtbSplitForm",
  "/cbcNtbSplitEnabled",
  "/fintechBorrowersEntitySchemaVersion",
  "/fintechLoanApplicationsEntitySchemaVersion",
  "/fintechLoanSubApplicationsEntitySchemaVersion",
  "/fintechWhitelistEntitySchemaVersion",
  "/neoCrmExchangeName",
  "/neoCrmAuthToken",
  "/neoCrmClientId",
  "/neoCrmUrl",
  "/neoCrmPath",
  "/cryptexBundleEnabled",
  "/databaseConfigUrl",
  "/databaseConfigUser",
  "/databaseConfigPassword",
  "/databaseSlaveConfigUrl",
  "/databaseSlaveConfigUser",
  "/databaseSlaveConfigPassword",
  "/neoCrmPath",
  "/activeKycModes",
  "/createNewKycApplicationForOldUserEnabled",
  "/fintechSourceAttributionSchemaVersion",
  "/cash.name",
  "/cash.description",
  "/emi.name",
  "/emi.description",
  "/payLater.name",
  "/payLater.description",
  "/approvalScreen.title",
  "/approvalScreen.nonUpgradedTitle",
  "/approvalScreen.nonUpgradedSubtitle",
  "/approvalScreen.emiSubtitle",
  "/approvalScreen.payLaterSubtitle",
  "/approvalScreen.imageUrl",
  "/approvalScreen.nonUpgradedImageUrl",
  "/approvalScreen.logoUrl",
  "/approvalScreen.knowMoreText",
  "/approvalScreen.knowMoreLandingUrl",
  "/approvalScreen.submitButtonText",
  "/approvalScreen.submitButtonUrl",
  "/approvalScreen.creditInfoTitle",
  "/approvalScreen.creditInfoLateFeeText",
  "/approvalScreen.howToUse.title",
  "/approvalScreen.howToUse.step1.description",
  "/approvalScreen.howToUse.step1.imageUrl",
  "/approvalScreen.howToUse.step1.title",
  "/approvalScreen.howToUse.step2.description",
  "/approvalScreen.howToUse.step2.imageUrl",
  "/approvalScreen.howToUse.step2.title",
  "/approvalScreen.howToUse.step3.description",
  "/approvalScreen.howToUse.step3.imageUrl",
  "/approvalScreen.howToUse.step3.title",
  "/rejectionScreen.title",
  "/rejectionScreen.subtitle",
  "/rejectionScreen.submitButtonText",
  "/rejectionScreen.submitButtonUrl",
  "/rejectionScreen.imageUrl",
  "/upgradeJourneyLimitReductionAllowed",
  "/mysqlLockPrefix",
  "/mysqlLockTimeout",
  "/enableTS",
  "/testAccountsForAbService",
  "/defaultKycMethodForTestAccount",
  "/dobSubtextEnabled",
  "/kycSchedulerTtl",
  "/sourceAttributionRedisTtlInSeconds",
  "/sourceAttributionConfigEnabled",
  "/redisMasterName",
  "/redisTimeout",
  "/redisSentinelAddress",
  "/redisPassword",
  "/redisMasterConnectionMinIdleSize",
  "/redisMasterConnectionPoolSize",
  "/redisMasterLockWaitTime",
  "/redisMasterLockReleaseTime",
  "/TSTestAccounts",
  "/databaseDriver",
  "/connekt.exchangeName",
  "/connekt.callbackUrl",
  "/connekt.domain",
  "/connekt.emailUrl",
  "/connekt.pnUrl",
  "/connekt.smsUrl",
  "/connekt.emailApiKey",
  "/connekt.pnApiKey",
  "/connekt.smsApiKey",
  "/connekt.emailAppName",
  "/connekt.pnAppName",
  "/connekt.smsAppName",
  "/connekt.emailBucket",
  "/connekt.emailSubBucket",
  "/connekt.pnBucket",
  "/connekt.pnSubBucket",
  "/connekt.smsBucket",
  "/connekt.smsSubBucket",
  "/connekt.pnChannelId",
  "/connekt.transactionalEmail",
  "/connekt.transactionalPN",
  "/connekt.transactionalSMS",
  "/accountsEnabledForCheckStatusV3",
  "/CbcAxisNpsIsEnabledForEveryoneFlag",
  "/CbcAxisNpsWhitelistedAccountIds",
  "/advanzIDFCNudgingIsEnabledForEveryoneFlag",
  "/advanzIDFCNudgingWhitelistedAccountIds",
  "/accountsEnabledForConsoleCug",
  "/winterfellUrl",
  "/lockin.url",
  "/lockin.clientId",
  "/cryptexClientConfig.endpoint",
  "/winterfellHost",
   "/winterfellClientId",
   "/winterfellConnectionTimeout",
   "/winterfellReadTimeout",
   "/ffbUrl",
   "/ffbCallbackEndPoint",
   "/ffbExchangeName",
   "/ffbEnvironment",
  "/accountEnabledForKycV2",
  "/cbcPhonePeWhiteListId",
  "/cbcPhonePeWhitelistIdFkPlus",
  "/conditionalApproval.subtitle",
  "/firstYearFreeStartDate",
  "/firstYearFreeEndDate",
  "/kafkaBrokers",
  "/kafkaAMSEventConsumerGroupId",
  "/kafkaSessionTimeOut",
  "/kafkaConsumerThreadCount",
  "/kafkaAMSTopicName",
  "/kafkaPollTimeOut",
  "/enableDlq",
  "/dlqTopicName",
  "/enableKafkaConsumer",
  "/accountsEnabledForStoreMergeExp",
  "/storeMergeEnabled",
  "/kycKey",
  "/default",
  "/cbcKey",
  "/tijoriExchangeName",
  "/gibraltarConnectionTimeoutInMs",
  "/gibraltarSocketTimeoutInMs",
  "/gibraltarHttpRequestRetryCount",
  "/pinakaExchangeName",
  "/vkycEnabled",
  "/ebcExchangeName",
  "/plutusUrl",
  "/fintechUserServiceUrl",
  "/caishenUrl",
  "/caishenTargetClientId",
  "/uslExchangeName",
  "/khaataUrl",
  "/skylerUrl",
  "/cbcBilling.initiationQueue",
  "/fkPayUrl",
  "/fkPayClientId",
  "/fkPayClientSecret",
  "/fkPayEncryptionkey",
  "/d42AccessKey",
  "/d42SecretKey",
  "/d42Endpoint",
  "/d42MaxConnection",
  "/d42ConnectionTimeout",
  "/fintechEbcApplicationSchemaVersion",
  "/whitelistIdOffline",
  "/databaseName",
  "/defaultDaysToKeep",
  "/shardNames",
  "/uslConnectionTimeoutInMs",
  "/uslReadTimeoutInMs",
  "/amKafkaProducerBootstrapServer",
  "/amKafkaProducerAcks",
  "/amKafkaProducerRetries",
  "/amKafkaProducerBatchSize",
  "/amKafkaProducerLingerInMs",
  "/amReatimeSegmentationUsecaseEnabled",
  "/amReatimeSegmentationTestAccounts",
  "/amReatimeSegmentationTopicName",
  "/amRedisTtlInSeconds",
  "/changeEntityEventTopic",
  "/maxInFlightRequestsPerConnection",
  "/amReatimeSegmentationEnabledJourneyList",
  "/idfcTncUrl",
  "/idfcTollFreeNo",
  "/idfcEmailId",
  "/idfcXmlKycTncUrl",
  "/idfcXmlKycDisclaimer",
  "/idfcReviewAndSubmitDisclaimer",
  "/idfcReviewAndSubmitTncUrl",
  "/idfcReviewAndSubmitTncText",
  "/idfcXmlKycConsentContext",
  "/xmlKycType",
  "/idfcNameValidationEnabled",
  "/xmlKycValidityInYears",
  "/xmlKycValidityExtensionPeriod",
  "/xmlKycAndroidAppVersion",
  "/idfcEKycConsentContext",
  "/eKycType",
  "/idfcNameMatchThreshold",
  "/eKycValidityInYears",
  "/ekycValidityExtensionPeriod",
  "/idfcCkycDisclaimer",
  "/cKycType",
  "/idfcIncorrectDetailsPopupHeading",
  "/idfcIncorrectDetailsPopupText",
  "/idfcIncorrectDetailsPopupImageUrl",
  "/idfcIncorrectDetailsPopupButtonText",
  "/cKycValidityInYears",
  "/ckycValidityExtensionPeriod",
  "/ckycFallbackMethods",
  "/idfcPanValidationRequired",
  "/idfcCkycConsentContext",
  "/bigFootBatchIngestionSize",
  "/whitelistChangeEntityEnabled",
  "/ffbProxyMaxConnections",
  "/ffbProxyMaxConnectionsPerHost",
  "/ffbProxyPooledConnectionIdleTimeoutInMS",
  "/ffbProxyExecutorThreadsCount",
  "/ffbProxyExecutionIsolationSemaphoreMaxConcurrentRequests",
  "/ffbProxyExecutionTimeoutInMS",
  "/ffbProxyHystrixCommandKey",
  "/ffbProxyHystrixGroupKey",
  "/ffbProxyRewriteProtocal",
  "/ffbProxyRewriteHostName",
  "/ffbProxyRewritePortNumber",
  "/ffbProxyEnable",
  "/accountsEnabledForEtccAlternateCug",
  "/plusPayLaterWhitelistId",
  "/transactionSummaryAccountEnabled",
  "/cbcOffersWhitelistId",
  "/externalWhitelistIds",
  "/orchestratorURL",
  "/incomeBinThreshold",
  "/cbcMyntraWhitelistIdFkPlus",
  "/cbcMyntraWhitelistId",
  "/resourcesFilePath",
  "/cbcHyperlinkPhoneEnabled",
  "/checkApplyNowVisibilty",
  "/cbcNewRejectPageEnabled",
  "/cfaExchangeName",
  "/cbcOpenWhitelistId",
  "/payLater.minimizedDescription",
  "/payLater.logoUrl",
  "/fsup.name",
  "/fsup.description",
  "/fsup.minimizedDescription",
  "/fsup.logoUrl",
  "/fsup.submitButtonText",
  "/payLaterCohortPriorityList",
  "/fsupCohortPriorityList",
  "/advanzDedupeExchangeName",
  "/turboConfig.turboDbUrl",
  "/turboConfig.turboDbUsername",
  "/turboConfig.turboDbPassword",
  "/gibraltarHost",
  "/gibraltarClientId",
  "/criHost",
  "/criMaxRetries",
  "/emiUserList",
  "/ntbCbcWhitelistIds",
  "/etccCbcWhitelistIds",
  "/etbpaCbcWhitelistIds",
  "/etbnpaCbcWhitelistIds",
  "/ntbSupercoinWhitelistIds",
  "/etccSupercoinWhitelistIds",
  "/etbpaSupercoinWhitelistIds",
  "/etbnpaSupercoinWhitelistIds",
  "/ntbCugUserList",
  "/emiBillingEnabled",
  "/cbcNtbRevampValue",
  "/cbcNtbRevampValue",
  "/applicationRetryDuration",
  "/applicationSyncCutoffDate"
]
