{{ if exists "/hystrixDefaultConfig" }}{{range jsonArray (getv "/hystrixDefaultConfig")}}{{.}}
{{end}}{{end}}

{{ range $element1 := jsonArray (getv "/clients")}}
{{ with .threadPoolCoreSize }}hystrix.threadpool.{{ toUpper $element1.poolName }}.coreSize={{.}}{{end}}
{{ with .queueSizeRejectionThreshold }}hystrix.threadpool.{{ toUpper $element1.poolName }}.queueSizeRejectionThreshold={{.}}{{end}}
{{ with .maxQueueSize }}hystrix.threadpool.{{ toUpper $element1.poolName }}.maxQueueSize={{.}}{{end}}
{{ $parent := . }}
{{ with .commandKeys }}
{{ range $element:= $parent.commandKeys }}
{{ with $parent.isolation }}hystrix.command.{{$element}}.execution.isolation.strategy={{.}}{{end}}
{{ with $parent.maxSemaphore }}hystrix.command.{{$element}}.execution.isolation.semaphore.maxConcurrentRequests={{.}}{{end}}
{{ with $parent.threadTimeout }}hystrix.command.{{$element}}.execution.isolation.thread.timeoutInMilliseconds={{.}}{{end}}
{{ with $parent.circuitBreaker }}hystrix.command.{{$element}}.circuitBreaker.enabled={{.}}{{end}}
{{end}}
{{end}}{{end}}
