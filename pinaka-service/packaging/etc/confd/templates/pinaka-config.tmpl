server:
  type: default
  applicationContextPath: /pinaka
  adminContextPath: /admin
  applicationConnectors:
    - type: http
      port: 9090
  adminConnectors:
    - type: http
      port: 9091
  requestLog:
      # Note:
      # 1. We do not specify a logFormat because the default log format is Common Log Format (CLF), which is good
      # 2. If we specify a logFormat, note that the format specifiers for access logs are different
      # 3. Access log accepts ONLY ONE appender - we cannot get access logs on both console and file simultaneously
      appenders:
        - type: file
          currentLogFilename: /var/log/pinaka/access.log
          threshold: ALL
          archive: true
          archivedLogFilenamePattern: /var/log/pinaka/access-%d{yyyy-MM-dd-HH}.log.gz
          archivedFileCount: 24
          timeZone: IST

rateLimitingConfig:
  [
      {"limiterKey" : "FETCH_UI",  "timeoutInMs":5},
      {"limiterKey" : "PAN_SUBMIT",  "timeoutInMs":5},
      {"limiterKey" : "AADHAR_VERIFICATION",  "timeoutInMs":5},
      {"limiterKey" : "GENERATE_OTP",  "timeoutInMs":5},
      {"limiterKey" : "CREATE_APPLICATION",  "timeoutInMs":5},
      { "limiterKey": "PENNY_DROP_SUBMIT_DETAILS",  "timeoutInMs":5},
      { "limiterKey": "FETCH_LOADER",  "timeoutInMs":5},
      { "limiterKey": "VERIFY_OTP", "timeoutInMs":5 },
      { "limiterKey": "GEO_LOCATION", "timeoutInMs":5 }

  ]

logging:
  level: {{ getv "/loglevel" }}
  loggers:
      "org.hibernate": ERROR
      "com.flipkart.affordability.clients.oauth": ERROR
      "com.flipkart.restbus.client.shards": ERROR
      "com.flipkart.abservice.resources": ERROR
      "org.hibernate.SQL":
        level: {{ getv "/sqlloglevel" }}
        additive: false
        appenders:
          - type: file
            currentLogFilename: /var/log/pinaka/pinaka-sql.log
            archivedLogFilenamePattern: /var/log/pinaka/pinaka-sql-%i.log.gz
            archivedFileCount: 5
            maxFileSize: 20MB
  appenders:
    - type: file
      threshold: ALL
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: /var/log/pinaka/pinaka.log
      archivedLogFilenamePattern: /var/log/pinaka/pinaka-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: file
      threshold: ERROR
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: /var/log/pinaka/pinaka-error.log
      archivedLogFilenamePattern: /var/log/pinaka/pinaka-error-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: console
      threshold: ALL
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      timeZone: IST
      target: stdout

#healthCheckName: PinakaHealth

swagger:
  title: Pinaka Service
  description: APIs for Pinaka Service
  contact: <EMAIL>
  resourcePackage: com.flipkart.fintech.pinaka.service.web

databaseConfig:
  encryptedUrl: {{ getv "/databaseConfigUrl" }}
  encryptedUser: {{ getv "/databaseConfigUser" }}
  encryptedPassword: {{ getv "/databaseConfigPassword" }}
  driverClass: {{getv "/databaseDriver"}}
  properties:
    charSet: UTF-8
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 8
    # the minimum number of connections to keep open
    minSize: 8
    # the maximum number of connections to keep open
    maxSize: 10000
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

databaseSlaveConfig:
  slaveEncryptedUrl: {{ getv "/databaseSlaveConfigUrl" }}
  slaveEncryptedUser: {{ getv "/databaseSlaveConfigUser" }}
  slaveEncryptedPassword: {{ getv "/databaseSlaveConfigPassword" }}
  driverClass: {{getv "/databaseDriver"}}
  properties:
    charSet: UTF-8
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 2
    # the minimum number of connections to keep open
    minSize: 2
    # the maximum number of connections to keep open
    maxSize: 1000
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

cryptexConfiguration:
  cryptexBundleEnabled: {{getv "/cryptexBundleEnabled"}}
  authNClientConfig:
      url: {{getv "/authNClientConfig.url"}}
      clientId: {{getv "/authNClientConfig.clientId"}}
      clientSecret: {{getv "/authNClientConfig.clientSecret"}}

  cryptexClientConfig:
    endpoint: {{ getv "/cryptexClientConfig.endpoint"}}
    maxConnections: {{getv "/cryptexClientConfig.maxConnections"}}
    connectTimeOut: {{getv "/cryptexClientConfig.connectTimeOut"}}
    readTimeOut: {{getv "/cryptexClientConfig.readTimeOut"}}

  dynamicBucketConfig:
    bucketName: {{getv "/dynamicBucketConfig.bucketName"}}
    enableLocalDynamicBucket: {{getv "/dynamicBucketConfig.localDynamicConfigBucketEnabled"}}

pinakaClientConfig:
  url: {{ getv "/pinakaUrl" }}
  client: pinaka

ardourClientConfig:
  url: {{ getv "/ardourUrl" }}
  client: pinaka

userServiceClientConfig:
  usUrl: {{ getv "/userServiceUrl" }}
  usClientId: affordability

oAuthServiceClientConfig:
  oAuthUrl: {{ getv "/oAuthUrl" }}
  oAuthClientID : {{ getv "/oAuthClientId" }}
  oAuthTargetClientID: {{ getv "/oAuthTargetClientID" }}
  oAuthClientSecret : {{ getv "/oAuthClientSecret" }}
  cachedAccessTokenTTL : {{ getv "/oAuthCachedAccessTokenTTL" }}

oAuthLockinServiceClientConfig:
  oAuthUrl: {{ getv "/lockin.oAuthUrl" }}
  oAuthClientID : {{ getv "/lockin.oAuthClientId" }}
  oAuthTargetClientID: {{ getv "/lockin.oAuthTargetClientID" }}
  oAuthClientSecret : {{ getv "/lockin.oAuthClientSecret" }}
  cachedAccessTokenTTL : {{ getv "/lockin.oAuthCachedAccessTokenTTL" }}

loginServiceClientConfig:
  loginServiceUrl: {{ getv "/logInServiceUrl" }}
  loginServiceClientId: affordability

bigfootConfiguration:
  url: {{ getv "/bigFootUrl" }}
  poolName: pool
  company: fkint
  org: cp
  namespace: affordability
  exchangeName: {{ getv "/bigFootExchangeName" }}
  exchangeType: queue
  batchIngestionSize: {{ getv "/bigFootBatchIngestionSize" }}

fluxAsyncClientConfig:
  url: {{ getv "/fluxUrl" }}
  clientId: pinaka
  exchangeName: {{ getv "/pinakaExchangeName" }}

connektClientConfig:
  exchangeName: {{ getv "/connekt.exchangeName"}}
  callbackUrl: {{ getv "/connekt.callbackUrl"}}
  domain: {{ getv "/connekt.domain"}}
  emailUrl: {{ getv "/connekt.emailUrl"}}
  pnUrl: {{ getv "/connekt.pnUrl"}}
  smsUrl: {{ getv "/connekt.smsUrl"}}
  emailApiKey: {{ getv "/connekt.emailApiKey"}}
  pnApiKey: {{ getv "/connekt.pnApiKey"}}
  smsApiKey: {{ getv "/connekt.smsApiKey"}}
  emailAppName: {{ getv "/connekt.emailAppName"}}
  pnAppName: {{ getv "/connekt.pnAppName"}}
  smsAppName: {{ getv "/connekt.smsAppName"}}
  emailBucket: {{ getv "/connekt.emailBucket"}}
  emailSubBucket: {{ getv "/connekt.emailSubBucket"}}
  pnBucket: {{ getv "/connekt.pnBucket"}}
  pnSubBucket: {{ getv "/connekt.pnSubBucket"}}
  smsBucket: {{ getv "/connekt.smsBucket"}}
  smsSubBucket: {{ getv "/connekt.smsSubBucket"}}
  pnChannelId: {{ getv "/connekt.pnChannelId"}}
  transactionalEmail : {{ getv "/connekt.transactionalEmail"}}
  transactionalPN : {{ getv "/connekt.transactionalPN"}}
  transactionalSMS : {{ getv "/connekt.transactionalSMS"}}


robinhoodAsyncClientConfig:
  url: {{ getv "/robinHoodUrl" }}
  clientId: pinaka
  exchangeName: "{{ getv "/robinhoodExchangeName" }}"

pandoraClientConfig:
  url: {{ getv "/pandoraUrl" }}
  client: pinaka

pandoraLiteClientConfig:
  url: {{ getv "/pandoraLiteUrl" }}
  client: pinaka

fluxConfiguration:
  fluxRuntimeUrl: {{ getv "/fluxRuntimeUrl" }}
  connectionTimeout: {{ getv "/fluxConnectionTimeout" }}
  socketTimeout: {{ getv "/fluxSocketTimeout" }}

onboardingClientConfig:
  url: {{ getv "/onboardingUrl" }}
  client: pinaka

gibraltarConfig:
  maxPublicKeyCount: 100
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  trustStorePath: "{{ getv "/gibraltarTrustStorePath" }}"
  trustStorePass: {{ getv "/gibraltarTrustStorePass" }}
  keyStorePath: "{{ getv "/gibraltarKeyStorePath" }}"
  keyStorePass: {{ getv "/gibraltarKeyStorePass" }}
  certificateAlias: gibraltarSelfSigned
  url: {{ getv "/gibraltarUrl" }}
  generateKeysOnStartUp: {{ getv "/gibraltarGenerateKeysOnStartUp" }}
  connectionTimeoutInMs: {{ getv "/gibraltarConnectionTimeoutInMs" }}
  socketTimeoutInMs: {{ getv "/gibraltarSocketTimeoutInMs" }}
  httpRequestRetryCount: {{ getv "/gibraltarHttpRequestRetryCount" }}

creditModelConfig:
  url: {{ getv "/cremoUrl" }}
  enabled: {{ getv "/cremoEnabled" }}
  testUsers: {{ getv "/testUsers" }}

bnplOnboardingConfig:
  optionalPermissions:
    - CONTACT
    - LOCATION
  mandatoryPermissions:
    - SMS
    - DEVICE
  tncUrl: https://www.flipkart.com/pages/pay-later-tnc
  privaryPolicyUrl: https://www.flipkart.com/pages/fapl-privacy-policy

efaOnboardingConfig:
  ceEnabled: {{ getv "/efaCeEnabled" }}
  ceTestUsers: {{ getv "/efaCeTestUsers" }}
  testAccountPhones: {{ getv "/testAccountPhones" }}
  optionalPermissions:
    - CONTACT
    - LOCATION
    - DEVICE
  mandatoryPermissions:
    - SMS
  tncUrl: https://www.flipkart.com/pages/efa-preapproval-tnc
  privaryPolicyUrl: https://www.flipkart.com/m/st/policies
  formFields:
      CITI:
      - fieldType: TEXT
        label: Company Name
        name: Company
      - fieldType: NUMBER
        label: Monthly Income
        name: Income
      - fieldType: DROPDOWN
        label: Employment
        name: Employment
      - fieldType: DROPDOWN
        label: Occupation
        name: Occupation
      - fieldType: DROPDOWN
        label: Qualification
        name: Qualification
      - fieldType: ADDRESS
        label: Current Address
        name: Address
      KISSHT: []
  lenderWiseFormFields:
      KISSHT:
      - PAN_NUMBER
      - DOB
      - GENDER
      - ADDRESS
      INDIA_BULLS:
      - PAN_NUMBER
      - DOB
      - GENDER
      - EMPLOYMENT_STATUS
      - MONTHLY_SALARY
      - ADDRESS
  underwritingCallouts:
      KISSHT: {{ getv "/kisshtUnderwritingCallouts" }}
      INDIA_BULLS: {{ getv "/indiaBullsUnderwritingCallouts" }}
  approvalCallouts:
      KISSHT: {{ getv "/kisshtApprovalCallouts" }}
      INDIA_BULLS: {{ getv "/indiaBullsApprovalCallouts" }}


bigfootEntityConfig:
  borrowerConfig:
    name: fintech_borrowers
    schemaVersion: {{ getv "/fintechBorrowersEntitySchemaVersion" }}
  loanApplicationConfig:
    name: fintech_loan_application
    schemaVersion: {{ getv "/fintechLoanApplicationsEntitySchemaVersion" }}
  subApplicationConfig:
    name: fintech_loan_subapplication
    schemaVersion: {{ getv "/fintechLoanSubApplicationsEntitySchemaVersion" }}
  whitelistConfig:
    name: fintech_whitelist
    schemaVersion: {{ getv "/fintechWhitelistEntitySchemaVersion" }}
  sourceAttributionConfig:
    name: fintech_source_attribution
    schemaVersion: {{ getv "/fintechSourceAttributionSchemaVersion" }}
  ebcApplicationConfig:
      name: fintech_ebc_borrower
      schemaVersion : {{ getv "/fintechEbcApplicationSchemaVersion" }}


cryptoConfig:
  algo: AES
  secretKey: "{{ getv "/secretKey" }}"
  cbcTelesaleSecretKey: "{{ getv "/cbcTelesaleSecretKey" }}"

underwritingConfig:
  endPoint: "{{ getv "/underwritingServiceUrl" }}"

citiConfig:
  enabled: {{ getv "/citiEnabled" }}
  productConfig:
      productCode: PC400
      sourceCode: AAFKMNET
      organization: 730
      logo: 400
      embossName: "Matthew Hyden"
  supportedPincodes: ["148023","110001","110002","110003","110004","110005","110006","110007","110008","110009","110011","110012","110013","110014","110015","110016","110017","110018","110020","110021","110022","110023","110010","110019","110024","110026","110027","110028","110029","110030","110031","110032","110033","110034","110035","110036","110037","110038","110039","110040","110041","110042","110043","110045","110046","110047","110049","110050","110051","110048","110025","110044","110052","110053","110054","110055","110056","110057","110058","110059","110060","110061","110062","110063","110065","110066","110069","110070","110071","110072","110073","110074","110075","110076","110077","110078","110079","110064","110067","110068","110080","110081","110082","110083","110084","110085","110086","110087","110088","110089","110090","110091","110093","110094","110095","110096","110097","110098","110099","110092","160105","160106","160107","160134","509823","382001","382002","382003","382004","382005","382037","120002","605009","121001","121002","121003","121004","121005","121006","121007","121008","121009","121011","121014","121010","122003","122007","122008","122009","122010","122011","122004","122001","122002","122012","122018","122017","122015","122016","122022","122103","123504","134107","134109","134113","134111","134105","134108","134115","134114","134102","134112","134118","134116","141001","141002","141003","141004","141005","141006","141007","141008","140301","140603","140103","140604","141009","141010","144001","144002","144003","144004","144005","144006","144007","144008","148023","160001","160002","160003","160004","160005","160006","160007","160008","160009","160010","160011","160012","160013","160014","160015","160016","160017","160018","160019","160020","160021","160022","160023","160024","160025","160026","160027","160028","160029","160030","160031","160032","160033","160034","160035","160036","160037","160038","160039","160040","160041","160042","160043","160044","160045","160046","160047","160051","160052","160053","160054","160055","160056","160057","160058","160059","160063","160064","160065","160067","160068","160069","160070","160062","160061","160060","160050","160048","160049","160071","160066","160101","160104","160109","173205","201001","201003","201004","201005","201006","201007","201008","201009","201010","201013","201014","201016","201002","201012","201011","201203","201303","201304","201309","201301","201308","201305","201310","201307","201302","201306","226001","226002","226004","226005","226006","226007","226008","226009","226010","226011","226012","226013","226014","226015","226016","226017","226018","226019","226020","226021","226022","226023","226024","302001","302002","302003","302004","302005","302006","302007","302008","302009","302010","302011","302012","302013","302014","302015","302016","302017","302018","302019","302020","302021","302022","302023","302024","302025","302026","302027","302028","302029","302030","302031","302032","302033","303007","303101","303902","303905","380003","380004","380005","380007","380008","380013","380014","380015","380016","380023","380024","380025","380009","380006","380019","380001","380050","380051","380052","380053","380054","380027","380058","380059","380060","380063","380061","382021","382025","382029","382028","382027","382026","382030","382022","382023","382024","382009","382017","382018","382019","382020","382016","382014","382015","382006","382007","382008","382010","382011","382012","382013","382045","382170","382031","382041","382042","382043","382044","382038","382039","382040","382035","382036","382032","382033","382034","382210","382340","382350","382355","382405","382410","382345","382330","382440","382415","382424","382470","382481","382729","382445","382480","388001","388110","388120","388121","389330","390001","390002","390003","390004","390005","390006","390008","390010","390011","390012","390013","390014","390015","390016","390017","390018","390019","390007","390025","390022","390023","390024","390020","390021","389350","390009","391310","391101","391345","391346","391347","391410","391440","391770","391350","391740","394221","394230","394270","394510","394515","394516","394550","396191","396193","396195","400002","400004","400005","400006","400007","400008","400009","400010","400011","400012","400013","400014","400015","400016","400017","400018","400019","400020","400021","400022","400023","400024","400025","400001","396192","396194","400026","400027","400028","400029","400030","400031","400032","400033","400034","400035","400036","400037","400038","400039","400040","400041","400042","400043","400044","400045","400046","400047","400048","400049","400050","400051","400052","400053","400054","400055","400056","400057","400058","400060","400061","400062","400064","400065","400066","400067","400068","400069","400070","400071","400072","400073","400074","400075","400076","400077","400078","400079","400080","400081","400063","400059","400082","400083","400084","400085","400086","400087","400088","400089","400090","400091","400092","400094","400095","400096","400097","400098","400099","400101","400102","400103","400104","400093","400601","400602","400603","400604","400605","400607","400608","400610","400611","400613","400606","400614","400615","400701","400702","400703","400704","400705","400706","400707","400708","400709","400710","401101","401105","401107","401104","401207","401208","401209","401210","401303","401202","410204","410206","410207","410209","410216","410218","410301","410210","410501","410419","411002","411003","411004","411005","411006","411007","411008","411009","411010","411011","411012","411015","411016","411017","411018","411019","411020","411021","411022","411026","411027","411028","411014","411001","411013","411029","411030","411031","411032","411033","411034","411035","411036","411037","411038","411039","411040","411041","411042","411043","411044","411046","411047","411048","411049","411050","411051","411052","411053","411054","411055","411056","411045","411058","411059","411060","411061","411062","411063","411064","411065","411066","411067","411068","411069","411070","411071","411072","411073","411074","411075","411076","411077","411078","411079","411080","411081","411082","411083","411084","411057","411085","411086","411087","411088","411089","411090","411091","411092","411093","411094","411095","411096","411097","411098","411099","411100","411101","411102","411103","411104","411105","411106","411107","411108","411109","411110","411111","411112","411113","411114","411115","411116","411117","411118","411119","411120","411121","411122","411123","411124","411125","411126","411127","411128","411129","411130","411131","411132","411133","411134","411135","411136","411137","411138","411139","411140","411141","411142","411143","411144","411145","411146","411147","411148","411149","411150","411151","411152","411153","411154","411155","411156","411157","411158","411159","411160","411161","411162","411163","411164","411165","411166","411167","411168","411169","411170","411171","411172","411173","411174","411175","411176","411177","411178","411179","411180","411181","411182","411183","411184","411185","411186","411187","411188","411189","411190","411191","411192","411193","411194","411195","411196","411197","411198","411199","411200","411201","411202","411203","411204","411205","411206","411207","411208","411209","411210","411211","411212","411213","411214","411215","411216","411217","411218","411219","411220","411221","411222","411223","411224","411225","411226","411227","411228","411229","411230","411231","411232","411233","411234","411235","411236","411237","411238","411239","411240","411241","411242","411243","411244","411245","411246","411247","411248","411249","411250","411251","411252","411253","411254","411255","411256","411257","411258","411259","411260","411261","411262","411263","411264","411265","411266","411267","411268","411269","411270","411271","411272","411273","411274","411275","411276","411277","411278","411279","411280","411281","411282","411283","411284","411285","411286","411287","411288","411289","411290","411291","411292","411293","411294","411295","411296","411297","411298","411299","411300","411301","411302","411303","411304","411305","411306","411307","411308","411309","411310","411311","411312","411313","411314","411315","411316","411317","411318","411319","411320","411321","411322","411323","411324","411325","411326","411327","411328","411329","411330","411331","411332","411333","411334","411335","411336","411337","411338","411339","411340","411341","411342","411343","411344","411345","411346","411347","411348","411349","411350","411351","411352","411353","411354","411355","411356","411357","411358","411359","411360","411361","411362","411363","411364","411365","411366","411367","411368","411369","411370","411371","411372","411373","411374","411375","411376","411377","411378","411379","411380","411381","411382","411383","411384","411385","411386","411387","411388","411389","411390","411391","411392","411393","411394","411395","411396","411397","411398","411399","411400","411401","411402","411403","411404","411405","411406","411407","411408","411409","411410","411411","411412","411413","411414","411415","411416","411417","411418","411419","411420","411421","411422","411423","411424","411425","411426","411427","411428","411429","411430","411431","411432","411433","411434","411435","411436","411437","411438","411439","411440","411441","411442","411443","411444","411445","411446","411447","411448","411449","411450","411451","411452","411453","411454","411455","411456","411457","411458","411459","411460","411461","411462","411463","411464","411465","411466","411467","411468","411469","411470","411471","411472","411473","411474","411475","411476","411477","411478","411479","411480","411481","411482","411483","411484","411485","411486","411487","411488","411489","411490","411491","411492","411493","411494","411495","411496","411497","411498","411499","411500","411501","411502","411503","411504","411505","411506","411507","411508","411509","411510","411511","411512","411513","411514","411515","411516","411517","411518","411519","411520","411521","411522","411523","411524","411525","411526","411527","411528","411529","411530","411531","411532","411533","411534","411535","411536","411537","411538","411539","411540","411541","411542","411543","411544","411545","411546","411547","411548","411549","411550","411551","411552","411553","411554","411555","411556","411557","411558","411559","411560","411561","411562","411563","411564","411565","411566","411567","411568","411569","411570","411571","411572","411573","411574","411575","411576","411577","411578","411579","411580","411581","411582","411583","411584","411585","411586","411587","411588","411589","411590","411591","411592","411593","411594","411595","411596","411597","411598","411599","411600","411601","411602","411603","411604","411605","411606","411607","411608","411609","411610","411611","411612","411613","411614","411615","411616","411617","411618","411619","411620","411621","411622","411623","411624","411625","411626","411627","411628","411629","411630","411631","411632","411633","411634","411635","411636","411637","411638","411639","411640","411641","411642","411643","411644","411645","411646","411647","411648","411649","411650","411651","411652","411653","411654","411655","411656","411657","411658","411659","411660","411661","411662","411663","411664","411665","411666","411667","411668","411669","411670","411671","411672","411673","411674","411675","411676","411677","411678","411679","411680","411681","411682","411683","411684","411685","411686","411687","411688","411689","411690","411691","411692","411693","411694","411695","411696","411697","411698","411699","411700","411701","411702","411703","411704","411705","411706","411707","411708","411709","411710","411711","411712","411713","411714","411715","411716","411717","411718","411719","411720","411721","411722","411723","411724","411725","411726","411727","411728","411729","411730","411731","411732","411733","411734","411735","411736","411737","411738","411739","411740","411741","411742","411743","411744","411745","411746","411747","411748","411749","411750","411751","411752","411753","411754","411755","411756","411757","411758","411759","411760","411761","411762","411763","411764","411765","411766","411767","411768","411769","411770","411771","411772","411773","411774","411775","411776","411777","411778","411779","411780","411781","411782","411783","411784","411785","411786","411787","411788","411789","411790","411791","411792","411793","411794","411795","411796","411797","411798","411799","411800","411801","411802","411803","411804","411805","411806","411807","411808","411809","411810","411811","411812","411813","411814","411815","411816","411817","411818","411819","411820","411821","411822","411823","411824","411825","411826","411827","411828","411829","411830","411831","411832","411833","411834","411835","411836","411837","411838","411839","411840","411841","411842","411843","411844","411845","411846","411847","411848","411849","411850","411851","411852","411853","411854","411855","411856","411857","411858","411859","411860","411861","411862","411863","411864","411865","411866","411867","411868","411869","411870","411871","411872","411873","411874","411875","411876","411877","411878","411879","411880","411881","411882","411883","411884","411885","411886","411887","411888","411889","411890","411891","411892","411893","411894","411895","411896","411897","411898","411899","411900","411901","411902","411903","411904","411905","411906","411907","411908","411909","411910","411911","411912","411913","411914","411915","411916","411917","411918","411919","411920","411921","411922","411923","411924","411925","411926","411927","411928","411929","411930","411931","411932","411933","411934","411935","411936","411937","411938","411939","411940","411941","411942","411943","411944","411945","411946","411947","411948","411949","411950","411951","411952","411953","411954","411955","411956","411957","411958","411959","411960","411961","411962","411963","411964","411965","411966","411967","411968","411969","411970","411971","411972","411973","411974","411975","411976","411977","411978","411979","411980","411981","411982","411983","411984","411985","411986","411987","411988","411989","411990","411991","411992","411993","411994","411995","411996","411997","411998","411999","412105","412108","412114","412201","412302","412220","412207","412308","421201","421202","421203","421301","421306","422001","422002","422004","422005","422006","422007","422009","422010","422011","422012","422013","422101","422102","422105","422206","422207","422402","431001","431002","431003","431004","431005","431105","431110","431131","431133","431136","431201","431210","431601","431602","431603","431604","431605","444001","444002","444003","444005","444004","452001","445203","452002","452003","452004","452006","452007","452008","452009","452010","462002","462003","462004","462007","462011","462012","462013","462016","462021","462022","462023","462024","462026","462027","462030","462031","462032","462036","462039","462041","462046","463107","463108","463109","463110","463111","463120","500001","500002","500003","500004","500005","500006","500007","500008","500009","500010","500012","500013","500014","500015","500016","500017","500018","500019","500020","500021","500022","500023","500024","500025","500026","500027","500028","500029","500030","500031","500033","500036","500037","500038","500034","500035","500032","500039","500040","500041","500042","500043","500044","500045","500046","500047","500048","500049","500050","500051","500052","500053","500054","500055","500056","500057","500058","500061","500062","500063","500064","500065","500066","500059","500060","500011","500067","500068","500069","500070","500071","500072","500073","500074","500075","500076","500077","500078","500079","500080","500083","500084","500085","500086","500087","500088","500089","500090","500091","500092","500093","500094","500081","500082","500095","500096","500097","500098","500409","501218","501401","501510","501511","502001","502032","501301","502123","502205","502220","502278","502307","502319","502291","502309","502324","502325","502329","518001","518002","518003","518005","518301","518360","518501","518502","518503","535128","560002","560003","560004","560005","560006","560007","560008","560009","560010","560011","560012","560013","560014","560015","560016","560018","560019","560020","560022","560001","560017","560021","560023","560024","560025","560026","560027","560028","560029","560030","560031","560032","560034","560035","560036","560038","560039","560040","560041","560042","560044","560046","560048","560049","560050","560033","560043","560037","560047","560045","560051","560052","560053","560054","560055","560056","560058","560059","560060","560061","560062","560063","560064","560065","560067","560068","560069","560070","560071","560072","560073","560074","560075","560076","560077","560078","560057","560066","560079","560080","560081","560082","560083","560084","560085","560086","560087","560088","560089","560090","560091","560092","560094","560096","560098","560099","560102","560103","560104","560093","560095","560106","560097","560100","560107","560108","560300","561229","562106","562114","562109","562158","562125","600001","600002","600003","600004","600005","600006","600007","600008","600009","600010","600011","600012","600013","600014","600015","600016","600017","600018","600019","600020","600021","600022","600023","600024","600025","600026","600027","600028","600029","600030","600031","600033","600034","600035","600036","600038","600039","600040","600041","600042","600043","600044","600037","600032","600045","600046","600047","600048","600049","600050","600051","600052","600053","600055","600056","600058","600059","600060","600061","600062","600063","600064","600065","600066","600067","600068","600069","600070","600071","600072","600057","600054","600074","600075","600076","600077","600078","600079","600080","600081","600082","600083","600084","600085","600086","600087","600088","600090","600091","600092","600093","600094","600095","600097","600098","600099","600100","600073","600089","600096","600101","600102","600103","600104","600105","600106","600107","600108","600109","600110","600111","600112","600114","600115","600116","600117","600118","600122","600126","600125","600113","600119","603002","602105","603102","603203","603202","603204","603110","603210","603103","603209","605001","605002","605003","605004","605005","605006","605007","605008","605010","605011","605012","605013","605014","605015","605016","605017","605018","605019","605020","605021","605022","605023","605024","605025","605026","605027","605028","605029","605030","605031","605032","605033","605034","605035","605036","605037","605038","605039","605040","605041","605042","605043","605044","605045","605046","605047","605048","605049","605050","605051","605052","605053","605054","605055","605056","605057","605058","605059","605060","605061","605062","605063","605064","605065","605066","605067","605068","605069","605070","605071","605072","605073","605074","605075","605076","605077","605078","605079","605080","605081","605082","605083","605084","605085","605086","605087","605088","605089","605090","605091","605092","605093","605094","605095","605096","605097","605098","605099","605100","605101","605102","605103","605104","605105","605106","605107","605108","605109","605110","605111","607402","641001","641002","641003","641004","641005","641006","641007","641008","641009","641010","641011","641012","641013","641014","641015","641016","641017","641018","641019","641020","641021","641022","641023","641024","641025","641026","641027","641028","641029","641030","641031","641032","641033","641034","641035","641036","641037","641038","641039","641040","641041","641042","641043","641044","641045","641046","641047","641062","641104","641105","641103","641049","641107","641048","641114","641301","641401","641402","641406","641111","641659","682001","682002","682003","682005","682006","682008","682009","682010","682011","682012","682013","682014","682015","682016","682017","682018","682019","682020","682021","682022","682023","682024","682025","682026","682027","682028","682029","682030","682031","682032","682033","682034","682035","682036","682037","682301","682302","682303","682304","682305","682306","682307","682308","682309","682310","682311","682312","682314","682317","682506","683101","683102","683103","683104","683105","683107","683108","683109","683501","683502","683503","683513","683518","683519","683525","683562","700001","700002","700003","700004","700005","700006","700008","700009","700010","700011","700012","700014","700007","700013","700015","700016","700017","700018","700019","700020","700021","700022","700023","700024","700025","700026","700027","700028","700029","700030","700031","700032","700033","700034","700035","700036","700038","700040","700041","700042","700037","700039","700043","700044","700045","700046","700047","700048","700049","700050","700051","700052","700053","700054","700055","700056","700057","700058","700059","700060","700061","700062","700063","700065","700066","700067","700068","700069","700070","700064","700071","700072","700073","700074","700075","700076","700077","700079","700080","700081","700082","700083","700084","700085","700086","700087","700089","700090","700092","700093","700094","700095","700096","700097","700098","700091","700088","700078","700099","700101","700102","700106","700108","700109","700110","700111","700115","700116","700117","700118","700120","700121","700122","700124","700100","700104","700103","700107","700105","700125","700113","700126","700114","700129","700130","700131","700127","700143","700145","700148","700150","700152","700128","700135","700136","700149","700132","700133","700142","700147","711101","700157","700156","700159","700160","711102","711103","711104","711106","711109","711111","711201","711202","711204","711205","711224","711227","711105","711107","711203","711302","711108","712101","712102","712103","712104","712105","712123","712124","712132","712137","712201","712202","712203","712204","712205","712222","712223","712232","712233","712136","712235","712248","712249","712258","712301","712310","712503","712504","712513","721601","721602","721603","721604","721605","721606","721607","721635","721657","743101","743102","743108","743121","743122","743127","743128","743134","743144","743145","743155","743165","743166","743175","743176","743177","743178","743179","743185","743186","743201","743203","743204","743205","743250","743275","743276","743296","743298","743302","743313","743318","743319","743330","743352","743358","743359","743361","743398","743413","743423","743424","743508","743510","743512","743515","743518","743612","743700","751001","751002","751003","751004","751005","751007","751008","751009","751011","751012","751013","751014","751015","751017","751019","751020","751021","751022","751023","751024","751025","751026","751027","751028","751029","751032","751033","751034","751035","751036","751037","751038","751039","751040","751041","751042","751043","751044","751045","751046","751047","751048","751049","751051","751052","751053","751056","751057","751058","751059","751060","751061","751062","751063","751064","751065","751066","751067","751068","751069","751070","751071","751072","751073","751074","751075","751076","751077","751078","751079","751080","751081","751082","751083","751084","751085","751086","751087","751088","751089","751090","751091","751092","751093","751094","751095","751096","751097","751098","751099","751100","638157","821108","382443","641407","590012","382427","382430","382475","391330","501505","560109","562130","700112","641109","700154","641201","641654","515214","682000"]

lenderConfiguration:
  configurations:
    EFA:
    -
      consentTextKey: {{ getv "/efaCitiConsentKey" }}
      hasAdditionalForm: true
      interestRate: {{ getv "/efaCitiInterestRate" }}
      emiSupported: true
      paylaterSupported: false
      lateFees: {{ getv "/efaCitiLateFee" }}
      lender: CITI
      postApprovalTncKey: {{ getv "/efaCitiPostApprovalTncKey" }}
      preApprovalTncKey: {{ getv "/efaCitiPreApprovalTncKey" }}
      showKycDetails: false
      primaryDataEditable: false
    -
      consentTextKey: {{ getv "/efaKisshtConsentKey" }}
      hasAdditionalForm: false
      interestRate: {{ getv "/efaKisshtInterestRate" }}
      emiSupported: true
      paylaterSupported: true
      lateFees: {{ getv "/efaKisshtLateFee" }}
      lender: KISSHT
      postApprovalTncKey: {{ getv "/efaKisshtPostApprovalTncKey" }}
      preApprovalTncKey: {{ getv "/efaKisshtPreApprovalTncKey" }}
      showKycDetails: true
      primaryDataEditable: true
    -
      consentTextKey: {{ getv "/efaKisshtConsentKey" }}
      hasAdditionalForm: false
      interestRate: {{ getv "/efaKisshtInterestRate" }}
      emiSupported: true
      paylaterSupported: true
      lateFees: {{ getv "/efaKisshtLateFee" }}
      lender: INDIA_BULLS
      postApprovalTncKey: {{ getv "/efaKisshtPostApprovalTncKey" }}
      preApprovalTncKey: {{ getv "/efaKisshtPreApprovalTncKey" }}
      showKycDetails: true
      primaryDataEditable: true

    BNPL:
    -
      consentTextKey: dummy
      hasAdditionalForm: false
      interestRate: 0
      emiSupported: false
      paylaterSupported: true
      lateFees: 0
      lender: KISSHT
      postApprovalTncKey: dummy
      preApprovalTncKey: dummy
      showKycDetails: false
      primaryDataEditable: false
      minimumAgeRequired: 0
    -
      hasAdditionalForm: false
      lender: IDFC
      primaryDataEditable: true
      nameMatchThreshold: {{ getv "/idfcBnplEkycNameMatchThreshold" }}
      nameValidationEnabled: {{ getv "/idfcBnplEkycNameValidationEnabled" }}
      panValidationRequired: {{ getv "/isIdfcPanValidationRequired" }}
      postApprovalTncKey: dummy
      preApprovalTncKey: dummy
      consentTextKey: dummy

    FLIPKART_ADVANZ:
    -
      hasAdditionalForm: false
      lender: IDFC
      primaryDataEditable: true
      nameMatchThreshold: {{ getv "/idfcEkycNameMatchThreshold" }}
      nameValidationEnabled: {{ getv "/idfcEkycNameValidationEnabled" }}
      panValidationRequired: {{ getv "/isIdfcPanValidationRequired" }}
      postApprovalTncKey: dummy
      preApprovalTncKey: dummy
      consentTextKey: dummy
      minimumAgeRequired: {{ getv "/idfcAdvanzMinimumAgeRequired" }}

    CBC:
    -
      lender: AXIS

upgradationConfig:
  upgradationEnabled: {{ getv "/upgradationEnabled" }}

tijoriConfig:
  url: {{ getv "/tijoriUrl" }}
  clientName: robinhood

tijoriAsyncClientConfig:
  merchant: mp_flipkart
  exchangeName: {{ getv "/tijoriExchangeName" }}
  tijoriUrl: {{ getv "/tijoriUrl" }}

external_client_config:
  page_service_config:
    host: {{ getv "/pageServiceHost" }}
    port: {{ getv "/pageServicePort" }}

cbcOnboardingConfig:
  fkpayTriggerOtpUrl: {{ getv "/cbcFkpayTriggerOtpUrl" }}
  fkpayDisplayCardEtbUrl: {{ getv "/cbcFkpayDisplayCardEtbUrl" }}
  fkpayDisplayCardEtccUrl: {{ getv "/cbcFkpayDisplayCardEtccUrl" }}
  fkpayDisplayCardConsoleUrl: {{ getv "/cbcFkpayDisplayCardConsoleUrl" }}
  etbApprovalCallouts: {{ getv "/cbcEtbApprovalCallouts" }}
  ntbApprovalCallouts: {{ getv "/cbcNtbApprovalCallouts" }}
  cbcComingSoonBannerUrl: {{ getv "/cbcComingSoonBannerUrl" }}
  cbcApplyNowBannerUrl: {{ getv "/cbcApplyNowBannerUrl" }}
  cbcContinueApplicationBannerUrl: {{ getv "/cbcContinueApplicationBannerUrl" }}
  cbcViewCardDetailsBannerUrl: {{ getv "/cbcViewCardDetailsBannerUrl" }}
  cbcApplicationEtccBuzzBannerUrl: {{ getv "/cbcApplicationEtccBuzzBannerUrl" }}
  cbcApplicationEtccNonBuzzBannerUrl: {{ getv "/cbcApplicationEtccNonBuzzBannerUrl" }}
  cbcApplicationEtccBuzzBannerAspectRatio: {{ getv "/cbcApplicationEtccBuzzBannerAspectRatio" }}
  cbcApplicationEtccNonBuzzBannerAspectRatio: {{ getv "/cbcApplicationEtccNonBuzzBannerAspectRatio" }}
  externalMerchnats: ["phonepe","offers","myntra","offers_FYFemployee","myntra_employee","mastercard","wakefit","referral"]
  plusWhitelistIdMap:
   phonepe : {{ getv "/cbcPhonePeWhitelistIdFkPlus" }}
   offers_FYFemployee : {{ getv "/cbcOpenWhitelistId" }}
   offers : {{ getv "/cbcOpenWhitelistId" }}
   myntra_employee : {{ getv "/cbcOpenWhitelistId" }}
   myntra: {{ getv "/cbcMyntraWhitelistIdFkPlus" }}
   mastercard: {{ getv "/cbcOpenWhitelistId" }}
   wakefit: {{ getv "/cbcOpenWhitelistId" }}
   referral : {{ getv "/cbcOpenWhitelistId" }}
  whitelistIdMap:
   phonepe : {{ getv "/cbcPhonePeWhiteListId" }}
   offers_FYFemployee : {{ getv "/cbcOpenWhitelistId" }}
   offers : {{ getv "/cbcOpenWhitelistId" }}
   myntra_employee : {{ getv "/cbcOpenWhitelistId" }}
   myntra: {{ getv "/cbcMyntraWhitelistId" }}
   mastercard: {{ getv "/cbcOpenWhitelistId" }}
   wakefit: {{ getv "/cbcOpenWhitelistId" }}
   referral : {{ getv "/cbcOpenWhitelistId" }}

  ntbFormSubTypeWiseFormList:
      PERSONAL_DETAILS:
        - personal_details
      CONTACT_DETAILS:
        - contact_details
        - address_form
      PROFESSIONAL_DETAILS:
        - professional_details
      CREDIT_CARD_FEATURES:
        - credit_card_features
  etsFormSubTypeWiseFormList:
      PERSONAL_DETAILS:
        - personal_details
      CONTACT_DETAILS:
        - contact_details
        - address_form
      PROFESSIONAL_DETAILS:
        - professional_details
      CREDIT_CARD_FEATURES:
        - credit_card_features
  accountsEnabledForCbcSplit: {{ getv "/accountsEnabledForCbcSplit" }}
  accountsEnabledForConsoleCug: {{ getv "/accountsEnabledForConsoleCug" }}
  accountsEnabledForCbcNtbSplitForm: {{ getv "/accountsEnabledForCbcNtbSplitForm" }}
  channelsEnabledForCbcNtbSplitForm: {{ getv "/channelsEnabledForCbcNtbSplitForm" }}
  whitelistIdsEnabledForCbcNtbSplitForm: {{ getv "/whitelistIdsEnabledForCbcNtbSplitForm" }}
  cbcNtbSplitEnabled: {{ getv "/cbcNtbSplitEnabled" }}
  cohortWiseFormList:
      ETB_PRE_APPROVED:
      - personal_details
      - address_form
      - credit_card_features
      ETB_NON_PRE_APPROVED:
      - personal_details
      - address_form
      - professional_details
      - credit_card_features
      ETB_PRE_APPROVED_AUTODEBIT:
      - personal_details
      - autodebit_details
      - address_form
      - credit_card_features
      ETB_NON_PRE_APPROVED_AUTODEBIT:
      - personal_details
      - address_form
      - professional_details
      - autodebit_details
      - credit_card_features
      ETCC:
      - personal_details
      - address_form
      NTB:
      - personal_details
      - contact_details
      - professional_details
      - address_form
      - credit_card_features
  axisThinCremoScoreThreshold: {{ getv "/axisThinCremoScoreThreshold" }}
  autoRejectThinEnabled: {{ getv "/autoRejectThinEnabled" }}
  encryptionKey: "53EACE72CD83D6B60754C2F3959168EA"
  dobSubtextEnabled: {{ getv "/dobSubtextEnabled" }}
  kycSchedulerTtl: {{getv "/kycSchedulerTtl"}}
  accountsEnabledForCheckStatusV3: {{ getv "/accountsEnabledForCheckStatusV3" }}
  accountEnabledForKycV2: {{ getv "/accountEnabledForKycV2" }}
  firstYearFreeStartDate: {{ getv "/firstYearFreeStartDate" }}
  firstYearFreeEndDate: {{ getv "/firstYearFreeEndDate" }}
  accountsEnabledForStoreMergeExp: {{ getv "/accountsEnabledForStoreMergeExp" }}
  storeMergeEnabled: {{ getv "/storeMergeEnabled" }}
  cbcVkycEnabled: {{ getv "/vkycEnabled" }}
  emiUserList: {{ getv "/emiUserList" }}
  emiBillingEnabled: {{ getv "/emiBillingEnabled"}}
  ntbCbcWhitelistIds: {{ getv "/ntbCbcWhitelistIds" }}
  etccCbcWhitelistIds: {{ getv "/etccCbcWhitelistIds" }}
  etbpaCbcWhitelistIds: {{ getv "/etbpaCbcWhitelistIds" }}
  etbnpaCbcWhitelistIds: {{ getv "/etbnpaCbcWhitelistIds" }}
  ntbSupercoinWhitelistIds: {{ getv "/ntbSupercoinWhitelistIds" }}
  etccSupercoinWhitelistIds: {{ getv "/etccSupercoinWhitelistIds" }}
  etbpaSupercoinWhitelistIds: {{ getv "/etbpaSupercoinWhitelistIds" }}
  etbnpaSupercoinWhitelistIds: {{ getv "/etbnpaSupercoinWhitelistIds" }}
  whitelistIdForOfflineCustomers: {{ getv "/whitelistIdOffline" }}
  accountsEnabledForEtccAlternateCug: {{ getv "/accountsEnabledForEtccAlternateCug" }}
  accountsEnabledForTransactionSummary: {{ getv "/transactionSummaryAccountEnabled" }}
  externalWhitelistIds : {{ getv "/externalWhitelistIds" }}
  incomeBinThreshold: {{ getv "/incomeBinThreshold" }}
  cbcHyperlinkPhoneEnabled: {{ getv "/cbcHyperlinkPhoneEnabled" }}
  checkApplyNowVisibilty: {{ getv "/checkApplyNowVisibilty" }}
  cbcNewRejectPageEnabled: {{ getv "/cbcNewRejectPageEnabled" }}
  ntbCugUserList: {{ getv "/ntbCugUserList" }}
  cbcNtbRevampValue: {{ getv "/cbcNtbRevampValue" }}
  applicationRetryDuration: {{ getv "/applicationRetryDuration" }}
  applicationSyncCutoffDate: {{ getv "/applicationSyncCutoffDate" }}

collectionsClientConfiguration:
  scheme: http
  host: {{ getv "/collectionServiceUrl" }}
  port: {{ getv "/collectionServicePort" }}

contextWiseWhitelistConfig:
  contextMap:
    NUDGING-EFA-INDIA_BULLS:
      isEnabledForEveryone: {{ getv "/EfaIbNudgingIsEnabledForEveryoneFlag" }}
      whitelistedAccountIds: {{ getv "/EfaIbNudgingWhitelistedAccountIds" }}
    NUDGING-CBC-AXIS:
          isEnabledForEveryone: {{ getv "/CbcAxisNudgingIsEnabledForEveryoneFlag" }}
          whitelistedAccountIds: {{ getv "/CbcAxisNudgingWhitelistedAccountIds" }}
    NPS-CBC-AXIS:
          isEnabledForEveryone: {{ getv "/CbcAxisNpsIsEnabledForEveryoneFlag" }}
          whitelistedAccountIds: {{ getv "/CbcAxisNpsWhitelistedAccountIds" }}
    NUDGING-FLIPKART_ADVANZ-IDFC:
        isEnabledForEveryone: {{ getv "/advanzIDFCNudgingIsEnabledForEveryoneFlag" }}
        whitelistedAccountIds: {{ getv "/advanzIDFCNudgingWhitelistedAccountIds" }}

contextWiseCacheBuilderConfig:
  contextMap:
    PAN_RETRY:
      maximumSize: {{ getv "/panRetryCacheMaximumSize" }}
      duration: {{ getv "/panRetryCacheDuration" }}
      durationUnit: {{ getv "/panRetryCacheDurationUnit" }}

schedulerClientConfig:
  host: {{ getv "/schedulerHost" }}
  port: {{ getv "/schedulerPort" }}
  poolSize: {{ getv "/schedulerPoolSize" }}
  clientId: {{ getv "/schedulerClientId" }}

heliosProxyConfig:
  maxConnections: {{ getv "/proxyMaxConnections" }}
  maxConnectionsPerHost: {{ getv "/proxyMaxConnectionsPerHost" }}
  pooledConnectionIdleTimeoutInMS: {{ getv "/proxyPooledConnectionIdleTimeoutInMS" }}
  executorThreadsCount: {{ getv "/proxyExecutorThreadsCount" }}
  executionIsolationSemaphoreMaxConcurrentRequests: {{ getv "/proxyExecutionIsolationSemaphoreMaxConcurrentRequests" }}
  executionTimeoutInMS: {{ getv "/proxyExecutionTimeoutInMS" }}
  hystrixCommandKey: {{ getv "/proxyHystrixCommandKey" }}
  hystrixGroupKey: {{ getv "/proxyHystrixGroupKey" }}
  rewriteProtocal: {{ getv "/proxyRewriteProtocal" }}
  rewriteHostName: {{ getv "/proxyRewriteHostName" }}
  rewritePortNumber: {{ getv "/proxyRewritePortNumber" }}
  enableProxy: {{ getv "/proxyEnable" }}

bigfootCallbackConfiguration:
    pinakaBaseUrl: {{ getv "/pinakaUrl" }}
    callbackQueue: {{ getv "/bigFootExchangeName" }}

fldgConfiguration:
  enableFldg: {{ getv "/enableFldg" }}

abConfiguration:
  endpoint: {{ getv "/abUrl" }}
  layerList: "Affordability"
  clientId: "Mapi.affordability"
  testAccounts: {{ getv "/testAccountsForAbService" }}
  unlayeredAbEnabled: false
  clientSecretKey: fintech-pinaka-prod-ee4b7347a0ee409f88cccb70d54238c1

kycConfig:
  xmlKycAndroidAppVersion: {{ getv "/xmlKycAndroidAppVersion" }}
  xmlKycChannels: {{ getv "/xmlKycChannels" }}
  activeKycModes: {{ getv "/activeKycModes" }}
  createNewKycApplicationForOldUserEnabled: {{ getv "/createNewKycApplicationForOldUserEnabled" }}
  defaultMethodForTestAccount: {{ getv "/defaultKycMethodForTestAccount" }}

onboardingConfig:
  upgradeJourneyLimitReductionAllowed: {{ getv "/upgradeJourneyLimitReductionAllowed" }}
  genericFlowEnabled: {{ getv "/onboardingGenericFlowEnabled" }}
  whitelistChangeEntityEnabled: {{ getv "/whitelistChangeEntityEnabled" }}
  genericFlowAccountIds: {{ getv "/onboardingGenericFlowAccountIds" }}
  plusWhitelistId: {{ getv "/plusPayLaterWhitelistId" }}
  productOnboardingConfig:
      BNPL:
        storePageUrl: {{ getv "/bnplStorePageUrl" }}
        queue: {{ getv "/bnplOnboardingQueue" }}
      FLIPKART_ADVANZ:
        storePageUrl: {{ getv "/advanzStorePageUrl" }}
        queue: {{ getv "/advanzOnboardingQueue" }}

alfredClientConfig:
  url: "{{ getv "/alfredClientUrl" }}"
  secretKey: "{{ getv "/alfredClientSecretKey" }}"

neoCrmClientConfig:
  exchangeName: "{{ getv "/neoCrmExchangeName" }}"
  authToken: "{{ getv "/neoCrmAuthToken" }}"
  clientId: "{{ getv "/neoCrmClientId" }}"
  url: "{{ getv "/neoCrmUrl" }}"
  path: "{{ getv "/neoCrmPath" }}"

uiConfiguration:
  payLaterCohortPriorityList: {{ getv "/payLaterCohortPriorityList" }}
  fsupCohortPriorityList: {{ getv "/fsupCohortPriorityList" }}
  cohorts:
    CASH:
      name: "{{ getv "/cash.name" }}"
      full_description: "{{ getv "/cash.description" }}"
    EMI:
      name: "{{ getv "/emi.name" }}"
      full_description: "{{ getv "/emi.description" }}"
    PAY_LATER:
      name: "{{ getv "/payLater.name" }}"
      full_description: "{{ getv "/payLater.description" }}"
      minimized_description: "{{getv "/payLater.minimizedDescription"}}"
      logo_url: "{{getv "/payLater.logoUrl"}}"
    FSUP:
      name: "{{ getv "/fsup.name" }}"
      full_description: "{{ getv "/fsup.description" }}"
      minimized_description: "{{ getv "/fsup.minimizedDescription" }}"
      logo_url: "{{ getv "/fsup.logoUrl" }}"
  uiConfigMap:
    APPROVED:
      title: "{{ getv "/approvalScreen.title" }}"
      nonUpgradedTitle: "{{ getv "/approvalScreen.nonUpgradedTitle" }}"
      nonUpgradedSubtitle : "{{ getv "/approvalScreen.nonUpgradedSubtitle" }}"
      emiSubtitle: "{{ getv "/approvalScreen.emiSubtitle" }}"
      payLaterSubtitle: "{{ getv "/approvalScreen.payLaterSubtitle" }}"
      imageUrl: "{{ getv "/approvalScreen.imageUrl" }}"
      nonUpgradedImageUrl: "{{ getv "/approvalScreen.nonUpgradedImageUrl" }}"
      logoUrl: "{{ getv "/approvalScreen.logoUrl" }}"
      knowMoreText: "{{ getv "/approvalScreen.knowMoreText" }}"
      knowMoreLandingUrl: "{{ getv "/approvalScreen.knowMoreLandingUrl" }}"
      payLaterSubmitButtonText: "{{ getv "/approvalScreen.submitButtonText" }}"
      payLaterSubmitButtonUrl: "{{ getv "/approvalScreen.submitButtonUrl" }}"
      fsupSubmitButtonText: "{{ getv "/fsup.submitButtonText" }}"
      creditInfoTitle: "{{ getv "/approvalScreen.creditInfoTitle" }}"
      creditInfoLateFeeText: "{{ getv "/approvalScreen.creditInfoLateFeeText" }}"
      howToUseInfo:
        title: "{{ getv "/approvalScreen.howToUse.title" }}"
        stepsList:
          -
            description: "{{ getv "/approvalScreen.howToUse.step1.description" }}"
            imageUrl: "{{ getv "/approvalScreen.howToUse.step1.imageUrl" }}"
            title: "{{ getv "/approvalScreen.howToUse.step1.title" }}"
          -
            description: "{{ getv "/approvalScreen.howToUse.step2.description" }}"
            imageUrl: "{{ getv "/approvalScreen.howToUse.step2.imageUrl" }}"
            title: "{{ getv "/approvalScreen.howToUse.step2.title" }}"
          -
            description: "{{ getv "/approvalScreen.howToUse.step3.description" }}"
            imageUrl: "{{ getv "/approvalScreen.howToUse.step3.imageUrl" }}"
            title: "{{ getv "/approvalScreen.howToUse.step3.title" }}"
    CONDITIONALLY_APPROVED:
      title: "{{ getv "/approvalScreen.title" }}"
      nonUpgradedTitle: "{{ getv "/approvalScreen.title" }}"
      nonUpgradedSubtitle : "{{ getv "/conditionalApproval.subtitle" }}"
      emiSubtitle: "{{ getv "/conditionalApproval.subtitle" }}"
      payLaterSubtitle: "{{ getv "/conditionalApproval.subtitle" }}"
      imageUrl: "{{ getv "/approvalScreen.imageUrl" }}"
      nonUpgradedImageUrl: "{{ getv "/approvalScreen.imageUrl" }}"
      logoUrl: "{{ getv "/approvalScreen.logoUrl" }}"
      knowMoreText: "{{ getv "/approvalScreen.knowMoreText" }}"
      knowMoreLandingUrl: "{{ getv "/approvalScreen.knowMoreLandingUrl" }}"
      payLaterSubmitButtonText: "{{ getv "/approvalScreen.submitButtonText" }}"
      payLaterSubmitButtonUrl: "{{ getv "/approvalScreen.submitButtonUrl" }}"
      creditInfoTitle: "{{ getv "/approvalScreen.creditInfoTitle" }}"
      creditInfoLateFeeText: "{{ getv "/approvalScreen.creditInfoLateFeeText" }}"
      howToUseInfo:
        title: "{{ getv "/approvalScreen.howToUse.title" }}"
        stepsList:
          -
            description: "{{ getv "/approvalScreen.howToUse.step1.description" }}"
            imageUrl: "{{ getv "/approvalScreen.howToUse.step1.imageUrl" }}"
            title: "{{ getv "/approvalScreen.howToUse.step1.title" }}"
          -
            description: "{{ getv "/approvalScreen.howToUse.step2.description" }}"
            imageUrl: "{{ getv "/approvalScreen.howToUse.step2.imageUrl" }}"
            title: "{{ getv "/approvalScreen.howToUse.step2.title" }}"
          -
            description: "{{ getv "/approvalScreen.howToUse.step3.description" }}"
            imageUrl: "{{ getv "/approvalScreen.howToUse.step3.imageUrl" }}"
            title: "{{ getv "/approvalScreen.howToUse.step3.title" }}"
    REJECTED:
      title: "{{ getv "/rejectionScreen.title" }}"
      subtitle: "{{ getv "/rejectionScreen.subtitle" }}"
      payLaterSubmitButtonText: "{{ getv "/rejectionScreen.submitButtonText" }}"
      payLaterSubmitButtonUrl: "{{ getv "/rejectionScreen.submitButtonUrl" }}"
      fsupSubmitButtonText: "{{ getv "/fsup.submitButtonText" }}"
      imageUrl: "{{ getv "/rejectionScreen.imageUrl" }}"
      nonUpgradedTitle: "{{ getv "/approvalScreen.nonUpgradedTitle" }}"
      nonUpgradedImageUrl: "{{ getv "/approvalScreen.nonUpgradedImageUrl" }}"
      nonUpgradedSubtitle : "{{ getv "/approvalScreen.nonUpgradedSubtitle" }}"



mysqlLockConfig:
  prefix: {{ getv "/mysqlLockPrefix" }}
  timeout: {{ getv "/mysqlLockTimeout" }}

dataPointConfiguration:
  enableDataPoint:
    TS: {{ getv "/enableTS"}}

dataPointConfiguration:
  enableDataPoint:
    TS:
      enable: {{ getv "/enableTS"}}
      testAccountList: {{ getv "/TSTestAccounts"}}

redisConfig:
  masterName: {{ getv "/redisMasterName"}}
  timeout: {{ getv "/redisTimeout"}}
  sentinelAddresses: {{ getv "/redisSentinelAddress"}}
  password: {{ getv "/redisPassword"}}
  masterConnectionMinimumIdleSize: {{ getv "/redisMasterConnectionMinIdleSize"}}
  masterConnectionPoolSize: {{ getv "/redisMasterConnectionPoolSize"}}
  lockWaitTime: {{ getv "/redisMasterLockWaitTime"}}
  lockReleaseTime: {{ getv "/redisMasterLockReleaseTime"}}

sourceAttributionConfig:
  enabled: {{ getv "/sourceAttributionConfigEnabled"}}
  redisTtlInSeconds: {{ getv "/sourceAttributionRedisTtlInSeconds"}}

rotation:
  enableRotator: true
  defaultRotationStatus: true

winterfellClientConfig:
  host: {{ getv "/winterfellHost"}}
  clientId: {{ getv "/winterfellClientId"}}
  connectionTimeout: {{ getv "/winterfellConnectionTimeout"}}
  readTimeout: {{ getv "/winterfellReadTimeout"}}

lockinClientConfig:
  url: {{ getv "/lockin.url"}}
  clientId: {{ getv "/lockin.clientId"}}

coinManagerClientConfig:
  url: {{ getv "/coinManager.url"}}
  clientId: {{ getv "/coinManager.clientId"}}
  merchantId: {{ getv "/coinManager.merchantId"}}
  requestId: {{ getv "/coinManager.requestId"}}

robinhoodAsyncCbcConfig:
  url: {{ getv "/robinHoodUrl" }}
  exchangeName: "cbc_robinhood_queue"

skylerClientConfig:
  url: http://localhost:9009
  clientId: "pinaka"
  exchangeName: skylerQueue

robinhoodCfaClientConfig:
  url: {{ getv "/robinHoodUrl" }}
  exchangeName: "cfa_onboarding_queue"

coreLogisticsClientConfig:
  url: http://**********
  referer: "http://www.fintech.com"
  key: "79a271ce-b870-c03b-cdbb-a52861a90ea9"

pandoraAsyncClientConfig:
  url: {{ getv "/pandoraUrl" }}
  exchangeName: {{ getv "/ffbExchangeName"}}
  exchangeType: queue


ffbConfig:
  url: {{ getv "/ffbUrl"}}
  endPoint: {{ getv "/ffbCallbackEndPoint"}}
  exchangeName : {{ getv "/ffbExchangeName"}}
  environment: {{ getv "/ffbEnvironment"}}
  businessToLenderDocumentTypeMapping:
    BUSINESS_PROOF: BUSINESS_PROOF
    SHOP1: OTHER_1
    SHOP2: OTHER_2
    SHOP3: OTHER_3

kafkaConsumerConfiguration:
  enableConsumer: {{ getv "/enableKafkaConsumer"}}
  kafkaConsumerProps:
    AMS:
      bootStrapServers: {{ getv "/kafkaBrokers"}}
      groupId: {{ getv "/kafkaAMSEventConsumerGroupId"}}
      sessionTimeoutMs: {{ getv "/kafkaSessionTimeOut"}}
      noOfThreads: {{ getv "/kafkaConsumerThreadCount"}}
      topic: {{ getv "/kafkaAMSTopicName"}}
      pollTimeOut: {{ getv "/kafkaPollTimeOut"}}
      enableDlq: {{ getv "/enableDlq"}}
      dlqTopicName: {{ getv "/dlqTopicName"}}

encryptionKeys:
  kycKey: {{ getv "/kycKey"}}
  cbcKey: {{ getv "/cbcKey"}}
  default: {{ getv "/default"}}

pinakaAsyncClientConfig:
  url: {{ getv "/pinakaUrl" }}
  exchangeName: {{ getv "/pinakaExchangeName"}}
  ebcExchangeName: {{ getv "/ebcExchangeName" }}
  changeEntityEventTopic: {{ getv "/changeEntityEventTopic" }}
  cfaExchangeName: {{ getv "/cfaExchangeName" }}
  advanzDedupeExchangeName: {{ getv "/advanzDedupeExchangeName" }}

plutusClientConfig:
  url: {{ getv "/plutusUrl" }}

fintechUserServiceClientConfig:
  uslUrl: {{ getv "/fintechUserServiceUrl"}}
  exchangeName: {{ getv "/uslExchangeName"}}

caishenClientConfig:
  url: {{ getv "/caishenUrl"}}
  targetClientId: {{ getv "/caishenTargetClientId"}}

khaataAsyncClientConfig:
  url: {{ getv "/khaataUrl" }}
  exchangeName: {{ getv "/cbcBilling.initiationQueue" }}

skylerAsyncClientConfig:
  url: {{ getv "/skylerUrl" }}
  exchangeName: {{ getv "/cbcBilling.initiationQueue" }}

fkPayClientConfig:
  url: {{ getv "/fkPayUrl"}}
  clientId: {{ getv "/fkPayClientId"}}
  clientSecret: {{ getv "/fkPayClientSecret"}}
  encryptionKey: {{ getv "/fkPayEncryptionkey"}}

stratumD42Configuration:
  accessKey: {{ getv "/d42AccessKey" }}
  secretKey: {{ getv "/d42SecretKey" }}
  endPoint: {{ getv "/d42Endpoint" }}
  maxConnections: {{ getv "/d42MaxConnection" }}
  connectionTimeout: {{ getv "/d42ConnectionTimeout" }}

outboundBundleConfiguration:
  defaultDaysToKeep: {{ getv "/defaultDaysToKeep"}}
  shardNames: {{ getv "/shardNames"}}
  databaseName: {{ getv "/databaseName"}}

uslClientConfig:
  host: {{ getv "/fintechUserServiceUrl" }}
  clientId: pinaka
  connectionTimeout: {{ getv "/uslConnectionTimeoutInMs"}}
  readTimeout: {{ getv "/uslReadTimeoutInMs" }}

audienceManagerConfiguration:
    kafkaProducerConfiguration:
        bootstrapServer: {{ getv "/amKafkaProducerBootstrapServer" }}
        acks: {{ getv "/amKafkaProducerAcks" }}
        retries: {{ getv "/amKafkaProducerRetries" }}
        batchSize: {{ getv "/amKafkaProducerBatchSize" }}
        lingerInMilliSecond: {{ getv "/amKafkaProducerLingerInMs" }}
        maxInFlightRequestsPerConnection: {{ getv "/maxInFlightRequestsPerConnection" }}
    realtimeSegmentConfiguration:
        enabled: {{ getv "/amReatimeSegmentationUsecaseEnabled" }}
        testAccounts: {{ getv "/amReatimeSegmentationTestAccounts" }}
        topicName: {{ getv "/amReatimeSegmentationTopicName" }}
        enabledJourneyList: {{ getv "/amReatimeSegmentationEnabledJourneyList" }}
    redisTtlInSeconds: {{ getv "/amRedisTtlInSeconds"}}

financialProviderConfig:
  kycMethodConfig:
    AADHAAR_XML_EKYC:
      metadata:
        IDFC:
            tncUrl: {{ getv "/idfcTncUrl" }}
            tollFreeNo: {{ getv "/idfcTollFreeNo" }}
            email: {{ getv "/idfcEmailId" }}
            xmlKycTncUrl: {{ getv "/idfcXmlKycTncUrl" }}
            xmlKycDisclaimer: {{ getv "/idfcXmlKycDisclaimer" }}
            reviewAndSubmitDisclaimer: {{ getv "/idfcReviewAndSubmitDisclaimer" }}
            reviewAndSubmitTncUrl: {{ getv "/idfcReviewAndSubmitTncUrl" }}
            reviewAndSubmitTncText: {{ getv "/idfcReviewAndSubmitTncText" }}
            consentContext: {{ getv "/idfcXmlKycConsentContext" }}
            kycType: {{ getv "/xmlKycType"}}
      validation:
          IDFC:
            nameMatchThreshold: {{ getv "/idfcNameMatchThreshold" }}
            nameValidationEnabled: {{ getv "/idfcNameValidationEnabled" }}
            panValidationRequired: {{ getv "/idfcPanValidationRequired" }}
      validityInYears: {{ getv "/xmlKycValidityInYears" }}
      validityExtensionPeriod : {{ getv "/xmlKycValidityExtensionPeriod" }}
      supportedAndroidAppVersion: {{ getv "/xmlKycAndroidAppVersion" }}
      supportedChannelList: {{ getv "/xmlKycChannels" }}
    EKYC:
      metadata:
        IDFC:
          consentContext: {{ getv "/idfcEKycConsentContext" }}
          kycType: {{ getv "/eKycType"}}
      validation:
        IDFC:
          nameMatchThreshold: {{ getv "/idfcNameMatchThreshold" }}
          nameValidationEnabled: {{ getv "/idfcNameValidationEnabled" }}
          panValidationRequired: {{ getv "/idfcPanValidationRequired" }}
      validityInYears: {{ getv "/eKycValidityInYears" }}
      validityExtensionPeriod : {{ getv "/ekycValidityExtensionPeriod" }}
    CKYC:
      metadata:
        IDFC:
          tncUrl: {{ getv "/idfcTncUrl" }}
          tollFreeNo: {{ getv "/idfcTollFreeNo" }}
          email: {{ getv "/idfcEmailId" }}
          disclaimer: {{ getv "/idfcCkycDisclaimer" }}
          reviewAndSubmitDisclaimer: {{ getv "/idfcReviewAndSubmitDisclaimer" }}
          reviewAndSubmitTncUrl: {{ getv "/idfcReviewAndSubmitTncUrl" }}
          reviewAndSubmitTncText: {{ getv "/idfcReviewAndSubmitTncText" }}
          consentContext: {{ "/idfcCkycConsentContext" }}
          kycType: {{ getv "/cKycType"}}
          incorrectDetailsPopupHeading: {{ getv "/idfcIncorrectDetailsPopupHeading"}}
          incorrectDetailsPopupText: {{ getv "/idfcIncorrectDetailsPopupText"}}
          incorrectDetailsPopupImageUrl: {{ getv "/idfcIncorrectDetailsPopupImageUrl"}}
          incorrectDetailsPopupButtonText: {{ getv "/idfcIncorrectDetailsPopupButtonText"}}
      validation:
        IDFC:
          nameMatchThreshold: {{ getv "/idfcNameMatchThreshold" }}
          nameValidationEnabled: {{ getv "/idfcNameValidationEnabled" }}
          panValidationRequired: {{ getv "/idfcPanValidationRequired" }}
      validityInYears: {{ getv "/cKycValidityInYears" }}
      validityExtensionPeriod : {{ getv "/ckycValidityExtensionPeriod" }}
      fallbackMethods: {{ getv "/ckycFallbackMethods"}}

ffbProxyConfiguration:
  maxConnections: {{ getv "/ffbProxyMaxConnections" }}
  maxConnectionsPerHost: {{ getv "/ffbProxyMaxConnectionsPerHost" }}
  pooledConnectionIdleTimeoutInMS: {{ getv "/ffbProxyPooledConnectionIdleTimeoutInMS" }}
  executorThreadsCount: {{ getv "/ffbProxyExecutorThreadsCount" }}
  executionIsolationSemaphoreMaxConcurrentRequests: {{ getv "/ffbProxyExecutionIsolationSemaphoreMaxConcurrentRequests" }}
  executionTimeoutInMS: {{ getv "/ffbProxyExecutionTimeoutInMS" }}
  hystrixCommandKey: {{ getv "/ffbProxyHystrixCommandKey" }}
  hystrixGroupKey: {{ getv "/ffbProxyHystrixGroupKey" }}
  rewriteProtocal: {{ getv "/ffbProxyRewriteProtocal" }}
  rewriteHostName: {{ getv "/ffbProxyRewriteHostName" }}
  rewritePortNumber: {{ getv "/ffbProxyRewritePortNumber" }}
  enableProxy: {{ getv "/ffbProxyEnable" }}

orchestratorClientConfig:
  url: {{ getv "/orchestratorURL" }}

hystrixModuleConfiguration:
  hystrixPropertiesFileName: hystrix.properties
  resourcesFilePath: {{ getv "/resourcesFilePath"}}

multiTenantOAuthClientConfig:
  cachedAccessTokenTTL: {{ getv "/oAuthCachedAccessTokenTTL" }}
  oAuthUrl: {{ getv "/oAuthUrl" }}
  oAuthServiceClientConfigMap:
    FK_CONSUMER_CREDIT:
      oAuthClientID: {{ getv "/oAuthClientId" }}
      oAuthClientSecret: {{ getv "/oAuthClientSecret" }}
      cachedAccessTokenTTL: {{ getv "/oAuthCachedAccessTokenTTL" }}
      oAuthUrl: {{ getv "/oAuthUrl" }}

multiTenantConnektClientConfig:
  tenantConnektClientConfigMap:
    FK_CONSUMER_CREDIT:
      exchangeName: {{ getv "/connekt.exchangeName"}}
      callbackUrl: {{ getv "/connekt.callbackUrl"}}
      domain: {{ getv "/connekt.domain"}}
      emailUrl: {{ getv "/connekt.emailUrl"}}
      pnUrl: {{ getv "/connekt.pnUrl"}}
      smsUrl: {{ getv "/connekt.smsUrl"}}
      emailApiKey: {{ getv "/connekt.emailApiKey"}}
      pnApiKey: {{ getv "/connekt.pnApiKey"}}
      smsApiKey: {{ getv "/connekt.smsApiKey"}}
      emailAppName: {{ getv "/connekt.emailAppName"}}
      pnAppName: {{ getv "/connekt.pnAppName"}}
      smsAppName: {{ getv "/connekt.smsAppName"}}
      emailBucket: {{ getv "/connekt.emailBucket"}}
      emailSubBucket: {{ getv "/connekt.emailSubBucket"}}
      pnBucket: {{ getv "/connekt.pnBucket"}}
      pnSubBucket: {{ getv "/connekt.pnSubBucket"}}
      smsBucket: {{ getv "/connekt.smsBucket"}}
      smsSubBucket: {{ getv "/connekt.smsSubBucket"}}
      pnChannelId: {{ getv "/connekt.pnChannelId"}}
      transactionalEmail : {{ getv "/connekt.transactionalEmail"}}
      transactionalPN : {{ getv "/connekt.transactionalPN"}}
      transactionalSMS : {{ getv "/connekt.transactionalSMS"}}

turboConfig:
  singleDbWriteEnabled: false
  multiDbWriteEnabled: true
  turboOutboundWithoutTrxEnabled: false
  sharding: true
  appDbType: "mysql"
  queue_shard_strategy:
    default_suffix: default
    clusters:
      queues:
        - advanz_onboarding_queue
        - fintech_los_connekt
        - fintech_cf_scheduler

  mysql:
    hibernate.c3p0.idle_test_period: "60000"
    hibernate.c3p0.max_size: "100"
    hibernate.c3p0.max_statements: "50"
    hibernate.c3p0.min_size: "20"
    hibernate.c3p0.timeout: "300"
    hibernate.connection.driver_class: com.mysql.jdbc.Driver
    hibernate.connection.password: {{ getv "/turboConfig.turboDbPassword" }}
    hibernate.connection.url: {{ getv "/turboConfig.turboDbUrl" }}
    hibernate.connection.username: {{ getv "/turboConfig.turboDbUsername" }}
    hibernate.connection.autocommit: true
    hibernate.show_sql: "true"
    hibernate.connection.zeroDateTimeBehavior: convertToNull

gibraltarClientConfig:
  host: {{ getv "/gibraltarHost" }}
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  targetClientId: {{ getv "/gibraltarClientId" }}
  maxPublicKeyCount: 100

criClientConfig:
  host: {{ getv "/criHost" }}
  maxRetries : {{ getv "/criMaxRetries" }}

esClientConfig:
  hostName: "************"
  port: 80
  connectionTimeout : 3000

khaataClientConfig:
  url: http://localhost:8980
  clientName: "pinaka"