#!/bin/bash

# Enable debug prints and exit on failure
set -ex

# Configure parameters
PACKAGE=_PACKAGE_
PACKAGE_GROUP=_GROUP_
PACKAGE_USER=_USER_

# Create a group for the package
if ! getent group ${PACKAGE_GROUP} > /dev/null; then
    addgroup --system ${PACKAGE_GROUP}
fi

# Create a user for the package
if ! getent passwd ${PACKAGE_USER} > /dev/null; then
    adduser --system --ingroup ${PACKAGE_GROUP} --disabled-password --shell /bin/false ${PACKAGE_USER}
fi

# Take ownership and set the access permissions for a given filesystem object
takeOwnership() {
    chown -R ${PACKAGE_USER}:${PACKAGE_GROUP} $1
    chmod -R 777 $1
}

# Create (if required) and take ownership of the directories needed by the application
INSTALL_DIR="/usr/share/${PACKAGE}"
CONFIG_DIR="/etc/${PACKAGE}"
SERVICE_DIR="/etc/service/${PACKAGE}"
LOG_DIR="/var/log/${PACKAGE}"
RUN_DIR="/var/run/${PACKAGE}"
for dir in ${INSTALL_DIR} ${CONFIG_DIR} ${SERVICE_DIR} ${LOG_DIR} ${RUN_DIR}
do
    mkdir -p ${dir}
    takeOwnership ${dir}
done

# Take ownership of init scripts and set access permissions for it
takeOwnership /etc/init.d/${PACKAGE}

# Grant non sudo read-access to log files
chmod -R 755 ${LOG_DIR}

# What is the environment that this box belongs to? Read from /etc/envname if it exists
ENVNAME=$(cat /etc/envname || echo 'local')

# Validate the environment. Default to 'local' in case of problems
case "${ENVNAME}" in
    prod|preprod|cbc-preprod|local|test) ;; # All is well
    *) ENVNAME='local'  ;; # Default to local
esac

# Point to the right configuration bucket based on the environment of the box we are deployed on
BUCKET_NAME="${PACKAGE}-${ENVNAME}"
echo "Setting prefix (config bucket) in confd toml to ${BUCKET_NAME}"
echo "prefix = \"${BUCKET_NAME}\"" >> /etc/confd/conf.d/${PACKAGE}.toml
echo "prefix = \"${BUCKET_NAME}\"" >> /etc/confd/conf.d/${PACKAGE}-hystrix.toml

# Restart confd so that our new toml and templates are picked up
/etc/init.d/fk-config-service-confd restart

#Replace hostname for cosmos-jmx
fullHostName=$(hostname -I  | xargs)
sed -i -e "s/_HOSTNAME_/${fullHostName}/g" /etc/cosmos-jmx/pinaka-jmx.json

#Logsvc
mkdir -p /etc/logsvc
echo 'export LOGSVC_CLUSTER="fintech-pinaka-prod"' > /etc/logsvc/config

takeOwnership /etc/rsyslog.d/

#Restart Logsvc
if [ -r "/etc/envname" ]
then
    ENVNAME=$(cat /etc/envname)
    if [[ "$ENVNAME" == "prod" ]]
    then
        svc -t /etc/service/stream-relay.svc
    fi
fi

# Start our service explicitly
# This is required as we explicitly stop our package in prerm (uninstall or upgrade scenario)
# When we install a new deb, the service will stay stopped until we start it explicitly
/etc/init.d/${PACKAGE} start

sed -i -e "s/__ENV__/$ENVNAME/g" /etc/confd/conf.d/$PACKAGE-hystrix.toml
sed -i -e "s/_PACKAGE_/$PACKAGE/g" /etc/confd/conf.d/$PACKAGE-hystrix.toml

