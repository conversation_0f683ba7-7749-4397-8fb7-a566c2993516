#!/bin/bash

# Enable debug prints and exit on failure
set -ex

PACKAGE=_PACKAGE_
CMD="$1"

# Bail out the command is neither remove nor upgrade
if [ "$CMD" != "remove" -a "$CMD" != "upgrade" ]; then
    exit 0;
fi

# Stop our service first
# As long as the daemon runs, the log and other files will remain open, and will not get
# purged from the system even if we delete them below.
# However, this also means that the service will stay stopped even if we upgrade to a new version
# The postinst script needs to issue an explicit start to get it running again
/etc/init.d/${PACKAGE} stop

# These are the filesystem objects that need to be cleaned up
INSTALL_DIR="/usr/share/${PACKAGE}"
CONFIG_DIR="/etc/${PACKAGE}"
LOG_DIR="/var/log/${PACKAGE}"
RUN_DIR="/var/run/${PACKAGE}"

# Remove all the application specific directories
# WARNING: DO NOT DELETE the LOG directory and the SERVICE directory
#  1. We use multilog. Blindly deleting the log directory will cause multilog to go into a bad state.
#     Eventually it stops draning logs, and causes the service to get blocked on console logs.
#
#  2. It is better to preserve the logs across installations for debugging / analysis.
#
#  3. We use supervise. Blindly deleting the service directory can cause issues with managing the service
#     as a daemon

for x in ${INSTALL_DIR} ${CONFIG_DIR} ${RUN_DIR}
do
    echo "Removing ${x}"
    rm -rf ${x}
done

# The init scripts need to be cleaned up
rm "/etc/init.d/${PACKAGE}"

# Logsvc
rm "/etc/rsyslog.d/50-${PACKAGE}.info.log.conf"
