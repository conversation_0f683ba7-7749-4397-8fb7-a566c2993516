#!/bin/bash

## some variables to use for application tuning
## expects /etc/podinfo should be available
if [ -f "/etc/podinfo/mem_request" -a -f "/etc/podinfo/cpu_request" ]; then
    MEMTOTAL="$(cat /etc/podinfo/mem_request)"
    CPUTOTAL="$(cat /etc/podinfo/cpu_request)"
else
    MEMTOTAL=1073741824
    CPUTOTAL=1
fi

## java vars
APPNAME="__PACKAGE__"
JMXPORT="9311"
REMOTE_DEBUG_PORT="5005"
XHEAPSIZE="$(echo $(awk -v mem="$MEMTOTAL" 'BEGIN{printf "%.0f\n", mem*0.8 / 1048576 }'))m"
SHEAPSIZE="$(echo $(awk -v mem="$MEMTOTAL" 'BEGIN{printf "%.0f\n", mem*0.5 / 1048576 }'))m"
APPCONFDIR="/etc/${APPNAME}"
APPJARDIR="/var/lib/${APPNAME}"
LOGDIR="/var/log/flipkart/${APPNAME}"
GCLOG="${LOGDIR}/gc.log"
LOGBACK="${APPCONFDIR}/logback.xml"

mkdir -p "$LOGDIR"

### magic starts
JAVA_XM="-Xms${SHEAPSIZE} -Xmx${XHEAPSIZE}"
JAVA_OPTS="-server \
-XX:+UseG1GC -verbose:gc -Xloggc:${GCLOG} -XX:+PrintGCTimeStamps -XX:+PrintGCDetails -XX:GCLogFileSize=100M -XX:-UseGCLogFileRotation \
-Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=${JMXPORT} -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false \
-XX:+UnlockCommercialFeatures -XX:+FlightRecorder -DaopType=GUICE \
-Duser.timezone=Asia/Kolkata \
-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=${REMOTE_DEBUG_PORT} \
-Djava.net.preferIPv4Stack=true -Dfile.encoding=UTF-8 -XX:+PrintTenuringDistribution \
-XX:+HeapDumpOnOutOfMemoryError"


## By default java will try to pick logback.xml from target/classes/logback.xml
## to override it by moving it to ${LOGBACK}
if [ -f $LOGBACK ]; then
    JAVA_OPTS="${JAVA_OPTS} -Dlogback.configurationFile=$LOGBACK"
    echo "Stating with logback config: $LOGBACK"
fi

### make cmd
jcmd="java ${JAVA_XM} ${JAVA_OPTS} ${JVM_PARAMS} -cp ${APPJARDIR}/classes -jar ${APPJARDIR}/${APPNAME}.jar $@"

### exec and take over parent PID
exec $jcmd
