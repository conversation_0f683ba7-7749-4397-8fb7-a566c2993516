#!/usr/bin/env bash
#------------------------------------------------------------------------------------
# VM setup script for prod environment
#
# WARNING:
# This script sets the bucket prefix for confd to point to the prod bucket
#------------------------------------------------------------------------------------

# Define the environment that this box belongs to
# This can be used to point our boxes to the right config bucket and more
echo 'prod' > /etc/envname

# Install reposervice cli
echo "deb http://***********/repos/infra-cli/3 /" > /etc/apt/sources.list.d/infra-cli-svc.list
apt-get update
apt-get install --yes --allow-unauthenticated infra-cli

# Create and mount ramdisk for redis dump file
# mkdir /var/lib/fk-3p-redis
# chmod a+rwx /var/lib/fk-3p-redis
# echo 'tmpfs       /var/lib/fk-3p-redis tmpfs   nodev,nosuid,noexec,nodiratime,size=35G   0 0' >> /etc/fstab
# mount -a

# Mount additional disk
mkdir /var/lib/fk-3p-redis
chmod a+rwx /var/lib/fk-3p-redis
mkfs.ext4 /dev/vdb
echo '/dev/vdb    /var/lib/fk-3p-redis    ext4    defaults    0    1' >> /etc/fstab
mount -a

# Enable recycling of TIME_WAIT CONNECTIONS
echo 'net.ipv4.tcp_tw_reuse = 1' >> /etc/sysctl.conf
echo 1 > /proc/sys/net/ipv4/tcp_tw_reuse

# Use reposervice to fetch the deb sources for our service
reposervice --host **********  --port 8080 getenv --appkey fintech-redis-prod --name fintech-redis-prod  > /etc/apt/sources.list.d/fintech-redis.list

export CONFIG_BUCKET="fintech-redis-prod"

# Install packages
apt-get update

# Config Service
apt-get install --yes --allow-unauthenticated fk-config-service-confd

# Redis
apt-get install --yes --allow-unauthenticated fk-3p-redis-5.0.x

# Cosmos
apt-get install --yes --allow-unauthenticated cosmos-collectd

# Cosmos-Redis
apt-get install --yes --allow-unauthenticated cosmos-redis

# Alertz
# Configure nagios
echo 'team_name="Fintech"' > /etc/default/nsca_wrapper
echo 'nagios_server_ip="***********"' >> /etc/default/nsca_wrapper
apt-get install --yes --allow-unauthenticated fk-nagios-common

# Debugging tools:
apt-get install --yes --allow-unauthenticated htop