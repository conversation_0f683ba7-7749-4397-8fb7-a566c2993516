                                  ************    Preprod Set up  ************************

## Creating environment in reposervice for preprod app
reposervice --host repo-svc-app-0001.nm.flipkart.com --port 8080 putenv --appkey pinaka-preprod --name pinaka-preprod --envdef envspec-preprod.json

## Fetching latest version from reposervice
reposervice --host repo-svc-app-0001.nm.flipkart.com --port 8080 getenv --appkey pinaka-preprod --name pinaka-preprod > /etc/apt/sources.list.d/pinaka.list

##Creating preprod instance
kloud-cli instance create --appId=fintech-pinaka-preprod --type=c1.medium --image=debian-8-guest --script=setup-preprod.sh --users=users.txt --zone=in-chennai-1