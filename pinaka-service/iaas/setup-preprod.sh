#!/bin/bash

#------------------------------------------------------------------------------------
# VM setup script for preprod environment
#
# WARNING:
# This script sets the bucket prefix for confd to point to the preprod bucket
#------------------------------------------------------------------------------------

# Define the environment that this box belongs to
# This can be used to point our boxes to the right config bucket and more
echo 'preprod' > /etc/envname

# Install reposervice cli
echo "deb http://**********/repos/infra-cli/3 /" > /etc/apt/sources.list.d/infra-cli-svc.list
apt-get update
apt-get install --yes --allow-unauthenticated infra-cli

# Use reposervice to fetch the deb sources for our service
reposervice --host repo-svc-app-0001.nm.flipkart.com --port 8080 getenv --appkey pinaka-preprod --name pinaka-preprod > /etc/apt/sources.list.d/pinaka.list

# Install packages
apt-get update
# Java
apt-get install --yes --allow-unauthenticated oracle-j2sdk1.8
# Config Service
apt-get install --yes --allow-unauthenticated fk-config-service-confd
# common dependencies
apt-get install --yes --allow-unauthenticated fk-libestr
apt-get install --yes --allow-unauthenticated fk-liblognorm
apt-get install --yes --allow-unauthenticated fk-rsyslog
# Logsvc
apt-get install --yes --allow-unauthenticated stream-relay
# Cosmos
apt-get install --yes --allow-unauthenticated cosmos-base
apt-get install --yes --allow-unauthenticated cosmos-collectd
apt-get install --yes --allow-unauthenticated cosmos-jmx
apt-get install --yes --allow-unauthenticated cosmos-tail
# Alertz
# Configure nagios
echo 'team_name="Fintech"' > /etc/default/nsca_wrapper
echo 'nagios_server_ip="***********"' >> /etc/default/nsca_wrapper
apt-get install --yes --allow-unauthenticated fk-nagios-common
# Install our Service
apt-get install --yes --allow-unauthenticated pinaka

# Leave a script on the box that will let us upgrade the pinaka package easily
cat > /usr/local/bin/upgrade-pinaka.sh <<HEREDOC
#!/bin/bash
reposervice --host repo-svc-app-0001.nm.flipkart.com --port 8080 getenv --appkey pinaka-preprod --name pinaka-preprod > /etc/apt/sources.list.d/pinaka.list
apt-get update
apt-get install --yes --allow-unauthenticated pinaka
HEREDOC
chmod a+x /usr/local/bin/upgrade-pinaka.sh
