swagger:
  resourcePackage: com.flipkart.fintech.pinaka.service.web

server:
  type: default
  applicationContextPath: /pinaka
  applicationConnectors:
    - type: http
      port: 9090
  adminConnectors:
    - type: http
      port: 9091

databaseConfig:
  driverClass: org.h2.Driver
  url: jdbc:h2:./target/fintech_pinaka_test_db
  user: test
  password: test
  initialSize: 10

rateLimitingConfig:
  [
    { "limiterKey": "FETCH_UI", "timeoutInMs":5 },
    { "limiterKey": "PAN_SUBMIT", "timeoutInMs":5 },
    { "limiterKey": "AADHAR_VERIFICATION", "timeoutInMs":5 },
    { "limiterKey": "GENERATE_OTP", "timeoutInMs":5 },
    { "limiterKey": "CREATE_APPLICATION", "timeoutInMs":5 },
    { "limiterKey": "PENNY_DROP_SUBMIT_DETAILS", "timeoutInMs":5 },
    { "limiterKey": "<PERSON>ETCH_LOADER", "timeoutInMs":5 },
    { "limiterKey": "VERIFY_OTP", "timeoutInMs":5 }
  ]
#  properties:
#    charSet: UTF-8
#    hibernate.generate_statistics: false
#    hibernate.session.events.log: false
#    hibernate.show_sql: true
#    hibernate.format_sql: true
#    # the maximum amount of time to wait on an empty pool before throwing an exception
#    maxWaitForConnection: 100s
#    # the SQL query to run when validating a connection's liveness
#    validationQuery: "/* MyService Health Check */ SELECT 1"
#    initialSize: 10
#    # the minimum number of connections to keep open
#    minSize: 10
#    # the maximum number of connections to keep open
#    maxSize: 1000
#    # whether or not idle connections should be validated
#    checkConnectionWhileIdle: true
#    # how long a connection must be held before it can be validated
#    validationInterval: 5s
#    # the maximum lifetime of an idle connection
#    minIdleTime: 1 minute

pinakaClientConfig:
  url: http://localhost:9090
  client: pinaka

ardourClientConfig:
  url: http://************:9080
  client: pinaka

pandoraClientConfig:
  url: http://************:8214/pandora
  client: pinaka

pandoraLiteClientConfig:
  url: http://***********/pandoralite
  client: pinaka

userServiceClientConfig:
  usUrl: http://************:35200
  usClientId: affordability

oAuthServiceClientConfig:
  oAuthUrl: http://localhost:6160
  oAuthClientID : 5df700f641aa44bbb24891531e174032
  oAuthClientSecret : jHuvCzzrfBkWQ8ksdwjgdR9mn31Iz1vc4P9vkt+WIiOFvns0
  cachedAccessTokenTTL : 30

#loginServiceClientConfig:
#  loginServiceUrl: http://***********:35100
#  loginServiceClientId: affordability

bigfootConfiguration:
  url: http://***********:28223
  #  url: http://localhost:28223
  poolName: pool
  company: fkint
  org: cp
  namespace: affordability
  exchangeName: bnpl_ingestion_queue
  exchangeType: queue

fluxAsyncClientConfig:
  url: http://************:9998/api/machines
  clientId: pinaka
  exchangeName: efa_pinaka

onboardingClientConfig:
  url: http://************:7070
  client: pinaka

connektClientConfig:
  smsUrl: http://***********
  emailUrl: http://***********
  pnUrl: http://***********
  pnApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  smsApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  emailApiKey: BM9kig6BGwN68aeJG2ESWkxt8
  exchangeName: fintech_los_connekt
  callbackUrl: http://localhost:9090/pinaka/communications
  pnAppName: RetailApp
  pnChannelId: fk_channel_order_payments
  pnBucket: alerts
  pnSubBucket: pay
  domain: flipkart
  transactionalPN : true
  smsAppName: flipkart
  emailAppName: flipkart-promo

robinhoodAsyncClientConfig:
  url: http://127.0.0.1:8180
  clientId: pinaka
  exchangeName: "%s_onboarding"

fluxConfiguration:
  fluxRuntimeUrl: http://************:9998
  connectionTimeout: 10000
  socketTimeout: 10000

creditModelConfig:
  url: http://***********:80
  enabled: true
  testUsers: ["test1", "test2","AC5PD5WPWNURTFQ1D8NA9MP85WUXB2UI","Test_1", "Test_2", "Test_3", "ACC14118057725487501", "ACC14118057728187506", "ACC14118057731001326", "ACC14032583400405772", "ACC798D3CC5AC4447869C1B1DD645A7128AZ", "ACC14215635411148470", "ACCA17D4EA4D70F43388C6EC80E73A69180R"]

gibraltarConfig:
  maxPublicKeyCount: 100
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  trustStorePath: "./pinaka-service/config/local/client-truststore.jks"
  trustStorePass: password
  keyStorePath: "./pinaka-service/config/local/client-keystore.jks"
  keyStorePass: password
  certificateAlias: gibraltarSelfSigned
  url: http://localhost:8443
  generateKeysOnStartUp: false

logging:
  level: INFO
  loggers:
    "com.flipkart": INFO
    "org.hibernate.SQL": ERROR
    "com.flipkart.affordability.clients.oauth": ERROR
    "com.flipkart.restbus.client.shards": ERROR

  appenders:
    - type: console
      threshold: ALL
      timeZone: IST
      logFormat: "%-5level [%date] [%thread] [%cyan(%logger{0})]: %message%n"
      target: stdout

authNClientConfig:
  url: http://***********
  clientId: pinaka-service
  clientSecret: VdsO8BjanfTADqp3RNaaDm0bkm14iyVGJ3lUuGdaSTS

cryptexConfiguration:
  cryptexBundleEnabled: false
  authNClientConfig:
    url: http://***********
    clientId: pinaka-service
    clientSecret: VdsO8BjanfTADqp3RNaaDm0bkm14iyVGJ3lUuGdaSTS+7Ba0

  cryptexClientConfig:
    maxConnections: 5  # default value
    connectTimeOut: 1500 # default value
    readTimeOut: 1500 # default value

  dynamicBucketConfig:
    bucketName: pinaka-local-constants
    dynamicConfigBucketEnabled: false

bnplOnboardingConfig:
  optionalPermissions:
    - CONTACT
    - CALL_LOG
    - LOCATION
    - DEVICE
    - SMS
  mandatoryPermissions:
  tncUrl: https://www.flipkart.com/rv/fintech/khaata/terms-v2
  privaryPolicyUrl: https://www.flipkart.com/m/st/policies

efaOnboardingConfig:
  ceEnabled: true
  ceTestUsers: ["test1", "test2","AC5PD5WPWNURTFQ1D8NA9MP85WUXB2UI","Test_1", "Test_2", "Test_3", "test_efa_ce_1", "ACC798D3CC5AC4447869C1B1DD645A7128AZ", "ACC14215635411148470", "ACCA17D4EA4D70F43388C6EC80E73A69180R"]
  optionalPermissions:
    - CONTACT
    - CALL_LOG
    - LOCATION
    - DEVICE
    - SMS
  mandatoryPermissions:
  tncUrl: https://www.flipkart.com/pages/efa-preapproval-tnc
  privaryPolicyUrl: https://www.flipkart.com/m/st/policies
  formFields:
    CITI:
      - fieldType: TEXT
        label: Company Name
        name: Company
      - fieldType: NUMBER
        label: Monthly Income
        name: Income
      - fieldType: DROPDOWN
        label: Employment
        name: Employment
      - fieldType: DROPDOWN
        label: Occupation
        name: Occupation
      - fieldType: DROPDOWN
        label: Qualification
        name: Qualification
      - fieldType: ADDRESS
        label: Current Address
        name: Address
    kissht: []
  lenderWiseFormFields:
    KISSHT:
      - PAN_NUMBER
      - DOB
      - GENDER
      - ADDRESS
    INDIA_BULLS:
      - PAN_NUMBER
      - DOB
      - GENDER
      - EMPLOYMENT_STATUS
      - MONTHLY_SALARY
      - ADDRESS

  approvalCallouts:
    INDIA_BULLS:
      - callout: "Start shopping, select cardless credit as your payment option."
        icon:
          url: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d388836b-a541-4f36-95df-49818d720bb7.png?q={@quality}
    KISSHT:
      - callout: "Start shopping, select cardless credit as your payment option."
        icon:
          url: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d388836b-a541-4f36-95df-49818d720bb7.png?q={@quality}
  underwritingCallouts:
    INDIA_BULLS:
      - callout: "Repay by 15th of next month at 10% interest"
        "icon":
          "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"
    KISSHT:
      - callout: "Repay by 15th of next month at 0% interest"
        "icon":
          "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"

bigfootEntityConfig:
  borrowerConfig:
    name: fintech_borrowers
    schemaVersion: 1.4
  loanApplicationConfig:
    name: fintech_loan_application
    schemaVersion: 2.0
  subApplicationConfig:
    name: fintech_loan_subapplication
    schemaVersion: 1.0
  whitelistConfig:
    name: fintech_whitelist
    schemaVersion: 2.0
  sourceAttributionConfig:
    name: fintech_source_attribution
    schemaVersion: 1.0


cryptoConfig:
  algo: AES
  secretKey: "Cd1/SXipT)So3=19"

underwritingConfig:
  endPoint: http://************:8780/fintech-underwriting

citiConfig:
  enabled: false
  productConfig:
    productCode: PC400
    sourceCode: AAFKMNET
    organization: 730
    logo: 400
    embossName: "Matthew Hyden"
  supportedPincodes: ["pincode1", "pincode2"]

lenderConfiguration:
  configurations:
    EFA:
      -
        consentTextKey: key3
        hasAdditionalForm: true
        interestRate: 10
        emiSupported: true
        paylaterSupported: false
        lateFees: 10
        lender: CITI
        postApprovalTncKey: key2
        preApprovalTncKey: key1
        showKycDetails: false
        primaryDataEditable: false
      -
        consentTextKey: key3
        hasAdditionalForm: false
        interestRate: 10
        emiSupported: true
        paylaterSupported: true
        lateFees: 10
        lender: KISSHT
        postApprovalTncKey: key2
        preApprovalTncKey: key1
        showKycDetails: true
        primaryDataEditable: true
      -
        consentTextKey: key3
        hasAdditionalForm: false
        interestRate: 10
        emiSupported: true
        paylaterSupported: true
        lateFees: 10
        lender: INDIA_BULLS
        postApprovalTncKey: key2
        preApprovalTncKey: key1
        showKycDetails: true
        primaryDataEditable: true

    CBC:
      -
        lender: AXIS
    BNPL:
      -
        hasAdditionalForm: false
        lender: IDFC
        primaryDataEditable: true
        nameMatchThreshold: 50
        nameValidationEnabled: true
        panValidationRequired: true
        postApprovalTncKey: key2
        consentTextKey: key3
        preApprovalTncKey: key1
        minimumAgeRequired: 21

    FLIPKART_ADVANZ:
      -
        hasAdditionalForm: false
        lender: IDFC
        primaryDataEditable: true
        nameMatchThreshold: 50
        nameValidationEnabled: true
        panValidationRequired: true
        postApprovalTncKey: key2
        consentTextKey: key3
        preApprovalTncKey: key1
        minimumAgeRequired: 21


loginServiceClientConfig:
  loginServiceUrl: http://************:35100
  loginServiceClientId: affordability

upgradationConfig:
  upgradationEnabled: false

tijoriConfig:
  url: http://************:9090
  clientName: robinhood

external_client_config:
  page_service_config:
    host: ***********
    port: 80

cbcOnboardingConfig:
  fkpayTriggerOtpUrl: http://localhost:9095/fkpay/api/v3/fintech/otp/send
  fkpayDisplayCardEtbUrl: http://localhost:9095/fkpay/api/v3/fintech/paymentInstrument/CBC_AXIS_APP_SERNO
  fkpayDisplayCardEtccUrl: http://localhost:9095/fkpay/api/v3/fintech/paymentInstrument/CBC_AXIS_CARD_SERNO
  fkpayDisplayCardConsoleUrl: http://localhost:9095/fkpay/api/v3/fintech/paymentInstrument/CBC_AXIS_CARD_CONSOLE
  cbcComingSoonBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/09/2019/9afe3704-98b0-4faf-9f0b-0391069cfde3.jpg?q={@quality}"
  cbcApplyNowBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/09/2019/052efd3d-a219-4350-b1f5-0ce8911e5a1b.jpg?q={@quality}"
  cbcContinueApplicationBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/09/2019/71652a80-3aad-4104-b156-e3f5fd05e51f.jpg?q={@quality}"
  cbcViewCardDetailsBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/10/07/2019/8f863e90-5ebe-49f9-a23f-d2e0fb438f18.jpg?q={@quality}"
  cbcApplicationEtccBuzzBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/09/2019/70b693fa-4fc8-416a-bed7-fb4f7e1c8c77.png?q={@quality}"
  cbcApplicationEtccNonBuzzBannerUrl: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/26/06/2019/8f52e838-3942-458c-ba93-c274c5de9df0.png?q={@quality}"
  cbcApplicationEtccBuzzBannerAspectRatio: "312:44"
  cbcApplicationEtccNonBuzzBannerAspectRatio: "312:80"
  etbApprovalCallouts:    [
  {
    "callout": "Access your card details",
    "description": "Account Summary - Details of dues, transactions",
    "icon": {
      "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"
    }
  },
  {
    "callout": "Reward Points Summary",
    "description": "Manage/Redeem reward points or Set Goals",
    "icon": {
      "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"
    }
  },
  {
    "callout": "Create Requests",
    "description": "Subscribe for E-statement or duplicate statement, Block Lost/Stolen card, Active international usage...",
    "icon": {
      "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"
    }
  }]
  ntbApprovalCallouts: [
  {
    "callout": "You will need to provide an identity proof from the list of accepted documents",
    "icon": {
      "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"
    }
  }
  ]
  cohortWiseFormList:
    ETB_PRE_APPROVED:
      - personal_details
      - address_form
      - credit_card_features
    ETB_NON_PRE_APPROVED:
      - personal_details
      - address_form
      - professional_details
      - credit_card_features
    ETCC:
      - personal_details
      - address_form
    NTB:
      - personal_details
      - contact_details
      - professional_details
      - address_form
      - credit_card_features
    ETS:
      - personal_details
      - contact_details
      - professional_details
      - address_form
      - credit_card_features
  axisThinCremoScoreThreshold: 493
  autoRejectThinEnabled: "Y"

collectionsClientConfiguration:
  scheme: http
  host: ************
  port: 7980

schedulerClientConfig:
  host: *************
  port: 24000
  poolSize: 100
  clientId: fintech_clc

contextWiseWhitelistConfig:
  contextMap:
    NUDGING-EFA-INDIA_BULLS:
      isEnabledForEveryone: false
      whitelistedAccountIds: ["ACC798D3CC5AC4447869C1B1DD645A7128AZ"]
    NUDGING-CBC-AXIS:
      isEnabledForEveryone: false
      whitelistedAccountIds: []

contextWiseCacheBuilderConfig:
  contextMap:
    PAN_RETRY:
      maximumSize: 10000
      duration: 30
      durationUnit: DAYS

bigfootCallbackConfiguration:
  pinakaBaseUrl: "http://*************:9090"
  callbackQueue: "bnpl_ingestion_queue"

heliosProxyConfig:
  maxConnections: 1024
  maxConnectionsPerHost: 1024
  pooledConnectionIdleTimeoutInMS: 6000
  executorThreadsCount: 32
  executionIsolationSemaphoreMaxConcurrentRequests: 1024
  executionTimeoutInMS: 10000
  hystrixCommandKey: PinakaServiceHystrixCommand
  hystrixGroupKey: PinakaServiceHystrixGroup
  rewriteProtocal: http
  rewriteHostName: 127.0.0.1
  rewritePortNumber: 29992
  enableProxy: false

fldgConfiguration:
  enableFldg: false

abConfiguration:
  endpoint: ***********:80
  layerList: "Affordability"
  clientId: "Mapi.affordability"

kycConfig:
  xmlKycAndroidAppVersion: "1080100"
  xmlKycChannels: ["ANDROID"]

onboardingConfig:

  genericFlowEnabled: true
  genericFlowAccountIds: ["ACC76FCADFC7B3A443F939DFE42003603AAI"]
  productOnboardingConfig:
    BNPL:
      storePageUrl: "/pages/mw/flipkart-advanz/apply-now"
      queue: bnpl_onboarding_queue
    FLIPKART_ADVANZ:
      storePageUrl: "/pages/mw/flipkart-advanz/apply-now"
      queue: advanz_onboarding_queue


alfredClientConfig:
  url: http://************:8091
  secretKey: 123rewedrgrvcz

neoCrmClientConfig:
  exchangeName: "fintech_cbc_production"
  authToken: "LVNKY7IAABXXAAZB"
  clientId: "Fintech"
  url: "http://***********"
  path: "/v1/crm/publish"

uiConfiguration:
  cohortPriorityList: [CASH, EMI, PAY_LATER]
  cohorts:
    CASH:
      name: Cash Loan
      description: "For withdrawal upto ₹%s"
    EMI:
      name: EMIs
      description: "For purchases upto ₹%s"
    PAY_LATER:
      name: Pay Later
      description: "Pay next month at No Cost for shopping upto ₹%s"
  uiConfigMap:
    APPROVED:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle : "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "Know more about Flipkart Pay Later"
      knowMoreLandingUrl: ""
      submitButtonText: "Activate Flipkart Pay Later"
      submitButtonUrl: ""
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          -
            description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          -
            description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          -
            description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
    LOAN_CREATION_IN_PROGRESS:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle: "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "knowMoreText"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          - description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          - description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          - description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    POST_PROCESSING_PENDING:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle: "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "knowMoreText"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          - description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          - description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          - description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    REJECTED:
      title: "We are really sorry!"
      subtitle: "Thank you for your interest in Flipkart Pay Later. However, with the present information you’ve given, we are unable to activate Flipkart Pay Later for you. Please continue shopping to your heart’s content on Flipkart. We’ll let you know when we are able to open up Flipkart Pay Later for you."
      submitButtonText: "Continue Shopping"
      submitButtonUrl: "www.flipkart.com"
      imageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"

redisConfig:
  masterName: fintech-redis-prod
  timeout: 5000
  sentinelAddresses: ["redis://*************:26379","redis://************:26379","redis://*************:26379"]
  password: ABtr@25
  masterConnectionMinimumIdleSize: 3
  masterConnectionPoolSize: 100
  lockWaitTime: 10
  lockReleaseTime: 3

mysqlLockConfig:
  prefix: ""
  timeout: 0

dataPointConfiguration:
  enableDataPoint:
    TS:
      enable: false
      testAccountList: ["ACC8B2427BE8D354EAF9D94046347FDA13ES"]

sourceAttributionConfig:
  enabled: false
  redisTtlInSeconds: 43200

rotation:
  enableRotator: true
  defaultRotationStatus: true

turboConfig:
  singleDbWriteEnabled: false                # write in fksc relayer format
  multiDbWriteEnabled: true                 # write in turbo relayer format
  turboOutboundWithoutTrxEnabled: false     # only write to turbo outbound table
  sharding: true                           # enables shard db writes based on queue_shard_strategy
  appDbType: "mysql"                        # {supports two values [mysql,tds]}
  queue_shard_strategy:
    default_suffix: default
    clusters:
      queues:
        - advanz_onboarding_queue
        - fintech_los_connekt
        - fintech_cf_scheduler

  mysql:
    hibernate.c3p0.idle_test_period: "60000"
    hibernate.c3p0.max_size: "100"
    hibernate.c3p0.max_statements: "50"
    hibernate.c3p0.min_size: "20"
    hibernate.c3p0.timeout: "300"
    hibernate.connection.driver_class: com.mysql.jdbc.Driver
    hibernate.connection.password: root
    hibernate.connection.url: "jdbc:h2:./target/fintech_pinaka_test_db;TRACE_LEVEL_FILE=0;TRACE_LEVEL_SYSTEM_OUT=0"
    hibernate.connection.username: root
    hibernate.connection.autocommit: true
    hibernate.show_sql: "true"
    hibernate.connection.zeroDateTimeBehavior: convertToNull