server:
  type: default
  applicationContextPath: /pinaka
  adminContextPath: /admin
  applicationConnectors:
    - type: http
      port: 9090
  adminConnectors:
    - type: http
      port: 9091
  requestLog:
    # Note:
    # 1. We do not specify a logFormat because the default log format is Common Log Format (CLF), which is good
    # 2. If we specify a logFormat, note that the format specifiers for access logs are different
    # 3. Access log accepts ONLY ONE appender - we cannot get access logs on both console and file simultaneously
    appenders:
      - type: file
        currentLogFilename: access.log
        threshold: ALL
        archive: true
        archivedLogFilenamePattern: access-%d{yyyy-MM-dd-HH}.log.gz
        archivedFileCount: 24
        timeZone: IST

logging:
  level: INFO
  loggers:
    "org.hibernate": ERROR
    "com.flipkart.affordability.clients.oauth": ERROR
    "com.flipkart.restbus.client.shards": ERROR
    "com.flipkart.abservice.resources": ERROR
    "org.hibernate.SQL":
      level: INFO
      additive: false
      appenders:
        - type: file
          currentLogFilename: pinaka-sql.log
          archivedLogFilenamePattern: pinaka-sql-%i.log.gz
          archivedFileCount: 6
          maxFileSize: 20MB
  appenders:
    - type: file
      threshold: ALL
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: pinaka.log
      archivedLogFilenamePattern: /pinaka-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: file
      threshold: ERROR
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: pinaka-error.log
      archivedLogFilenamePattern: pinaka-error-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: console
      threshold: ALL
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message %replace(%exception){'\n',' | '} %n"
      timeZone: IST
      target: stdout

#healthCheckName: PinakaHealth

swagger:
  title: Pinaka Service
  description: APIs for Pinaka Service
  contact: <EMAIL>
  resourcePackage: com.flipkart.fintech.pinaka.service.web

databaseConfig:
  encryptedUrl: **********************************
  encryptedUser: root
  encryptedPassword: root
  driverClass: com.mysql.cj.jdbc.Driver
  properties:
    charSet: UTF-8
    hibernate.dialect: org.hibernate.dialect.MySQL57Dialect
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 8
    # the minimum number of connections to keep open
    minSize: 8
    # the maximum number of connections to keep open
    maxSize: 10000
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

databaseSlaveConfig:
  slaveEncryptedUrl: **********************************
  slaveEncryptedUser: root
  slaveEncryptedPassword: root
  driverClass: com.mysql.cj.jdbc.Driver
  properties:
    charSet: UTF-8
    hibernate.dialect: org.hibernate.dialect.MySQL57Dialect
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 2
    # the minimum number of connections to keep open
    minSize: 2
    # the maximum number of connections to keep open
    maxSize: 1000
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

rateLimitingConfig:
  [
    {"limiterKey" : "FETCH_UI",  "timeoutInMs":5},
    {"limiterKey" : "PAN_SUBMIT",  "timeoutInMs":5},
    {"limiterKey" : "AADHAR_VERIFICATION",  "timeoutInMs":5},
    {"limiterKey" : "GENERATE_OTP",  "timeoutInMs":5},
    {"limiterKey" : "CREATE_APPLICATION",  "timeoutInMs":5},
    { "limiterKey": "PENNY_DROP_SUBMIT_DETAILS",  "timeoutInMs":5 },
    { "limiterKey": "FETCH_LOADER",  "timeoutInMs":5 },
    { "limiterKey": "VERIFY_OTP", "timeoutInMs":5 },
    { "limiterKey": "GEO_LOCATION", "timeoutInMs": 5 }
  ]

cryptexConfiguration:
  cryptexBundleEnabled: true
  authNClientConfig:
    url: https://service.authn-prod.fkcloud.in
    clientId: pinaka-service
    clientSecret: VdsO8BjanfTADqp3RNaaDm0bkm14iyVGJ3lUuGdaSTS+7Ba0

  cryptexClientConfig:
    endpoint: https://service.cryptex-prod.fkcloud.in
    maxConnections: 5
    connectTimeOut: 1500
    readTimeOut: 1500

  dynamicBucketConfig:
    bucketName: pinaka-prod
    enableLocalDynamicBucket: false

pinakaClientConfig:
  url: http://localhost:9090
  client: pinaka

#ardourClientConfig:
#  url: http://************:80
#  client: pinaka

userServiceClientConfig:
  usUrl: http://***********:35200
  usClientId: affordability

oAuthServiceClientConfig:
  oAuthUrl: http://************:80
  oAuthClientID : c2c95e0075c04b2e9b83e1bc8a09f57e
  oAuthClientSecret : FVMWGlJ22AoWfQ+ithjYn+M0tjr8xgakuHl06J5A+DELtVtc
  cachedAccessTokenTTL : 120

loginServiceClientConfig:
  loginServiceUrl: http://***********:35100
  loginServiceClientId: affordability

bigfootConfiguration:
  url: http://************:28223
  poolName: pool
  company: fkint
  org: cp
  namespace: affordability
  exchangeName: advanz_ingestion_queue
  exchangeType: queue
  batchIngestionSize: 22

fluxAsyncClientConfig:
  url: dummy
  clientId: dummy
  exchangeName: dummy

connektClientConfig:
  exchangeName: fintech_los_connekt
  callbackUrl: http://************/pinaka/communications
  domain: flipkart
  emailUrl: http://************
  pnUrl: http://************
  smsUrl: http://************
  inAppUrl: http://************
  emailApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  pnApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  smsApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  inAppApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  emailAppName: flipkart
  pnAppName: RetailApp
  smsAppName: flipkart
  inAppName: flipkart
  emailBucket: alerts
  emailSubBucket: pay
  inAppBucket: alerts
  inAppSubBucket: pay
  pnBucket: alerts
  pnSubBucket: pay
  smsBucket: alerts
  smsSubBucket: pay
  pnChannelId: fk_channel_order_payments
  transactionalEmail: true
  transactionalPN: true
  transactionalSMS: true
  transactionalInApp: true

#
#robinhoodAsyncClientConfig:
#  url: http://************
#  clientId: pinaka
#  exchangeName: "%s_onboarding_2"

pandoraClientConfig:
  url: http://************/pandora
  client: pinaka

pandoraLiteClientConfig:
  url: http://**********/pandoralite
  client: pinaka

#fluxConfiguration:
#  fluxRuntimeUrl: dummy
#  connectionTimeout: 10000
#  socketTimeout: 10000

#onboardingClientConfig:
#  url: http://***********:8080
#  client: pinaka
#
#gibraltarConfig:
#  maxPublicKeyCount: 100
#  keySpaceName: onboarding
#  clientName: efa-onboarding-client
#  trustStorePath: "/etc/gibraltar/prod-client-truststore.jks"
#  trustStorePass: password
#  keyStorePath: "/etc/gibraltar/prod-client-keystore.jks"
#  keyStorePass: password
#  certificateAlias: gibraltarSelfSigned
#  url: https://10.32.110.181:8443,https://10.34.29.80:8443
#  generateKeysOnStartUp: true
#  connectionTimeoutInMs: 150
#  socketTimeoutInMs: 300
#  httpRequestRetryCount: 5

#creditModelConfig:
#  url: dummy
#  enabled: true
#  testUsers: ["ACC8B7C282E53D04507887CCB77E8FD04CAF","ACC14007814698302952","ACKY8VLVESNBPPZ8PCU679JJF4C66CBE","ACC1372849854047282","ACC2B7595F43FE84446A14688B4A9CF6236W","ACC394529E06D95499890D1BC521FB4439B7","ACCFFB7D7D63A9C4AC8B88519E77E31A74FE","ACCADD2D6BC4D124697A74202082857764BJ","ACC13575663756775320","ACC98018EL399JC2WGD0Q9LFEHJSXIN1","ACC14034398457502444","ACSY7IHGWVIE6V86M52E06GZMV0DKJI6","ACC29AA46041F344370A096202FA5ED943BW","ACC13497967438548608","ACC14024646984263496","ACC13547123388071822","ACC14164940558211150","ACCDB120529A3594E978A6FC9923E03FC48H","ACC590C6287C747408CA0C07115FB5D186F6","ACQ23LU0JFDB2RR6K07NG1IJ7ZL5NQYI","ACCBC9EDB7190CD4ECE9F017266B2D0ECEAY","ACC13980802179394739","ACC13719525240733773","ACC13820750845955870","ACC14155104907289589","ACC14215631514718925","ACC79101E4B34D8488991E798A953CFAF9CL","ACC9B136E3E6BEB49DFAD09B1B3D2782251G","ACCCCB17ACAD946486BA0FC12EB27C31524P","ACCX0M1SP2H34MKCGP7YPBDNXK6L1DFI","AC5PD5WPWNURTFQ1D8NA9MP85WUXB2UI","ACCE36E0BCF906848FC8DA046725F3954F7X","ACC13943014773232420","ACC53C25F62F8FC40DBBA1CDB6DD75537517","ACC8B2BBB88591C4F47BD1ED9381CF7284BV","ACC38A3FDECBB984B8D993C542E3026A40FL","ACCB08774A2DFF4402D89AC7400DB4ACFEDS","ACC14218181577396089","ACC3866C2ECD3AF419CA924DB63E2E253B68","ACC590C6287C747408CA0C07115FB5D186F6","ACC45B03AAA18134F25966217EBAE7DC8C87","ACC14215635411148470","ACC138882070540693","ACCEE79114ED745438BB1AD7D813193798FD"]

bnplOnboardingConfig:
  optionalPermissions:
    - CONTACT
    - LOCATION
  mandatoryPermissions:
    - SMS
    - DEVICE
  tncUrl: https://www.flipkart.com/pages/pay-later-tnc
  privaryPolicyUrl: https://www.flipkart.com/pages/fapl-privacy-policy

efaOnboardingConfig:
  ceEnabled: true
  ceTestUsers: ["ACCE36E0BCF906848FC8DA046725F3954F7X","ACC14218181577396089","ACC3866C2ECD3AF419CA924DB63E2E253B68","ACVZEO0YNZ20G3KW0LARK46MCFC31U2L","ACC14097250110205176","ACC83381E9EE2104E18B00FE41B5366F1AFK","ACC444A92A023CE438BA42EBD286959A886V","ACC1381593417917907","ACCFBB94BF645E2411AA75F29EA6D00CEDB3","ACC357DDF5B138F4FF6A093A47358CE6E5DE","ACC13452869281616531","ACCE3121E1F1DD9447D88B052803DBEE848Z","ACC29AA46041F344370A096202FA5ED943BW","ACC14215635411148470","ACC138882070540693","ACCEE79114ED745438BB1AD7D813193798FD"]
  testAccountPhones: []
  optionalPermissions:
    - CONTACT
    - LOCATION
    - DEVICE
  mandatoryPermissions:
    - SMS
  tncUrl: https://www.flipkart.com/pages/efa-preapproval-tnc
  privaryPolicyUrl: https://www.flipkart.com/m/st/policies
  formFields:
    CITI:
      - fieldType: TEXT
        label: Company Name
        name: Company
      - fieldType: NUMBER
        label: Monthly Income
        name: Income
      - fieldType: DROPDOWN
        label: Employment
        name: Employment
      - fieldType: DROPDOWN
        label: Occupation
        name: Occupation
      - fieldType: DROPDOWN
        label: Qualification
        name: Qualification
      - fieldType: ADDRESS
        label: Current Address
        name: Address
    KISSHT: []
  lenderWiseFormFields:
    KISSHT:
      - PAN_NUMBER
      - DOB
      - GENDER
      - ADDRESS
    INDIA_BULLS:
      - PAN_NUMBER
      - DOB
      - GENDER
      - EMPLOYMENT_STATUS
      - MONTHLY_SALARY
      - ADDRESS
  underwritingCallouts:
    KISSHT: [{"callout":"Repay by 15th of next month at 0% interest","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}},{"callout":"Repay in 6-12 EMI's at 25% per annum","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/dfcd50c7-4c29-404e-91da-dab3c7861872.png?q={@quality}"}},{"callout":"No preclosure charges","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/93ab33b6-f57b-433a-a336-745b63c4d7a3.png?q={@quality}"}},{"callout":"Late fee - 3% of the unpaid bill* (Min ₹200)","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/8e307790-3518-446f-b545-9e22243bcfeb.png?q={@quality}"}}]
    INDIA_BULLS: [{"callout":"Repay by 15th of next month at 0% interest","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}},{"callout":"Repay in 3-12 EMIs starting at 22-28% interest p.a","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/dfcd50c7-4c29-404e-91da-dab3c7861872.png?q={@quality}"}},{"callout":"Late fee - 2.5% of the unpaid bill","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/8e307790-3518-446f-b545-9e22243bcfeb.png?q={@quality}"}}]
  approvalCallouts:
    KISSHT: [{"callout":"Start shopping, select cardless credit as your payment option.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d388836b-a541-4f36-95df-49818d720bb7.png?q={@quality}"}},{"callout":"Repay by 15th of the month using debit card/netbanking.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d4534a52-a197-4463-9530-90ac5a25b4aa.png?q={@quality}"}}]
    INDIA_BULLS: [{"callout":"Start shopping, select cardless credit as your payment option.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d388836b-a541-4f36-95df-49818d720bb7.png?q={@quality}"}},{"callout":"Repay by 15th of the month using debit card/netbanking.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d4534a52-a197-4463-9530-90ac5a25b4aa.png?q={@quality}"}}]


bigfootEntityConfig:
  borrowerConfig:
    name: fintech_borrowers
    schemaVersion: 1.7
  loanApplicationConfig:
    name: fintech_loan_application
    schemaVersion: 4.2
  pLApplicationConfig:
    name: calm_loan_application
    schemaVersion: 1.0
  subApplicationConfig:
    name: fintech_loan_subapplication
    schemaVersion: 3.0
  whitelistConfig:
    name: fintech_whitelist
    schemaVersion: 2.0
  sourceAttributionConfig:
    name: fintech_source_attribution
    schemaVersion: 1.0
  ebcApplicationConfig:
    name: fintech_ebc_borrower
    schemaVersion : 1.0
  pincodeConfig:
    name: fintech_non_serviceable_pincode
    schemaVersion: 1.0
  revalidationApplicationConfig:
    name: fintech_revalidation_application
    schemaVersion: 2.1

cryptoConfig:
  algo: AES
  secretKey: "Cd1/SXipT)So3=19"
  cbcTelesaleSecretKey: "cbc/Tele$@les(=3"

#underwritingConfig:
#  endPoint: "http://***********:80/fintech-underwriting"

citiConfig:
  enabled: false
  productConfig:
    productCode: PC400
    sourceCode: AAFKMNET
    organization: 730
    logo: 400
    embossName: "Matthew Hyden"
  supportedPincodes: ["682000"]

lenderConfiguration:
  configurations:
    EFA:
      -
        consentTextKey: efa-citi-consent-text
        hasAdditionalForm: true
        interestRate: 12
        emiSupported: true
        paylaterSupported: false
        lateFees: 15
        lender: CITI
        postApprovalTncKey: efa-citi-postapproval-tnc
        preApprovalTncKey: efa-citi-preapproval-tnc
        showKycDetails: false
        primaryDataEditable: false
      -
        consentTextKey: efa-consent-text
        hasAdditionalForm: false
        interestRate: 20
        emiSupported: true
        paylaterSupported: true
        lateFees: 20
        lender: KISSHT
        postApprovalTncKey: efa-postapproval-tnc
        preApprovalTncKey: efa-preapproval-tnc
        showKycDetails: true
        primaryDataEditable: true
      -
        consentTextKey: efa-consent-text
        hasAdditionalForm: false
        interestRate: 20
        emiSupported: true
        paylaterSupported: true
        lateFees: 20
        lender: INDIA_BULLS
        postApprovalTncKey: efa-postapproval-tnc
        preApprovalTncKey: efa-preapproval-tnc
        showKycDetails: true
        primaryDataEditable: true

    BNPL:
      -
        consentTextKey: dummy
        hasAdditionalForm: false
        interestRate: 0
        emiSupported: false
        paylaterSupported: true
        lateFees: 0
        lender: KISSHT
        postApprovalTncKey: dummy
        preApprovalTncKey: dummy
        showKycDetails: false
        primaryDataEditable: false
        minimumAgeRequired: 0
      -
        hasAdditionalForm: false
        lender: IDFC
        primaryDataEditable: true
        nameMatchThreshold: 65
        nameValidationEnabled: true
        panValidationRequired: false
        postApprovalTncKey: dummy
        preApprovalTncKey: dummy
        consentTextKey: dummy

    FLIPKART_ADVANZ:
      -
        hasAdditionalForm: false
        lender: IDFC
        primaryDataEditable: true
        nameMatchThreshold: 70
        nameValidationEnabled: true
        panValidationRequired: false
        postApprovalTncKey: dummy
        preApprovalTncKey: dummy
        consentTextKey: dummy
        minimumAgeRequired: 18

    CBC:
      -
        lender: AXIS

upgradationConfig:
  upgradationEnabled: true

tijoriConfig:
  url: http://************:80
  clientName: robinhood

tijoriAsyncClientConfig:
  merchant: mp_flipkart
  exchangeName: lms_onboarding_queue
  tijoriUrl: http://************:80

#hawkeyeAsyncConfig:
#  client: FK_CONSUMER_CREDIT
#  exchangeName: hawkeye_dedupe_ingestion
#  url: http://************/cf-events/v1/identity-aggregation

external_client_config:
  page_service_config:
    host: ***********
    port: 80

cbcOnboardingConfig:
  fkpayTriggerOtpUrl: https://pay.payzippy.com/fkpay/api/v3/fintech/otp/send
  fkpayDisplayCardEtbUrl: https://pay.payzippy.com/fkpay/api/v3/fintech/paymentInstrument/CBC_AXIS_APP_SERNO
  fkpayDisplayCardEtccUrl: https://pay.payzippy.com/fkpay/api/v3/fintech/paymentInstrument/CBC_AXIS_CARD_SERNO
  fkpayDisplayCardConsoleUrl: https://pay.payzippy.com/fkpay/api/v3/fintech/paymentInstrument/CBC_AXIS_CARD_CONSOLE
  etbApprovalCallouts: [{"callout":"Access your card details","description":"Account Summary - Details of dues, transactions","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}},{"callout":"Reward Points Summary","description":"Manage/Redeem reward points or Set Goals","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}},{"callout":"Create Requests","description":"Subscribe for E-statement or duplicate statement, Block Lost/Stolen card, Active international usage...","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}}]
  ntbApprovalCallouts: [{"callout":"You will need to provide an identity proof from the list of accepted documents","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}}]
  cbcComingSoonBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/01/2020/011254a8-5cb8-4286-84aa-d893f4026216.jpg?q={@quality}
  cbcApplyNowBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/01/2020/6dbcf9ef-766e-4297-90c5-300d14bf5b9a.jpg?q={@quality}
  cbcContinueApplicationBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/07/07/2019/a9a79dfe-ad02-4241-925b-31331f619f46.jpg?q={@quality}
  cbcViewCardDetailsBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/01/2020/702e0955-3475-42a0-a71e-36a410e043a8.jpg?q={@quality}
  cbcApplicationEtccBuzzBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/09/2019/0790d498-9844-47a9-b8ab-25b2c3d5ba0c.png?q={@quality}
  cbcApplicationEtccNonBuzzBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/26/06/2019/8f52e838-3942-458c-ba93-c274c5de9df0.png?q={@quality}
  cbcApplicationEtccBuzzBannerAspectRatio: 312:62
  cbcApplicationEtccNonBuzzBannerAspectRatio: 312:80
  externalMerchnats: ["phonepe","offers","myntra","offers_FYFemployee","myntra_employee","mastercard","wakefit","referral","PVR_OTA","Cred_OTA","Jeeves_OTA","Ekart_OTA","PhonePe_OTA","MyGate_OTA","BookMyShow_OTA","Nobroker_OTA","Truecaller_OTA","Shopsy_OTA"]
  plusWhitelistIdMap:
    phonepe : 120
    offers : 142
    offers_FYFemployee : 142
    myntra: 145
    myntra_employee : 142
    mastercard : 142
    wakefit : 142
    referral: 142
    PVR_OTA: 175
    Cred_OTA: 175
    Jeeves_OTA: 175
    Ekart_OTA: 175
    PhonePe_OTA: 175
    MyGate_OTA: 175
    BookMyShow_OTA: 175
    Nobroker_OTA: 175
    Truecaller_OTA: 175
    Shopsy_OTA: 175
  whitelistIdMap:
    phonepe : 119
    offers : 142
    offers_FYFemployee : 142
    myntra: 144
    myntra_employee : 142
    mastercard : 142
    wakefit : 142
    referral: 142
    PVR_OTA: 175
    Cred_OTA: 175
    Jeeves_OTA: 175
    Ekart_OTA: 175
    PhonePe_OTA: 175
    MyGate_OTA: 175
    BookMyShow_OTA: 175
    Nobroker_OTA: 175
    Truecaller_OTA: 175
    Shopsy_OTA: 175

  ntbFormSubTypeWiseFormList:
    PERSONAL_DETAILS:
      - personal_details
    CONTACT_DETAILS:
      - contact_details
      - address_form
    PROFESSIONAL_DETAILS:
      - professional_details
    CREDIT_CARD_FEATURES:
      - credit_card_features
  etsFormSubTypeWiseFormList:
    PERSONAL_DETAILS:
      - personal_details
    CONTACT_DETAILS:
      - contact_details
      - address_form
    PROFESSIONAL_DETAILS:
      - professional_details
    CREDIT_CARD_FEATURES:
      - credit_card_features
  accountsEnabledForCbcSplit: ["ACC50115DF31D8A45AC856774B1346610B46","ACC14127469967838707","ACC13628329423566913","ACC9479A7B4B6B44721BA2F49970FA1BE99P","ACCB7C21F4E383A476DBA48617DF483F46B1","ACC71966AF0CC6B4F5DA5D939933C64E59D2","ACCDFCDCBB789AB41BD901DD35AF3780A87R","ACCE6940FD99C894AC391D36F32784F347AN","ACCF549211B36ED40F5895EF456497FD2DE6","ACC8FB6DC77EAE34D70BAA1CB253157877DK","ACCBD7F239FE8674C1192CB1F6E2695B2AD5","ACC14194967898305938","ACC13952973821181947","ACC002CB2D6D12A4C3CB5418A53895592E5B","ACC14196548493061762","ACC85178D2BDE77474093C148F6CD03548CT","ACC14125264344238798","ACC61E764539E444321AC83CA2C40F89894Q","ACC13583174092516984","ACC23B478798A7941E6A4AC5EB8CB0EA555X","ACC14266505952067444","ACCZ1QYLLEK5M2VS95UBJT0T07HP55NI","ACC8D93F0CA42284E29B439324440A70985B","ACC9F70C9ABED854214ABADF7FF662DD16E1","ACC14170935484913214","ACC13966161768743525","ACCF22A7A40BBBE48B1B265FFF7611E66EA7","ACCF5DDAFF38EE340BE84C37FE7CE51B020F"]
  accountsEnabledForConsoleCug: []
  accountsEnabledForCbcNtbSplitForm: ["ACXGN4LIBRS1DYHS6PNEDBHZNDZGJXPU","ACCB4DEB84ADC7F4558B066A1ED494509ECR"]
  channelsEnabledForCbcNtbSplitForm: ["ANDROID","iOS"]
  whitelistIdsEnabledForCbcNtbSplitForm: ["108","109","66","107","110","119","120","42","127","134","136","137","138","142","143","144","145","151","152","161"]
  cbcNtbSplitEnabled: true
  cohortWiseFormList:
    ETB_PRE_APPROVED:
      - personal_details
      - address_form
      - credit_card_features
    ETB_NON_PRE_APPROVED:
      - personal_details
      - address_form
      - professional_details
      - credit_card_features
    ETB_PRE_APPROVED_AUTODEBIT:
      - personal_details
      - autodebit_details
      - address_form
      - credit_card_features
    ETB_NON_PRE_APPROVED_AUTODEBIT:
      - personal_details
      - address_form
      - professional_details
      - autodebit_details
      - credit_card_features
    ETCC:
      - personal_details
      - address_form
    NTB:
      - personal_details
      - contact_details
      - professional_details
      - address_form
      - credit_card_features
  axisThinCremoScoreThreshold: 474
  autoRejectThinEnabled: N
  encryptionKey: "53EACE72CD83D6B60754C2F3959168EA"
  dobSubtextEnabled: Y
  kycSchedulerTtl: 60
  accountsEnabledForCheckStatusV3: []
  accountEnabledForKycV2: []
  firstYearFreeStartDate: 05/03/2021
  firstYearFreeEndDate: 06/04/2021
  accountsEnabledForStoreMergeExp: ["ACXGN4LIBRS1DYHS6PNEDBHZNDZGJXPU","ACCD1FAFCA251E848B48031CB2D42EBD100H"]
  storeMergeEnabled: true
  cbcVkycEnabled: true
  emiUserList: ["ACCDACCADDC749C469E9B8359BB4B3CC57DX", "ACC3BB3DEE46C6B482F9ECD3F33667719CC4", "AC1F3MDHM89Q3HBGAQDF5VIED4W4QUI2", "ACC6D49AB8EF27E4F739CB0CB3BB7E7EF38Q", "ACC1422097734542828", "ACC05D577873443449FA9E165A662D03B961", "ACCD5BE3B6779104E5FAC342CD54EA67C85X", "ACCDF71C9D2869C468AAA84879DFACDE6B4U", "ACC13652414813072222","ACC14125708244584354","ACC13852111086162751"]
  whitelistIdForOfflineCustomers: 133
  accountsEnabledForEtccAlternateCug: []
  accountsEnabledForTransactionSummary: ["ACC13779619698657339","ACC14009331113814800","ACA4M6840LRN8ECDJ4DLYBK5JJIPQJNF","ACBI82Y7PKUOH0AE1B2TEWK8U9J27SQV","ACZ4PS931OOS8NZU5Z83WA3K4HPIIM8K","ACC05D577873443449FA9E165A662D03B961","ACC13869407034591619"]
  externalWhitelistIds : [119,120,142,144,145]
  incomeBinThreshold: 474.1
  cbcHyperlinkPhoneEnabled: true
  checkApplyNowVisibilty: true
  cbcNewRejectPageEnabled: false
  ntbCugUserList: ["ACC39537669976E4CDF95F265AC514076EF6"]
  cbcNtbRevampValue: true
  ntbCbcWhitelistIds: [ "66" ]
  etccCbcWhitelistIds: [ "42" ]
  etbpaCbcWhitelistIds: [ "136" ]
  etbnpaCbcWhitelistIds: [ "137" ]
  ntbSupercoinWhitelistIds: [ "142" ]
  etccSupercoinWhitelistIds: [ "143" ]
  etbpaSupercoinWhitelistIds: [ "144" ]
  etbnpaSupercoinWhitelistIds: [ "145" ]
  emiBillingEnabled: true
  applicationRetryDuration: 500
  applicationSyncCutoffDate: "2022-12-22 20:00:00"

collectionsClientConfiguration:
  scheme: http
  host: ************
  port: 7980

contextWiseWhitelistConfig:
  contextMap:
    NUDGING-EFA-INDIA_BULLS:
      isEnabledForEveryone: true
      whitelistedAccountIds: []
    NUDGING-CBC-AXIS:
      isEnabledForEveryone: true
      whitelistedAccountIds: ["ACCB4DEB84ADC7F4558B066A1ED494509ECR","ACCD1FAFCA251E848B48031CB2D42EBD100H"]
    NUDGING-CBC_SC_ELITE-AXIS:
      isEnabledForEveryone: true
      whitelistedAccountIds: [ ]
    NUDGING-CBC_SELECTION-AXIS:
      isEnabledForEveryone: true
      whitelistedAccountIds: []
    NPS-CBC-AXIS:
      isEnabledForEveryone: true
      whitelistedAccountIds: ["ACC1A75EB1E55584AE89225A4B9CC7232FCT"]
    NUDGING-FLIPKART_ADVANZ-IDFC:
      isEnabledForEveryone: true
      whitelistedAccountIds: []

contextWiseCacheBuilderConfig:
  contextMap:
    PAN_RETRY:
      maximumSize: 10000
      duration: 30
      durationUnit: DAYS

#schedulerClientConfig:
#  host: ************
#  port: 80
#  poolSize: 20
#  clientId: fintech_cf

heliosProxyConfig:
  maxConnections: 1024
  maxConnectionsPerHost: 1024
  pooledConnectionIdleTimeoutInMS: 6000
  executorThreadsCount: 32
  executionIsolationSemaphoreMaxConcurrentRequests: 1024
  executionTimeoutInMS: 10000
  hystrixCommandKey: PinakaServiceHystrixCommand
  hystrixGroupKey: PinakaServiceHystrixGroup
  rewriteProtocal: http
  rewriteHostName: 127.0.0.1
  rewritePortNumber: 80
  enableProxy: false

#bigfootCallbackConfiguration:
#  pinakaBaseUrl: http://************:80
#  callbackQueue: advanz_ingestion_queue

fldgConfiguration:
  enableFldg: false

abConfiguration:
  endpoint: ***********:80
  layerList: "Affordability"
  clientId: "Mapi.affordability"
  testAccounts: ["ACC14125653191935118","ACC2DEDB3BC47054F818FAA8E680072E011R","ACC8B2427BE8D354EAF9D94046347FDA13ES","ACC13751787976165289","ACCFGSS5XIRIFCRNJN4ESU8U0C16CYFD","ACVJUFRNA0C62TSCCU8X6LY1OFGWJ9KC","ACCF152B52D9AD444E0B7BFF9781CF15672U","ACC209807A5FBBD42A6866DD717E9F63190V","ACC13902857290074280","ACCE73189B3192B406AA7A5EF89A31CAC51M", "ACCD2A42BF9E2744EC2AA90D68A66CC05B7F", "ACC14159601674158924", "ACC18D1EA7880024BC8BF3C1B2503D19375U", "ACC0963D770D9D34715B0CBCC68731BC12D4", "ACC00A1D9FD30B148329F8B7A5C2C51BD40E", "ACC2CBE947F34F94764B1C7A2D2A134E012C", "ACCB92329A0DAB047A89FED58A61552C01F3", "ACC0D47F8B9AE4B4D88A133354DD8D23723M", "ACCCBE6B65DCCF546C78D6B6CC4C97851E2O", "ACC0ACDB8083FCB40C4BAE7DABD4971A28FZ", "ACCF15CDBB28108421B972ACD9021AA29C9O", "ACC2C0136173EF746E4AD7F941E9108A7CFG", "ACC5CD31F79023C4084B6183B7D66A299D9C", "ACC14076817150162100", "ACCACC54415130E4D5196C215FF591E6C28Q", "ACC338CF791A95C42CF824458C07F6BC9B5"]
  unlayeredAbEnabled: true
  clientSecretKey: fintech-pinaka-prod-ee4b7347a0ee409f88cccb70d54238c1

kycConfig:
  xmlKycAndroidAppVersion: 1090100
  xmlKycChannels: ["ANDROID"]
  activeKycModes: ["CKYC","EKYC","AADHAAR_XML_EKYC"]
  createNewKycApplicationForOldUserEnabled: false
  defaultMethodForTestAccount: CKYC

onboardingConfig:
  upgradeJourneyLimitReductionAllowed: false
  genericFlowEnabled: true
  whitelistChangeEntityEnabled: true
  genericFlowAccountIds: ["ACC8B2427BE8D354EAF9D94046347FDA13ES","ACC76FCADFC7B3A443F939DFE42003603AAI","ACC13949080388889691","ACCFGSS5XIRIFCRNJN4ESU8U0C16CYFD","AC3QOAOVQY564L9UUF32F1IK337QJSJI","AC2NB2C5YC767IW4L0BWERK8QLHZ7NZV","ACC76FCADFC7B3A443F939DFE42003603AAI","ACCA82A18F3B68446A0B6778880AD04B619A","AC3QOAOVQY564L9UUF32F1IK337QJSJI","AC6XS5CP996NNX4ORNUBFFRY0AFLIDK7","ACCDE528BE0481E44D8A1AA659CE5C16552R","********************************","ACZR6TK7PW29IW7THD7EN5P4ORU0NBH5","ACC3CF41AD0525A48F8AC35FE4F4A7E55EBR","ACC13650015713567535","ACC13707716386068457","ACGCV7V9OWMYM7II11B3R2GIKI209AFJ","ACC14020001643342490","ACDJS2DWR3QKMKXB4JIFS30T0HJMUR4T","ACCA82A18F3B68446A0B6778880AD04B619A","ACC7D1E281CA77448E084C1430D789B4679N","ACMM3GTMZ561LL5B8TEIBSSFTU722MTU","ACCFGSS5XIRIFCRNJN4ESU8U0C16CYFD","ACC14125653191935118","ACC3CF41AD0525A48F8AC35FE4F4A7E55EBR","ACC4C5070D22EBD4F26A017B71167444CFCW","ACSCAJ0F4M9XEIQ1LZUFRE06NUSG7QGR","ACC1390841791736163","AC2NB2C5YC767IW4L0BWERK8QLHZ7NZV","ACC76FCADFC7B3A443F939DFE42003603AAI","ACCA82A18F3B68446A0B6778880AD04B619A","AC3QOAOVQY564L9UUF32F1IK337QJSJI","AC6XS5CP996NNX4ORNUBFFRY0AFLIDK7","ACCDE528BE0481E44D8A1AA659CE5C16552R","********************************","ACZR6TK7PW29IW7THD7EN5P4ORU0NBH5","ACC3CF41AD0525A48F8AC35FE4F4A7E55EBR","ACC13650015713567535","ACC13707716386068457","ACGCV7V9OWMYM7II11B3R2GIKI209AFJ","ACC14020001643342490","ACQ23LU0JFDB2RR6K07NG1IJ7ZL5NQYI","ACLW8SA2B1HF8REY6E874HIC6RWY1PU4","ACC13906123955101848","ACXQ0MEGDT4TMQQK2L6MKVNC1EF4Z5LG","ACC13448758795321772","ACC34E011F6573C49F99454A57DDFCA0233A","ACX3S9XRMJCHNKMH24QUW32787JTPLG2","AC1YGH19NG7TA2EHE96X3KJAKKRKCIG9","ACC13949080388889691","ACLBEUUIJDCIIH8O9GIAGTBVQS27UT40","ACC13697461245573315","ACC89DF564F153749BFBAEE53BD69E006C7W","ACC13494141454499528","ACC13697461245573315","ACX3S9XRMJCHNKMH24QUW32787JTPLG2","ACC13909443445886072","ACC14241680542752590","ACCF5245FAC1FFD4AE78CE7A40D23375D88D","AC5KM3V9NOUYTQ9NAYL4V1SH8HHKRCVB","ACVLYN1JH42Y56COO3VHMB2E1R6K48MG","ACJFFESW6TOWJS6RKG2TI0XG0UCSCXZ9","ACC14051029814219831","ACL4KJTNNZBC5MKAZUA70XDRZYVIOR1M","ACC13765478998789162","ACCD11FB4A20FC94FEEACA4D04F40E4749EW","ACC13455580597092172"]
  plusWhitelistId: 135
  

#alfredClientConfig:
#  url: "http://************:80"
#  secretKey: "123rewedrgrvcz"

neoCrmClientConfig:
  exchangeName: "fintech_cf_scheduler"
  authToken: "LVNKY7IAABXXAAZB"
  clientId: "Fintech"
  url: "http://***********"
  path: "/v1/crm/publish"

uiConfiguration:
  payLaterCohortPriorityList: ["CASH","PAY_LATER", "EMI", "FSUP"]
  fsupCohortPriorityList: ["CASH", "FSUP", "PAY_LATER", "EMI"]
  cohorts:
    CASH:
      name: Cash Loan
      full_description: For withdrawal upto ₹%s
    EMI:
      name: EMIs
      full_description: For purchases upto ₹%s
    PAY_LATER:
      name: Pay Later
      full_description: Pay next month for shopping upto ₹%s
      minimized_description: Flipkart Pay Later enabled
      logo_url: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/07/2021/9a80ffd5-d436-48b8-bec9-4abcf4ca444e.png?q={@quality}
    FSUP:
      name: Smart Upgrade
      full_description: Get your phone by paying just a fraction of amount upfront
      minimized_description: Flipkart Smart Upgrade enabled
      logo_url: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/08/2021/76e1349c-dc49-4cd7-bc04-c4aa36b2a035.png?q={@quality}
  uiConfigMap:
    APPROVED:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle : "We thank you for showing interest and taking the effort to complete the application, however, based on the information provided, our financing partner’s internal policies, and anti-fraud checks, we are unable to extend “Flipkart Pay Later” to you. Please continue your shopping using numerous other payment modes available on Flipkart."
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month, or pay in easy EMIs of upto 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/24/09/2020/7db826c0-8301-45fb-8b04-a787a6ac294d.png?q=100"
      knowMoreText: "Learn more about Pay Later"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Go to Home Page"
      payLaterSubmitButtonUrl: "https://www.flipkart.com/"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 5th of that month. After 5th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          -
            description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          -
            description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          -
            description: "Pay your bills by 5th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    LOAN_CREATION_IN_PROGRESS:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle: "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "knowMoreText"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          - description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          - description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          - description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    CONDITIONALLY_APPROVED:
      title: "Congratulations"
      nonUpgradedTitle: "Congratulations"
      nonUpgradedSubtitle : "Based on your details, here are some exciting benefits for you!"
      emiSubtitle: "Based on your details, here are some exciting benefits for you!"
      payLaterSubtitle: "Based on your details, here are some exciting benefits for you!"
      imageUrl: ""
      nonUpgradedImageUrl: ""
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/24/09/2020/7db826c0-8301-45fb-8b04-a787a6ac294d.png?q=100"
      knowMoreText: "Know more about Flipkart Pay Later"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Activate Flipkart Pay Later"
      payLaterSubmitButtonUrl: "https://www.flipkart.com/"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 5th of that month. After 5th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          -
            description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          -
            description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          -
            description: "Pay your bills by 5th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
    POST_PROCESSING_PENDING:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle: "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "knowMoreText"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          - description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          - description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          - description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    REJECTED:
      title: "Thank you for your interest in %s"
      subtitle: "Due to our financial partner policies, we are not able to provide a credit line at this moment"
      payLaterSubmitButtonText: "Go to Home Page"
      payLaterSubmitButtonUrl: "https://www.flipkart.com/"
      fsupSubmitButtonText: "Back to product page"
      imageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      nonUpgradedSubtitle : "We thank you for showing interest and taking the effort to complete the application, however, based on the information provided, our financing partner’s internal policies, and anti-fraud checks, we are unable to extend “Flipkart Pay Later” to you. Please continue your shopping using numerous other payment modes available on Flipkart."



mysqlLockConfig:
  prefix: ""
  timeout: 0

dataPointConfiguration:
  enableDataPoint:
    TS:
      enable: false
      testAccountList: ["ACC8B2427BE8D354EAF9D94046347FDA13ES"]

redisConfig:
  masterName: fintech-redis-prod
  timeout: 5000
  sentinelAddresses: ["redis://*************:26379","redis://************:26379","redis://*************:26379"]
  password: ABtr@25
  masterConnectionMinimumIdleSize: 3
  masterConnectionPoolSize: 100
  lockWaitTime: 10
  lockReleaseTime: 3

sourceAttributionConfig:
  enabled: true
  redisTtlInSeconds: 54000

rotation:
  enableRotator: true
  defaultRotationStatus: true

winterfellClientConfig:
  host: http://***********/fintech-winterfell
  clientId: pinaka
  connectionTimeout: 30000
  readTimeout: 30000

#
#lockinClientConfig:
#  url: http://************
#  clientId: Fintech

#robinhoodAsyncCbcConfig:
#  url: http://************
#  exchangeName: "cbc_robinhood_queue"

#robinhoodAsyncPlConfig:
#  url: http://************
#  exchangeName: "pl_robinhood_topic"
#
#
#robinhoodCfaClientConfig:
#  url: http://************
#  exchangeName: "cfa_onboarding_queue"
#
#coreLogisticsClientConfig:
#  url: http://**********
#  referer: "http://www.fintech.com"
#  key: "79a271ce-b870-c03b-cdbb-a52861a90ea9"

pandoraAsyncClientConfig:
  url: http://************/pandora
  exchangeName: advanz_onboarding_queue
  exchangeType: queue


ffbConfig:
  url: http://***********
  endPoint: /v1/ffb/event/process
  exchangeName : advanz_onboarding_queue
  environment: PRODUCTION
  businessToLenderDocumentTypeMapping:
    BUSINESS_PROOF: BUSINESS_PROOF
    SHOP1: OTHER_1
    SHOP2: OTHER_2
    SHOP3: OTHER_3

#kafkaConsumerConfiguration:
#  enableConsumer: true
#  kafkaConsumerProps:
#    AMS:
#      bootStrapServers: 10.64.134.15:9092,10.69.26.5:9092,10.69.186.178:9092,10.66.122.38:9092,10.67.90.133:9092
#      groupId: fk-consumer-credit-pinaka-prod-group
#      sessionTimeoutMs: 30000
#      noOfThreads: 3
#      topic: fintech_los_fk_consumer_credit_topic
#      pollTimeOut: 100
#      enableDlq: true
#      dlqTopicName: fintech_los_fk_consumer_credit_topic_dlq
#    CALM:
#      bootStrapServers: 10.64.134.15:9092,10.69.26.5:9092,10.69.186.178:9092,10.66.122.38:9092,10.67.90.133:9092
#      groupId: calm-pinaka-prod-group
#      sessionTimeoutMs: 30000
#      noOfThreads: 1 #increase this with traffic/consumer lag
#      topic: fintech_calm_topic
#      pollTimeOut: 100
#      enableDlq: true
#      dlqTopicName: fintech_calm_topic_dlq

encryptionKeys:
  kycKey: 9D81B4B4791B791D50315E7A9BB93C4E
  cbcKey: 59AC6C2B95DEFC3EC76C56CF232AF829
  default: 59AC6C2B95DEFC3EC76C56CF232AF829

pinakaAsyncClientConfig:
  url: http://************
  exchangeName: advanz_onboarding_queue
  ebcExchangeName: production.pandora.ebc.callback
  changeEntityEventTopic: fintech.change.entity
  cfaExchangeName: cfa_onboarding_queue
  advanzDedupeExchangeName: efa_onboarding_2
  cbcCardSerNoExchangeName: cbc_card_ser_queue
  cbcCardSerNoHostUrl: http://************:80
  cbcCardSerNoPath: /pinaka/5/applications/process-application

#plutusClientConfig:
#  url: http://************:80

#fintechUserServiceClientConfig:
#  uslUrl: http://************:80
#  exchangeName: fintech_usl_ingestion_queue

#caishenClientConfig:
#  url: http://************
#  targetClientId: caishen_flipkart

#khaataAsyncClientConfig:
#  url: http://**********:8980
#  exchangeName: cbc_bill_initiation_queue
#
#skylerAsyncClientConfig:
#  url: http://************
#  exchangeName: cbc_bill_initiation_queue
#
#fkPayClientConfig:
#  url: http://**********
#  clientId: Fintech
#  clientSecret: ypg37lwetecaj96pih96
#  encryptionKey: FINTECH_AES_KEYS

#stratumD42Configuration:
#  accessKey: GOOG1E3SJJCU2IZ2SJFAKQHTM5APXUQNPBLOPLFB4ATU2DD4G253OH3IPPWCQ
#  secretKey: s8xARM9wZnZF5dc+z/2hleHJVIVTPxvNStx65lLA
#  endPoint: https://storage.googleapis.com
#  maxConnections: 10
#  connectionTimeout: 3000

outboundBundleConfiguration:
  defaultDaysToKeep: 15
  shardNames: ["default","queues"]
  databaseName: pinaka

uslClientConfig:
  host: http://************:80
  clientId: pinaka
  connectionTimeout: 2000
  readTimeout: 2000

consentClientConfig:
  host: http://************:80
  clientId: pinaka
  connectionTimeout: 2000
  readTimeout: 2000

consentConfig:
  consentVersionMap:
    KYC-PAGE-CONSENT-V0: 1678065961719
    EXPERIAN-BUREAU-PULL-CONSENT-V0: 1678065920927
    DEVICE-DETAILS-UPLOAD-CONSENT-V0: 1678065854822
    LIMIT_CONSENT_KYC_UPGRADE_JOURNEY: *************
  consentGroups:
    ONBOARDING_CONSENTS:
      - KYC-PAGE-CONSENT-V0
      - EXPERIAN-BUREAU-PULL-CONSENT-V0
      - DEVICE-DETAILS-UPLOAD-CONSENT-V0

audienceManagerConfiguration:
  kafkaProducerConfiguration:
    bootstrapServer: *************:9092,*************:9092,************:9092,************:9092
    acks: 1
    retries: 3
    batchSize: 20
    lingerInMilliSecond: 5
    maxInFlightRequestsPerConnection: 1
  realtimeSegmentConfiguration:
    enabled: true
    testAccounts: []
    topicName: tagger-kafka-fintech
    enabledJourneyList: ["LOAN_APPLICATION_JOURNEY","WHITELIST_JOURNEY"]
  redisTtlInSeconds: 7776000

financialProviderConfig:
  kycMethodConfig:
    AADHAAR_XML_EKYC:
      metadata:
        IDFC:
          tncUrl: https://www.flipkart.com/pages/pay-later-tnc-v12
          tollFreeNo: 1860 500 9900
          email: <EMAIL>
          xmlKycTncUrl: https://www.flipkart.com/pages/advanz-service-tnc
          xmlKycDisclaimer: If you do not wish to share your Aadhaar number, you can submit your KYC documents at the nearest IDFC FIRST Bank branch.
          reviewAndSubmitDisclaimer: I agree to (1) share the above details with Flipkart Advanz  (“FAPL”)  & its lending partners (2) allow FAPL's lending partners to obtain my credit information from a credit information company (3) provide my express consent and instruction to FAPL to obtain, and Experian CIC (“Bureau”) to provide a copy of my credit information to FAPL, from time to time upon FAPL's request, for the purposes of this facility offered by Flipkart Advanz and checking my eligibility for current & future products & services of FAPL (“FAPL P&S“). I understand that this consent is valid for a period of 12 months from today. I also provide my consent to being contacted by FAPL or its Affiliates (defined in T&Cs) for offers of current & future FAPL P&S. I acknowledge that my credit information may be used by FAPL or an Extract (defined in T&Cs) may be shared by FAPL with its partner lenders for the purposes of this facility or to assess eligibility for current and future FAPL P&S. I understand that I shall have the option to opt-out/unsubscribe from this facility and confirm that I have read and accepted the T&Cs and Privacy Policy, in accordance with which FAPL will store, use and share this information. (4) FAPL
          reviewAndSubmitTncUrl: https://www.flipkart.com/pages/advanz-service-tnc
          reviewAndSubmitTncText: Terms & Conditions
          consentContext: KYC-PAGE-CONSENT-V0
          kycType: UPGRADABLE
      validation:
        IDFC:
          nameMatchThreshold: 50
          nameValidationEnabled: true
          panValidationRequired: true
      validityInYears: 1
      validityExtensionPeriod : 9
      supportedAndroidAppVersion: 1090100
      supportedChannelList: ["ANDROID"]
    EKYC:
      metadata:
        IDFC:
          consentContext: KYC-PAGE-CONSENT-V0
          kycType: PARTIAL
      validation:
        IDFC:
          nameMatchThreshold: 50
          nameValidationEnabled: true
          panValidationRequired: true
      validityInYears: 1
      validityExtensionPeriod : 0
    CKYC:
      metadata:
        IDFC:
          tncUrl: https://www.flipkart.com/pages/pay-later-tnc-v12
          tollFreeNo: 1860 500 9900
          email: <EMAIL>
          disclaimer: If you do not wish to share your Aadhaar number, you can submit your KYC documents at the nearest IDFC FIRST Bank branch.
          reviewAndSubmitDisclaimer: I agree to (1) share the above details with Flipkart Advanz  (“FAPL”)  & its lending partners (2) allow FAPL's lending partners to obtain my credit information from a credit information company (3) provide my express consent and instruction to FAPL to obtain, and Experian CIC (“Bureau”) to provide a copy of my credit information to FAPL, from time to time upon FAPL's request, for the purposes of this facility offered by Flipkart Advanz and checking my eligibility for current & future products & services of FAPL (“FAPL P&S“). I understand that this consent is valid for a period of 12 months from today. I also provide my consent to being contacted by FAPL or its Affiliates (defined in T&Cs) for offers of current & future FAPL P&S. I acknowledge that my credit information may be used by FAPL or an Extract (defined in T&Cs) may be shared by FAPL with its partner lenders for the purposes of this facility or to assess eligibility for current and future FAPL P&S. I understand that I shall have the option to opt-out/unsubscribe from this facility and confirm that I have read and accepted the T&Cs and Privacy Policy, in accordance with which FAPL will store, use and share this information. (4) FAPL
          reviewAndSubmitTncUrl: https://www.flipkart.com/pages/advanz-service-tnc
          reviewAndSubmitTncText: Terms & Conditions
          consentContext: /idfcCkycConsentContext
          kycType: UPGRADABLE
          incorrectDetailsPopupHeading: Complete KYC using another method
          incorrectDetailsPopupText: Since you noticed incorrect information - we recommend you to restart KYC using another method.
          incorrectDetailsPopupImageUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/06/2020/5f99d43c-5433-4c09-93bb-75f9caa6edda.png?q={@quality}
          incorrectDetailsPopupButtonText: Continue to complete KYC
      validation:
        IDFC:
          nameMatchThreshold: 50
          nameValidationEnabled: true
          panValidationRequired: true
      validityInYears: 1
      validityExtensionPeriod : 9

#ffbProxyConfiguration:
#  maxConnections: 1024
#  maxConnectionsPerHost: 1024
#  pooledConnectionIdleTimeoutInMS: 6000
#  executorThreadsCount: 32
#  executionIsolationSemaphoreMaxConcurrentRequests: 1024
#  executionTimeoutInMS: 10000
#  hystrixCommandKey: PinakaServiceHystrixCommand
#  hystrixGroupKey: PinakaServiceHystrixGroup
#  rewriteProtocal: http
#  rewriteHostName: ************
#  rewritePortNumber: 80
#  enableProxy: true

#orchestratorClientConfig:
#  url: http://***********:80

hystrixModuleConfiguration:
  hystrixPropertiesFileName: hystrix.properties
  resourcesFilePath: /etc/config/

multiTenantOAuthClientConfig:
  cachedAccessTokenTTL: 120
  oAuthUrl: http://************:80
  oAuthServiceClientConfigMap:
    FK_CONSUMER_CREDIT:
      oAuthClientID: c2c95e0075c04b2e9b83e1bc8a09f57e
      oAuthClientSecret: FVMWGlJ22AoWfQ+ithjYn+M0tjr8xgakuHl06J5A+DELtVtc
      cachedAccessTokenTTL: 120
      oAuthUrl: http://************:80

multiTenantConnektClientConfig:
  tenantConnektClientConfigMap:
    FK_CONSUMER_CREDIT:
      exchangeName: fintech_los_connekt
      callbackUrl: http://************/pinaka/communications
      domain: flipkart
      emailUrl: http://************
      pnUrl: http://************
      smsUrl: http://************
      inAppUrl: http://************
      emailApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      pnApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      smsApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      inAppApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      emailAppName: flipkart
      pnAppName: RetailApp
      smsAppName: flipkart
      inAppName: flipkart
      emailBucket: alerts
      emailSubBucket: pay
      inAppBucket: alerts
      inAppSubBucket: pay
      pnBucket: alerts
      pnSubBucket: pay
      smsBucket: alerts
      smsSubBucket: pay
      pnChannelId: fk_channel_order_payments
      transactionalEmail: true
      transactionalPN: true
      transactionalSMS: true
      transactionalInApp: true

turboConfig:
  singleDbWriteEnabled: false
  multiDbWriteEnabled: true
  turboOutboundWithoutTrxEnabled: false
  sharding: true
  appDbType: "mysql"
  queue_shard_strategy:
    default_suffix: default
    clusters:
      queues:
        - advanz_onboarding_queue
        - fintech_los_connekt
        - fintech_cf_scheduler
        - lms_onboarding_queue
        - hawkeye_dedupe_ingestion

  mysql:
    hibernate.dialect: org.hibernate.dialect.MySQL57Dialect
    hibernate.c3p0.idle_test_period: "60000"
    hibernate.c3p0.max_size: "100"
    hibernate.c3p0.max_statements: "50"
    hibernate.c3p0.min_size: "20"
    hibernate.c3p0.timeout: "300"
    hibernate.connection.driver_class: com.mysql.jdbc.Driver
    hibernate.connection.password: "root"
    hibernate.connection.url: "************************************************************************************************"
    hibernate.connection.username: "root"
    hibernate.connection.autocommit: true
    hibernate.show_sql: "true"
    hibernate.connection.zeroDateTimeBehavior: convertToNull

#gibraltarClientConfig:
#  host: https://************
#  keySpaceName: onboarding
#  clientName: efa-onboarding-client
#  targetClientId: gibraltar-prod
#  maxPublicKeyCount: 100
#
criClientConfig:
  host: http://************
  maxRetries: 3
#
#oAuthLockinServiceClientConfig:
#  oAuthUrl: https://service.authn-prod.fkcloud.in
#  oAuthClientID : CBC
#  oAuthTargetClientID: wallet_flipkart_plus_prod
#  oAuthClientSecret : Ex1ay/bDFyTeRB2aABf8fUO7vMpOPvxWHhF6hITatNpsxtDe
#  cachedAccessTokenTTL : 120

#coinManagerClientConfig:
#  url: http://************
#  clientId: "CBC"
#  merchantId: "MERCH220714214155975QVPWD"
#  requestId: "ABCD"

#skylerClientConfig:
#  url: http://************:80/fintech-skyler/
#  clientId: "pinaka"
#  exchangeName: "cbc_bill_initiation_queue"

esClientConfig:
  hostName: "sm-es-prod-elasticsearch.sm-es-prod.fkcloud.in"
  port: 80
  connectionTimeout : 3000

#khaataClientConfig:
#  url: http://**********:8980
#  clientName: "pinaka"

viestiConfig:
  enableConsumer: true
  viestiConsumerProps:
    #    CRI:
    #      pulsarClientConfig:
    #        authnSecret: VdsO8BjanfTADqp3RNaaDm0bkm14iyVGJ3lUuGdaSTS+7Ba0
    #        authnClientId: pinaka-service
    #        issuerUrl: https://service.authn-prod.fkcloud.in
    #        viestiEndPoint: http://************:80
    #      pulsarConsumerConfiguration:
    #        topic: persistent://fpg-viesti-prod-test/revalidation/revalidation-v1
    #        subscriptionName: fpg-viesti-prod-test/revalidation-consume
    #        numberOfConsumers: 2
    #    FK_CONSUMER_CREDIT:
    #      pulsarClientConfig:
    #        authnSecret: VdsO8BjanfTADqp3RNaaDm0bkm14iyVGJ3lUuGdaSTS+7Ba0
    #        authnClientId: pinaka-service
    #        issuerUrl: https://service.authn-prod.fkcloud.in
    #        viestiEndPoint: http://************:80
    #      pulsarConsumerConfiguration:
    #        topic: persistent://prod-yak-archive/prod_fintech_los/yak_archive_fintech_los_fk_consumer_credit
    #        subscriptionName: lending-los/fintech_los_fk_consumer_credit_sub
    #        numberOfConsumers: 2
    CALM:
      pulsarClientConfig:
        authnSecret: VdsO8BjanfTADqp3RNaaDm0bkm14iyVGJ3lUuGdaSTS+7Ba0
        authnClientId: pinaka-service
        issuerUrl: https://service.authn-prod.fkcloud.in
        viestiEndPoint: http://************:80
      pulsarConsumerConfiguration:
        topic: persistent://prod-yak-archive/prod_fintech_los/yak_archive_fintech_los_calm
        subscriptionName: lending-los/fintech_los_calm_sub
        numberOfConsumers: 1



fintechLogConfig:
  serviceTag: pinaka
  bucketNames: ["pinaka-prod"]