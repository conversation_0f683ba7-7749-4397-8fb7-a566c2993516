server:
  type: default
  applicationContextPath: /pinaka
  adminContextPath: /admin
  applicationConnectors:
    - type: http
      port: 9090
  adminConnectors:
    - type: http
      port: 9091
  requestLog:
      # Note:
      # 1. We do not specify a logFormat because the default log format is Common Log Format (CLF), which is good
      # 2. If we specify a logFormat, note that the format specifiers for access logs are different
      # 3. Access log accepts ONLY ONE appender - we cannot get access logs on both console and file simultaneously
      appenders:
        - type: file
          currentLogFilename: /var/log/pinaka/access.log
          threshold: ALL
          archive: true
          archivedLogFilenamePattern: /var/log/pinaka/access-%d{yyyy-MM-dd-HH}.log.gz
          archivedFileCount: 24
          timeZone: IST

logging:
  level: INFO
  appenders:
    - type: file
      threshold: INFO
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: /var/log/pinaka/pinaka.log
      archivedLogFilenamePattern: /var/log/pinaka/pinaka-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: file
      threshold: ERROR
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: /var/log/pinaka/pinaka-error.log
      archivedLogFilenamePattern: /var/log/pinaka/pinaka-error-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: file
      threshold: DEBUG
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: /var/log/pinaka/pinaka-debug.log
      archivedLogFilenamePattern: /var/log/pinaka/pinaka-debug-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: console
      threshold: ALL
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      timeZone: IST
      target: stdout

#healthCheckName: PinakaHealth

swagger:
  title: Pinaka Service
  description: APIs for Pinaka Service
  contact: <EMAIL>
  resourcePackage: com.flipkart.fintech.pinaka.service.web

database:
  driverClass: com.mysql.cj.jdbc.Driver
  url: *************************************
  user: sm_admin
  password: G3XWFs29c8
  initialSize: 10

pinakaClientConfig:
  url: http://***********:9090
  client: pinaka

ardourClientConfig:
  url: http://***********:9080
  client: pinaka

userServiceClientConfig:
  usUrl: http://***********:35200
  usClientId: affordability

oAuthServiceClientConfig:
  oAuthUrl: http://***********:80
  oAuthClientID : 5df700f641aa44bbb24891531e174032
  oAuthClientSecret : jHuvCzzrfBkWQ8ksdwjgdR9mn31Iz1vc4P9vkt+WIiOFvns0
  cachedAccessTokenTTL : 30

loginServiceClientConfig:
  loginServiceUrl: http://*************:25101
  loginServiceClientId: affordability

bigfootConfiguration:
  url: http://***********:28223
  poolName: pool
  company: fkint
  org: cp
  namespace: affordability
  exchangeName: EXCHANGE_NAME
  exchangeType: queue

fluxAsyncClientConfig:
    url: http://***********:9998/api/machines
    clientId: pinaka
    exchangeName: efa_pinaka

connektClientConfig:
  pnUrl: http://***********
  pnApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  exchangeName: fintech_los_connekt
  callbackUrl: http://***********:9090/pinaka/communications
  pnAppName: RetailApp
  pnChannelId: fk_channel_order_payments
  pnBucket: alerts
  pnSubBucket: pay
  domain: flipkart
  transactionalPN : true

robinhoodAsyncClientConfig:
  url: http://***********:8180
  clientId: pinaka
  exchangeName: "%s_onboarding"

pandoraClientConfig:
  url: http://*************:8214/pandora
  client: pinaka

pandoraLiteClientConfig:
  url: http://**********/pandoralite
  client: pinaka

onboardingClientConfig:
  url: http://************:8080
  client: pinaka

fluxConfiguration:
  fluxRuntimeUrl: http://***********:9998
  connectionTimeout: 1000
  socketTimeout: 1000

creditModelConfig:
  url: http://************:35681
  enabled: true

gibraltarConfig:
  maxPublicKeyCount: 100
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  trustStorePath: "/etc/gibraltar/gibraltar-service/preprod/client-truststore.jks"
  trustStorePass: password
  keyStorePath: "/etc/gibraltar/gibraltar-service/preprod/client-keystore.jks"
  keyStorePass: password
  certificateAlias: gibraltarSelfSigned
  url: https://***********:8443
  generateKeysOnStartUp: true

bnplOnboardingConfig:
  optionalPermissions:
    - CONTACT
    - CALL_LOG
    - LOCATION
    - DEVICE
  mandatoryPermissions:
    - SMS
  tncUrl: https://www.flipkart.com/rv/fintech/khaata/terms-v2
  privaryPolicyUrl: https://www.flipkart.com/m/st/policies

efaOnboardingConfig:
  ceEnabled: false
  ceTestUsers: ["test1", "test2","AC5PD5WPWNURTFQ1D8NA9MP85WUXB2UI","Test_1", "Test_2", "Test_3"]
  optionalPermissions:
    - CONTACT
    - CALL_LOG
    - LOCATION
    - DEVICE
  mandatoryPermissions:
    - SMS
  tncUrl: https://www.flipkart.com/pages/efa-preapproval-tnc
  privaryPolicyUrl: https://www.flipkart.com/m/st/policies
  formFields:
      citi:
      - fieldType: TEXT
        label: Company Name
        name: Company
      - fieldType: TEXT
        label: Monthly Income
        name: Income
      - fieldType: DROPDOWN
        label: Employment
        name: Employment
      - fieldType: DROPDOWN
        label: Occupation
        name: Occupation
      - fieldType: DROPDOWN
        label: Qualification
        name: Qualification
      - fieldType: ADDRESS
        label: Current Address
        name: Address
      kissht: []

underwritingConfig:
  endPoint: http://*************:8780/fintech-underwriting

lenderConfiguration:
  configurations:
    EFA:
    -
      consentTextKey: key3
      hasAdditionalForm: true
      interestRate: 10
      emiSupported: true
      paylaterSupported: false
      lateFees: 10
      lender: CITI
      postApprovalTncKey: key2
      preApprovalTncKey: key1
    -
      consentTextKey: key3
      hasAdditionalForm: false
      interestRate: 10
      emiSupported: true
      paylaterSupported: true
      lateFees: 10
      lender: KISSHT
      postApprovalTncKey: key2
      preApprovalTncKey: key1
    BNPL:
      -
        hasAdditionalForm: false
        lender: IDFC
        primaryDataEditable: true
        nameMatchThreshold: 50
        nameValidationEnabled: true
        panValidationRequired: true


tijoriConfig:
  url: http://127.0.0.1:9090
  clientName: robinhood

external_client_config:
  page_service_config:
    host: *************
    port: 25050

gibraltarClientConfig:
  host: https://**********
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  targetClientId: gibraltar-preprod
  maxPublicKeyCount: 100

criClientConfig:
  host: http://***********
  maxRetries: 3

esClientConfig:
  hostName: "sm-es-prod-elasticsearch.sm-es-prod.fkcloud.in"
  port: 80
  connectionTimeout : 3000