imports:
  - org/glassfish/jersey/server/ContainerRequest
  - org/glassfish/jersey/server/ContainerResponse
  - org/glassfish/jersey/message/internal/OutboundJaxrsResponse
  - org/glassfish/jersey/internal/util/collection/Value
  - javax/servlet/http/HttpServletRequest
  - javax/servlet/http/HttpServletResponse

  # For for dropwizard 1.X
  - org/glassfish/jersey/servlet/ServletContainer

  - java/lang/Object
  - java/lang/Class
  - java/lang/Boolean
  - java/lang/String
  - java/net/URI
  - java/util/List

  #   jersey http client
  - org/glassfish/jersey/client/ClientRuntime
  - org/glassfish/jersey/client/ClientRequest
  - org/glassfish/jersey/client/ClientResponse

metrics:

  ServletContainer.service(LURI;LURI;LHttpServletRequest;LHttpServletResponse;)LValue;: #for dropwizard 1.X
    - type: Timed
      name: http.server
      doc: Measuring http resource latencies
      labels: ['$__perf:$2.getHeader("X-PERF-TEST")']

    - type: ExceptionCounted
      name: http.server
      doc: Measuring http resource latencies
      labels: ['statusCode:$3.status', '$__perf:$2.getHeader("X-PERF-TEST")']
      exception: '$3.status:[4-5][0-9][0-9]'

  #   jersey http client
  ClientRuntime.invoke(LClientRequest;)LClientResponse;:
    - type: Timed
      name: http.client
      doc: Http methods call latencies
      labels: ['IP_address:$0.uri.host', 'HTTPMethod:$0.method', 'statusCode:$RESPONSE.status']
      exception: '$RESPONSE.status:[4-5][0-9][0-9]'

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.write(Ljava/lang/String;Lcom/sumo/infosys/bigtable/client/models/RowData;)V:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.write
      doc: Measuring latencies for method write in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkWrite(Ljava/util/Map;)V:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkWrite
      doc: Measuring latencies for method bulkWrite in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkWrite(Ljava/util/Map;Ljava/util/List;)V:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkWrite
      doc: Measuring latencies for method bulkWrite in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.read(Ljava/lang/String;)Lcom/sumo/infosys/bigtable/client/models/Row;:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.read
      doc: Measuring latencies for method read in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.read(Ljava/lang/String;Ljava/util/Map;)Lcom/sumo/infosys/bigtable/client/models/Row;:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.read
      doc: Measuring latencies for method read in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkRead(Ljava/lang/String;Ljava/lang/String;)Ljava/util/Map;:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkRead
      doc: Measuring latencies for method bulkRead in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkRead(Lcom/sumo/infosys/bigtable/client/models/RowsFilter;)Ljava/util/Map;:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkRead
      doc: Measuring latencies for method bulkRead in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.delete(Ljava/lang/String;)V:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.delete
      doc: Measuring latencies for method delete in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkDelete(Ljava/util/List;)V:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkDelete
      doc: Measuring latencies for method bulkDelete in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkDelete(Ljava/lang/String;)V:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.bulkDelete
      doc: Measuring latencies for method bulkDelete in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.truncate()V:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.truncate
      doc: Measuring latencies for method truncate in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.getAllKeys()Ljava/util/List;:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.getAllKeys
      doc: Measuring latencies for method getAllKeys in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.readRowKeysWithPrefix(Ljava/lang/String;)Ljava/util/List;:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.readRowKeysWithPrefix
      doc: Measuring latencies for method readRowKeysWithPrefix in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

  com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.checkAndMutate(Ljava/lang/String;Lcom/sumo/infosys/bigtable/client/models/RowCondition;Lcom/sumo/infosys/bigtable/client/models/RowData;)Lcom/sumo/infosys/bigtable/client/models/CheckAndMutateResponse;:
    - type: Timed
      name: com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl.checkAndMutate
      doc: Measuring latencies for method checkAndMutate in com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl
      labels: []

scope: #choose 1 of below
  ServletContainer.service(LURI;LURI;LHttpServletRequest;LHttpServletResponse;)LValue; #For dropwizard 1.X

system:
  jvm:
    - gc
    - memory
    - threads