curl --location '0.0.0.0:9000/api/fpg/1/action/view' \
--header 'Accept: */*' \
--header 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8' \
--header 'Connection: keep-alive' \
--header 'Origin: https://preprod.flipkart.com' \
--header 'Referer: https://preprod.flipkart.com/' \
--header 'Sec-Fetch-Dest: empty' \
--header 'Sec-Fetch-Mode: cors' \
--header 'Sec-Fetch-Site: cross-site' \
--header 'Sec-Fetch-Storage-Access: active' \
--header 'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1' \
--header 'content-type: application/json' \
--header 'flipkart_secure: true' \
--header 'sn: VI107E16CAAAD64460827859ABD3BAA02C.TOK35CD42BAB7AE47B1AC46E19192E9935A.1751019808627.LI' \
--header 'x-kevlar-enabled: true' \
--header 'x-merchant-id: FLIPKART' \
--header 'x-request-id: 6e6a8a3c-7c94-4d2e-84d2-797336851475' \
--header 'x-user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 FKUA/msite/0.0.1/msite/Mobile' \
--data '{
    "actionRequestContext": {
        "type": "CALM__STATUS_ACTION",
        "token": "",
        "additionalParams": {}
    }
}'