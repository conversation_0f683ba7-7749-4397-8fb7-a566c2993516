# Reduce Latency LV4 - Data Caching Strategy

## Problem Statement
Currently, LV3/LV4 transformers make 5 external API calls per page render during `fetch-bulk-v2`:
- 2x Profile Service calls (duplicate)
- 1x Bureau Data Manager (Experian)
- 1x Location Service (getUserAddress)
- 1x UPI User Service (conditional)
- Multiple ElasticSearch calls (pincode validation)

This results in ~15 API calls per user session across 3 transformers, causing high latency.

## Solution Overview
Move data collection from page render time to application creation time by:
1. Collecting all user data during `apply-now`/`submit` calls
2. Caching data in `applicationData` 
3. Reading from cache during `fetch-bulk-v2` page renders

## Implementation Plan

### 1. Enhance LeadV4DataGatheringResponse
- Add fields for profile data, bureau data, address data, UPI data
- Add pincode validation results
- Add data freshness timestamps

### 2. Update LeadServiceImpl
- Call data gathering service during `apply-now`/`submit`
- Store `LeadV4DataGatheringResponse` in `applicationData`
- Handle data collection failures gracefully

### 3. Update All Transformers
- **LV3ReviewPage1FormTransformer**: Remove direct calls to `InitialUserReviewDataSource` and `LeadPageDataSource`, read from cache
- **LV3ReviewPage2FormTransformer**: Remove redundant external API calls, use cached data
- **LeadApprovedAmountCard**: Use cached data for user information

### 4. Optimize FormWidgetDataFetcher
- Create cached data adapter to convert `LeadV4DataGatheringResponse` to required format
- Eliminate `LocationRequestHandler` calls for pincode validation
- Remove duplicate profile service calls

### 5. Implement Fallback Strategy
- Cache validation with TTL (30 minutes)
- Graceful degradation to existing API calls if cache unavailable
- Log cache hit/miss metrics

## Expected Impact

### API Call Reduction
- **Before**: 5 external calls per transformer × 3 transformers = 15 calls per session
- **After**: 5 external calls during application creation + 0 calls during page renders
- **Reduction**: ~10 API calls per user session (67% reduction)

### Latency Improvement
- **Page render time**: Reduced from ~500-800ms to ~50-100ms
- **User experience**: Faster page loads, better responsiveness
- **System load**: Reduced external service pressure

## Files to Modify

### Core Files
- `pinaka-service/src/main/java/com/flipkart/fintech/lead/model/LeadV4DataGatheringResponse.java`
- `pinaka-service/src/main/java/com/flipkart/fintech/lead/service/LeadServiceImpl.java`

### Transformer Files
- `pinaka-service/src/main/java/com/flipkart/fintech/pinaka/service/widgettransformer/lead/V3/LV3ReviewPage1FormTransformer.java`
- `pinaka-service/src/main/java/com/flipkart/fintech/pinaka/service/widgettransformer/lead/V3/LV3ReviewPage2FormTransformer.java`
- `pinaka-service/src/main/java/com/flipkart/fintech/pinaka/service/widgettransformer/lead/V3/LeadApprovedAmountCard.java`

### Utility Files
- `pinaka-service/src/main/java/com/flipkart/fintech/pinaka/service/widgettransformer/transformerUtils/FormWidgetDataFetcher.java`

## Risk Mitigation
1. **Backward Compatibility**: Keep existing API call logic as fallback
2. **Data Staleness**: Implement TTL and refresh mechanisms
3. **Memory Usage**: Monitor `applicationData` size growth
4. **Testing**: Comprehensive unit tests for cache hit/miss scenarios

## Success Metrics
- Page render latency reduction: Target 60% improvement
- External API call reduction: Target 67% reduction
- Cache hit rate: Target >95%
- Zero functional regressions
