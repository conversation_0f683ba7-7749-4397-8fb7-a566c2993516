package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.CreditScoreCategory;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CreditScoreWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.sm.pages.utils.StyleUtils;
import com.flipkart.sm.pages.utils.WidgetUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

public class CreditScoreWidgetBuilder extends WidgetBuilder<CreditScoreWidgetData> {
    private static final String WIDGET_IDENTIFIER = "CREDIT_SCORE_SCALE";
    private static final String DATE_FORMAT_OF_REPORT="EEE MMM dd HH:mm:ss zzz yyyy";
    private static final String DATE_FORMAT_TO_SHOW=" dd MMM ''yy";
    private final WidgetTemplateProvider widgetTemplateProvider;
    private final BureauDataResponse bureauDataResponse;
    private CreditScoreCategory creditScoreCategory;

    public CreditScoreWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
        this.bureauDataResponse = WidgetUtils.getBureauData(widgetInfo);
        this.creditScoreCategory  = getCreditScoreCategory();
    }

    @Override
    public CreditScoreWidgetData getWidgetData() {
        CreditScoreWidgetData creditScoreWidgetData = widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER,
                new TypeReference<CreditScoreWidgetData>() {
                });


        creditScoreWidgetData.getCreditScore().getValue().setScoreText(bureauDataResponse.getCreditScore());
        creditScoreWidgetData.getCreditScore().getValue().setScore(Integer.parseInt(bureauDataResponse.getCreditScore()));
        setDateOfReportFetch(creditScoreWidgetData);
        creditScoreWidgetData.getCreditScore().getValue().setScoreCategory(creditScoreCategory);
        StyleUtils.setColorForCreditScore(creditScoreWidgetData.getCreditScore().getMetadata(),creditScoreCategory);

        return creditScoreWidgetData;
    }
    CreditScoreCategory getCreditScoreCategory(){
        if (Objects.nonNull(bureauDataResponse.getCreditScore()) && Integer.parseInt(bureauDataResponse.getCreditScore()) >= 750) {
            return CreditScoreCategory.EXCELLENT ;
        } else if (Objects.nonNull(bureauDataResponse.getCreditScore()) && Integer.parseInt(bureauDataResponse.getCreditScore()) < 750
                && Integer.parseInt(bureauDataResponse.getCreditScore()) >= 650) {
            return CreditScoreCategory.GOOD ;
        } else {
           return CreditScoreCategory.FAIR;
        }
   }
    private void setDateOfReportFetch(CreditScoreWidgetData creditScoreWidgetData){
        creditScoreWidgetData.getCreditScore().getValue().setScore(Integer.parseInt(bureauDataResponse.getCreditScore()));
        String inputDate = bureauDataResponse.getLastUpdatedAt();
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat(DATE_FORMAT_OF_REPORT, Locale.ENGLISH);
            Date date = inputFormat.parse(inputDate);

            SimpleDateFormat outputFormat = new SimpleDateFormat(DATE_FORMAT_TO_SHOW, Locale.ENGLISH);
            String outputDate = outputFormat.format(date);
            creditScoreWidgetData.getCreditScore().getValue().setUpdatedAtString("last updated on " + outputDate);
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    @Override
    public Map<String, String> getTrackingParamsForWidget() {
        Map<String, String> tracking = super.getTrackingParamsForWidget();
        tracking.put(Constants.CREDIT_SCORE_CATEGORY, creditScoreCategory.name());
        return tracking;
    }
}
