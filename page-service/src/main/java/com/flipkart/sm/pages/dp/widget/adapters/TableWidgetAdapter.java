
package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.TableWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.sm.pages.dp.widget.builderMappers.TableWidgetBuilderMapper;

@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.TABLE_V4)
public class TableWidgetAdapter extends com.flipkart.sm.pages.dp.widget.adapters.WidgetAdapter<TableWidgetData> {

  public TableWidgetAdapter(WidgetInfo widgetInfo) {
    super(widgetInfo, TableWidgetBuilderMapper.getWidgetBuilderByType(widgetInfo));
  }
}
