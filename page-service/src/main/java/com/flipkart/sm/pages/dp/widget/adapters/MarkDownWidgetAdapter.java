package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.MarkDownWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.sm.pages.dp.widget.builderMappers.MarkDownWidgetMapper;
import lombok.CustomLog;

@CustomLog
@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.MARKDOWN_WIDGET)
public class MarkDownWidgetAdapter extends WidgetAdapter<MarkDownWidgetData> {
    public MarkDownWidgetAdapter(WidgetInfo widgetInfo) {
        super(widgetInfo, MarkDownWidgetMapper.getWidgetBuilderByType(widgetInfo));
    }
}
