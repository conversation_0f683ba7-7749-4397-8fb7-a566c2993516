package com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.sm.pages.utils.WidgetUtils;


public class OnTimePaymentsCreditScoreCardSummaryListWidgetBuilder extends WidgetBuilder<CardSummaryListWidgetData> {

    private static final String WIDGET_IDENTIFIER = "ON_TIME_PAYMENT_CREDIT_METRIC_CARD_SUMMARY_LIST_IDENTIFIER";
    private final WidgetTemplateProvider widgetTemplateProvider;
    private final BureauDataResponse bureauDataResponse;

    public OnTimePaymentsCreditScoreCardSummaryListWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
        this.bureauDataResponse = WidgetUtils.getBureauData(widgetInfo);
    }

    @Override
    public CardSummaryListWidgetData getWidgetData() {
        CardSummaryListWidgetData cardSummaryListWidgetData = widgetTemplateProvider.getWidgetTemplate(
                WIDGET_IDENTIFIER,
                new TypeReference<CardSummaryListWidgetData>() {}
        );
        cardSummaryListWidgetData.getRenderableComponents().forEach(renderableComponent -> renderableComponent.getValue().getSuperTitle().setText(
                getValueForCard(renderableComponent.getValue().getTitle().getValue().getText())
        ));

        return cardSummaryListWidgetData;
    }

    private String getValueForCard(String cardName) {
        switch (cardName) {
            case "On time payments":
                return String.valueOf(bureauDataResponse.getOnTimePayment());
            case "Delayed payments":
                return String.valueOf(bureauDataResponse.getDelayedPayment());
            default:
                return null;
        }
    }

}
