package com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;

import java.util.Arrays;

import static com.flipkart.sm.pages.dp.constants.Constants.DELIMITER_DOUBLE_UNDERSCORE;


public class CreditScoreBottomSheetCardWidgetBuilder extends WidgetBuilder<CardWidgetData> {
    private static final String WIDGET_IDENTIFIER = "CREDIT_SCORE_CARD_IDENTIFIER";
    private final WidgetTemplateProvider widgetTemplateProvider;
    private final String creditMetric;

    public CreditScoreBottomSheetCardWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
        this.creditMetric = (String) widgetInfo.getSlotParams().get(Constants.CREDIT_METRIC);
    }

    @Override
    public CardWidgetData getWidgetData() {
        return widgetTemplateProvider.getWidgetTemplate(getTemplateKey(), new TypeReference<CardWidgetData>() {});
    }

    private String getTemplateKey() {
        return String.join(DELIMITER_DOUBLE_UNDERSCORE, Arrays.asList(WIDGET_IDENTIFIER, creditMetric));
    }
}
