package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.sm.pages.dp.widget.builderMappers.SubmitButtonWidgetBuilderMapper;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.RefreshCSSubmitWidgetBuilder;
import lombok.CustomLog;

@CustomLog
@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.SUBMIT_BUTTON_WIDGET)
public class SubmitButtonWidgetAdapter extends WidgetAdapter<SubmitButtonWidgetData> {

  public SubmitButtonWidgetAdapter(WidgetInfo widgetInfo) {
    super(widgetInfo, SubmitButtonWidgetBuilderMapper.getWidgetBuilderByType(widgetInfo));

  }
}
