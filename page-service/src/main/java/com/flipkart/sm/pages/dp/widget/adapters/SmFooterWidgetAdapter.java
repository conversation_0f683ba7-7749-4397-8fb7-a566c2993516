package com.flipkart.sm.pages.dp.widget.adapters;


import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SmFooterWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.sm.pages.dp.widget.builderMappers.SmFooterWidgetMapper;
import lombok.CustomLog;

@CustomLog
@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.SM_FOOTER_WIDGET)
public class SmFooterWidgetAdapter extends WidgetAdapter<SmFooterWidgetData> {

    public SmFooterWidgetAdapter(WidgetInfo widgetInfo) {
        super(widgetInfo, SmFooterWidgetMapper.getWidgetBuilderByType(widgetInfo));
    }
}
