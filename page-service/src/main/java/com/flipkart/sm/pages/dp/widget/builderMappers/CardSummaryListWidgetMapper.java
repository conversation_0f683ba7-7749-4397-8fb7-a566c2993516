package com.flipkart.sm.pages.dp.widget.builderMappers;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets.ActiveInactiveAccountsCardSummaryListWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets.CreditUtilisationCreditScoreCardSummaryListWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets.OnTimePaymentsCreditScoreCardSummaryListWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets.CreditScoreMetricsCardListWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.NoCSCardSummaryListWidgetBuilder;

public class CardSummaryListWidgetMapper {
    public static WidgetBuilder<CardSummaryListWidgetData> getWidgetBuilderByType(WidgetInfo widgetInfo) {
        PageType context = PageType.valueOf((String) widgetInfo.getSlotParams().get(Constants.TYPE));
        switch (context) {
            case ACCOUNTS_CREDIT_SCORE_CARD_SUMMARY_LIST:
                return new ActiveInactiveAccountsCardSummaryListWidgetBuilder(widgetInfo);
            case ON_TIME_PAYMENTS_CREDIT_SCORE_CARD_SUMMARY_LIST:
                return new OnTimePaymentsCreditScoreCardSummaryListWidgetBuilder(widgetInfo);
            case CREDIT_UTILISATION_CREDIT_SCORE_CARD_SUMMARY_LIST:
                return new CreditUtilisationCreditScoreCardSummaryListWidgetBuilder(widgetInfo);
            case CREDIT_SCORE:
                return new CreditScoreMetricsCardListWidgetBuilder(widgetInfo);
            case NO_CS_AVAILABLE:
                return new NoCSCardSummaryListWidgetBuilder(widgetInfo);
            default:
                throw new IllegalStateException("Unexpected value: " + context);
        }
    }
}
