package com.flipkart.sm.pages.dp.widget.builderMappers;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.NavigationBarWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.BackNavBarWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.CashComingSoonNavBarWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.SuperCashNavBarWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.SuperCashNavBarWithBackWidgetBuilder;

public class NavBarWidgetMapper {
    public static WidgetBuilder<NavigationBarWidgetData> getWidgetBuilderByType(WidgetInfo widgetInfo) {
        PageType context = PageType.valueOf((String) widgetInfo.getSlotParams().get(Constants.TYPE));
        switch (context) {
            case SUPERCASH_NAV:
                return new SuperCashNavBarWidgetBuilder(widgetInfo);
            case BACK_BUTTON_NAV:
                return new BackNavBarWidgetBuilder(widgetInfo);
            case COMING_SOON_CASH:
                return new CashComingSoonNavBarWidgetBuilder(widgetInfo);
            case SUPERCASH_NAV_WITH_BACK:
                return new SuperCashNavBarWithBackWidgetBuilder(widgetInfo);
        }
        return null;
    }
}
