package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.rome.datatypes.response.page.v4.gamifiedOnboarding.StepperWidgetData;
import com.flipkart.sm.pages.dp.widget.builderMappers.StepperWidgetMapper;
import lombok.CustomLog;

@CustomLog
@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.STEPPER_WIDGET)
public class StepperWidgetAdapter extends WidgetAdapter<StepperWidgetData> {
    public StepperWidgetAdapter(WidgetInfo widgetInfo) {
        super(widgetInfo, StepperWidgetMapper.getWidgetBuilderByType(widgetInfo));
    }
}
