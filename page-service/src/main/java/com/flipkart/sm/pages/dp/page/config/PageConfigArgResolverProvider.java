package com.flipkart.sm.pages.dp.page.config;

import com.google.inject.Inject;
import com.google.inject.Provides;

import java.util.Map;

public class PageConfigArgResolverProvider {
    private final Map<String, IPageConfigArgumentsResolver> pageConfigArgumentsResolverMap;

    @Inject
    public PageConfigArgResolverProvider(Map<String, IPageConfigArgumentsResolver> pageConfigArgumentsResolverMap){
        this.pageConfigArgumentsResolverMap = pageConfigArgumentsResolverMap;
    }

    public IPageConfigArgumentsResolver get(String pageUrl){
        if (!pageConfigArgumentsResolverMap.containsKey(pageUrl))
            throw new RuntimeException(String.format("PageConfigArgumentsResolver not found for url %s", pageUrl));
        return pageConfigArgumentsResolverMap.get(pageUrl);
    }
}
