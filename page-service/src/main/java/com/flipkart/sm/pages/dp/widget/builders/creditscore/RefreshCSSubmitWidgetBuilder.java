package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.profile.config.ProfileServiceConfig;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.rome.datatypes.request.page.action.v1.ActionType;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.sm.pages.utils.WidgetUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;


public class RefreshCSSubmitWidgetBuilder extends WidgetBuilder<SubmitButtonWidgetData> {
    private static final String DATE_FORMATE = "EEE MMM dd HH:mm:ss zzz yyyy";
    private static final String TIME_TO_REFRESH_IN_MIN = "TIME_TO_REFRESH_IN_MIN";
    private final WidgetTemplateProvider widgetTemplateProvider;
    private static final String WIDGET_IDENTIFIER = "CS_REFRESH_BUTTON";

    private final BureauDataResponse bureauDataResponse;
    private ProfileServiceConfig profileServiceConfig;

    public RefreshCSSubmitWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
        bureauDataResponse = WidgetUtils.getBureauData(widgetInfo);
        this.profileServiceConfig = GuiceInjector.getInjector().getInstance(ProfileServiceConfig.class);

    }

    @Override
    public SubmitButtonWidgetData getWidgetData() {

        SubmitButtonWidgetData submitButtonWidgetData = widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER,
                new TypeReference<SubmitButtonWidgetData>() {
                });
        if (getMinOfReportFetch() > profileServiceConfig.getExperianConfig().getShowRefreshButton()) {
            if (bureauDataResponse.isConsentValid()) {
                submitButtonWidgetData.getSubmitButton().setConsent(null);
                submitButtonWidgetData.getSubmitButton().getButton().getAction()
                        .setType(ActionType.CALM__REFRESH_CREDIT_SCORE.toString());
                submitButtonWidgetData.getSubmitButton().getButton().getAction().setParams( new HashMap<>());
            }
            return submitButtonWidgetData;
        }

        return null;
    }

    private Integer getMinOfReportFetch() {
        String reportFetchDate = bureauDataResponse.getLastUpdatedAt();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMATE);
        LocalDateTime inputDate = LocalDateTime.parse(reportFetchDate, formatter);
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        Integer minDiff = Math.toIntExact(ChronoUnit.MINUTES.between(inputDate, now));
        return minDiff;
    }

}
