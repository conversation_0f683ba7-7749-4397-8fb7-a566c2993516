package com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.TableWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;


public class CreditScoreTableWidgetBuilder extends WidgetBuilder<TableWidgetData> {

    private static final String WIDGET_IDENTIFIER = "CREDIT_SCORE_TABLE";
    private final WidgetTemplateProvider widgetTemplateProvider;

    public CreditScoreTableWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
    }

    @Override
    public TableWidgetData getWidgetData() {
        return widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER, new TypeReference<TableWidgetData>() {});
    }

}
