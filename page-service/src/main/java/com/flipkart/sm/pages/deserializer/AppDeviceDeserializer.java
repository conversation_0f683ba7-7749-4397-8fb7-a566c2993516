package com.flipkart.sm.pages.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.flipkart.mobile.parser.vo.AppDevice;

import java.io.IOException;

public class AppDeviceDeserializer extends JsonDeserializer<AppDevice> {

  @Override
  public AppDevice deserialize(JsonParser jp, DeserializationContext deserializationContext)
      throws IOException {
    ObjectCodec oc = jp.getCodec();
    JsonNode node = oc.readTree(jp);

    final String manufacturer = node.get("manufacturer").asText();
    final String model = node.get("model").asText();
    final String id = node.get("id").asText();
    final Boolean idSupported = node.get("idSupported").asBoolean();

    return new AppDevice(manufacturer, model, id, idSupported);
  }
}
