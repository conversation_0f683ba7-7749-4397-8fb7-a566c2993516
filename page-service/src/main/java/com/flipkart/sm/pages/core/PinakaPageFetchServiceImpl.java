package com.flipkart.sm.pages.core;

import com.flipkart.aapi.multiwidget.controllers.PageFetchController;
import com.flipkart.aapi.multiwidget.models.response.AggregatedDataResponse;
import com.flipkart.aapi.multiwidget.utils.PageFetchUtils;
import com.flipkart.cse.pages.model.page.PageClientView;
import com.flipkart.dataprovider.models.controller.exceptions.PageFetchControllerException;
import com.flipkart.dataprovider.models.controller.request.PageFetchControllerRequest;
import com.flipkart.dataprovider.models.controller.request.SingleSourcePageFetchControllerRequest;
import com.flipkart.dataprovider.models.controller.response.ControllerViewResponse;
import com.flipkart.sm.pages.dp.page.config.IPageConfigArgumentsResolver;
import com.flipkart.sm.pages.dp.page.config.PageConfigArgResolverProvider;
import com.flipkart.sm.pages.utils.ControllerViewResponseUtils;
import com.flipkart.sm.pages.utils.UrlUtils;
import com.flipkart.uad.columbus.model.ColumbusException;
import com.google.inject.Inject;
import com.supermoney.pages.exceptions.PageNotConfiguredException;
import com.supermoney.pages.services.UrlPageResolverService;
import lombok.CustomLog;

import java.util.HashMap;
import java.util.Map;

@CustomLog
public class PinakaPageFetchServiceImpl implements PinakaPageFetchService {

    private final PageFetchController pageFetchController;
    private final PageConfigArgResolverProvider pageConfigArgResolverProvider;
    private final UrlPageResolverService urlPageResolverService;

    @Inject
    public PinakaPageFetchServiceImpl(
            PageFetchController pageFetchController,
            PageConfigArgResolverProvider pageConfigArgResolverProvider,
            UrlPageResolverService urlPageResolverService
    ) {
        this.pageFetchController = pageFetchController;
        this.pageConfigArgResolverProvider = pageConfigArgResolverProvider;
        this.urlPageResolverService = urlPageResolverService;
    }

    @Override
    public ControllerViewResponse fetchPage(PageFetchControllerRequest request) throws PageFetchControllerException {
        AggregatedDataResponse aggregatedDataResponse = pageFetchController.getPageData(request);
        ControllerViewResponse controllerViewResponse = pageFetchController.
                adaptToView(request, aggregatedDataResponse);
        ControllerViewResponseUtils.postBuildProcess(request, aggregatedDataResponse, controllerViewResponse);
        return controllerViewResponse;
    }

    @Override
    public ControllerViewResponse fetchPageFromSingleSourceRequest(SingleSourcePageFetchControllerRequest request) throws PageFetchControllerException {
        IPageConfigArgumentsResolver pageConfigArgumentsResolver = pageConfigArgResolverProvider.get(UrlUtils.getUrlPath(request.getPageUri()));
        Map<String, String> tags = pageConfigArgumentsResolver.getArguments(request);
        try {
            PageClientView pageClientView = urlPageResolverService.resolve(
                    request.getPageUri(),
                    request.getRequestContext().getDeviceContext().getOsName().toUpperCase(),
                    request.getRequestContext().getDeviceContext().getNormalisedAppVersion(),
                    tags
            );
            PageFetchControllerRequest transformedRequest = PageFetchUtils.transformRequest(pageClientView, request);
            transformedRequest.setPageParams(pageClientView.getPageConfig());
            AggregatedDataResponse aggregatedDataResponse = pageFetchController.getPageData(transformedRequest);
            ControllerViewResponse controllerViewResponse = pageFetchController.adaptToView(transformedRequest, aggregatedDataResponse);
            controllerViewResponse.setPageId(pageClientView.getPageId());
            ControllerViewResponseUtils.postBuildProcess(transformedRequest, aggregatedDataResponse, controllerViewResponse);
            return controllerViewResponse;
        } catch (ColumbusException | PageNotConfiguredException e) {
            log.error("Failed to resolve pageConfig for url {}, tags {}, due to {}", request.getPageUri(), tags, e.getMessage());
            throw new PageFetchControllerException(e);
        }
    }

}
