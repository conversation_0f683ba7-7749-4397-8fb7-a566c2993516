package com.flipkart.sm.pages.dp.widget.builderMappers;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CreditScoreWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.CreditScoreWidgetBuilder;

public class CreditScoreWidgetMapper {
    public static WidgetBuilder<CreditScoreWidgetData> getWidgetBuilderByType(WidgetInfo widgetInfo) {
        PageType context = PageType.valueOf((String) widgetInfo.getSlotParams().get(Constants.TYPE));
        switch (context) {
            case CREDIT_SCORE: {
                return new CreditScoreWidgetBuilder(widgetInfo);
            }
            default:
                throw new IllegalStateException("Unexpected value: " + context);
        }
    }
}
