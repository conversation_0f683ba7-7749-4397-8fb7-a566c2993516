package com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.profile.model.bureauLender;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.fintech.profile.utils.AccountTypeToLoanMapping;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.ContainerImageValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.HeaderValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.cards.RichCard;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.style.Colors;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.sm.pages.enums.CreditMetrics;
import com.flipkart.sm.pages.utils.StyleUtils;
import com.flipkart.sm.pages.utils.WidgetBuilderUtils;
import com.flipkart.sm.pages.utils.WidgetUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;

import static com.flipkart.sm.pages.dp.constants.Constants.CREDIT_METRIC;
import static com.flipkart.sm.pages.dp.constants.Constants.DELIMITER_DOUBLE_UNDERSCORE;


public class ActiveInactiveAccountsCardSummaryListWidgetBuilder extends WidgetBuilder<CardSummaryListWidgetData> {

    private static final String WIDGET_IDENTIFIER = "ACCOUNTS_CARD_IDENTIFIER";
    private final WidgetTemplateProvider widgetTemplateProvider;
    private final BureauDataResponse bureauDataResponse;
    private final String accountStatus;
    private final String creditMetric;

    public ActiveInactiveAccountsCardSummaryListWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
        this.bureauDataResponse = WidgetUtils.getBureauData(widgetInfo);
        this.accountStatus = (String) widgetInfo.getSlotParams().get(Constants.ACCOUNT_STATUS);
        this.creditMetric = (String) widgetInfo.getSlotParams().get(Constants.CREDIT_METRIC);
    }

    @Override
    public RenderableComponent<HeaderValue> getRenderableHeaders() {
        String title = "ACTIVE".equals(accountStatus) ? "Active accounts" : "Inactive accounts";
        return WidgetBuilderUtils.buildWidgetTitle(title);
    }

    @Override
    public CardSummaryListWidgetData getWidgetData() {
        CardSummaryListWidgetData cardSummaryListWidgetData = new CardSummaryListWidgetData();
        cardSummaryListWidgetData.setRenderableComponents(new ArrayList<>());



        bureauDataResponse.getLenders().forEach(bureauLender -> {
            if (filterBureauLenders(bureauLender) && ( filterOnTimePayments(bureauLender) || filterCreditUtilization(bureauLender) || filterCreditAge(bureauLender) || filterCreditMix(bureauLender))) {
                RichCard richCard = widgetTemplateProvider.getWidgetTemplate(getTemplateKey(), new TypeReference<RichCard>() {});
                richCard.getTitle().getValue().setText(bureauLender.getName());
                richCard.getDescription().getValue().setText(AccountTypeToLoanMapping.loanMap.get(String.valueOf(bureauLender.getAccountType())));
                richCard.getNext().getValue().setTitle(getCreditMetricValue(creditMetric, bureauLender));

                setImage(richCard, bureauLender);

                cardSummaryListWidgetData.getRenderableComponents().add(new RenderableComponent<>(null, richCard));
            }
        });
        return cardSummaryListWidgetData.getRenderableComponents().isEmpty() ?  null : cardSummaryListWidgetData;
    }

    private String getCreditMetricValue(String creditMetric, bureauLender bureauLender) {
        switch (CreditMetrics.valueOf(creditMetric)) {
            case ON_TIME_PAYMENTS:
                return Math.round(bureauLender.getOnTimePaymentPer()) + "%";
            case CREDIT_UTILISATION:
                return Math.round(bureauLender.getCreditUsage()) + "%";
            case CREDIT_AGE:
                return bureauLender.getCreditAge();
            case CREDIT_MIX:
                return bureauLender.getCreditMix();
            default:
                return "";
        }

    }

    private boolean filterOnTimePayments(bureauLender bureauLender){
        return CreditMetrics.ON_TIME_PAYMENTS.toString().equals(creditMetric);
    }

    private boolean filterCreditUtilization(bureauLender bureauLender){
        boolean filter = CreditMetrics.CREDIT_UTILISATION.toString().equals(creditMetric);
        return filter && String.valueOf(bureauLender.getAccountType()).equals(Constants.CREDIT_CARD_ACCOUNT_TYPE) && Objects.nonNull(bureauLender.getCreditUsage());
    }

    private boolean filterCreditAge(bureauLender bureauLender){
        return CreditMetrics.CREDIT_AGE.toString().equals(creditMetric);
    }

    private boolean filterCreditMix(bureauLender bureauLender){
        return CreditMetrics.CREDIT_MIX.toString().equals(creditMetric);
    }



    private void setImage(RichCard richCard, bureauLender bureauLender) {
        String bankImageUrl = WidgetUtils.getBankImage(bureauLender.getName());

        if (StringUtils.isEmpty(bankImageUrl)) {
            String baseImageUrl = String.valueOf(bureauLender.getAccountType()).equals(Constants.CREDIT_CARD_ACCOUNT_TYPE) ? Constants.CREDIT_CARD_IMAGE : Constants.BANK_IMAGE;
            richCard.setIcon(StyleUtils.buildImageValue(baseImageUrl, 24, 24));
            richCard.setIconBackgroundColor(Colors.White.name());
        }
        else {
            ContainerImageValue containerImageValue = (ContainerImageValue) richCard.getIcon();
            String baseImageUrl = String.valueOf(bureauLender.getAccountType()).equals(Constants.CREDIT_CARD_ACCOUNT_TYPE) ? Constants.CREDIT_CARD_IMAGE : Constants.BANK_IMAGE;
            containerImageValue.getBaseImage().setSource(baseImageUrl);
            containerImageValue.getBaseImage().setDynamicImageUrl(baseImageUrl);

            containerImageValue.getChildImage().setSource(bankImageUrl);
            containerImageValue.getChildImage().setDynamicImageUrl(bankImageUrl);
        }
    }


    private String getTemplateKey() {
        return String.join(DELIMITER_DOUBLE_UNDERSCORE, Arrays.asList(WIDGET_IDENTIFIER, accountStatus));
    }

    private boolean filterBureauLenders(bureauLender bureauLender) {
        return ("ACTIVE".equals(accountStatus) && bureauLender.isActive()) || ("INACTIVE".equals(accountStatus) && !bureauLender.isActive());
    }

}
