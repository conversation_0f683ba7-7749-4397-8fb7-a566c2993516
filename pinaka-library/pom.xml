<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.flipkart.fintech.pinaka.library</groupId>
    <artifactId>pinaka-library</artifactId>
    <version>3.4.25-SM</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <repositories>
        <repository>
            <id>fk-art-snapshot</id>
            <name>Flipkart-Artifactory</name>
            <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
        </repository>
        <repository>
            <id>fk-art-release</id>
            <name>Flipkart-Artifactory</name>
            <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
        </repository>
    </repositories>

    <distributionManagement>
        <snapshotRepository>
            <id>fk-art-snapshot</id>
            <name>libs-snapshot</name>
            <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
        </snapshotRepository>
        <repository>
            <id>fk-art-release</id>
            <name>libs-rel</name>
            <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
        </repository>
    </distributionManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.15.0</version>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.11.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.9.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-logger</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>