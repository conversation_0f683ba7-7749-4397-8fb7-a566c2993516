package com.flipkart.fintech.pinaka.library.helper;


import org.junit.Test;

import java.time.LocalDate;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class CalculatorHelperTest {


    @Test
    public void testIsWithinTimeline() {
        String reportDate1 = "20230517";
        LocalDate timelineDate1 = LocalDate.of(2023, 5, 17);
        int monthDifference1 = 3;
        assertTrue(CalculatorHelper.isWithinTimeline(reportDate1, timelineDate1, monthDifference1));
        String reportDate2 = "20230517";
        LocalDate timelineDate2 = LocalDate.of(2023, 2, 17);
        int monthDifference2 = 3;
        assertFalse(CalculatorHelper.isWithinTimeline(reportDate2, timelineDate2, monthDifference2));
        String reportDate3 = "20230517";
        LocalDate timelineDate3 = LocalDate.of(2023, 1, 16);
        int monthDifference3 = 4;
        assertFalse(CalculatorHelper.isWithinTimeline(reportDate3, timelineDate3, monthDifference3));
        String reportDate4 = "20230517";
        LocalDate timelineDate4 = LocalDate.of(2022, 11, 18);
        int monthDifference4 = 6;
        assertTrue(CalculatorHelper.isWithinTimeline(reportDate4, timelineDate4, monthDifference4));
    }
}
