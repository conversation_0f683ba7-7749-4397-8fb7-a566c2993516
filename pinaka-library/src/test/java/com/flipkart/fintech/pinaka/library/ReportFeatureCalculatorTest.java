package com.flipkart.fintech.pinaka.library;
import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.flipkart.fintech.pinaka.library.dao.CostInflationFactorDaoImpl;
import com.flipkart.fintech.pinaka.library.dao.DivisionFactorDaoImpl;
import com.flipkart.fintech.pinaka.library.parser.ExperianReportParser;
import com.flipkart.fintech.pinaka.library.parser.ParserFactory;
import com.flipkart.fintech.pinaka.library.parser.ReportParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.junit.Test;


import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.io.IOException;

import static org.junit.Assert.assertEquals;

@Slf4j
public class ReportFeatureCalculatorTest {
    private final MetricRegistry metricRegistry = new MetricRegistry();
    @Test
    public void testFeatureScoreCalculator() throws IOException {
        for (int i = 1; i <= 6; i++) {
            InputStream inputStream = getClass().getResourceAsStream( "/xmlfiles/file" + i + ".xml");
            InputStream outputStream = getClass().getResourceAsStream("/xmlfiles/output" + i + ".json");
            testFeatureScoreCalculator(inputStream, outputStream, i);
        }
    }

    private void testFeatureScoreCalculator(InputStream inputStream, InputStream outputStream, int fileNumber) throws IOException {
        String xmlContent = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        ReportContext reportContext = new ReportContext();
        reportContext.setReport(xmlContent);
        reportContext.setReportType(ReportType.EXPERIAN_REPORT);
        reportContext.setUserDetails(new UserDetails());
        reportContext.getUserDetails().setMonthlyIncome(70000);

        List<Feature> featureList = getFeatureList();
        ObjectMapper objectMapper = new ObjectMapper();
        ReportFeatureCalculator scoreCalculatorService = new ReportFeatureCalculator(getParserFactory(), new CostInflationFactorDaoImpl(objectMapper), new DivisionFactorDaoImpl(objectMapper), metricRegistry);

        Map<Feature, String> featureScores = scoreCalculatorService.calculateFeatureScore(reportContext, featureList, null).getFeatureMap();

        Map<String, String> expectedOutput = objectMapper.readValue(IOUtils.toString(outputStream, StandardCharsets.UTF_8), Map.class);

        for (Map.Entry<Feature, String> entry : featureScores.entrySet()) {
            String expectedScore = expectedOutput.get(reportContext.getReportType().getName()+"_"+entry.getKey().getName());
            try {
                assertEquals(expectedScore,entry.getValue());
            } catch (AssertionError e) {
                log.info(String.format("Test failed for file: %s, feature: %s", fileNumber, entry.getKey().getName()));
                throw e;
            }
        }
    }

    public ParserFactory getParserFactory() {
        ObjectMapper xmlMapper = new XmlMapper();
        xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        xmlMapper.configure(DeserializationFeature.FAIL_ON_MISSING_EXTERNAL_TYPE_ID_PROPERTY, false);
        ExperianReportParser experianReportParser = new ExperianReportParser(xmlMapper);
        Map<ReportType, ReportParser> reportTypeReportParserMap = new HashMap<>();
        reportTypeReportParserMap.put(ReportType.EXPERIAN_REPORT, experianReportParser);
        return new ParserFactory(reportTypeReportParserMap);
    }

    @Test
    public void testNTCCase() throws IOException {
        ReportContext reportContext = new ReportContext();
        reportContext.setReport(null);
        reportContext.setReportType(ReportType.EXPERIAN_REPORT);
        ObjectMapper objectMapper = new ObjectMapper();
        List<Feature> featureList = Collections.singletonList(Feature.NEW_TO_CREDIT);
        ReportFeatureCalculator scoreCalculatorService = new ReportFeatureCalculator(getParserFactory(), new CostInflationFactorDaoImpl(objectMapper), new DivisionFactorDaoImpl(objectMapper), null);

        Map<Feature, String> featureScores = scoreCalculatorService.calculateFeatureScore(reportContext, featureList , "consumer record not found").getFeatureMap();
        assertEquals(featureScores.get(Feature.NEW_TO_CREDIT), "0");

    }

    @Test
    public void testNTCCaseWithPanError() throws IOException {
        ReportContext reportContext = new ReportContext();
        reportContext.setReport(null);
        reportContext.setReportType(ReportType.EXPERIAN_REPORT);
        ObjectMapper objectMapper = new ObjectMapper();
        List<Feature> featureList = Collections.singletonList(Feature.NEW_TO_CREDIT);
        ReportFeatureCalculator scoreCalculatorService = new ReportFeatureCalculator(getParserFactory(), new CostInflationFactorDaoImpl(objectMapper), new DivisionFactorDaoImpl(objectMapper), null);

        Map<Feature, String> featureScores = scoreCalculatorService.calculateFeatureScore(reportContext, featureList , "Authorized to Call Masked Mobile API").getFeatureMap();
        assertEquals(featureScores.get(Feature.NEW_TO_CREDIT), "1");

    }

    private List<Feature> getFeatureList() {
        return Feature.getAllFeatures();
    }

}
