package com.flipkart.fintech.pinaka.library.helper;

import com.flipkart.fintech.pinaka.library.dao.CostInflationFactorDao;
import com.flipkart.fintech.pinaka.library.entities.*;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.Optional;

import static com.flipkart.fintech.pinaka.library.helper.EmiCalculatorHelper.getAccountHolderType;
import static com.flipkart.fintech.pinaka.library.helper.EmiCalculatorHelper.getAccountType;

@CustomLog
public class CalculatorHelper {

    public static Integer getDPD(CAISAccountHistory caisAccountHistory) {
        return Optional.ofNullable(caisAccountHistory.getDaysPastDue())
                .orElseGet(() -> {
                    String assetClassification = caisAccountHistory.getAssetClassification();
                    return assetClassification != null && Constant.HIGH_RISK_CLASSIFICATION.contains(assetClassification) ? 180 : 0;
                });
    }

    public static boolean isWithinTimeline(String reportDate, LocalDate timelineDate, int monthDifference) {
        LocalDate repDate = DateFormatter.getDateInYYYYMMDDFormat(reportDate);
        if(repDate == null || timelineDate == null) {
            return false;
        }
        return repDate.minusMonths(monthDifference).isBefore(timelineDate);
    }

    public static Boolean writtenOffCalculator(CAISAccountDetails caisAccountDetails) {
        boolean suitFiledWillfulDefaultWrittenOffStatusNotEmpty = StringUtils.isNotEmpty(caisAccountDetails.getSuitFiledWillfulDefaultWrittenOffStatus());
        boolean suitFiledWilfulDefault = Constant.SUIT_FILLED_WITH_DEFAULT_LIST.contains(caisAccountDetails.getSuitFiledWilfulDefault());
        boolean creditFacilityStatusNotEmpty = StringUtils.isNotEmpty(caisAccountDetails.getCreditFacilityStatus());
        boolean creditFacilityStatusNot99 = !Constant.CREDIT_FACILITY_STATUS_CHECKER.equals(caisAccountDetails.getCreditFacilityStatus());
        boolean settlementAmountGreaterThanZero = (StringUtils.isEmpty(caisAccountDetails.getSettlementAmount()) ? 0
                : parseInt(caisAccountDetails.getSettlementAmount())) > 0;
        boolean originalChargeOffAmountGreaterThanZero = (StringUtils.isEmpty(caisAccountDetails.getOriginalChargeOffAmount()) ? 0
                : parseInt(caisAccountDetails.getOriginalChargeOffAmount())) > 0;
        boolean writtenOffAmountTotalGreaterThanZero = (StringUtils.isEmpty(caisAccountDetails.getWrittenOffAmtTotal()) ? 0
                : parseInt(caisAccountDetails.getWrittenOffAmtTotal())) > 0;
        boolean writeOffStatusDateNotEmpty = StringUtils.isNotEmpty(caisAccountDetails.getWriteOffStatusDate());

        return suitFiledWillfulDefaultWrittenOffStatusNotEmpty ||
                suitFiledWilfulDefault ||
                (creditFacilityStatusNotEmpty && creditFacilityStatusNot99) ||
                settlementAmountGreaterThanZero ||
                originalChargeOffAmountGreaterThanZero ||
                writtenOffAmountTotalGreaterThanZero ||
                writeOffStatusDateNotEmpty;
    }

    public static int parseInt(String input) {
        try {
            return StringUtils.isEmpty(input) ? 0 : Integer.parseInt(input);
        } catch (Exception e) {
            log.info("Unable to parse integer from {}", input);
            return 0;
        }
    }

    public static Income getBureauIncome(CAISAccountDetails caisAccountDetails) {
        AccountType accountType = getAccountType(caisAccountDetails);
        AccountHolderCodeType accountHolderCodeType = getAccountHolderType(caisAccountDetails);
        int creditLimit = CalculatorHelper.parseInt(caisAccountDetails.getCreditLimitAmount());
        int loanAmount = CalculatorHelper.parseInt(caisAccountDetails.getHighestCreditOrOriginalLoanAmount());
        boolean isJointAccount = accountHolderCodeType == AccountHolderCodeType.JOINT;

        int incomeAmount = (accountType == AccountType.CC && creditLimit != 0) ? creditLimit : loanAmount;
        if (isJointAccount) {
            incomeAmount /= 2;
        }

        String incomeType = (accountType == AccountType.CC && creditLimit != 0) ? "CreditLimit" : "HCSA";

        return Income.builder()
                .amount(incomeAmount)
                .incomeType(incomeType)
                .build();
    }

    public static Double getInflationFactor(CostInflationFactorDao costInflationFactorDao, LocalDate reportDate, LocalDate openDate) {
        Double accountInflationFactor = Double.valueOf(costInflationFactorDao.getCostInflationFactor(openDate.getYear()));
        Double reportInflationFactor = Double.valueOf(costInflationFactorDao.getCostInflationFactor(reportDate.getYear()));
        return reportInflationFactor / accountInflationFactor;
    }
}
