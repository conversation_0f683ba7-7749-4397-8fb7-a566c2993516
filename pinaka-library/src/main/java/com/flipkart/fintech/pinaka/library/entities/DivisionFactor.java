package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DivisionFactor {

    @JsonProperty("AccountType")
    private String accountType;

    @JsonProperty("IncomeType")
    private String incomeType;

    @JsonProperty("MinAmount")
    private Integer minAmount;

    @JsonProperty("MaxAmount")
    private Integer maxAmount;

    @JsonProperty("DivisionFactor")
    private Double divisionFactor;

}