package com.flipkart.fintech.pinaka.library.entities;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;

@Data
@Builder
public class GetEmiRequest {
    private LocalDate reportDate;
    private Integer repaymentTenure;
    private LocalDate openedDate;
    private LocalDate lastPaymentDate;
    private AccountType accountType;
    private Integer sanctionedAmount;
    private Integer currentAmount;
    private Integer emi;
}
