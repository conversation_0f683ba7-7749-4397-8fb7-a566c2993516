package com.flipkart.fintech.pinaka.library.derived.calculator;

import com.flipkart.fintech.pinaka.library.Feature;
import com.flipkart.fintech.pinaka.library.FeatureScoreCalculator;

import java.util.Map;

import com.flipkart.fintech.pinaka.library.UserDetails;
import com.flipkart.fintech.pinaka.library.helper.CalculatorHelper;
import com.flipkart.fintech.pinaka.library.parser.ReportAnalysis;

public class AxisFlagCalculator implements FeatureScoreCalculator<Integer> {
    @Override
    public Integer calculateScore(ReportAnalysis reportAnalysis, Map<Feature, String> featureIntegerMap, UserDetails userDetails) {
        return (
                CalculatorHelper.parseInt(featureIntegerMap.get(Feature.OD_AMT_SCORE)) <= 5000 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.L6M_30DPD_SCORE)) <= 0 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.L12M_90DPD_SCORE)) <= 0 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.Wo_EVER_SCORE)) <= 0 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.PL_BL_25K_L3M_SCORE)) <= 2 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.L36M_90DPD_SCORE)) <= 0 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.EXP_SCORE)) >= 740
        ) ? 1 : 0;
    }
}
