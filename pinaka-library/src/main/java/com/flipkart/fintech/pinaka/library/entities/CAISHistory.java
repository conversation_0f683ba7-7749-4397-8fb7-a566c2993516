package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CAISHistory {
    @JacksonXmlProperty(localName = "Months")
    private String months;

    @JacksonXmlProperty(localName = "Days")
    private String days;

    @JacksonXmlProperty(localName = "Enquiry_Reason")
    private String enquiryReason;

    @JacksonXmlProperty(localName = "Account_Type")
    private String accountType;

    @JacksonXmlProperty(localName = "Industry_Code")
    private String industryCode;

    @JacksonXmlProperty(localName = "Subscriber_Code")
    private String subscriberCode;

    @JacksonXmlProperty(localName = "Enquiry_Amount")
    private String enquiryAmount;

    @JacksonXmlProperty(localName = "Enquiry_Results")
    private String enquiryResults;
}
