package com.flipkart.fintech.pinaka.library.entities;

import lombok.CustomLog;

@CustomLog
public enum ErrorEnum {
    CONSUMER_NOT_FOUND,
    PAN_PHONE_MISMATCH,
    LARGE_REPORT,
    MANDATORY_FIELD_MISSING,
    SYSTEM_ERROR,
    OTHER;

    public int getIndex() {
        return this.ordinal();
    }


    public static ErrorEnum fromString(String value) {
        if (value == null) {
            return null;
        }

        switch (value) {
            case "consumer record not found":
                return CONSUMER_NOT_FOUND;
            case "Authorized to Call Masked Mobile API":
                return PAN_PHONE_MISMATCH;
            case "Mandatory field missing":
                return MANDATORY_FIELD_MISSING;
            case "SystemError":
                return SYSTEM_ERROR;
            default:
                if (value.startsWith("SYS100009")) {
                    return LARGE_REPORT;
                }
                log.info("No proper match found for value: {}", value);
                return OTHER;
        }
    }
}
