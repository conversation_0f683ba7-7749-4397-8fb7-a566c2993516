package com.flipkart.fintech.pinaka.library;

import com.flipkart.fintech.pinaka.library.derived.calculator.AxisFlagCalculator;
import com.flipkart.fintech.pinaka.library.derived.calculator.FibeFlagCalculator;
import com.flipkart.fintech.pinaka.library.derived.calculator.MoneyViewFlagCalculator;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

import static com.flipkart.fintech.pinaka.library.helper.Constant.*;

public enum Feature {
    EXP_SCORE("score", () -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getScore(), FeatureType.ORIGINAL),

    EXP_BUREAU_VINTAGE("bureau_vintage", () ->
            (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getVintageScore(), FeatureType.ORIGINAL),

    L3M_15DPD_SCORE("L3M_15dpd", () ->
            (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getLpd(3,15, true), FeatureType.ORIGINAL),

    L6M_30DPD_SCORE("L6M_30dpd", () ->
            (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getLpd(6,30, true), FeatureType.ORIGINAL),
    L6M_MAX_SCORE("L6M_maxdpd", () ->
            (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getMaxLpd(6), FeatureType.ORIGINAL),

    L12M_90DPD_SCORE("L12M_90dpd", () ->
            (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getLpd(12,90, false), FeatureType.ORIGINAL),

    L36M_90DPD_SCORE("L36M_90dpd", () ->
            (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getLpd(36,90, false), FeatureType.ORIGINAL),

    OD_AMT_SCORE("od_amt", () ->
            (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getO2d(6), FeatureType.ORIGINAL),

    INCOME_SCORE("income_cc", () -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getIncomeCC(), FeatureType.ORIGINAL),
    ENQ_L1M_SCORE("enq_L1M", () -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getL1M(), FeatureType.ORIGINAL),
    COUNT_UNSECURE_SCORE("cnt_unsec", () -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getUnsecure(), FeatureType.ORIGINAL),

    PL_BL_25K_L3M_SCORE("PLBL_25k_L3M", () ->
            (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getPL_BL(3, 25000), FeatureType.ORIGINAL),

    Wo_3YR_SCORE("wo_3yr", () -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getWo(36), FeatureType.ORIGINAL),
    Wo_5YR_SCORE("wo_5yr", () -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getWo(60), FeatureType.ORIGINAL),
    Wo_EVER_SCORE("wo_ever",() -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getWoEver(), FeatureType.ORIGINAL),
    ACTIVE_CARD("active_cc", () -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getActiveCard(), FeatureType.ORIGINAL),
    OBLIGATION("obligation", () -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getTotalObligation(), FeatureType.ORIGINAL),
    INCOME("income", () -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getTotalIncome(), FeatureType.ORIGINAL),
    MONEYVIEW_SCORE("moneyview_flag", MoneyViewFlagCalculator::new, FeatureType.DERIVED),
    FIBE_SCORE("fibe_flag", FibeFlagCalculator::new, FeatureType.DERIVED),
    AXIS_SCORE("axispl_flag", AxisFlagCalculator::new, FeatureType.DERIVED),
    NEW_TO_CREDIT("ntc", () -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getNtc(), FeatureType.ORIGINAL),
    FOIR("foir", () -> (reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getFoir(), FeatureType.ORIGINAL),
    HL_MAX("HL_max",() -> (reportAnalysis, featureIntegerMap, userDetails) ->  reportAnalysis.getMaxLoanAmount(HL_ACCOUNT_TYPE), FeatureType.ORIGINAL),
    AL_MAX("AL_max",()-> (reportAnalysis, featureIntegerMap, userDetails) ->  reportAnalysis.getMaxLoanAmount(AL_ACCOUNT_TYPE), FeatureType.ORIGINAL),
    CC_MAX("cc_max",()-> (reportAnalysis, featureIntegerMap, userDetails) ->  reportAnalysis.getMaxCreditLimit(), FeatureType.ORIGINAL),
    PL_BL_MAX("PL_BL_max",()-> (reportAnalysis, featureIntegerMap, userDetails) ->  reportAnalysis.getMaxLoanAmount(
            PL_BL_ACCOUNT_TYPE), FeatureType.ORIGINAL),
    CD_TWL_MAX("CD_TWL_max",()-> (reportAnalysis, featureIntegerMap, userDetails) ->  reportAnalysis.getMaxLoanAmount(CD_TWL_ACCOUNT_TYPE), FeatureType.ORIGINAL),
    PL_L6M("PL_L6M",()-> (reportAnalysis, featureIntegerMap, userDetails) ->  reportAnalysis.getCountOfPL(PL_BL_ACCOUNT_TYPE,6), FeatureType.ORIGINAL),
    PL_L3M("PL_L3M",()-> (reportAnalysis, featureIntegerMap, userDetails) ->  reportAnalysis.getCountOfPL(PL_BL_ACCOUNT_TYPE,3), FeatureType.ORIGINAL),
    ENQ_L6M("enq_L6M",()-> (reportAnalysis, featureIntegerMap, userDetails) ->  reportAnalysis.getL6M(), FeatureType.ORIGINAL),
    PL_LIVE("PL_live", ()->(reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getCountExpPlLive(PL_BL_ACCOUNT_TYPE), FeatureType.ORIGINAL),
    GL_ACTIVE("GL_active", ()->(reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getCountActiveGoldLoans(GL_ACCOUNT_TYPE), FeatureType.ORIGINAL),
    GL_EVER("GL_ever", ()->(reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getTotalCountGoldLoans(GL_ACCOUNT_TYPE), FeatureType.ORIGINAL),
    FOIR_DEC("foir_dec", ()->(reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getFoirWithMinDeclaredOrExperianIncome(userDetails.getMonthlyIncome()), FeatureType.ORIGINAL),
    IDFC_ACTIVE("IDFC_active", ()->(reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getCountActiveLenderSpecificLoans(IDFC_KEYWORDS), FeatureType.ORIGINAL),
    CS_ACTIVE("CS_active", ()->(reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getCountActiveLenderSpecificLoans(CS_KEYWORDS), FeatureType.ORIGINAL),
    EXP_PFL_ACTIVE("PFL_active", ()->(reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getCountActiveLenderSpecificPLLoans(PFL_KEYWORDS,PL_BL_AXIS_PFL_ACCOUNT_TYPE), FeatureType.ORIGINAL),
    EXP_AXIS_PL_ACTIVE("AXIS_PL_active", ()->(reportAnalysis, featureIntegerMap, userDetails) -> reportAnalysis.getCountActiveLenderSpecificPLLoans(AXIS_KEYWORDS,PL_BL_AXIS_PFL_ACCOUNT_TYPE), FeatureType.ORIGINAL);



    @Getter
    private final String name;
    private final Supplier<FeatureScoreCalculator> calculatorSupplier;
    @Getter
    private final FeatureType type;

    Feature(String name, Supplier<FeatureScoreCalculator> calculatorSupplier, FeatureType type) {
        this.name = name;
        this.calculatorSupplier = calculatorSupplier;
        this.type = type;
    }

    public FeatureScoreCalculator getScoreCalculator() {
        return calculatorSupplier.get();
    }

    public static List<Feature> getAllFeatures() {
        return Arrays.asList(Feature.values());
    }

    public static List<Feature> getNtc(){
        return Collections.singletonList(NEW_TO_CREDIT);
    }
}
