package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.flipkart.fintech.pinaka.library.dao.CostInflationFactorDao;
import com.flipkart.fintech.pinaka.library.dao.DivisionFactorDao;
import com.flipkart.fintech.pinaka.library.parser.ExperianReportAnalysis;
import com.flipkart.fintech.pinaka.library.parser.ReportAnalysis;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;


@JacksonXmlRootElement(localName = "INProfileResponse")
@XmlAccessorType(XmlAccessType.FIELD)
@Getter
@Setter
public class ExperianReport implements Report {

    @JacksonXmlProperty(localName = "Header")
    private Header header;

    @JacksonXmlProperty(localName = "UserMessage")
    private UserMessage userMessage;

    @JacksonXmlProperty(localName = "CreditProfileHeader")
    private CreditProfileHeader creditProfileHeader;

    @JacksonXmlProperty(localName = "Current_Application")
    private CurrentApplication currentApplication;

    @JacksonXmlProperty(localName = "CAIS_Account")
    private CAISAccount caisAccount;

    @JacksonXmlProperty(localName = "SCORE")
    private Score score;

    @JacksonXmlProperty(localName = "CAPS")
    private CAPS caps;

    @JacksonXmlProperty(localName = "TotalCAPS_Summary")
    private TotalCAPS_Summary totalCAPSSummary;

    @Override
    public ReportAnalysis getReportAnalysis(CostInflationFactorDao costInflationFactorDao, DivisionFactorDao divisionFactorDao, String bureauErrorMessage) {
        return new ExperianReportAnalysis(this, costInflationFactorDao, divisionFactorDao, bureauErrorMessage);
    }

    @Override
    public String getReportDate() {
        if(this.getHeader() == null) {
            return null;
        }
        return this.getHeader().getReportDate();
    }

}