package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CAISAccount {
    @JacksonXmlProperty(localName = "CAIS_Summary")
    private CAISSummary caisSummary;

    @JacksonXmlProperty(localName = "CAIS_Account_DETAILS")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<CAISAccountDetails> caisAccountDetails;
}
