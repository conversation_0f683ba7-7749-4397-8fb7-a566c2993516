package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class CreditProfileHeader implements Serializable {
  @JacksonXmlProperty(localName = "Enquiry_Username")
  private String enquiryUsername;

  @JacksonXmlProperty(localName = "ReportDate")
  private String reportDate;

  @JacksonXmlProperty(localName = "ReportTime")
  private String reportTime;

  @JacksonXmlProperty(localName = "Version")
  private String version;

  @JacksonXmlProperty(localName = "ReportNumber")
  private String reportNumber;

  @JacksonXmlProperty(localName = "Subscriber")
  private String subscriber;

  @JacksonXmlProperty(localName = "Subscriber_Name")
  private String subscriberName;
}