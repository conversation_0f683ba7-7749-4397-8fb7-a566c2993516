package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CAPS_Summary implements Serializable {
    @JacksonXmlProperty(localName = "CAPSLast7Days")
    private int capsLast7Days;

    @JacksonXmlProperty(localName = "CAPSLast30Days")
    private int capsLast30Days;

    @JacksonXmlProperty(localName = "CAPSLast90Days")
    private int capsLast90Days;

    @JacksonXmlProperty(localName = "CAPSLast180Days")
    private int capsLast180Days;
}
