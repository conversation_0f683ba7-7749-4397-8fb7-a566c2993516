package com.flipkart.fintech.pinaka.library.helper;

import com.flipkart.fintech.pinaka.library.entities.AccountType;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class Constant {
    public static final List<String> HIGH_RISK_CLASSIFICATION = Arrays.asList("B", "D", "M", "L");
    public static final List<String> SUIT_FILLED_WITH_DEFAULT_LIST = Arrays.asList("01", "02", "03");
    public static final String CREDIT_FACILITY_STATUS_CHECKER = "99";
    public static final List<Integer> UNSECURED_ACCOUNT = Arrays.asList(1, 2, 3, 5, 10, 13, 45, 46, 69);
    public static final List<Integer> ACTIVE_CREDIT_ACCOUNT = Collections.singletonList(10);
    public static final List<AccountType> SEC_ACCOUNT_TYPE = Arrays.asList(AccountType.AL,AccountType.TW,AccountType.HL,AccountType.LAP,AccountType.UCL);
    public static final List<AccountType> UNSEC_ACCOUNT_TYPE = Arrays.asList(AccountType.BL,AccountType.PL,AccountType.CD,AccountType.OTHERS,AccountType.EL,AccountType.OD);
    public static final List<AccountType> CC_ACCOUNT_TYPE = Arrays.asList(AccountType.CC);
    public static final List<Integer> PL_BL_ACCOUNT_TYPE = Arrays.asList(5,51,52,53,54,55,56,57,58,61,69);
    public static final List<Integer> PL_BL_AXIS_PFL_ACCOUNT_TYPE = Arrays.asList(5,41,45,69);
    public static final List<Integer> CD_TWL_ACCOUNT_TYPE = Arrays.asList(13,6);
    public static final List<Integer> AL_ACCOUNT_TYPE = Arrays.asList(1,32);
    public static final List<Integer> HL_ACCOUNT_TYPE = Arrays.asList(2);
    public static final List<Integer> AUTHORIZED_GUARANTOR_ACCOUNT_HOLDER = Arrays.asList(3,7);
    public static final List<Integer> AUTHORIZED_ACCOUNT_HOLDER = Arrays.asList(3);
    public static final List<Integer> JOINT_ACCOUNT_HOLDER  = Arrays.asList(2);
    public static final List<Integer> GL_ACCOUNT_TYPE = Arrays.asList(7,70);
    public static final List<String> IDFC_KEYWORDS = Collections.singletonList("IDFC");
    public static final List<String> CS_KEYWORDS = Arrays.asList("Kisetsu","Kiset");
    public static final List<String> AXIS_KEYWORDS = Arrays.asList("axis","Axis","AXIS");
    public static final List<String> PFL_KEYWORDS = Arrays.asList("poonawalla","Poonawalla","POONAWALLA","PFL");
    public static class MetricsConstant {
        public static final String INSIGHTSEXCEPTION  = "insightsException";
    }
}
