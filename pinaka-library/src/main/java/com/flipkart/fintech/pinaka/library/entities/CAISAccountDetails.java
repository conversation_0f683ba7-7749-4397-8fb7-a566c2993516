package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CAISAccountDetails {
    @JacksonXmlProperty(localName = "Identification_Number")
    private String identificationNumber;

    @JacksonXmlProperty(localName = "Subscriber_Name")
    private String subscriberName;

    @JacksonXmlProperty(localName = "Account_Number")
    private String accountNumber;

    @JacksonXmlProperty(localName = "Portfolio_Type")
    private String portfolioType;

    @JacksonXmlProperty(localName = "Account_Type")
    private String accountType;

    @JacksonXmlProperty(localName = "Open_Date")
    private String openDate;

    @JacksonXmlProperty(localName = "Credit_Limit_Amount")
    private String creditLimitAmount;

    @JacksonXmlProperty(localName = "Highest_Credit_or_Original_Loan_Amount")
    private String highestCreditOrOriginalLoanAmount;

    @JacksonXmlProperty(localName = "Terms_Duration")
    private String termsDuration;

    @JacksonXmlProperty(localName = "Terms_Frequency")
    private String termsFrequency;

    @JacksonXmlProperty(localName = "Scheduled_Monthly_Payment_Amount")
    private String scheduledMonthlyPaymentAmount;

    @JacksonXmlProperty(localName = "Account_Status")
    private String accountStatus;

    @JacksonXmlProperty(localName = "Payment_Rating")
    private String paymentRating;

    @JacksonXmlProperty(localName = "Payment_History_Profile")
    private String paymentHistoryProfile;

    @JacksonXmlProperty(localName = "Special_Comment")
    private String specialComment;

    @JacksonXmlProperty(localName = "Current_Balance")
    private String currentBalance;

    @JacksonXmlProperty(localName = "Amount_Past_Due")
    private String amountPastDue;

    @JacksonXmlProperty(localName = "Original_Charge_Off_Amount")
    private String originalChargeOffAmount;

    @JacksonXmlProperty(localName = "Date_Reported")
    private String dateReported;

    @JacksonXmlProperty(localName = "Date_of_First_Delinquency")
    private String dateOfFirstDelinquency;

    @JacksonXmlProperty(localName = "Date_Closed")
    private String dateClosed;

    @JacksonXmlProperty(localName = "Date_of_Last_Payment")
    private String dateOfLastPayment;

    @JacksonXmlProperty(localName = "SuitFiledWillfulDefaultWrittenOffStatus")
    private String suitFiledWillfulDefaultWrittenOffStatus;

    @JacksonXmlProperty(localName = "SuitFiled_WilfulDefault")
    private String suitFiledWilfulDefault;

    @JacksonXmlProperty(localName = "Written_off_Settled_Status")
    private String writtenOffSettledStatus;

    @JacksonXmlProperty(localName = "Value_of_Credits_Last_Month")
    private String valueOfCreditsLastMonth;

    @JacksonXmlProperty(localName = "Occupation_Code")
    private String occupationCode;

    @JacksonXmlProperty(localName = "Settlement_Amount")
    private String settlementAmount;

    @JacksonXmlProperty(localName = "Value_of_Collateral")
    private String valueOfCollateral;

    @JacksonXmlProperty(localName = "Credit_Facility_Status")
    private String creditFacilityStatus;

    @JacksonXmlProperty(localName = "Type_of_Collateral")
    private String typeOfCollateral;

    @JacksonXmlProperty(localName = "Written_Off_Amt_Total")
    private String writtenOffAmtTotal;

    @JacksonXmlProperty(localName = "Written_Off_Amt_Principal")
    private String writtenOffAmtPrincipal;

    @JacksonXmlProperty(localName = "Rate_of_Interest")
    private String rateOfInterest;

    @JacksonXmlProperty(localName = "Repayment_Tenure")
    private String repaymentTenure;

    @JacksonXmlProperty(localName = "Promotional_Rate_Flag")
    private String promotionalRateFlag;

    @JacksonXmlProperty(localName = "Income")
    private String income;

    @JacksonXmlProperty(localName = "Income_Indicator")
    private String incomeIndicator;

    @JacksonXmlProperty(localName = "Income_Frequency_Indicator")
    private String incomeFrequencyIndicator;

    @JacksonXmlProperty(localName = "DefaultStatusDate")
    private String defaultStatusDate;

    @JacksonXmlProperty(localName = "LitigationStatusDate")
    private String litigationStatusDate;

    @JacksonXmlProperty(localName = "WriteOffStatusDate")
    private String writeOffStatusDate;

    @JacksonXmlProperty(localName = "DateOfAddition")
    private String dateOfAddition;

    @JacksonXmlProperty(localName = "CurrencyCode")
    private String currencyCode;

    @JacksonXmlProperty(localName = "Subscriber_comments")
    private String subscriberComments;

    @JacksonXmlProperty(localName = "Consumer_comments")
    private String consumerComments;

    @JacksonXmlProperty(localName = "AccountHoldertypeCode")
    private String accountHoldertypeCode;

    @JacksonXmlProperty(localName = "CAIS_Account_History")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<CAISAccountHistory> caisAccountHistory;

    @JacksonXmlProperty(localName = "CAIS_Holder_Details")
    private CAISHolderDetails caisHolderDetails;

    @JacksonXmlProperty(localName = "CAIS_Holder_Address_Details")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<CAISHolderAddressDetails> caisHolderAddressDetails;

    @JacksonXmlProperty(localName = "CAIS_Holder_Phone_Details")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<CAISHolderPhoneDetails> caisHolderPhoneDetails;


}