package com.flipkart.fintech.pinaka.library.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.library.entities.AccountType;
import com.flipkart.fintech.pinaka.library.entities.DivisionFactor;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.CustomLog;

import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;

@CustomLog
public class DivisionFactorDaoImpl implements DivisionFactorDao {

    private final List<DivisionFactor> factors;
    private final Double DEFAULT_DIVISION_FACTOR = 55.0;

    @Inject
    public DivisionFactorDaoImpl(@Named("ObjectMapper") ObjectMapper objectMapper) {
        try {
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("division_factor.json");
            this.factors  = objectMapper.readValue(inputStream, new TypeReference<List<DivisionFactor>>() {});
        } catch (Exception e) {
            log.error("error in getting division factor from file", e.getMessage());
            throw new RuntimeException(e);
        }
    }


    @Override
    public Double getDivisionFactor(AccountType accountType, Integer income, String incomeType) {
        if (factors == null) {
            throw new IllegalStateException("Factors are not initialized");
        }
        List<DivisionFactor> matchingFilters = factors.stream().filter(
                devisionFactors -> devisionFactors.getAccountType().equals(accountType.name()) && devisionFactors.getIncomeType().equals(incomeType)
        ).collect(Collectors.toList());

        if(matchingFilters.isEmpty()) {
            matchingFilters = factors.stream().filter(
                    devisionFactors -> devisionFactors.getAccountType().equals("Others") && devisionFactors.getIncomeType().equals(incomeType)
            ).collect(Collectors.toList());
        }

        for (DivisionFactor factor : matchingFilters) {
            if (income >= factor.getMinAmount() && income <= factor.getMaxAmount()) {
                return factor.getDivisionFactor();
            }
        }

        return DEFAULT_DIVISION_FACTOR;
    }
}
