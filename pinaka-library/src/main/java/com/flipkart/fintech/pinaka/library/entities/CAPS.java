package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CAPS implements Serializable {
    @JacksonXmlProperty(localName = "CAPS_Summary")
    private CAPS_Summary capsSummary;

    @JacksonXmlProperty(localName = "CAPS_Application_Details")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<CAPS_Application_Details> capsApplicationDetails;
}
