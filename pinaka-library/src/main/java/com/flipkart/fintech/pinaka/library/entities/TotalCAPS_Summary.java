package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class TotalCAPS_Summary implements Serializable {
    @JacksonXmlProperty(localName = "TotalCAPSLast7Days")
    private int totalCAPSLast7Days;

    @JacksonXmlProperty(localName = "TotalCAPSLast30Days")
    private int totalCAPSLast30Days;

    @JacksonXmlProperty(localName = "TotalCAPSLast90Days")
    private int totalCAPSLast90Days;

    @JacksonXmlProperty(localName = "TotalCAPSLast180Days")
    private int totalCAPSLast180Days;
}