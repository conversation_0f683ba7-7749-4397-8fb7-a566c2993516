package com.flipkart.fintech.pinaka.library.entities;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


public enum AccountType {
    CC(Collections.singletonList(10)),
    CD(Collections.singletonList(6)),
    GL(Arrays.asList(7, 70)),
    AL(Collections.singletonList(46)),
    TW(Collections.singletonList(13)),
    OD(Collections.singletonList(12)),
    PL(Arrays.asList(5, 45, 69, 41)),
    HL(Arrays.asList(2, 42)),
    LAP(Collections.singletonList(3)),
    EL(Arrays.asList(8, 47)),
    LABD(Collections.singletonList(15)),
    LAS(Collections.singletonList(4)),
    LTP(Collections.singletonList(9)),
    CVL(Collections.singletonList(17)),
    UCL(Collections.singletonList(32)),
    BL(Arrays.asList(40, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61)),
    CE(Collections.singletonList(33)),
    OTHERS(new ArrayList<>());

    private List<Integer> accountTypeList;

    AccountType(List<Integer> accountTypeList) {
        this.accountTypeList = accountTypeList;
    }

    public static AccountType getAccountType(Integer accountType) {
        for (AccountType type : AccountType.values()) {
            if (type.accountTypeList.contains(accountType)) {
                return type;
            }
        }
        return AccountType.OTHERS;
    }
}

