package com.flipkart.fintech.pinaka.library.helper;

import com.flipkart.fintech.pinaka.library.entities.CAISAccountHistory;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.Instant;

@CustomLog
public class DateFormatter {

    public static LocalDate getDateInYYYYMMDDFormat(String date) {
        if (StringUtils.isEmpty(date)) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            return LocalDate.parse(date, formatter);
        } catch (Exception e) {
            log.error("getting error while converting date {} to localdate {}", date, e.getMessage());
        }
        return null;
    }

    public static LocalDate getLocalDateFromCaisHistory(CAISAccountHistory caisAccountHistory){
        if(caisAccountHistory.getYear() == null || caisAccountHistory.getMonth() == null) {
            return null;
        }
        return LocalDate.of(caisAccountHistory.getYear(), caisAccountHistory.getMonth(), 1);
    }
}
