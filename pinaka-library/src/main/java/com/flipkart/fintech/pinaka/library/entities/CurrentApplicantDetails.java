package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class CurrentApplicantDetails implements Serializable {

    @JacksonXmlProperty(localName = "IncomeTaxPan")
    private String incomeTaxPan;

    @JacksonXmlProperty(localName = "EMailId")
    private String emailId;
}
