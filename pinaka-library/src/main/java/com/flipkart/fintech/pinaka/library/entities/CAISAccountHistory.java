package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.*;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CAISAccountHistory {
    @JacksonXmlProperty(localName = "Year")
    private Integer year;

    @JacksonXmlProperty(localName = "Month")
    private Integer month;

    @JacksonXmlProperty(localName = "Days_Past_Due")
    private Integer daysPastDue;

    @JacksonXmlProperty(localName = "Asset_Classification")
    private String assetClassification;
}