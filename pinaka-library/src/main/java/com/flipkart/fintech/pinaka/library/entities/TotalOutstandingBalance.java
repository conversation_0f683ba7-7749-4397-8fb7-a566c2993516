package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class TotalOutstandingBalance {
    @JacksonXmlProperty(localName = "Outstanding_Balance_Secured")
    private int outstandingBalanceSecured;

    @JacksonXmlProperty(localName = "Outstanding_Balance_Secured_Percentage")
    private int outstandingBalanceSecuredPercentage;

    @JacksonXmlProperty(localName = "Outstanding_Balance_UnSecured")
    private int outstandingBalanceUnsecured;

    @JacksonXmlProperty(localName = "Outstanding_Balance_UnSecured_Percentage")
    private int outstandingBalanceUnsecuredPercentage;

    @JacksonXmlProperty(localName = "Outstanding_Balance_All")
    private int outstandingBalanceAll;
}
