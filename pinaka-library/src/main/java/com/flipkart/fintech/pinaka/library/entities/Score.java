package com.flipkart.fintech.pinaka.library.entities;


import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class Score implements Serializable {
    @JacksonXmlProperty(localName = "BureauScore")
    private int bureauScore;

    @JacksonXmlProperty(localName = "BureauScoreConfidLevel")
    private String bureauScoreConfidLevel;
}