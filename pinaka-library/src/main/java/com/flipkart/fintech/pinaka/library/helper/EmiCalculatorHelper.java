package com.flipkart.fintech.pinaka.library.helper;

import com.flipkart.fintech.pinaka.library.entities.AccountHolderCodeType;
import com.flipkart.fintech.pinaka.library.entities.AccountType;
import com.flipkart.fintech.pinaka.library.entities.CAISAccountDetails;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.Objects;

public class EmiCalculatorHelper {
    public static AccountHolderCodeType getAccountHolderType(CAISAccountDetails caisAccountDetails) {
        return (Objects.equals(caisAccountDetails.getAccountHoldertypeCode(), "2"))
                ? AccountHolderCodeType.JOINT : AccountHolderCodeType.DEFAULT;
    }

    public static Integer getSanctionedAmount(AccountHolderCodeType accountHolderCodeType, CAISAccountDetails caisAccountDetails) {
        Integer sanctionedAmount = CalculatorHelper.parseInt(caisAccountDetails.getHighestCreditOrOriginalLoanAmount());
        return accountHolderCodeType == AccountHolderCodeType.DEFAULT ? sanctionedAmount : (sanctionedAmount / 2);
    }

    public static Integer getCurrentBalanceAmount(AccountHolderCodeType accountHolderCodeType, CAISAccountDetails caisAccountDetails) {
        Integer creditLimitAmount = CalculatorHelper.parseInt(caisAccountDetails.getCurrentBalance());
        return accountHolderCodeType == AccountHolderCodeType.DEFAULT ? creditLimitAmount : (creditLimitAmount / 2);
    }


    public static AccountType getAccountType(CAISAccountDetails caisAccountDetails) {
        return AccountType.getAccountType(CalculatorHelper.parseInt(caisAccountDetails.getAccountType()));
    }

    public static Boolean getLiveFlag(CAISAccountDetails caisAccountDetails, LocalDate reportDate) {
        AccountType accountType = getAccountType(caisAccountDetails);
        AccountHolderCodeType accountHolderCodeType = getAccountHolderType(caisAccountDetails);
        String  lastPaymentDateString = StringUtils.isEmpty(caisAccountDetails.getDateOfLastPayment()) ? caisAccountDetails.getDateReported() : caisAccountDetails.getDateOfLastPayment();
        LocalDate lastPaymentDate = DateFormatter.getDateInYYYYMMDDFormat(lastPaymentDateString);
        boolean isWithinTimeLine = Objects.isNull(lastPaymentDate) || reportDate.minusMonths(6).isBefore(lastPaymentDate);
        boolean hasSufficientBalance = getCurrentBalanceAmount(accountHolderCodeType, caisAccountDetails) >= 100;
        boolean isClosed = !StringUtils.isEmpty(caisAccountDetails.getDateClosed());

        if (accountType == AccountType.CC) {
            return !isClosed && isWithinTimeLine;
        } else {
            return !isClosed && hasSufficientBalance && isWithinTimeLine;
        }
    }

    public static Double getEMIFromInterest(Integer sanctionedAmount, Double interestRate, Double tenure) {
        return (sanctionedAmount * interestRate * Math.pow((1 + interestRate), tenure))
                / (Math.pow((1 + interestRate), tenure) - 1);
    }
}
