package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class CurrentOtherDetails {

    @JacksonXmlProperty(localName = "Employment_Status")
    private String employmentStatus;
}
