package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class Header implements Serializable {
    @JacksonXmlProperty(localName = "SystemCode")
    private String systemCode;

    @JacksonXmlProperty(localName = "MessageText")
    private String messageText;

    @JacksonXmlProperty(localName = "ReportDate")
    private String reportDate;

    @JacksonXmlProperty(localName = "ReportTime")
    private String reportTime;
}