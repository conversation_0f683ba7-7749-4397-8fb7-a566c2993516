package com.flipkart.fintech.pinaka.library;

import com.flipkart.fintech.pinaka.library.entities.ExperianReport;
import com.flipkart.fintech.pinaka.library.dao.CostInflationFactorDao;
import com.flipkart.fintech.pinaka.library.dao.DivisionFactorDao;
import com.codahale.metrics.MetricRegistry;
import com.flipkart.fintech.pinaka.library.entities.Report;
import com.flipkart.fintech.pinaka.library.helper.Constant;
import com.flipkart.fintech.pinaka.library.parser.ParserFactory;
import com.flipkart.fintech.pinaka.library.parser.ReportAnalysis;
import com.flipkart.fintech.pinaka.library.parser.ReportParser;
import com.google.inject.Inject;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@CustomLog
public class ReportFeatureCalculator {

    private final ParserFactory parserFactory;
    private final CostInflationFactorDao costInflationFactorDao;
    private final DivisionFactorDao divisionFactorDao;
    private final MetricRegistry metricRegistry;

    @Inject
    public ReportFeatureCalculator(ParserFactory parserFactory, CostInflationFactorDao costInflationFactorDao,
                                   DivisionFactorDao divisionFactorDao, MetricRegistry metricRegistry) {
        this.parserFactory = parserFactory;
        this.costInflationFactorDao = costInflationFactorDao;
        this.divisionFactorDao = divisionFactorDao;
        this.metricRegistry = metricRegistry;
    }

    public FeatureReport calculateFeatureScore(ReportContext reportContext, List<Feature> featureList, String bureauErrorMessage) {
        try {
            ReportParser parser = parserFactory.getExperianReportParser(reportContext.getReportType());
            Report report  = parser.parseReport(reportContext.getReport());

            ReportAnalysis reportAnalysis = report.getReportAnalysis(costInflationFactorDao, divisionFactorDao, bureauErrorMessage);
            Map<Feature, String> responseMap = new ConcurrentHashMap<>();

            featureList.stream().forEach(feature -> {
                if (feature.getType() == FeatureType.ORIGINAL) {
                    FeatureScoreCalculator scoreCalculator = feature.getScoreCalculator();
                    Object score = scoreCalculator.calculateScore(reportAnalysis, null, reportContext.getUserDetails());
                    if (Objects.nonNull(score)) {
                        responseMap.put(feature, score.toString());
                    }
                }
            });

            featureList.stream().forEach(feature -> {
                if (feature.getType() == FeatureType.DERIVED) {
                    FeatureScoreCalculator scoreCalculator = feature.getScoreCalculator();
                    Object score = scoreCalculator.calculateScore(reportAnalysis, responseMap, reportContext.getUserDetails());
                    responseMap.put(feature, score.toString());
                }
            });
            FeatureReport featureReport = new FeatureReport();
            featureReport.setFeatureMap(responseMap);
            if (StringUtils.isEmpty(report.getReportDate())) {
                LocalDate date = LocalDate.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                String formattedDate = date.format(formatter);
                featureReport.setReportDateInYyyyMmDd(formattedDate);
            } else {
                featureReport.setReportDateInYyyyMmDd(report.getReportDate());
            }
            try {
                featureReport.setReport(reportContext.getReport());
            } catch (Exception e){
                log.error("ReportFeatureCalculator:: unable to set experian report", e.getMessage());
            }

            return featureReport;
        } catch (Exception e) {
            log.error("getting exception in feature calculation for account Id {}", reportContext.getUserDetails().getAccountId(), e);
            metricRegistry.meter(MetricRegistry.name(ReportFeatureCalculator.class, Constant.MetricsConstant.INSIGHTSEXCEPTION)).mark();
        }
        return new FeatureReport();
    }

    public ExperianReport getParsedReport(ReportContext reportContext){
        try {
            ReportParser parser = parserFactory.getExperianReportParser(reportContext.getReportType());
            return (ExperianReport) parser.parseReport(reportContext.getReport());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

