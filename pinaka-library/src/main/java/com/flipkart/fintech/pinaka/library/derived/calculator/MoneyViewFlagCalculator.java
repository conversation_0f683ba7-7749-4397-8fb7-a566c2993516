package com.flipkart.fintech.pinaka.library.derived.calculator;

import com.flipkart.fintech.pinaka.library.Feature;
import com.flipkart.fintech.pinaka.library.FeatureScoreCalculator;
import com.flipkart.fintech.pinaka.library.UserDetails;
import com.flipkart.fintech.pinaka.library.helper.CalculatorHelper;
import com.flipkart.fintech.pinaka.library.parser.ReportAnalysis;

import java.util.Map;


public class MoneyViewFlagCalculator implements FeatureScoreCalculator<Integer> {
    @Override
    public Integer calculateScore(ReportAnalysis reportAnalysis, Map<Feature, String> featureIntegerMap, UserDetails userDetails) {
        return (
                CalculatorHelper.parseInt(featureIntegerMap.get(Feature.EXP_BUREAU_VINTAGE)) > 24 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.L3M_15DPD_SCORE)) <= 0 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.L6M_30DPD_SCORE)) <= 0 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.L12M_90DPD_SCORE)) <= 0 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.ENQ_L1M_SCORE)) <= 6 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.Wo_3YR_SCORE)) <= 0 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.EXP_SCORE)) >= 730 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.COUNT_UNSECURE_SCORE)) > 0
        ) ? 1 : 0;
    }

}
