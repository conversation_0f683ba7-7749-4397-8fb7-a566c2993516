package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CreditAccount {
    @JacksonXmlProperty(localName = "CreditAccountTotal")
    private int creditAccountTotal;

    @JacksonXmlProperty(localName = "CreditAccountActive")
    private int creditAccountActive;

    @JacksonXmlProperty(localName = "CreditAccountDefault")
    private int creditAccountDefault;

    @JacksonXmlProperty(localName = "CreditAccountClosed")
    private int creditAccountClosed;

    @JacksonXmlProperty(localName = "CADSuitFiledCurrentBalance")
    private int CADSuitFiledCurrentBalance;
}