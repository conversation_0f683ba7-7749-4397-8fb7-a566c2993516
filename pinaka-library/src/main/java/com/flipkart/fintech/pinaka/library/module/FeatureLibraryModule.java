package com.flipkart.fintech.pinaka.library.module;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.flipkart.fintech.pinaka.library.ReportFeatureCalculator;
import com.flipkart.fintech.pinaka.library.ReportType;
import com.flipkart.fintech.pinaka.library.dao.CostInflationFactorDao;
import com.flipkart.fintech.pinaka.library.dao.CostInflationFactorDaoImpl;
import com.flipkart.fintech.pinaka.library.dao.DivisionFactorDao;
import com.flipkart.fintech.pinaka.library.dao.DivisionFactorDaoImpl;
import com.flipkart.fintech.pinaka.library.parser.ExperianReportParser;
import com.flipkart.fintech.pinaka.library.parser.ParserFactory;
import com.flipkart.fintech.pinaka.library.parser.ReportParser;
import com.google.inject.AbstractModule;
import com.google.inject.Inject;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;

import java.util.HashMap;
import java.util.Map;

public class FeatureLibraryModule extends AbstractModule {

    private final MetricRegistry metricRegistry;

    public FeatureLibraryModule(MetricRegistry metricRegistry) {
        this.metricRegistry = metricRegistry;
    }


    @Override
    protected void configure() {
        bind(CostInflationFactorDao.class).to(CostInflationFactorDaoImpl.class);
        bind(DivisionFactorDao.class).to(DivisionFactorDaoImpl.class);
    }

    @Provides
    @Singleton
    @Named("xmlMapper")
    public ObjectMapper provideXmlMapper(){
        ObjectMapper xmlMapper = new XmlMapper();
        xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        xmlMapper.configure(DeserializationFeature.FAIL_ON_MISSING_EXTERNAL_TYPE_ID_PROPERTY, false);
        return xmlMapper;
    }

    @Provides
    @Singleton
    @Named("ObjectMapper")
    public ObjectMapper provideObjectMapper(){
        return new ObjectMapper();
    }

    @Provides
    @Singleton
    public Map<ReportType, ReportParser> provideReportParserMap(@Named("xmlMapper") ObjectMapper xmlMapper) {
        ExperianReportParser experianReportParser = new ExperianReportParser(xmlMapper);
        Map<ReportType, ReportParser> reportTypeReportParserMap= new HashMap<>();
        reportTypeReportParserMap.put(ReportType.EXPERIAN_REPORT, experianReportParser);
        return reportTypeReportParserMap;
    }

    @Provides
    @Singleton
    public MetricRegistry provideMetricRegistry() {
        return metricRegistry;
    }

    @Provides
    @Singleton
    @Inject
    public ReportFeatureCalculator provideReportFeatureCalculator(ParserFactory parserFactory, CostInflationFactorDao costInflationFactorDao, DivisionFactorDao divisionFactorDao) {
        return new ReportFeatureCalculator(parserFactory, costInflationFactorDao, divisionFactorDao, metricRegistry);
    }


}
