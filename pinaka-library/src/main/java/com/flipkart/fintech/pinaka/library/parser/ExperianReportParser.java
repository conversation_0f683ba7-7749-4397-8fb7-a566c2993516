package com.flipkart.fintech.pinaka.library.parser;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.library.entities.ExperianReport;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Named;
import java.util.Objects;

@Slf4j
public class ExperianReportParser implements ReportParser {

    private final ObjectMapper xmlMapper;

    @Inject
    public ExperianReportParser(@Named("xmlMapper") ObjectMapper xmlMapper) {
        this.xmlMapper = xmlMapper;
    }

    @Override
    public ExperianReport parseReport(String report) throws Exception {
        try {
            if (Objects.isNull(report)){
                return new ExperianReport();
            }
            return xmlMapper.readValue(report, ExperianReport.class);
        } catch (Exception e) {
            log.error("error in report parsing report {} error {}", report, e.getMessage());
            throw e;
        }
    }
}
