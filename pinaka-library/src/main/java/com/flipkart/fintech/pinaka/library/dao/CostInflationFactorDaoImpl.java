package com.flipkart.fintech.pinaka.library.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.library.entities.InflationFactor;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.CustomLog;

import java.io.InputStream;
import java.util.List;

@CustomLog
public class CostInflationFactorDaoImpl implements CostInflationFactorDao {

    private final List<InflationFactor> inflationFactors;
    private final Integer DEFAULT_INFLATION_FACTOR = 100;

    @Inject
    public CostInflationFactorDaoImpl(@Named("ObjectMapper")ObjectMapper objectMapper) {
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("inflation_factor.json");
            this.inflationFactors  = objectMapper.readValue(inputStream, new TypeReference<List<InflationFactor>>() {});
        } catch (Exception e) {
            log.error("error in getting inflation factor from file", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    @Override
    public Integer getCostInflationFactor(Integer year) {
        return inflationFactors.stream()
                .filter(factor -> factor.getFinancialYear().equals(year.toString()))
                .findFirst()
                .map(InflationFactor::getCostInflationIndex)
                .orElse(DEFAULT_INFLATION_FACTOR);
    }
}
