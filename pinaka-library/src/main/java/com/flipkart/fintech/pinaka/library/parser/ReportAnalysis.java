package com.flipkart.fintech.pinaka.library.parser;

import java.util.List;

public interface ReportAnalysis {
    Integer getLpd(int month, int days, boolean isActive);
    Integer getMaxLpd(int month);
    Integer getO2d(int month);
    Integer getIncomeCC();
    Integer getUnsecure();
    Integer getVintageScore();
    Integer getL1M();
    Integer getPL_BL(int month, int sanctionedAmount);
    Integer getScore();
    Integer getWo(int month);
    Integer getWoEver();
    Integer getActiveCard();
    Integer getTotalObligation();
    Integer getTotalIncome();
    Integer getNtc();
    Double getFoir();
    Integer getMaxLoanAmount(List<Integer> accountTypes);
    Integer getMaxCreditLimit();
    Integer getCountOfPL(List<Integer> accountTypes, int months);
    Integer getL6M();
    Integer getCountExpPlLive(List<Integer> accountTypes);
    Integer getCountActiveGoldLoans(List<Integer> accountTypes);
    Integer getTotalCountGoldLoans(List<Integer> accountTypes);
    Double getFoirWithMinDeclaredOrExperianIncome(Integer monthlyIncome);
    Integer getCountActiveLenderSpecificLoans(List<String> lenderKeywords);
    Integer getCountActiveLenderSpecificPLLoans(List<String> lenderKeywords,List<Integer> accountTypes);
}
