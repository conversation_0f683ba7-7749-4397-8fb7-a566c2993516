package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class CurrentApplicationDetails implements Serializable {
  @JacksonXmlProperty(localName = "Enquiry_Reason")
  private String enquiryReason;

  @JacksonXmlProperty(localName = "Finance_Purpose")
  private String finalPurpose;

  @JacksonXmlProperty(localName = "Amount_Financed")
  private String amountFinanced;

  @JacksonXmlProperty(localName = "Duration_Of_Agreement")
  private String durationOfAgreement;

  @JacksonXmlProperty(localName = "Current_Applicant_Details")
  private CurrentApplicantDetails currentApplicantDetails;

  @JacksonXmlProperty(localName = "Current_Other_Details")
  private CurrentOtherDetails currentOtherDetails;
}