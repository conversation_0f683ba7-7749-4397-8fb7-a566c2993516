package com.flipkart.fintech.pinaka.library.parser;

import com.flipkart.fintech.pinaka.library.entities.AccountType;
import com.flipkart.fintech.pinaka.library.entities.GetEmiRequest;
import com.flipkart.fintech.pinaka.library.helper.EmiCalculatorHelper;


import java.time.temporal.ChronoUnit;

public class BasicEmiCalculator implements EmiCaculator {
    @Override
    public Integer getEmi(GetEmiRequest getEmiRequest) {
        AccountType accountType = getEmiRequest.getAccountType();
        if (accountType == AccountType.CC) {
            return getEmiRequest.getCurrentAmount();
        }
        if(getEmiRequest.getEmi() != 0) {
            return getEmiRequest.getEmi();
        }
        double interestRate = (double) getInterestForAccountType(accountType) /1200;
        int emi = 0;
        if(getEmiRequest.getRepaymentTenure() != 0) {
            return calculateEmi(getEmiRequest.getSanctionedAmount(), interestRate,(double) getEmiRequest.getRepaymentTenure(), accountType);
        }

        if (getEmiRequest.getLastPaymentDate() != null && getEmiRequest.getOpenedDate() != null &&
                (ChronoUnit.DAYS.between(getEmiRequest.getOpenedDate(), getEmiRequest.getReportDate()) != 0)) {
            double tenure = ChronoUnit.DAYS.between(getEmiRequest.getOpenedDate(), getEmiRequest.getLastPaymentDate())/30.0;
            return calculateEmi(getEmiRequest.getSanctionedAmount()-getEmiRequest.getCurrentAmount(), interestRate, tenure, accountType);
        }

        if(getEmiRequest.getReportDate() != null && getEmiRequest.getOpenedDate() != null && (ChronoUnit.DAYS.between(getEmiRequest.getOpenedDate(), getEmiRequest.getReportDate()) != 0)) {
            double tenure = ChronoUnit.DAYS.between(getEmiRequest.getOpenedDate(), getEmiRequest.getReportDate())/30.0;
            return calculateEmi(getEmiRequest.getSanctionedAmount()-getEmiRequest.getCurrentAmount(), interestRate, tenure, accountType);
        }

        return emi;
    }

    private Integer getInterestForAccountType(AccountType accountType) {
        switch (accountType) {
            case HL:
                return 9;
            case LAP:
                return 11;
            case AL:
            case UCL:
                return 14;
            case TW:
                return 21;
            case BL:
            case PL:
            case OTHERS:
            case OD:
                return 16;
            case EL:
                return 12;
        }
        return 0;
    }

    private int calculateEmi(int principal, double rate, Double tenure, AccountType type) {
        if (type == AccountType.CD) {
            return(int) Math.round((float) principal / tenure);
        }
        return (int) Math.round(EmiCalculatorHelper.getEMIFromInterest(principal, rate, tenure));
    }
}
