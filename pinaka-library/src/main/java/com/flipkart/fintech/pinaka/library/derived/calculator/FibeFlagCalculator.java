package com.flipkart.fintech.pinaka.library.derived.calculator;

import com.flipkart.fintech.pinaka.library.Feature;
import com.flipkart.fintech.pinaka.library.FeatureScoreCalculator;
import com.flipkart.fintech.pinaka.library.UserDetails;
import com.flipkart.fintech.pinaka.library.helper.CalculatorHelper;
import com.flipkart.fintech.pinaka.library.parser.ReportAnalysis;

import java.util.Map;


public class FibeFlagCalculator implements FeatureScoreCalculator<Integer> {
    @Override
    public Integer calculateScore(ReportAnalysis reportAnalysis, Map<Feature, String> featureIntegerMap, UserDetails userDetails) {
        return (
                CalculatorHelper.parseInt(featureIntegerMap.get(Feature.L6M_30DPD_SCORE)) <= 0 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.OD_AMT_SCORE)) <= 5000 &&
                        CalculatorHelper.parseInt(featureIntegerMap.get(Feature.EXP_SCORE)) >= 730
        ) ? 1 : 0;
    }
}
