package com.flipkart.fintech.pinaka.library.parser;

import com.flipkart.fintech.pinaka.library.entities.AccountType;
import com.flipkart.fintech.pinaka.library.entities.GetEmiRequest;

public class UpdatedEmiCalculator implements EmiCaculator {
    @Override
    public Integer getEmi(GetEmiRequest getEmiRequest) {
        Integer emi = getEmiRequest.getEmi();
        Integer sanctionedAmount = getEmiRequest.getSanctionedAmount();
        AccountType accountType = getEmiRequest.getAccountType();

        if (emi != null && emi < 0 && !accountType.equals(AccountType.CC)) {
            if (sanctionedAmount <= 5000) return sanctionedAmount;
            else if (sanctionedAmount <= 10000) return sanctionedAmount / 2;
            else if (sanctionedAmount <= 15000) return sanctionedAmount / 3;
            else if (sanctionedAmount <= 25000) return sanctionedAmount / 4;
            else if (sanctionedAmount <= 50000) return sanctionedAmount / 6;
            else if (sanctionedAmount <= 100000) return sanctionedAmount / 12;
            else if (sanctionedAmount <= 1000000) return sanctionedAmount / 36;
            else return sanctionedAmount / 60;
        }

        double emiRatio = (emi != null && sanctionedAmount !=0) ? (double) emi / sanctionedAmount : 0;

        if ((accountType.equals(AccountType.HL) || accountType.equals(AccountType.LAP)) && emiRatio > 0.04) {
            return (int) (sanctionedAmount * 0.04);
        }
        if (emiRatio > 0.80 && !accountType.equals(AccountType.CC)) {
            if (sanctionedAmount <= 5000) return sanctionedAmount;
            else if (sanctionedAmount <= 10000) return sanctionedAmount / 2;
            else if (sanctionedAmount <= 15000) return sanctionedAmount / 3;
            else if (sanctionedAmount <= 25000) return sanctionedAmount / 4;
            else if (sanctionedAmount <= 50000) return sanctionedAmount / 6;
            else return sanctionedAmount / 12;
        }

        return emi;
    }
}
