package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;


@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CAISHolderAddressDetails {

    @JacksonXmlProperty(localName = "First_Line_Of_Address_non_normalized")
    private String firstLineOfAddress;

    @JacksonXmlProperty(localName = "Second_Line_Of_Address_non_normalized")
    private String secondLineOfAddress;

    @JacksonXmlProperty(localName = "Third_Line_Of_Address_non_normalized")
    private String thirdLineOfAddress;

    @JacksonXmlProperty(localName = "City_non_normalized")
    private String cityOfAddress;

    @JacksonXmlProperty(localName = "Fifth_Line_Of_Address_non_normalized")
    private String fifthLineOfAddress;

    @JacksonXmlProperty(localName = "State_non_normalized")
    private String stateOfAddress;

    @JacksonXmlProperty(localName = "ZIP_Postal_Code_non_normalized")
    private String zipPostalCodeOfAddress;

    @JacksonXmlProperty(localName = "CountryCode_non_normalized")
    private String countryOfAddress;

}
