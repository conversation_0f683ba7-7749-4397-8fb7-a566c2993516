package com.flipkart.fintech.pinaka.library.parser;

import com.flipkart.fintech.pinaka.library.dao.CostInflationFactorDao;
import com.flipkart.fintech.pinaka.library.dao.DivisionFactorDao;
import com.flipkart.fintech.pinaka.library.entities.*;
import com.flipkart.fintech.pinaka.library.helper.CalculatorHelper;
import com.flipkart.fintech.pinaka.library.helper.Constant;
import com.flipkart.fintech.pinaka.library.helper.DateFormatter;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

import static com.flipkart.fintech.pinaka.library.helper.CalculatorHelper.getBureauIncome;
import static com.flipkart.fintech.pinaka.library.helper.CalculatorHelper.getInflationFactor;
import static com.flipkart.fintech.pinaka.library.helper.Constant.*;
import static com.flipkart.fintech.pinaka.library.helper.EmiCalculatorHelper.*;

import java.math.BigDecimal;
import java.math.RoundingMode;

/*
For logic around value calculation, please check
https://docs.google.com/spreadsheets/d/18Q7hFjR8itDXe1N-cMzbyGARdb1pHM33pgapDZWfr30/edit#gid=440386491
*/

@CustomLog
public class ExperianReportAnalysis implements ReportAnalysis {

    private final ExperianReport report;
    private final Integer minIncome = 5000;
    private final Integer maxIncome = 1000000;
    private final CostInflationFactorDao costInflationFactorDao;
    private final DivisionFactorDao divisionFactorDao;
    private final String bureauErrorMessage;

    public ExperianReportAnalysis(ExperianReport report, CostInflationFactorDao costInflationFactorDao, DivisionFactorDao divisionFactorDao, String bureauErrorMessage) {
        this.report = report;
        this.costInflationFactorDao = costInflationFactorDao;
        this.divisionFactorDao = divisionFactorDao;
        this.bureauErrorMessage = bureauErrorMessage;
    }

    @Override
    public Integer getLpd(int month, int days, boolean isActive) {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }
        return (int) report.getCaisAccount().getCaisAccountDetails().stream()
                .flatMap(caisAccountDetails -> {
                    if ((isActive && !StringUtils.isEmpty(caisAccountDetails.getDateClosed())) || caisAccountDetails.getCaisAccountHistory() == null) {
                        return Stream.empty();
                    }
                    return caisAccountDetails.getCaisAccountHistory().stream()
                            .filter(caisAccountHistory ->
                                    CalculatorHelper.isWithinTimeline(report.getHeader().getReportDate(),
                                            DateFormatter.getLocalDateFromCaisHistory(caisAccountHistory),
                                            month) &&
                                            CalculatorHelper.getDPD(caisAccountHistory) > days);
                })
                .count();
    }

    @Override
    public Integer getMaxLpd(int month) {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                Objects.isNull(report.getHeader().getReportDate())) {
            return null;
        }
        return (int) report.getCaisAccount().getCaisAccountDetails().stream()
                .mapToInt(caisAccountDetails -> {
                    if ((!StringUtils.isEmpty(caisAccountDetails.getDateClosed())) || Objects.isNull(caisAccountDetails.getCaisAccountHistory())) {
                        return 0;
                    }
                    return caisAccountDetails.getCaisAccountHistory().stream()
                            .filter(caisAccountHistory ->
                                    CalculatorHelper.isWithinTimeline(report.getHeader().getReportDate(),
                                            DateFormatter.getLocalDateFromCaisHistory(caisAccountHistory),
                                            month)).mapToInt(
                                    CalculatorHelper::getDPD
                            ).max().orElse(0);
                }).max().orElse(0);
    }

    @Override
    public Integer getO2d(int month) {
        if (report == null || report.getCaisAccount() == null ||
                report.getCaisAccount().getCaisAccountDetails() == null ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        return report.getCaisAccount().getCaisAccountDetails().stream()
                .filter(caisAccountDetails -> {
                    LocalDate reportedDate = DateFormatter.getDateInYYYYMMDDFormat(caisAccountDetails.getDateReported());
                    return Objects.nonNull(reportedDate) && CalculatorHelper.isWithinTimeline(report.getHeader().getReportDate(), reportedDate, month) &&
                            StringUtils.isEmpty(caisAccountDetails.getDateClosed()) &&
                            caisAccountDetails.getAmountPastDue() != null;
                })
                .mapToInt(caisAccountDetails ->
                        CalculatorHelper.parseInt(caisAccountDetails.getAmountPastDue()))
                .sum();
    }

    @Override
    public Integer getIncomeCC() {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }
        return report.getCaisAccount().getCaisAccountDetails().stream().filter(
                caisAccountDetails -> Objects.equals(caisAccountDetails.getAccountType(), "10")
        ).mapToInt(caisAccountDetails -> {
            int cl = Math.max(CalculatorHelper.parseInt(caisAccountDetails.getHighestCreditOrOriginalLoanAmount()),
                    CalculatorHelper.parseInt(caisAccountDetails.getCreditLimitAmount()));
            int income;
            if (cl < 50000) {
                income = 20000;
            } else if (cl <= 200000) {
                income = cl / 3;
            } else {
                income = (int) (cl / 3.5);
            }
            return income;
        }).max().orElse(0);
    }

    @Override
    public Integer getUnsecure() {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty()) {
            return null;
        }

        return (int) report.getCaisAccount().getCaisAccountDetails().stream().filter(caisAccountDetails ->
                Constant.UNSECURED_ACCOUNT.contains(CalculatorHelper.parseInt(caisAccountDetails.getAccountType()))
        ).count();
    }

    @Override
    public Integer getVintageScore() {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        return report.getCaisAccount().getCaisAccountDetails().stream()
                .mapToInt(caisAccountDetails -> {
                    LocalDate openDate = DateFormatter.getDateInYYYYMMDDFormat(caisAccountDetails.getOpenDate());
                    LocalDate reportLocalDate = DateFormatter.getDateInYYYYMMDDFormat(report.getHeader().getReportDate());
                    if (Objects.isNull(openDate) || Objects.isNull(reportLocalDate)) {
                        return 0;
                    }
                    return (int) Math.abs(ChronoUnit.MONTHS.between(reportLocalDate.withDayOfMonth(1), openDate.withDayOfMonth(1)));
                })
                .max()
                .orElse(0);
    }

    @Override
    public Integer getL1M() {
        return Objects.isNull(report.getCaps()) || Objects.isNull(report.getCaps().getCapsSummary())
                ? null : report.getCaps().getCapsSummary().getCapsLast30Days();
    }

    @Override
    public Integer getPL_BL(int month, int sanctionedAmount) {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }
        return (int) report.getCaisAccount().getCaisAccountDetails().stream().filter(caisAccountDetails -> {
            LocalDate openDate = DateFormatter.getDateInYYYYMMDDFormat(caisAccountDetails.getOpenDate());
            return Objects.nonNull(openDate) &&
                    CalculatorHelper.isWithinTimeline(report.getHeader().getReportDate(), openDate, month) &&
                    Constant.UNSECURED_ACCOUNT.contains(CalculatorHelper.parseInt(caisAccountDetails.getAccountType())) &&
                    CalculatorHelper.parseInt(caisAccountDetails.getHighestCreditOrOriginalLoanAmount()) > sanctionedAmount;
        }).count();
    }

    @Override
    public Integer getScore() {
        return report != null && report.getScore() != null
                ? report.getScore().getBureauScore()
                : null;
    }

    @Override
    public Integer getWo(int month) {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        for (CAISAccountDetails caisAccountDetails : report.getCaisAccount().getCaisAccountDetails()) {
            boolean withinTimeline = CalculatorHelper.isWithinTimeline(report.getHeader().getReportDate(),
                    DateFormatter.getDateInYYYYMMDDFormat(caisAccountDetails.getDateReported()), month);
            if (withinTimeline && CalculatorHelper.writtenOffCalculator(caisAccountDetails)) {
                return 1;
            }
        }
        return 0;
    }

    @Override
    public Integer getWoEver() {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty()) {
            return null;
        }
        for (CAISAccountDetails caisAccountDetails : report.getCaisAccount().getCaisAccountDetails()) {
            if (CalculatorHelper.writtenOffCalculator(caisAccountDetails)) {
                return 1;
            }
        }
        return 0;
    }
    @Override
    public Integer getActiveCard() {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty()) {
            return null;
        }
        return  (int) report.getCaisAccount().getCaisAccountDetails().stream().filter(caisAccountDetails ->
                ACTIVE_CREDIT_ACCOUNT.contains(CalculatorHelper.parseInt(caisAccountDetails.getAccountType()))
                        && StringUtils.isEmpty(caisAccountDetails.getDateClosed())).count();
    }

    @Override
    public Integer getTotalIncome() {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        try {
            LocalDate reportDate = DateFormatter.getDateInYYYYMMDDFormat(report.getHeader().getReportDate());
            Integer income = minIncome;
            for (CAISAccountDetails caisAccountDetails : report.getCaisAccount().getCaisAccountDetails()) {
                if (StringUtils.isNotEmpty(caisAccountDetails.getDateClosed()) || !isOpenDateWithinLimit(caisAccountDetails, reportDate)) {
                    continue;
                }
                int bureauIncome = Math.max(getFinalBureauIncome(caisAccountDetails, reportDate), minIncome);
                income = Math.max(income, bureauIncome);
            }
            return Math.min(income, maxIncome);
        } catch (Exception e) {
            log.info("getting exception while calculating income", e.getMessage());
        }
        return minIncome;
    }

    @Override
    public Integer getTotalObligation() {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }
        try {
            Integer secObligation = 0;
            Integer unsecObligation = 0;
            Integer ccObligation = 0;
            for (CAISAccountDetails caisAccountDetails : report.getCaisAccount().getCaisAccountDetails()) {
                Boolean isLive = getLiveFlag(caisAccountDetails, DateFormatter.getDateInYYYYMMDDFormat(report.getHeader().getReportDate()));
                if (!isLive) {
                    continue;
                }
                Integer emi = getEmi(caisAccountDetails);
                AccountType accountType = getAccountType(caisAccountDetails);
                if (Constant.SEC_ACCOUNT_TYPE.contains(accountType)) {
                    secObligation += emi;
                } else if (Constant.UNSEC_ACCOUNT_TYPE.contains(accountType)) {
                    unsecObligation += emi;
                } else if (Constant.CC_ACCOUNT_TYPE.contains(accountType)) {
                    ccObligation += emi;
                }
            }
            return (int) (ccObligation * 0.05 + secObligation + unsecObligation);
        } catch ( Exception e ) {
            log.info("Getting exception in calculating total obligation ", e.getMessage());
        }
        return 0;
    }


    public Integer getEmi(CAISAccountDetails caisAccountDetails) {
        GetEmiRequest emiRequest = getEmiRequest(caisAccountDetails);
        EmiCaculator emiCalculator = new BasicEmiCalculator();
        Integer emi= emiCalculator.getEmi(emiRequest);
        emiRequest.setEmi(emi);
        EmiCaculator updatedEmiCalculator = new UpdatedEmiCalculator();
        return updatedEmiCalculator.getEmi(emiRequest);
    }

    private GetEmiRequest getEmiRequest(CAISAccountDetails caisAccountDetails) {
        AccountHolderCodeType accountHolderCodeType = getAccountHolderType(caisAccountDetails);
        return GetEmiRequest.builder()
                .accountType(getAccountType(caisAccountDetails))
                .sanctionedAmount(getSanctionedAmount(accountHolderCodeType, caisAccountDetails))
                .currentAmount(getCurrentBalanceAmount(accountHolderCodeType, caisAccountDetails))
                .repaymentTenure(CalculatorHelper.parseInt(caisAccountDetails.getRepaymentTenure()))
                .reportDate(DateFormatter.getDateInYYYYMMDDFormat(caisAccountDetails.getDateReported()))
                .openedDate(DateFormatter.getDateInYYYYMMDDFormat(caisAccountDetails.getOpenDate()))
                .lastPaymentDate(DateFormatter.getDateInYYYYMMDDFormat(caisAccountDetails.getDateOfLastPayment()))
                .emi(CalculatorHelper.parseInt(caisAccountDetails.getScheduledMonthlyPaymentAmount()))
                .build();
    }

    @Override
    public Integer getNtc() {
        ErrorEnum errorEnum = ErrorEnum.fromString(bureauErrorMessage);
        return (errorEnum != null) ? errorEnum.getIndex() : null;
    }

    @Override
    public Double getFoir() {
        Integer totalIncome = getTotalIncome();
        Integer totalObligation = getTotalObligation();

        if (totalIncome == null || totalObligation == null || totalIncome == 0) {
            return null;
        }

        return totalObligation.doubleValue() / totalIncome.doubleValue();
    }

    private Integer getFinalBureauIncome(CAISAccountDetails caisAccountDetails, LocalDate reportDate) {
        AccountType accountType = getAccountType(caisAccountDetails);
        LocalDate openDate = DateFormatter.getDateInYYYYMMDDFormat(caisAccountDetails.getOpenDate());
        if (Objects.isNull(openDate)) {
            return 0;
        }
        Income accountIncome = getBureauIncome(caisAccountDetails);
        accountIncome.setAmount((int) (accountIncome.getAmount() * getInflationFactor(costInflationFactorDao, reportDate, openDate)));
        Double divisionFactor = divisionFactorDao.getDivisionFactor(accountType, accountIncome.getAmount(), accountIncome.getIncomeType());
        return (int) (accountIncome.getAmount() / divisionFactor);
    }

    private Boolean isOpenDateWithinLimit(CAISAccountDetails caisAccountDetails, LocalDate reportDate) {
        LocalDate openDate = DateFormatter.getDateInYYYYMMDDFormat(caisAccountDetails.getOpenDate());
        if(Objects.isNull(openDate)) {
            return false;
        }
        Integer daysDifference = (int) Math.abs(ChronoUnit.DAYS.between(reportDate, openDate));
        AccountType accountType = getAccountType(caisAccountDetails);
        Map<AccountType, Integer> minDaysDifference = new HashMap<>();
        minDaysDifference.put(AccountType.CC, 180);
        minDaysDifference.put(AccountType.CD, 270);
        minDaysDifference.put(AccountType.PL, 180);
        minDaysDifference.put(AccountType.BL, 270);

        return daysDifference >= minDaysDifference.getOrDefault(accountType, 0);
    }

    @Override
    public Integer getMaxLoanAmount(List<Integer> accountTypes) {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        return report.getCaisAccount().getCaisAccountDetails().stream()
                .filter(caisAccountDetails -> accountTypes.contains(CalculatorHelper.parseInt(caisAccountDetails.getAccountType())))
                .mapToInt(caisAccountDetails -> {
                    String accountHolderTypeCode = caisAccountDetails.getAccountHoldertypeCode();
                    int amount = CalculatorHelper.parseInt(caisAccountDetails.getHighestCreditOrOriginalLoanAmount());
                    if (AUTHORIZED_GUARANTOR_ACCOUNT_HOLDER.contains(CalculatorHelper.parseInt(accountHolderTypeCode))) {
                        return 0;
                    } else if (JOINT_ACCOUNT_HOLDER.contains(CalculatorHelper.parseInt(accountHolderTypeCode))) {
                        return amount / 2;
                    } else {
                        return amount;
                    }
                })
                .max().orElse(0);
    }

    @Override
    public Integer getMaxCreditLimit() {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        return report.getCaisAccount().getCaisAccountDetails().stream()
                .filter(caisAccountDetails -> ACTIVE_CREDIT_ACCOUNT.contains(CalculatorHelper.parseInt(caisAccountDetails.getAccountType())))
                .mapToInt(caisAccountDetails -> {
                    String accountHolderTypeCode = caisAccountDetails.getAccountHoldertypeCode();
                    int cl = Math.max(CalculatorHelper.parseInt(caisAccountDetails.getHighestCreditOrOriginalLoanAmount()),
                            CalculatorHelper.parseInt(caisAccountDetails.getCreditLimitAmount()));
                    if (AUTHORIZED_ACCOUNT_HOLDER.contains(CalculatorHelper.parseInt(accountHolderTypeCode))) {
                        return 0;
                    } else {
                        return cl;
                    }
                })
                .max().orElse(0);
    }

    @Override
    public Integer getCountOfPL(List<Integer> accountTypes, int months) {
        if (report == null || report.getCaisAccount() == null ||
                report.getCaisAccount().getCaisAccountDetails() == null ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        return (int) report.getCaisAccount().getCaisAccountDetails().stream()
                .filter(caisAccountDetails -> accountTypes.contains(CalculatorHelper.parseInt(caisAccountDetails.getAccountType())))
                .filter(caisAccountDetails -> {
                    LocalDate openDate = DateFormatter.getDateInYYYYMMDDFormat(caisAccountDetails.getOpenDate());
                    return Objects.nonNull(openDate) &&
                            CalculatorHelper.isWithinTimeline(report.getHeader().getReportDate(), openDate, months);
                })
                .count();
    }

    @Override
    public Integer getL6M() {
        if (report == null || report.getCaps() == null || report.getCaps().getCapsSummary() == null) {
            return null;
        }
        return report.getCaps().getCapsSummary().getCapsLast180Days();
    }
    @Override
    public Integer getCountExpPlLive(List<Integer> accountTypes){
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        return (int) report.getCaisAccount().getCaisAccountDetails().stream()
                .filter(caisAccountDetails -> accountTypes.contains(CalculatorHelper.parseInt(caisAccountDetails.getAccountType()))
                        && StringUtils.isEmpty(caisAccountDetails.getDateClosed())
                        && CalculatorHelper.parseInt(caisAccountDetails.getCurrentBalance())>0)
                .count();
    }

    @Override
    public Integer getCountActiveGoldLoans(List<Integer> accountTypes){
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        return (int) report.getCaisAccount().getCaisAccountDetails().stream()
                .filter(caisAccountDetails -> accountTypes.contains(CalculatorHelper.parseInt(caisAccountDetails.getAccountType()))
                        && StringUtils.isEmpty(caisAccountDetails.getDateClosed())
                        && CalculatorHelper.parseInt(caisAccountDetails.getCurrentBalance())>0)
                .count();
    }

    @Override
    public Integer getTotalCountGoldLoans(List<Integer> accountTypes){
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        return (int) report.getCaisAccount().getCaisAccountDetails().stream()
                .filter(caisAccountDetails -> accountTypes.contains(CalculatorHelper.parseInt(caisAccountDetails.getAccountType())))
                .count();
    }

    @Override
    public Double getFoirWithMinDeclaredOrExperianIncome(Integer monthlyIncome){
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }
        if(monthlyIncome==null) {
            monthlyIncome=Integer.MAX_VALUE;
        }

        if(getTotalIncome()==null || getTotalObligation()==null || getTotalIncome()==0|| getTotalObligation()==0 || monthlyIncome==0) return 0.0;

        BigDecimal totalObligation = new BigDecimal(getTotalObligation());
        BigDecimal totalIncome = new BigDecimal(getTotalIncome());
        BigDecimal monthlyIncomeBD = new BigDecimal(monthlyIncome);

        BigDecimal minIncome = totalIncome.min(monthlyIncomeBD);

        // Calculate FOIR and round to 4 decimal places
        BigDecimal foir = totalObligation.divide(minIncome, 4, RoundingMode.HALF_UP);

        return foir.doubleValue();
    }


    @Override
    public Integer getCountActiveLenderSpecificLoans(List<String> lenderKeywords){
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        return (int) report.getCaisAccount().getCaisAccountDetails().stream()
                .filter(caisAccountDetails -> validateLenderKeywords(lenderKeywords,caisAccountDetails.getSubscriberName())
                        && StringUtils.isEmpty(caisAccountDetails.getDateClosed())
                        && CalculatorHelper.parseInt(caisAccountDetails.getCurrentBalance())>0)
                .count();
    }

    @Override
    public Integer getCountActiveLenderSpecificPLLoans(List<String> lenderKeywords, List<Integer> accountTypes){
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails()) ||
                report.getCaisAccount().getCaisAccountDetails().isEmpty() ||
                StringUtils.isEmpty(report.getHeader().getReportDate())) {
            return null;
        }

        return (int) report.getCaisAccount().getCaisAccountDetails().stream()
                .filter(caisAccountDetails -> validateLenderKeywords(lenderKeywords,caisAccountDetails.getSubscriberName())
                        && accountTypes.contains(CalculatorHelper.parseInt(caisAccountDetails.getAccountType()))
                        && StringUtils.isEmpty(caisAccountDetails.getDateClosed())
                        && CalculatorHelper.parseInt(caisAccountDetails.getCurrentBalance())>0)
                .count();
    }

    public Boolean validateLenderKeywords(List<String> lenderKeywords, String subscriberName){
        if(StringUtils.isEmpty(subscriberName)) return false;

        for(String keyword: lenderKeywords){
            if(subscriberName.contains(keyword)) return true;
        }

        return false;
    }
}
