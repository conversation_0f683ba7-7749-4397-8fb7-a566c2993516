package com.flipkart.fintech.pinaka.library.parser;

import com.flipkart.fintech.pinaka.library.ReportType;
import com.google.inject.Inject;

import java.util.Map;

public class ParserFactory {

    Map<ReportType, ReportParser> typeReportParserMap;

    @Inject
    public ParserFactory(Map<ReportType, ReportParser> typeReportParserMap) {
        this.typeReportParserMap = typeReportParserMap;
    }


    public ReportParser getExperianReportParser(ReportType reportType) {
        return this.typeReportParserMap.get(reportType);
    }
}
