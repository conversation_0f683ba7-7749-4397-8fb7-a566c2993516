package com.flipkart.fintech.pinaka.library.entities;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InflationFactor {

    @JsonProperty("FinancialYear")
    private String financialYear;

    @JsonProperty("CostInflationIndex")
    private Integer costInflationIndex;

}
