package com.flipkart.fintech.pinaka.library.entities;

import com.flipkart.fintech.pinaka.library.dao.CostInflationFactorDao;
import com.flipkart.fintech.pinaka.library.dao.DivisionFactorDao;
import com.flipkart.fintech.pinaka.library.parser.ReportAnalysis;

public interface Report {
    public abstract ReportAnalysis getReportAnalysis(CostInflationFactorDao costInflationFactorDao, DivisionFactorDao divisionFactorDao, String bureauErrorMessage);
    public abstract String getReportDate();
}
