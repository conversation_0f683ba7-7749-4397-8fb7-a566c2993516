package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CAPS_Application_Details implements Serializable {
    @JacksonXmlProperty(localName = "Subscriber_Code")
    private String subscriberCode;

    @JacksonXmlProperty(localName = "Subscriber_Name")
    private String subscriberName;

    @JacksonXmlProperty(localName = "Date_of_Request")
    private String dateOfRequest;

    @JacksonXmlProperty(localName = "Enquiry_Reason")
    private String enquiryReason;

    @JacksonXmlProperty(localName = "Finance_Purpose")
    private String financePurpose;

    @JacksonXmlProperty(localName = "Amount_Financed")
    private String amountFinanced;
}