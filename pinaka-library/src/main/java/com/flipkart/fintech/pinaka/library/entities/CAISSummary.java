package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CAISSummary {
    @JacksonXmlProperty(localName = "Credit_Account")
    private CreditAccount creditAccount;

    @JacksonXmlProperty(localName = "Total_Outstanding_Balance")
    private TotalOutstandingBalance totalOutstandingBalance;
}
