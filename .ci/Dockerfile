FROM jfrog.fkinternal.com/flow-step-template/build-jdk8-maven3.8:v1 AS builder

WORKDIR /src

RUN mkdir -p /root/.m2 && \
    echo '<settings><mirrors><mirror><id>J<PERSON><PERSON></id><name><PERSON><PERSON><PERSON></name><url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url><mirrorOf>central</mirrorOf></mirror></mirrors></settings>' \
    > /root/.m2/settings.xml

RUN mvn clean -T 4 -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn -B org.jacoco:jacoco-maven-plugin:prepare-agent install

FROM jfrog.fkinternal.com/supermoney-engg/openjdk8-builder:5365853


ARG appId=sm-pinaka
ARG appDirectory=pinaka-service
WORKDIR /src

# use env variables
RUN echo "deb [trusted=yes] http://packages.reposvc-prod.fkcloud.in/repos/infra-cli/10 /" > /etc/apt/sources.list.d/infra-cli.list
RUN echo "deb [trusted=yes] http://packages.reposvc-prod.fkcloud.in/repos/fk-3p-newrelic/19 /" > /etc/apt/sources.list.d/fk-3p-newrelic.list \
    && apt-get update \
      &&  apt-get install -y procps \
            ngrep \
            netcat \
            curl \
            iputils-ping \
            lsof \
            net-tools \
            sysstat \
            less \
            tcpdump \
            iproute2 \
            libcap2-bin \
            htop \
            strace \
            dnsutils \
    && apt-get --yes --allow-unauthenticated install infra-cli curl sed nr-java-apm-agent telnet

ARG user=lending
ARG group=fpg
ARG uid=108
ARG gid=112

COPY --from=builder /src/${appDirectory}/target/${appDirectory}-*.jar /var/lib/${appId}/${appId}.jar
COPY --from=builder /src/${appDirectory}/config/gibraltar/* /etc/gibraltar/
COPY --from=builder /src/${appDirectory}/config/fkagent/* /etc/fkagent/

RUN groupadd -g ${gid} ${group} && \
    useradd -u ${uid} -g ${gid} -m -s /bin/bash ${user} && \
    mkdir -p /var/lib/${appId} && chown -R ${user}:${group} /var/lib/${appId} && chmod -R u+rwx /var/lib/${appId} && \
    mkdir -p /var/lib/fkagent && chown -R ${user}:${group} /var/lib/fkagent && chmod -R u+rwx /var/lib/fkagent && \
    chown -R ${user}:${group} /entrypoint && chmod -R u+rwx /entrypoint && \
    sed -i "s/__PACKAGE__/${appId}/g" "/entrypoint"

RUN curl -L -X GET '************/artifactory/maven_internal/com/flipkart/instrumentation/fk-agent/2.0/fk-agent-2.0.jar' --output /var/lib/fkagent/fk-agent.jar

RUN sed -i "s/__INCLUDE_FK_AGENT__/TRUE/" "/entrypoint"
RUN sed -i "s|__FK_AGENT_OPTS__|-javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml|" "/entrypoint"

ENTRYPOINT [ "/entrypoint" ]
CMD ["com.flipkart.fintech.pinaka.service.application.PinakaApplication", "server", "/etc/config/config.yml"]
