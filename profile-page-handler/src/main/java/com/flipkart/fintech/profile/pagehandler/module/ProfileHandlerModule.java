package com.flipkart.fintech.profile.pagehandler.module;

import com.flipkart.fintech.profile.client.ProfileClientConfiguration;
import com.flipkart.fintech.profile.config.ProfileServiceConfig;
import com.flipkart.fintech.profile.pagehandler.pagedatasource.BureauDetailsPageDataSource;
import com.flipkart.fintech.profile.pagehandler.pagedatasource.BureauPageDataSource;
import com.flipkart.fintech.profile.pagehandler.pagedatasource.CreditScoreWaitScreenDataSource;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;

public class ProfileHandlerModule extends AbstractModule {

    private final ProfileClientConfiguration profileClientConfiguration;

    public ProfileHandlerModule(ProfileClientConfiguration profileClientConfiguration) {
        this.profileClientConfiguration = profileClientConfiguration;
    }

    @Override
    protected void configure() {
        bind(BureauPageDataSource.class).to(BureauDetailsPageDataSource.class);
        bind(ProfileClientConfiguration.class).toInstance(profileClientConfiguration);
    }
}
