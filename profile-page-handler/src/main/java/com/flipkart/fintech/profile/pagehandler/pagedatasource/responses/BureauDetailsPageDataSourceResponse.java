package com.flipkart.fintech.profile.pagehandler.pagedatasource.responses;

import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.rome.datatypes.response.common.leaf.value.Value;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import lombok.Data;

import java.util.Map;

@Data
public class BureauDetailsPageDataSourceResponse extends Value {
    private Map<String, Object> queryParams;
    private ProfileDetailedResponse profile;
    private EncryptionData encryptionData;
}
