package com.flipkart.fintech.profile.pagehandler.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.request.v6.PageServiceRequest;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.profile.client.ProfileClientConfiguration;
import com.flipkart.fintech.profile.pagehandler.pagedatasource.BureauDetailsPageDataSource;
import com.flipkart.fintech.profile.pagehandler.pagedatasource.responses.BureauDetailsPageDataSourceResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.google.inject.Inject;
import lombok.CustomLog;

import java.text.ParseException;


@CustomLog
public class BureauFormWidgetTransformers {

    private final ProfileClientConfiguration profileClientConfiguration;
    private final EncryptionData encryptionData;
    @Inject
    public BureauFormWidgetTransformers(ProfileClientConfiguration profileClientConfiguration, EncryptionData encryptionData, DynamicBucket dynamicBucket){
        this.profileClientConfiguration = profileClientConfiguration;
        this.encryptionData = encryptionData;
    }
    public GenericFormWidgetData buildWidgetData(String formType, PageServiceRequest pageServiceRequest)
            throws PinakaClientException, ParseException, JsonProcessingException {
        switch (formType) {
            case "CREDIT_SCORE_DETAILS_FORM":
                BureauDetailsPageDataSource bureauDetailsPageDataSource = new BureauDetailsPageDataSource(profileClientConfiguration,encryptionData);
                BureauDetailsPageDataSourceResponse bureauDetailsPageDataSourceResponse = bureauDetailsPageDataSource.getData(pageServiceRequest.getAccountId(),pageServiceRequest.getSmUserId());
                return new CreditScoreDetailsFormTransformer().buildWidgetData(pageServiceRequest.getAccountId(), pageServiceRequest.getSmUserId(),bureauDetailsPageDataSourceResponse);
            default:
                log.error("Unknown Widget Type {}", formType);
        }
        return null;
    }

}