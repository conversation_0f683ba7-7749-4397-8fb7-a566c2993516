package com.flipkart.fintech.profile.pagehandler.pagedatasource;

import com.fasterxml.jackson.databind.ObjectMapper;
//import com.flipkart.fintech.profile.api.response.BureauResponse;
import com.flipkart.fintech.profile.client.ProfileClientConfiguration;
import com.flipkart.fintech.profile.client.ProfileClientImpl;
import com.flipkart.fintech.profile.pagehandler.pagedatasource.responses.CreditScorePageDataSourceResponse;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.fintech.profile.service.BureauDataManagerImpl;
import com.flipkart.kloud.config.DynamicBucket;

import javax.inject.Inject;
import java.io.InputStream;

public class CreditScorePageDataSource {
    private final ProfileClientImpl profileClient;

    @Inject
    public CreditScorePageDataSource(ProfileClientConfiguration profileClientConfiguration){
        this.profileClient  = new ProfileClientImpl(profileClientConfiguration);
    }

    public CreditScorePageDataSourceResponse getData(String merchantUserId, String smUserId){
        CreditScorePageDataSourceResponse creditScorePageDataSourceResponse =  new CreditScorePageDataSourceResponse();
        BureauDataResponse bureauDataResponse = profileClient.getExistingCreditScore(merchantUserId,smUserId);
        creditScorePageDataSourceResponse.setBureauDataResponse(bureauDataResponse);
        return creditScorePageDataSourceResponse;
    }

    private BureauDataResponse dummy(){
        BureauDataResponse bureauDataResponse = new BureauDataResponse();

        try {
            InputStream inputStream = BureauDataManagerImpl.class.getClassLoader().getResourceAsStream("template/bureau/test.json");
            bureauDataResponse = new ObjectMapper().readValue(inputStream, BureauDataResponse.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return bureauDataResponse;
    }

}
