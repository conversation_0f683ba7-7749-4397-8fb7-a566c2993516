{"banner": {"value": {"title": {"value": {"type": "RichTextValue", "text": "Get instant loan offer of", "style": {"color": "#4A4C4F", "fontSize": 18, "fontWeight": "normal", "textAlign": "left", "lineHeight": 28}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "₹5,15,500", "style": {"color": "#2A55E5", "fontSize": 28, "fontWeight": "bold", "textAlign": "center", "lineHeight": 44}}}, "images": [{"title": {"type": "RichTextValue", "text": "Approval in 30 secs"}, "image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/04/2023/a5a41d35-ba75-4326-93f8-63907112dc0f.png?q={@quality}", "height": 24, "type": "ImageValue", "width": 24}, "type": "ImageTextValue"}, {"title": {"type": "RichTextValue", "text": "Paperless process"}, "image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/04/2023/a5a41d35-ba75-4326-93f8-63907112dc0f.png?q={@quality}", "height": 24, "type": "ImageValue", "width": 24}, "type": "ImageTextValue"}, {"title": {"type": "RichTextValue", "text": "Paperless process"}, "image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/03/04/2023/a5a41d35-ba75-4326-93f8-63907112dc0f.png?q={@quality}", "height": 24, "type": "ImageValue", "width": 24}, "type": "ImageTextValue"}], "richButton": {"action": {"url": "/test/bank-details", "encryption": {"publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCLo3cxsTNbj2c6FB4EZK5gtO0PsjWzOw9b2vVCaDY9IVFGHLROz3MINr0pThJwfoBl+ahhHyUioXgILCUk6y/HVRfgYwitsHQ7Hv+JkIDnnw0qtclRRUnRlejhfDHQozypFiTZNLG3Y1/JBS2tSPE6RHEQe/w98sqRu86yBorzMwIDAQAB", "keyId": "ASD"}, "params": {"applicationId": "APP2306261210185638975522832845299296512", "processInstanceId": "dummy_process_id"}, "type": "FORM_SUBMIT", "tracking": {}, "loginType": "LOGIN_NOT_REQUIRED"}, "value": {"type": "RichTextButtonValue", "title": "Apply Now"}}, "type": "RichMultiImageBannerValue"}}}