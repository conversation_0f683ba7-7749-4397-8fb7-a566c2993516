{"formId": "PERSONAL_DETAILS", "persistFormData": false, "renderableComponents": [{"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": true, "formFieldType": "TEXT_BOX_V0", "label": "Phone number", "mandatory": true, "name": "phoneNumber", "placeholder": "Enter phone number", "inputType": "PHONE_PAD", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid phone number", "validateOnSubmit": false, "regex": "^[6-9]{1}[0-9]{9}$"}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "PAN Number", "mandatory": true, "name": "pan", "placeholder": "Eg: **********", "subText": "Your PAN should be linked to the above phone number", "autoCapitalize": "characters", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid PAN", "validateOnSubmit": false, "regex": "^[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}$"}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "DateFormFieldValue", "disabled": false, "formFieldType": "DATE", "label": "Date of Birth", "mandatory": true, "maxValue": "2002-07-29T22:27:17.529", "minValue": "1963-07-29T22:27:17.529", "name": "dob"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "First name", "mandatory": true, "name": "firstName", "placeholder": "Please enter first name as per PAN", "replaceInput": {"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}, "replaceInputList": [{"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "^\\s", "replaceValue": ""}, {"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Last name", "mandatory": true, "name": "lastName", "placeholder": "Please enter last name as per PAN", "replaceInput": {"regex": "^[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}, "replaceInputList": [{"regex": "^\\s?[a-zA-Z]+( [a-zA-Z]+)* ?$", "replaceRegex": [{"regex": "[^a-zA-Z\\s]", "replaceValue": ""}, {"regex": "\\s+", "replaceValue": " "}]}]}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValueV0", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Email", "inputType": "EMAIL_ADDRESS", "mandatory": true, "name": "email", "placeholder": "Eg: <EMAIL>", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid email", "validateOnSubmit": false, "regex": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"}]}}], "submitButton": {"type": "SubmitButtonValue", "consent": {"checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "You hereby consent to Scapic Innovations Private Limited (SIPL) being appointed as your authorized representative to receive your Credit Information from Experian for the purpose of obtaining information from my personal credit profile from Credit Information Companies in order to confirm my identity and display my credit data to me and also consent and expressly authorize <PERSON><PERSON><PERSON> ([Read Experian Specific terms here](https://www.flipkart.com/pages/experian-new-tnc)) to share your Credit Information with SIPL and for SIPL to use such data to evaluate your credit worthiness/eligibility/identity for financial services and products such as personal loans, credit cards, buy now pay later (End Use Purpose)."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentType": "CHECKBOX"}, "button": {"action": {"url": "/api/sm/1/form", "encryption": {}, "params": {}, "loaderContent": {"type": "ANNOUNCEMENT_V2", "content": {"value": {"image": {"dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/04/04/2024/16390b7e-85b7-4d0a-b11c-f3c0e375ea04.png?q={@quality}", "height": 185, "width": 262, "aspectRatio": "262:185", "type": "ImageValue"}, "title": {"value": {"type": "RichTextValue", "text": "Fetching your credit score", "style": {"color": "#344054", "fontSize": 20, "fontWeight": "bold", "textAlign": "center", "lineHeight": 30}}}, "subTitle": {"value": {"type": "RichTextValue", "text": "Do not press back or switch apps.", "style": {"color": "#667085", "fontSize": 14, "fontWeight": "normal", "textAlign": "center", "lineHeight": 22}}}, "type": "AnnouncementV2Value"}}}, "type": "CALM__FORM_ACTION"}, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Continue"}}}, "resultStoreKey": null, "subTitle": null, "title": null}