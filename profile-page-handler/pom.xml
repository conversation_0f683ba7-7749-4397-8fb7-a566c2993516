<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.flipkart.fintech</groupId>
        <artifactId>pinaka</artifactId>
        <version>3.4.26-SM</version>
    </parent>

    <artifactId>profile-page-handler</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.flipkart.aapi</groupId>
            <artifactId>models-aapi</artifactId>
            <version>${aapi.models.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech.pinaka.common</groupId>
            <artifactId>pinaka-common</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-datatype-jdk8</artifactId>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
            <version>1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pinaka-client</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.jersey.core</groupId>
                    <artifactId>jersey-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.fintech</groupId>
                    <artifactId>fintech-logger</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>profile-service-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.de</groupId>
            <version>1.8.12</version>
            <artifactId>client-jar</artifactId>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>ams-bridge</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-datatype-guava</artifactId>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-kafka-consumers</artifactId>
            <version>1.1.8-SM</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>