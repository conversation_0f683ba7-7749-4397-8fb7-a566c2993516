<?xml version="1.0" encoding="UTF-8"?>
<!-- ====================================================================== -->
<!--                                                                        -->
<!-- Generated by Maven Help Plugin                                         -->
<!-- See: https://maven.apache.org/plugins/maven-help-plugin/               -->
<!--                                                                        -->
<!-- ====================================================================== -->
<!-- ====================================================================== -->
<!--                                                                        -->
<!-- Effective POM for project                                              -->
<!-- 'com.flipkart.fintech:profile-service:jar:3.4.25-SM-LV4-SNAPSHOT'                   -->
<!--                                                                        -->
<!-- ====================================================================== -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.flipkart.fintech</groupId>
    <artifactId>pinaka</artifactId>
    <version>3.4.25-SM-LV4-SNAPSHOT</version>
  </parent>
  <groupId>com.flipkart.fintech</groupId>
  <artifactId>profile-service</artifactId>
  <version>3.4.25-SM-LV4-SNAPSHOT</version>
  <distributionManagement>
    <repository>
      <id>fk-art-release</id>
      <name>libs-rel</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </repository>
    <snapshotRepository>
      <id>fk-art-snapshot</id>
      <name>libs-snapshot</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <aapi.models.version>3.2.39-SM</aapi.models.version>
    <aapi.multiwidget.version>3.0.0-SM</aapi.multiwidget.version>
    <affordability.service.client.version>5.0.3-SM</affordability.service.client.version>
    <ardour.api.version>1.0.83</ardour.api.version>
    <ardour.client.version>1.0.83</ardour.client.version>
    <citadel.version>3.0.8-SM</citadel.version>
    <dataprovider.core.version>3.0.0-SM</dataprovider.core.version>
    <dropwizard.hibernate.version>2.1.12</dropwizard.hibernate.version>
    <dropwizard.swagger.version>2.1.4-1</dropwizard.swagger.version>
    <dropwizard.version>2.0.4</dropwizard.version>
    <events.common.version>1.0.11-JAVA-8</events.common.version>
    <fintech.logger.version>1.0.0</fintech.logger.version>
    <flux.version>1.2.0</flux.version>
    <guice.version>4.0</guice.version>
    <h2database.version>1.4.195</h2database.version>
    <helios.proxy.lib.version>1.0.3</helios.proxy.lib.version>
    <hystrix.javanica.version>1.5.18</hystrix.javanica.version>
    <jacoco.version>0.7.5.201505241946</jacoco.version>
    <jdk.version>1.8</jdk.version>
    <junit.version>4.11</junit.version>
    <liquibase.version>3.8.5</liquibase.version>
    <lombok.version>1.18.30</lombok.version>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <onboarding.client.version>1.5.16</onboarding.client.version>
    <pandora.version>1.5.8-SM</pandora.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <pub.sub.version>1.0.15-JAVA-8</pub.sub.version>
    <skipCT>true</skipCT>
    <underwriting.version>1.8.39</underwriting.version>
    <user.data.client.version>1.0-SNAPSHOT</user.data.client.version>
    <winterfell.version>1.2.1-SM-TEST-SNAPSHOT</winterfell.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java</artifactId>
        <version>3.25.2</version>
      </dependency>
      <dependency>
        <groupId>com.sumo.dataplatform</groupId>
        <artifactId>event-publisher</artifactId>
        <version>1.5.16</version>
      </dependency>
      <dependency>
        <groupId>com.sumo.dataplatform</groupId>
        <artifactId>schema-registry</artifactId>
        <version>1.5.16</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-pubsub</artifactId>
        <version>1.132.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.avro</groupId>
        <artifactId>avro</artifactId>
        <version>1.11.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.avro</groupId>
        <artifactId>avro-maven-plugin</artifactId>
        <version>1.11.3</version>
      </dependency>
      <dependency>
        <groupId>com.flipkart.fpg</groupId>
        <artifactId>dexter-api</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>5.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-api</artifactId>
        <version>5.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>5.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-migrationsupport</artifactId>
        <version>5.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-params</artifactId>
        <version>5.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-commons</artifactId>
        <version>1.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-console</artifactId>
        <version>1.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-engine</artifactId>
        <version>1.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-jfr</artifactId>
        <version>1.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-launcher</artifactId>
        <version>1.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-reporting</artifactId>
        <version>1.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-runner</artifactId>
        <version>1.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite-api</artifactId>
        <version>1.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-testkit</artifactId>
        <version>1.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.vintage</groupId>
        <artifactId>junit-vintage-engine</artifactId>
        <version>5.7.2</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.15.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.glassfish.hk2.external</groupId>
      <artifactId>javax.inject</artifactId>
      <version>2.5.0-b32</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>32.1.3-jre</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.ws.rs</groupId>
      <artifactId>jakarta.ws.rs-api</artifactId>
      <version>2.1.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.dropwizard.metrics</groupId>
      <artifactId>metrics-annotation</artifactId>
      <version>4.1.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>1.6.9</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.dropwizard</groupId>
      <artifactId>dropwizard-hibernate</artifactId>
      <version>2.1.12</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>net.bytebuddy</groupId>
          <artifactId>byte-buddy</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.flipkart.affordability</groupId>
      <artifactId>affordability-service-clients</artifactId>
      <version>5.0.3-SM</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.flipkart.rome</groupId>
          <artifactId>*</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mockito</groupId>
          <artifactId>mockito-all</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mockito</groupId>
          <artifactId>mockito-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.flipkart.fintech</groupId>
          <artifactId>pandora-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>fintech-logger</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
      <version>4.2.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-xml</artifactId>
      <version>2.15.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>fintech-cryptex-bundle</artifactId>
      <version>1.1.3-SM-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-core</artifactId>
      <version>5.4.12.Final</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>javax.ws.rs</groupId>
      <artifactId>javax.ws.rs-api</artifactId>
      <version>2.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.netflix.hystrix</groupId>
      <artifactId>hystrix-javanica</artifactId>
      <version>1.5.18</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.apache.commons</groupId>
          <artifactId>commons-lang3</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.24</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.13.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>pandora-client</artifactId>
      <version>1.5.8-SM</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>pandora-service-client</artifactId>
      <version>1.5.8-SM</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>profile-api</artifactId>
      <version>3.4.25-SM-LV4-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>user-data-client</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech.pinaka.library</groupId>
      <artifactId>pinaka-library</artifactId>
      <version>3.4.14-SM</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.sumo.crisys</groupId>
      <artifactId>crisys-client</artifactId>
      <version>1.0.16</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>jakarta.persistence</groupId>
          <artifactId>jakarta.persistence-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.glassfish.jersey.media</groupId>
          <artifactId>jersey-media-json-jackson</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>resilience</artifactId>
      <version>1.0.0-SM</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.supermoney.google.clients</groupId>
      <artifactId>bigtable-client</artifactId>
      <version>1.0.4-JAVA8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>citadel-client</artifactId>
      <version>2.0.10-SM</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.cloud</groupId>
      <artifactId>google-cloud-pubsub</artifactId>
      <version>1.132.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.sumo.dataplatform</groupId>
      <artifactId>event-publisher</artifactId>
      <version>1.5.16</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>winterfell-client</artifactId>
      <version>1.2.1-SM-TEST-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>winterfell-api</artifactId>
      <version>1.2.1-SM-TEST-SNAPSHOT</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.hibernate</groupId>
          <artifactId>hibernate-validator</artifactId>
        </exclusion>
        <exclusion>
          <groupId>lombok</groupId>
          <artifactId>org.projectlombok</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.flipkart.upi</groupId>
      <artifactId>user-api-models</artifactId>
      <version>2.17-SM</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>lombok</groupId>
          <artifactId>org.projectlombok</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.glassfish.jersey.media</groupId>
          <artifactId>jersey-media-json-jackson</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testng</groupId>
      <artifactId>testng</artifactId>
      <version>6.9.8</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.supermoney.commons.pubsub.client</groupId>
      <artifactId>pub-sub-client</artifactId>
      <version>1.0.15-JAVA-8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>events-commons</groupId>
      <artifactId>event-commons</artifactId>
      <version>1.0.11-JAVA-8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>4.0.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.7.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <version>5.7.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>fk-art-snapshot</id>
      <name>Flipkart-Artifactory</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </repository>
    <repository>
      <id>fk-art-release</id>
      <name>Flipkart-Artifactory</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </repository>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Central Repository</name>
      <url>https://repo.maven.apache.org/maven2</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <id>fk-art-snapshot</id>
      <name>libs-snapshot</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </pluginRepository>
    <pluginRepository>
      <id>fk-art-release</id>
      <name>libs-rel</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </pluginRepository>
    <pluginRepository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Central Repository</name>
      <url>https://repo.maven.apache.org/maven2</url>
    </pluginRepository>
  </pluginRepositories>
  <build>
    <sourceDirectory>/Users/<USER>/projects/sm-pinaka/profile-service/src/main/java</sourceDirectory>
    <scriptSourceDirectory>/Users/<USER>/projects/sm-pinaka/profile-service/src/main/scripts</scriptSourceDirectory>
    <testSourceDirectory>/Users/<USER>/projects/sm-pinaka/profile-service/src/test/java</testSourceDirectory>
    <outputDirectory>/Users/<USER>/projects/sm-pinaka/profile-service/target/classes</outputDirectory>
    <testOutputDirectory>/Users/<USER>/projects/sm-pinaka/profile-service/target/test-classes</testOutputDirectory>
    <resources>
      <resource>
        <directory>/Users/<USER>/projects/sm-pinaka/profile-service/src/main/resources</directory>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>/Users/<USER>/projects/sm-pinaka/profile-service/src/test/resources</directory>
      </testResource>
    </testResources>
    <directory>/Users/<USER>/projects/sm-pinaka/profile-service/target</directory>
    <finalName>profile-service-3.4.25-SM-LV4-SNAPSHOT</finalName>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.7.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.7.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.5.1</version>
        <executions>
          <execution>
            <id>default-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <source>1.8</source>
              <target>1.8</target>
            </configuration>
          </execution>
          <execution>
            <id>default-testCompile</id>
            <phase>test-compile</phase>
            <goals>
              <goal>testCompile</goal>
            </goals>
            <configuration>
              <source>1.8</source>
              <target>1.8</target>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.3.0</version>
        <executions>
          <execution>
            <id>check-logging-imports</id>
            <phase>process-sources</phase>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <RestrictImports>
                  <reason>Use @CustomLog for logging</reason>
                  <bannedImports>
                    <bannedImport>lombok.extern.slf4j.*</bannedImport>
                    <bannedImport>org.slf4j.Logger</bannedImport>
                  </bannedImports>
                </RestrictImports>
              </rules>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>de.skuzzle.enforcer</groupId>
            <artifactId>restrict-imports-enforcer-rule</artifactId>
            <version>2.1.0</version>
            <scope>compile</scope>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.7.5.201505241946</version>
        <executions>
          <execution>
            <id>prepare-agent</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
          <execution>
            <id>post-unit-test</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
            <configuration>
              <dataFile>target/jacoco.exec</dataFile>
              <outputDirectory>target/site/jacoco-ut</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>versions-maven-plugin</artifactId>
        <version>2.7</version>
      </plugin>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <id>default-clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.3.1</version>
        <executions>
          <execution>
            <id>default-testResources</id>
            <phase>process-test-resources</phase>
            <goals>
              <goal>testResources</goal>
            </goals>
          </execution>
          <execution>
            <id>default-resources</id>
            <phase>process-resources</phase>
            <goals>
              <goal>resources</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.4.1</version>
        <executions>
          <execution>
            <id>default-jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.2.5</version>
        <executions>
          <execution>
            <id>default-test</id>
            <phase>test</phase>
            <goals>
              <goal>test</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-install-plugin</artifactId>
        <version>3.1.2</version>
        <executions>
          <execution>
            <id>default-install</id>
            <phase>install</phase>
            <goals>
              <goal>install</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>3.1.2</version>
        <executions>
          <execution>
            <id>default-deploy</id>
            <phase>deploy</phase>
            <goals>
              <goal>deploy</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.12.1</version>
        <executions>
          <execution>
            <id>default-site</id>
            <phase>site</phase>
            <goals>
              <goal>site</goal>
            </goals>
            <configuration>
              <outputDirectory>/Users/<USER>/projects/sm-pinaka/profile-service/target/site</outputDirectory>
              <reportPlugins>
                <reportPlugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-project-info-reports-plugin</artifactId>
                </reportPlugin>
              </reportPlugins>
            </configuration>
          </execution>
          <execution>
            <id>default-deploy</id>
            <phase>site-deploy</phase>
            <goals>
              <goal>deploy</goal>
            </goals>
            <configuration>
              <outputDirectory>/Users/<USER>/projects/sm-pinaka/profile-service/target/site</outputDirectory>
              <reportPlugins>
                <reportPlugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-project-info-reports-plugin</artifactId>
                </reportPlugin>
              </reportPlugins>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <outputDirectory>/Users/<USER>/projects/sm-pinaka/profile-service/target/site</outputDirectory>
          <reportPlugins>
            <reportPlugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-project-info-reports-plugin</artifactId>
            </reportPlugin>
          </reportPlugins>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <outputDirectory>/Users/<USER>/projects/sm-pinaka/profile-service/target/site</outputDirectory>
  </reporting>
</project>
