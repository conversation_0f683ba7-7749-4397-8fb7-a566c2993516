package com.flipkart.fintech.profile.module;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.affordability.clients.loginservice.LoginServiceClient;
import com.flipkart.affordability.clients.loginservice.LoginServiceClientImpl;
import com.flipkart.affordability.clients.oauth.MultiTenantOAuthClientImpl;
import com.flipkart.affordability.clients.userservice.USClient;
import com.flipkart.affordability.clients.userservice.USClientImpl;
import com.flipkart.affordability.config.LoginServiceClientConfig;
import com.flipkart.affordability.config.MultiTenantOAuthClientConfig;
import com.flipkart.affordability.config.UserServiceClientConfig;
import com.flipkart.fintech.pinaka.library.module.FeatureLibraryModule;
import com.flipkart.fintech.profile.Decrypter.Decrypter;
import com.flipkart.fintech.profile.config.*;
import com.flipkart.fintech.profile.dao.*;
import com.flipkart.fintech.profile.pubsub.PubsubPublisher;
import com.flipkart.fintech.profile.service.*;
import com.flipkart.fintech.profile.service.ProfileService;
import com.flipkart.fintech.profile.service.ProfileServiceImpl;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.user.data.client.UserDataClientImpl;
import com.flipkart.fintech.user.data.client.merchant.FkUserServiceClient;
import com.flipkart.fintech.user.data.client.merchant.SmUserServiceClient;
import com.flipkart.fintech.user.data.config.SmUserServiceClientConfig;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.supermoney.commons.pubsub.client.config.PublisherConfig;
import com.supermoney.google.clients.bigtable.client.config.BigtableConfig;
import com.sumo.infosys.retry.module.RetryModule;
import com.supermoney.google.clients.bigtable.client.module.BigtableClientModule;
import com.supermoney.google.clients.bigtable.client.registry.BigtableClientRegistry;
import lombok.CustomLog;
import com.flipkart.fintech.pandora.client.CheckBureauScoreClient;
import com.flipkart.fintech.pandora.client.PandoraClientConfiguration;
import com.flipkart.fintech.profile.config.ProfileServiceConfig;
import com.flipkart.fintech.profile.yak.BureauDataStore;
import org.glassfish.jersey.client.ClientProperties;
import org.hibernate.SessionFactory;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.WebTarget;
import java.io.IOException;


@CustomLog
public class ProfileServiceModule extends AbstractModule {
    private final UserServiceClientConfig userServiceClientConfig;
    private final LoginServiceClientConfig loginServiceClientConfig;
    private final MultiTenantOAuthClientConfig multiTenantOAuthClientConfig;
    private final SmUserServiceClientConfig smUserServiceClientConfig;
    private final Client client;
    private final SessionFactory sessionFactory;
    private final ProfileServiceConfig profileServiceConfig;
    private final MetricRegistry metricRegistry;
    private final BigtableConfig bigTableConfig;
    private final String CREDIT_REPORT_TABLE = "credit-report";
    private final PublisherConfig publisherConfig;

     public ProfileServiceModule(UserServiceClientConfig userServiceClientConfig, LoginServiceClientConfig loginServiceClientConfig,
                                 MultiTenantOAuthClientConfig multiTenantOAuthClientConfig, Client client,
                                 SessionFactory sessionFactory, SmUserServiceClientConfig smUserServiceClientConfig, ProfileServiceConfig profileServiceConfig,
                                 MetricRegistry metricRegistry, BigtableConfig bigTableConfig, PublisherConfig publisherConfig) {
        this.userServiceClientConfig = userServiceClientConfig;
        this.loginServiceClientConfig = loginServiceClientConfig;
        this.multiTenantOAuthClientConfig = multiTenantOAuthClientConfig;
        this.profileServiceConfig = profileServiceConfig;
        this.client = client;
        this.sessionFactory = sessionFactory;
        this.smUserServiceClientConfig = smUserServiceClientConfig;
        this.metricRegistry = metricRegistry;
        this.bigTableConfig = bigTableConfig;
         this.publisherConfig = publisherConfig;
     }

    @Override
    protected void configure() {
        install(new RetryModule());
//        Not needed post migration
//        System.setProperty("HADOOP_USER_NAME", profileServiceConfig.getYakDbConfig().getHadoopUserName());
        bind(ProfileDao.class).to(ProfileDaoImpl.class);
        bind(BureauDataService.class).to(ExperianBureauDataServiceImpl.class);
        bind(BureauDataManager.class).to(BureauDataManagerImpl.class);
        bind(ProfileServiceConfig.class).toInstance(profileServiceConfig);
        bind(PublisherConfig.class).toInstance(publisherConfig);
        bind(BureauInsights.class).to(BureauInsightsService.class);
        install(new FeatureLibraryModule(metricRegistry));
        install(new BigtableClientModule(bigTableConfig));
    }

    @Provides
    @Singleton
    public ExperianConfig providesExperianConfig() {
        return profileServiceConfig.getExperianConfig();
    }

    @Provides
    @Singleton
    @JsonProperty(value = "smUserServiceClientConfig")
    public SmUserServiceClientConfig providesSmUserServiceClientConfig() {
         return profileServiceConfig.getSmUserServiceClientConfig();
    }

    @Provides
    @Singleton
    public UserDataClient providesUserDataClient() {
        MultiTenantOAuthClientImpl multiTenantOAuthClient = new MultiTenantOAuthClientImpl(multiTenantOAuthClientConfig, client);
        LoginServiceClient loginServiceClient = new LoginServiceClientImpl(loginServiceClientConfig, client, multiTenantOAuthClient);
        USClient usClient = new USClientImpl(userServiceClientConfig, client, multiTenantOAuthClient, loginServiceClient);
        SmUserServiceClient smUserServiceClient = new SmUserServiceClient(smUserServiceClientConfig, client);
        FkUserServiceClient fkUserServiceClient = new FkUserServiceClient(usClient, smUserServiceClientConfig, smUserServiceClient);
        return new UserDataClientImpl(fkUserServiceClient, smUserServiceClient);
    }

    @Provides
    @Singleton
    public ProfileService providesProfileService(UserDataClient userDataClient, Decrypter decrypter) {
        return new ProfileServiceImpl(userDataClient, providesMetricRegistryForProfileService(), decrypter,
                new ProfileDaoImpl(sessionFactory, metricRegistry),new AddressDetailsDao(sessionFactory),
                new EmploymentDetailsDao(sessionFactory), new BasicDetailsDao(sessionFactory), new ContactDetailsDao(sessionFactory));
    }

    @Provides
    @Singleton
    private CheckBureauScoreClient provideBureauScoreClient(PandoraClientConfiguration config) {
        return new CheckBureauScoreClient(config);
    }

    @Provides
    @Singleton
    @Named("metricRegistryForProfileService")
    private MetricRegistry providesMetricRegistryForProfileService(){
         return metricRegistry;
    }


    @Provides
    @Named("bigTableCreditReportDao")
    public BureauDataStore getNewCreditReportDao(BigtableClientRegistry bigtableClientRegistry,ExperianFeatureService experianFeatureService) {
        return new NewCreditReportDaoImpl(bigtableClientRegistry.getClient(CREDIT_REPORT_TABLE), experianFeatureService);
    }

    @Provides
    @Singleton
    public PubsubPublisher providesPubsubPublisher() throws IOException {
         return new PubsubPublisher(profileServiceConfig.getPublisherConfig());
    }

    @Provides
    @Singleton
    @Named("UpiUserWebTarget")
    public WebTarget providesUpiUserClient(){
         Client client = ClientBuilder.newClient();
         client.property(ClientProperties.CONNECT_TIMEOUT, 5000);
         client.property(ClientProperties.READ_TIMEOUT, 5000);
         return client.target(profileServiceConfig.getUpiUserSvcConfig().getUrl());
    }

}
