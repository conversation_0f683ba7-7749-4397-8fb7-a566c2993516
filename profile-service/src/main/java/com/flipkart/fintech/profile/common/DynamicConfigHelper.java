package com.flipkart.fintech.profile.common;

import com.flipkart.kloud.config.DynamicBucket;

import java.util.List;

public class DynamicConfigHelper {
    public static boolean getBoolean(DynamicBucket dynamicBucket, String key, Boolean defaultValue) {
        if (dynamicBucket != null) {
            Boolean value = dynamicBucket.getBoolean(key);
            return value != null ? value : defaultValue;
        } else {
            return defaultValue;
        }
    }

    public static String getString(DynamicBucket dynamicBucket, String key, String defaultValue) {
        if (dynamicBucket != null) {
            String value = dynamicBucket.getString(key);
            return value != null ? value : defaultValue;
        } else {
            return defaultValue;
        }
    }

    public static List<String> getStringArray(DynamicBucket dynamicBucket, String key, List<String> defaultValue) {
        List<String> value = dynamicBucket.getStringArray(key);
        return value == null ? defaultValue : value;
    }
}
