package com.flipkart.fintech.profile.model;


import lombok.*;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Comparator;
import java.util.List;

@Entity
@Table(name = "profile")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Profile implements Serializable {
    @OneToMany(mappedBy = "profile", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<AddressDetails> addressDetailsList;

    public BasicDetails getBasicDetails() {
        return basicDetails;
    }
    public void setBasicDetails(BasicDetails basicDetails) {
        this.basicDetails = basicDetails;
    }
    public void setEmploymentDetails(EmploymentDetails employmentDetails) {
        this.employmentDetails = employmentDetails;
    }
    public void setContactDetails(ContactDetails contactDetails) {
        this.contactDetails = contactDetails;
    }

    public EmploymentDetails getEmploymentDetails() {
        return employmentDetails;
    }

    //TODO: WARNING!! THERE IS A 1-N mapping between Profile and ContactDetails, not a 1-1.
    // PROCEED WITH CAUTION
    public ContactDetails getContactDetails(){ return contactDetails;}

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "profile_id")
    private Long profileId;

    @Column(name = "user_id")
    private String userId;

    @Setter
    @Column(name = "sm_user_id")
    private String smUserId;

    public Long getProfileId() {
        return profileId;
    }

    public void setProfileId(Long profileId) {
        this.profileId = profileId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getSmUserId() {
        return smUserId;
    }

    public void setSmUserId(String smUserId) {
        this.smUserId = smUserId;
    }

    @Column(name = "pan")
    private String pan;

    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "profile_id", nullable = false, foreignKey = @ForeignKey(name = "profile_id"))
    private BasicDetails basicDetails;

    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "profile_id", nullable = false, foreignKey = @ForeignKey(name = "profile_id"))
    private EmploymentDetails employmentDetails;
    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "profile_id", nullable = false, foreignKey = @ForeignKey(name = "profile_id"))
    private ContactDetails contactDetails;


    public List<AddressDetails> getAddressDetailsList() {
        return addressDetailsList;
    }

    public AddressDetails getLatestAddress() {
        if(addressDetailsList.isEmpty()){
            return null;
        }
        return addressDetailsList.stream()
                .max(Comparator.comparing(AddressDetails::getCreatedAt, Comparator.nullsFirst(Comparator.naturalOrder())))
                .orElse(null);
    }

    public void setAddressDetailsList(List<AddressDetails> addressDetailsList) {
        this.addressDetailsList = addressDetailsList;
    }
}