package com.flipkart.fintech.profile.pubsub;

import com.google.inject.Inject;
import com.supermoney.commons.pubsub.client.config.PublisherConfig;
import com.supermoney.commons.pubsub.client.publisher.PubSubPublisherV2;
import com.supermoney.commons.pubsub.model.PubsubEvent;
import com.supermoney.events.common.Event;
import com.supermoney.events.common.domainevents.creditscore.CreditScoreEvent;
import lombok.CustomLog;

import java.io.IOException;
import java.util.HashMap;

@CustomLog
public class CreditScorePublisher {
    private final PubSubPublisherV2<Event> pubSubPublisherV2;

    @Inject
    public CreditScorePublisher(PublisherConfig publisherConfig) throws IOException {
        this.pubSubPublisherV2 = new PubSubPublisherV2<>(publisherConfig);
    }

    public void publish(CreditScoreEvent event) {
        PubsubEvent<Event> pubsubEvent = null;
        try {
            if (event.getMetadata() == null) {
                event.setMetadata(new HashMap<>());
            }

            pubsubEvent = PubsubEvent.builder()
                    .orderingKey(event.getSmUserId())
                    .payload(event)
                    .attributes(new HashMap<>())
                    .build();

            this.pubSubPublisherV2.publishSync(pubsubEvent);
        } catch (Exception e) {
            log.error("got error while publishing pubsub event - {}", pubsubEvent);
            throw e;
        }
    }
}
