package com.flipkart.fintech.profile.yak.entities;


import com.flipkart.fintech.profile.bureau.models.Consent;
import com.flipkart.fintech.profile.bureau.models.CreditScore;
import com.flipkart.fintech.profile.bureau.models.ExperianBureauResponse;
import com.flipkart.fintech.yak.annotation.Column;
import com.flipkart.fintech.yak.annotation.Table;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 05/02/24
 */
@Data
public class BureauDataEntity extends BaseEntity {

//  Version is basically timestamp to identify the latest data

    @Column(family = "bureau_raw", name = "experian_raw_bureau_data")
    private ExperianBureauResponse experianBureauResponse;

    @Column(family = "bureau_insights", name = "experian_consent")
    private Consent experianConsent;

    @Column(family = "bureau_insights", name = "experian_credit_score")
    private CreditScore creditScore;
    public BureauDataEntity(){
    }
    public BureauDataEntity(String id){super(id);}
}
