package com.flipkart.fintech.profile.service;

import com.flipkart.fintech.profile.request.ProfileRequest;
import com.flipkart.fintech.profile.response.ProfileBasicDetailResponse;
import com.flipkart.fintech.profile.response.ProfileCRUDResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;

public interface ProfileService {

    ProfileCRUDResponse createProfile(ProfileRequest profileRequest);
    ProfileDetailedResponse getProfileById(Long profileId);
    ProfileDetailedResponse getProfileByUserId(String userId, String smUserId, boolean isUnmaskedData);
    ProfileBasicDetailResponse getProfile(final String smUserId);
}
