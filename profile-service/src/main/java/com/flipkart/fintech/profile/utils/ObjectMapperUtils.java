package com.flipkart.fintech.profile.utils;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;


public class ObjectMapperUtils {
    private static final ObjectMapper snakeCaseObjectMapper;

    static {
        snakeCaseObjectMapper = new ObjectMapper();
        snakeCaseObjectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        snakeCaseObjectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);

    }


    public static ObjectMapper getSnakeCaseObjectMapper() {
        return snakeCaseObjectMapper;
    }
}
