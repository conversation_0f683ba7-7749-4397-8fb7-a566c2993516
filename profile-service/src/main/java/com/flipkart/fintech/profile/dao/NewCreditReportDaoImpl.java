package com.flipkart.fintech.profile.dao;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.profile.bureau.models.Consent;
import com.flipkart.fintech.profile.bureau.models.ExperianBureauResponse;
import com.flipkart.fintech.profile.bureau.utils.ErrorCode;
import com.flipkart.fintech.profile.bureau.utils.ProfileUtils;
import com.flipkart.fintech.profile.common.PinakaMetricRegistry;
import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;
import com.flipkart.fintech.profile.service.ExperianFeatureService;
import com.flipkart.fintech.profile.yak.BureauDataStore;
import com.flipkart.fintech.profile.yak.entities.BureauDataEntity;
import com.supermoney.google.clients.bigtable.client.BigtableClient;
import com.supermoney.google.clients.bigtable.client.models.ColumnFamily;
import com.supermoney.google.clients.bigtable.client.models.Row;
import com.supermoney.google.clients.bigtable.client.models.RowData;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import java.time.Instant;
import java.util.*;

@CustomLog
public class NewCreditReportDaoImpl implements BureauDataStore {

    private final BigtableClient bigtableClient;
    private final MetricRegistry metricsRegistry;
    private final ObjectMapper objectMapper;
    private final ExperianFeatureService experianFeatureService;

    private static final String RAW_REPORT_COLUMN_FAMILY = "report";
    private static final String RAW_REPORT_COLUMN_QUALIFIER = "report";
    private static final String CONSENT_COLUMN_FAMILY = "consent";
    private static final String CONSENT_COLUMN_QUALIFIER = "consent";
    private static final String MERCHANT_COLUMN_QUALIFIER = "merchant";
    private static final String CROSS_MERCHANT_CONSENT_COLUMN_QUALIFIER = "cross_consent";
    private static final String DEFAULT_CROSS_MERCHANT_COLUMN_QUALIFIER = "cross_consent_merchant";
    private static final String EXPERIAN_ID_COLUMN_FAMILY = "experian_id";
    private static final String EXPERIAN_USER_ID_COLUMN_QUALIFIER = "experian_user_id";
    private static final String STAGE_ONE_ID_COLUMN_QUALIFIER = "stage_one_id";
    private static final String STAGE_TWO_ID_COLUMN_QUALIFIER = "stage_two_id";

    @Inject
    public NewCreditReportDaoImpl(BigtableClient bigtableClient, ExperianFeatureService experianFeatureService) {
        this.bigtableClient = bigtableClient;
        this.experianFeatureService = experianFeatureService;
        this.metricsRegistry = PinakaMetricRegistry.getMetricRegistry();
        this.objectMapper = new ObjectMapper();
    }


    @Override
    public void putExperianBureauData(BureauDataEntity bureauDataEntity, String accountId, Long createdTimestampInMillis) {
        try (Timer.Context timer = metricsRegistry.timer("ADD_BURAEUA_DATA_BIG_TABLE").time()) {
            RowData rowData = createRowData(bureauDataEntity, createdTimestampInMillis);
            bigtableClient.write(bureauDataEntity.getId(), rowData);
        } catch (Exception e) {
            metricsRegistry.meter("ADD_BURAEUA_DATA_BIG_TABLE_EXCEPTION").mark();
            log.error("getting error while inserting data in big table data for account id {}", bureauDataEntity.getId(), e);
            throw new ServiceException(ProfileUtils.getServiceErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR, e.getMessage()));
        }
    }

    @Override
    public void updateExperianCrossMerchantConsent(CrossMerchantConsentRequest crossMerchantConsentRequest, Long createdTimestampInMillis) {
        try (Timer.Context timer = metricsRegistry.timer("READ_BUREAU_DATA_BIG_TABLE").time()) {
            Row dataEntity = bigtableClient.read(crossMerchantConsentRequest.getAccountId());
            updateRowData(crossMerchantConsentRequest,createdTimestampInMillis, dataEntity.getRowData());
            bigtableClient.write(crossMerchantConsentRequest.getAccountId(),dataEntity.getRowData());
        } catch (Exception e) {
            metricsRegistry.meter("UPDATE_BUREAU_DATA_BIG_TABLE_EXCEPTION").mark();
            log.error("getting error while updating data from big table data for account {}", crossMerchantConsentRequest.getAccountId(), e);
            throw new ServiceException(ProfileUtils.getServiceErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR, e.getMessage()));
        }
    }

    @Override
    public boolean getIfExperianCrossMerchantConsentExists(String accountId) {
        Row row = bigtableClient.read(accountId);
        return checkIfCrossMerchantConsentExists(row, accountId);
    }

    @Override
    public void deleteExperianConsent(String accountId) {
        try (Timer.Context timer = metricsRegistry.timer("DELETE_BUREAU_DATA_CONSENT_BIG_TABLE").time()) {
            bigtableClient.delete(accountId, CONSENT_COLUMN_FAMILY, CONSENT_COLUMN_QUALIFIER);
        } catch (Exception e) {
            metricsRegistry.meter("DELETE_BUREAU_DATA_CONSENT_BIG_TABLE_EXCEPTION").mark();
            log.error("getting error while deleting consent from big table data for account {}", accountId, e);
            throw new ServiceException(ProfileUtils.getServiceErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR, e.getMessage()));
        }
    }

    @Override
    public BureauDataEntity getExperianBureauRawData(String profiledId, String accountId) {
        try (Timer.Context timer = metricsRegistry.timer("READ_BUREAU_DATA_BIG_TABLE").time()) {
            Row dataEntity = bigtableClient.read(accountId);
            return convertToBureauDataEntity(dataEntity, accountId);
        } catch (Exception e) {
            metricsRegistry.meter("READ_BUREAU_DATA_BIG_TABLE_EXCEPTION").mark();
            log.error("getting error while reading data from big table data for account {}", accountId, e);
            throw new ServiceException(ProfileUtils.getServiceErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR, e.getMessage()));
        }
    }

    private void updateRowData(CrossMerchantConsentRequest crossMerchantConsentRequest, Long createdTimestampInMillis, RowData rowData) throws JsonProcessingException {
        Map<String, ColumnFamily> columnFamilyMap = rowData.getColumnFamilyMap();

        if (Objects.isNull(columnFamilyMap)) {
            return;
        }

        String merchant = Objects.isNull(crossMerchantConsentRequest.getMerchant()) ? DEFAULT_CROSS_MERCHANT_COLUMN_QUALIFIER.toLowerCase() :  crossMerchantConsentRequest.getMerchant().toLowerCase();

        if (Objects.nonNull(crossMerchantConsentRequest.getConsent())){
            columnFamilyMap.put(CONSENT_COLUMN_FAMILY, createOrAddColumnQualifierEntity(columnFamilyMap.get(CONSENT_COLUMN_FAMILY),
                    CROSS_MERCHANT_CONSENT_COLUMN_QUALIFIER + "_" + merchant , objectMapper.writeValueAsString(crossMerchantConsentRequest.getConsent()), createdTimestampInMillis));
        }
    }

    private RowData createRowData(BureauDataEntity bureauDataEntity, Long createdTimestampInMillis) throws JsonProcessingException {
        RowData rowData = new RowData();
        Map<String, ColumnFamily> columnFamilyMap = new HashMap<>();

        if (Objects.nonNull(bureauDataEntity.getExperianBureauResponse())) {
            columnFamilyMap.put(RAW_REPORT_COLUMN_FAMILY, createOrAddColumnQualifierEntity(columnFamilyMap.get(RAW_REPORT_COLUMN_FAMILY),
                    RAW_REPORT_COLUMN_QUALIFIER, bureauDataEntity.getExperianBureauResponse().getShowHtmlReportForCreditReport(), createdTimestampInMillis));
        }
        if(Objects.nonNull(bureauDataEntity.getExperianBureauResponse().getExperianUserId())){
            columnFamilyMap.put(EXPERIAN_ID_COLUMN_FAMILY, createOrAddColumnQualifierEntity(columnFamilyMap.get(EXPERIAN_ID_COLUMN_FAMILY),
                    EXPERIAN_USER_ID_COLUMN_QUALIFIER, String.valueOf(bureauDataEntity.getExperianBureauResponse().getExperianUserId()), createdTimestampInMillis));
        }
        if(Objects.nonNull(bureauDataEntity.getExperianBureauResponse().getStgOneHitId())){
            columnFamilyMap.put(EXPERIAN_ID_COLUMN_FAMILY, createOrAddColumnQualifierEntity(columnFamilyMap.get(EXPERIAN_ID_COLUMN_FAMILY),
                    STAGE_ONE_ID_COLUMN_QUALIFIER, bureauDataEntity.getExperianBureauResponse().getStgOneHitId(), createdTimestampInMillis));
        }
        if(Objects.nonNull(bureauDataEntity.getExperianBureauResponse().getStgTwoHitId())){
            columnFamilyMap.put(EXPERIAN_ID_COLUMN_FAMILY, createOrAddColumnQualifierEntity(columnFamilyMap.get(EXPERIAN_ID_COLUMN_FAMILY),
                    STAGE_TWO_ID_COLUMN_QUALIFIER, bureauDataEntity.getExperianBureauResponse().getStgTwoHitId(), createdTimestampInMillis));
        }
        if (Objects.nonNull(bureauDataEntity.getMerchant())) {
            columnFamilyMap.put(RAW_REPORT_COLUMN_FAMILY, createOrAddColumnQualifierEntity(columnFamilyMap.get(RAW_REPORT_COLUMN_FAMILY),
                    MERCHANT_COLUMN_QUALIFIER, bureauDataEntity.getMerchant(), createdTimestampInMillis));
        }
        if (Objects.nonNull(bureauDataEntity.getExperianConsent())) {
            columnFamilyMap.put(CONSENT_COLUMN_FAMILY, createOrAddColumnQualifierEntity(columnFamilyMap.get(CONSENT_COLUMN_FAMILY),
                    CONSENT_COLUMN_QUALIFIER, objectMapper.writeValueAsString(bureauDataEntity.getExperianConsent()), createdTimestampInMillis));
        }
        rowData.setColumnFamilyMap(columnFamilyMap);
        return rowData;
    }

    private ColumnFamily createOrAddColumnQualifierEntity(ColumnFamily columnFamily, String qualifier, String value, Long createdTimestampInMillis) {
        if(columnFamily != null) {
            columnFamily.getColumns().put(qualifier, value);
            return columnFamily;
        }
        columnFamily = new ColumnFamily();
        Map<String, String> qualifierMap = new HashMap<>();
        qualifierMap.put(qualifier, value);
        columnFamily.setColumns(qualifierMap);
        if(Objects.nonNull(createdTimestampInMillis)) {
            columnFamily.setCreatedAtMillis(createdTimestampInMillis);
        }
        return columnFamily;
    }

    private BureauDataEntity convertToBureauDataEntity(Row row, String accountId) {
        BureauDataEntity bureauDataEntity = new BureauDataEntity();
        if (row == null) {
            return bureauDataEntity;
        }
        try {
            ExperianBureauResponse experianBureauResponse = new ExperianBureauResponse();
            Map<String, ColumnFamily> columnFamilyMap = row.getRowData().getColumnFamilyMap();
            if (columnFamilyMap == null) {
                return bureauDataEntity;
            }
            String rawReport = null;
            if (columnFamilyMap.containsKey(RAW_REPORT_COLUMN_FAMILY)) {
                rawReport = columnFamilyMap.get(RAW_REPORT_COLUMN_FAMILY).getColumns().get(RAW_REPORT_COLUMN_QUALIFIER);
                String merchant = columnFamilyMap.get(RAW_REPORT_COLUMN_FAMILY).getColumns().get(MERCHANT_COLUMN_QUALIFIER);
                Long createdAt = columnFamilyMap.get(RAW_REPORT_COLUMN_FAMILY).getCreatedAtMillis();
                experianBureauResponse.setShowHtmlReportForCreditReport(rawReport);
                experianBureauResponse.setCreatedAt(Date.from(Instant.ofEpochMilli(createdAt)));
                bureauDataEntity.setMerchant(merchant);
            }
            if(columnFamilyMap.containsKey(EXPERIAN_ID_COLUMN_FAMILY)){
                String stgOneHitId = null;
                String stgTwoHitId = null;
                Integer experianUserId = null;

                if(Objects.nonNull(columnFamilyMap.get(EXPERIAN_ID_COLUMN_FAMILY).getColumns().get(STAGE_ONE_ID_COLUMN_QUALIFIER)))
                    stgOneHitId = columnFamilyMap.get(EXPERIAN_ID_COLUMN_FAMILY).getColumns().get(STAGE_ONE_ID_COLUMN_QUALIFIER);

                if(Objects.nonNull(columnFamilyMap.get(EXPERIAN_ID_COLUMN_FAMILY).getColumns().get(STAGE_TWO_ID_COLUMN_QUALIFIER)))
                    stgTwoHitId = columnFamilyMap.get(EXPERIAN_ID_COLUMN_FAMILY).getColumns().get(STAGE_TWO_ID_COLUMN_QUALIFIER);

                if(Objects.nonNull(columnFamilyMap.get(EXPERIAN_ID_COLUMN_FAMILY).getColumns().get(EXPERIAN_USER_ID_COLUMN_QUALIFIER)))
                    experianUserId = Integer.valueOf(columnFamilyMap.get(EXPERIAN_ID_COLUMN_FAMILY).getColumns().get(EXPERIAN_USER_ID_COLUMN_QUALIFIER));

                experianBureauResponse.setExperianUserId(experianUserId);
                experianBureauResponse.setStgOneHitId(stgOneHitId);
                experianBureauResponse.setStgTwoHitId(stgTwoHitId);
            }
            if (columnFamilyMap.containsKey(CONSENT_COLUMN_FAMILY)) {
                String consentJson = columnFamilyMap.get(CONSENT_COLUMN_FAMILY).getColumns().get(CONSENT_COLUMN_QUALIFIER);
                bureauDataEntity.setExperianConsent(getConsentFromJson(consentJson));
            }
            bureauDataEntity.setExperianBureauResponse(experianBureauResponse);
            bureauDataEntity.setId(accountId);
        } catch (Exception e) {
            log.error("Error converting BigTable response {} to Bureau entity", row, e);
            throw new ServiceException(ProfileUtils.getServiceErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR, e.getMessage()));
        }

        return bureauDataEntity;
    }

    private boolean checkIfCrossMerchantConsentExists(Row row, String accountId){
        Map<String, ColumnFamily> columnFamilyMap = row.getRowData().getColumnFamilyMap();
        return columnFamilyMap.get(CONSENT_COLUMN_FAMILY).getColumns().keySet().stream().anyMatch(key -> key.startsWith(CROSS_MERCHANT_CONSENT_COLUMN_QUALIFIER));
    }


    private Consent getConsentFromJson(String consentJson) {
        try {
            if (StringUtils.isNotEmpty(consentJson)) {
                return objectMapper.readValue(consentJson, Consent.class);
            }
        } catch (Exception e) {
            log.error("getting error in json conversion input {}", consentJson);
        }
        return null;
    }

    @Override
    public BureauDataEntity getExperianInsight(String profileId, String accountId) {
        try {
            BureauDataEntity bureauDataEntity = getExperianBureauRawData(profileId, accountId);
            if (Objects.nonNull(bureauDataEntity) && Objects.nonNull(bureauDataEntity.getExperianBureauResponse())) {
                bureauDataEntity.setCreditScore(experianFeatureService.getCreditScoreFromReport(
                        bureauDataEntity.getExperianBureauResponse().getShowHtmlReportForCreditReport(), bureauDataEntity.getExperianBureauResponse().getCreatedAt()));
            }
            return bureauDataEntity;
        } catch (Exception e) {
            metricsRegistry.meter("EXPERIAN_INSIGHT_DATA_BIG_TABLE_EXCEPTION").mark();
            log.error("getting error while getting insights data in big table data for account id {}", accountId, e);
            throw new ServiceException(ProfileUtils.getServiceErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR, e.getMessage()));
        }
    }

}
