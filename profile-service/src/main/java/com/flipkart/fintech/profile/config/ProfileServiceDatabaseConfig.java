package com.flipkart.fintech.profile.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.cryptex.annotation.EncryptedConfig;
import io.dropwizard.db.DataSourceFactory;


@JsonIgnoreProperties(ignoreUnknown = true)
public class ProfileServiceDatabaseConfig extends DataSourceFactory {

    @EncryptedConfig(key = "encryptedUrl")
    private String encryptedUrl;
    @EncryptedConfig(key = "encryptedUser")
    private String encryptedUser;
    @EncryptedConfig(key = "encryptedPassword")
    private String encryptedPassword;

    public String getEncryptedUrl() {
        return encryptedUrl;
    }

    public void setEncryptedUrl(String encryptedUrl) {
        super.setUrl(encryptedUrl);
        this.encryptedUrl = encryptedUrl;
    }

    public String getEncryptedUser() {
        return encryptedUser;
    }

    public void setEncryptedUser(String encryptedUser) {
        this.encryptedUser = encryptedUser;
    }

    public String getEncryptedPassword() {
        return encryptedPassword;
    }

    public void setEncryptedPassword(String encryptedPassword) {
        this.encryptedPassword = encryptedPassword;
    }
}
