package com.flipkart.fintech.profile.service;

import com.codahale.metrics.MetricRegistry;
import com.flipkart.fintech.pandora.client.CheckBureauScoreClient;
import com.flipkart.fintech.pinaka.library.ReportContext;
import com.flipkart.fintech.pinaka.library.ReportFeatureCalculator;
import com.flipkart.fintech.pinaka.library.entities.ErrorEnum;
import com.flipkart.fintech.pinaka.library.entities.ExperianReport;
import com.flipkart.fintech.profile.Decrypter.Decrypter;
import com.flipkart.fintech.profile.api.request.BureauRequest;
import com.flipkart.fintech.profile.api.request.RefreshBureauRequest;
import com.flipkart.fintech.profile.api.response.BureauResponse;
import com.flipkart.fintech.profile.bureau.models.*;
import com.flipkart.fintech.profile.bureau.utils.BureauUtils;
import com.flipkart.fintech.profile.common.PinakaMetricRegistry;
import com.flipkart.fintech.profile.config.ProfileServiceConfig;
import com.flipkart.fintech.profile.model.ExperianBureauDataDto;
import com.flipkart.fintech.profile.model.ReportFetchSource;
import com.flipkart.fintech.profile.pubsub.CreditScorePublisher;
import com.flipkart.fintech.profile.request.BureauDataRequest;
import com.flipkart.fintech.profile.request.BureauDataRequestSm;
import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;
import com.flipkart.fintech.profile.request.ProfileRequest;
import com.flipkart.fintech.profile.response.*;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.user.data.models.UserData;
import com.flipkart.fintech.user.data.models.enums.Merchant;
import com.flipkart.fintech.user.data.models.enums.PIIDataType;
import com.flipkart.kloud.config.DynamicBucket;
import com.sumo.infosys.retry.annotations.Retry;

import static com.flipkart.fintech.profile.Constants.*;

import com.supermoney.events.common.domainevents.creditscore.CreditScoreEvent;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.flipkart.fintech.pinaka.library.ReportType.EXPERIAN_REPORT;
import static com.flipkart.fintech.profile.helper.BureauDataManagerHelper.*;

@CustomLog
@RequiredArgsConstructor(onConstructor_ = @__(@Inject))
public class BureauDataManagerImpl implements BureauDataManager {
    private final ProfileService profileService;
    private final CheckBureauScoreClient checkBureauScoreClient;
    private final BureauDataService bureauDataService;
    private final ExperianFeatureService experianFeatureService;
    private final ReportFeatureCalculator reportFeatureCalculator;
    private final BureauInsights bureauInsights;
    private final UserDataClient userDataClient;
    private final ProfileServiceConfig profileServiceConfig;
    private final Decrypter decrypter;

    @Override
    public BureauDataResponse getBureauData(BureauDataRequest bureauDataRequest) {
        BureauDataResponse bureauDataResponse = new BureauDataResponse();
        bureauDataResponse.setAccountTypeMap(new HashMap<>());
        bureauDataResponse.setLenders(new ArrayList<>());

        try {

            ProfileDetailedResponse profile = checkProfileExistOrCreate(bureauDataRequest, bureauDataResponse);
            if (Objects.isNull(profile.getProfileId()) && Objects.nonNull(profile.getPhoneNo())) {
                profile = createProfileAndGetDetails(bureauDataRequest);
            }
            buildBureauRequestAndGetDataFromExperian(bureauDataRequest, profile, bureauDataResponse);
        } catch (Exception e) {
            log.error("getting error for check score request {} exception {}", bureauDataRequest, e.getMessage());
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(BureauDataManagerImpl.class, "getBureauData", "failed")).mark();
            throw new ProfileException(e.getMessage());
        }
        return bureauDataResponse;
    }

    @Override
    public void addBureauData(ExperianBureauDataDto experianBureauDataDto, String smUserId, String merchantKey) {
        bureauDataService.addBureauData(experianBureauDataDto, smUserId, merchantKey);
    }

    @Override
    public BureauDataResponse getRefreshBureauData(String merchantUserId, String smUserId) {
        try {

            BureauDataResponse bureauDataResponse = new BureauDataResponse();
            bureauDataResponse.setAccountTypeMap(new HashMap<>());
            bureauDataResponse.setLenders(new ArrayList<>());
            ProfileDetailedResponse profile = profileService.getProfileByUserId(merchantUserId, smUserId, false);
            ExperianBureauDataDto experianBureauDataDto = bureauDataService.getBureauInsight("", smUserId);
            if (Objects.isNull(experianBureauDataDto)) {
                return buildBureauDataNotExist(bureauDataResponse);
            }
            if (Objects.isNull(experianBureauDataDto.getConsent()) || !isConsentValidForRefreshScore(experianBureauDataDto.getConsent(), profileServiceConfig.getExperianConfig().getTtl())) {
                return buildBureauDataResponseForFailure(bureauDataResponse);
            }

            BureauResponse bureauResponse;

            BureauRequest bureauRequest;
            if (Objects.isNull(experianBureauDataDto.getExperianBureauResponse()) ||
                    Objects.isNull(experianBureauDataDto.getExperianBureauResponse().getStgOneHitId())) {
                if (!Objects.isNull(profile.getPan())) {
                    bureauRequest = buildBureauRequestFromProfile(profile);
                } else {
                    bureauRequest = buildBureauRequestSm(smUserId);
                }
                bureauResponse = getBureauResponseFromExperian(bureauRequest);
            } else {
                RefreshBureauRequest refreshBureauRequest = buildRefreshBureauRequest(experianBureauDataDto.getExperianBureauResponse().getStgOneHitId());
                bureauResponse = getBureauResponseForRefresh(refreshBureauRequest);
            }
            if (!Objects.isNull(bureauResponse.getErrorMsg())) {
                return buildBureauDataForMismatch(bureauDataResponse);
            }
            buildBureauDataDtoAndAddBureauData(bureauResponse, smUserId);
            experianFeatureService.createAndStoreExperianFeature(bureauResponse.getRawData(),
                    getReportDetails(merchantUserId, smUserId, "", bureauResponse.getId(), ReportFetchSource.CHECK_SCORE, null), bureauResponse.getErrorMsg(), null);
            return buildBureauDataResponseForSuccess(bureauDataResponse, getCreditScore(bureauResponse.getRawData()), new Date(), bureauResponse.getRawData(), experianBureauDataDto.getMerchant());
        } catch (Exception e) {
            log.error("getting error for refresh check score smUserId {} exception {}", smUserId, e.getMessage());
            throw new ProfileException(e.getMessage());
        }
    }

    @Override
    public BureauDataResponse getRefreshBureauDataSm(String smUserId) {
        try {
            BureauDataResponse bureauDataResponse = new BureauDataResponse();
            bureauDataResponse.setAccountTypeMap(new HashMap<>());
            bureauDataResponse.setLenders(new ArrayList<>());
            ExperianBureauDataDto experianBureauDataDto = bureauDataService.getBureauInsight(null, smUserId);
            if (Objects.isNull(experianBureauDataDto)) {
                return buildBureauDataNotExist(bureauDataResponse);
            }
            if (!isConsentValidForRefreshScore(experianBureauDataDto.getConsent(), profileServiceConfig.getExperianConfig().getTtl())) {
                return buildBureauDataResponseForFailure(bureauDataResponse);
            }
            buildBureauRequestAndGetDataFromExperianSm(smUserId, bureauDataResponse, experianBureauDataDto);
            bureauDataResponse.setConsentValid(true);
            bureauDataResponse.setCrossMerchantConsentExists(bureauDataService.getIfExperianCrossMerchantConsentExists(smUserId));
            return bureauDataResponse;

        } catch (Exception e) {
            log.error("getting error for refresh check score in sm app smUserId {} exception {}", smUserId, e.getMessage());
            throw new ProfileException(e.getMessage());
        }
    }

    @Override
    public CrossMerchantConsentResponse updateCrossMerchantConsent(CrossMerchantConsentRequest request) {
        try {
            bureauDataService.updateExperianCrossMerchantConsent(request, null);
            return buildUpdateConsentResponseForSuccess();
        } catch (Exception e) {
            log.error("Could not update cross merchant consent , {}", e.getMessage());
            return buildUpdateConsentResponseForFailure(e.getMessage());
        }
    }

    @Override
    public BureauDataResponse backfillEventData(String smUserId) {
        BureauDataResponse bureauDataResponse = new BureauDataResponse();
        bureauDataResponse.setAccountTypeMap(new HashMap<>());
        bureauDataResponse.setLenders(new ArrayList<>());

        ExperianBureauDataDto bureauDataDto = checkConsentValidityAndRetrieveBureauDataForExistingCreditScore(null, smUserId.toString());
        if (Objects.isNull(bureauDataDto)) {
            return buildBureauDataNotExist(bureauDataResponse);
        }

        experianFeatureService.createAndStoreExperianFeature(bureauDataDto.getExperianBureauResponse().getShowHtmlReportForCreditReport(),
                getReportDetails(smUserId, smUserId, null, bureauDataDto.getId(), ReportFetchSource.CHECK_SCORE_SUPERMONEY, null), null, null);

        return buildBureauDataExist(bureauDataResponse, bureauDataDto.getCreditScore(), smUserId, bureauDataDto);

    }

    @Override
    public InitialUserDataResponse initialUserData(String smUserId) {
        try {
            ExperianBureauDataDto experianBureauDataDto = checkConsentValidityAndRetrieveBureauDataForExistingCreditScore(null, smUserId);
            if (Objects.isNull(experianBureauDataDto)) {
                log.error("Not Found Experian Bureau Data for SmUserId: {}", smUserId);
                return InitialUserDataResponse.builder().build();
            }
            ExperianReport report = getParsedReport(experianBureauDataDto.getExperianBureauResponse().getShowHtmlReportForCreditReport());
            InitialUserDataResponse initialUserDataResponse = bureauInsights.fetchUserInitialData(report);
            return initialUserDataResponse;
        } catch (Exception e) {
            log.info("getting error in inititial user data for smUserId {} exception {}", smUserId, e.getMessage());
            throw new ProfileException(e.getMessage());
        }
    }

    @Override
    public DeleteBureauConsentResponse deleteBureauConsent(String smUserId) {
        try {
            bureauDataService.deleteExperianConsent(smUserId);
            return buildDeleteBureauConsentResponse(null);
        } catch (Exception e) {
            log.error("Error deleting bureau consent for accountId because : {} {}", smUserId, e.getMessage());
            return buildDeleteBureauConsentResponse(e.getMessage());
        }
    }


    @Override
    public BureauDataForInsight refreshBureauDataSm(String smUserId, String merchantUserId) {

        if(StringUtils.isEmpty(smUserId) || StringUtils.isEmpty(merchantUserId)){
            return null;
        }
        ExperianBureauDataDto experianBureauDataDto = bureauDataService.getBureauRawData("", smUserId);
        if(ObjectUtils.isEmpty(experianBureauDataDto) || ObjectUtils.isEmpty(experianBureauDataDto.getConsent()) || !isConsentValidForRefreshScore(experianBureauDataDto.getConsent(), profileServiceConfig.getExperianConfig().getTtl())){
            log.warn("Error while fetching experianBureauDataDto, returning null");
            return null;
        }
        if (Objects.nonNull(experianBureauDataDto.getExperianBureauResponse())
                && Objects.nonNull(experianBureauDataDto.getExperianBureauResponse().getShowHtmlReportForCreditReport())) {
            log.info("Fetching Bureau Data for Insight for smUserId: {}", smUserId);
            return getBureauDataForInsightFromDao(merchantUserId, smUserId, null, experianBureauDataDto, null, ReportFetchSource.OFFLINE_REFRESH, new Consent());
        }

        log.info("bureau data does not exist : {} ", smUserId);

        return null;
    }


    @Override
    public BureauDataResponse getExistingBureauData(String merchantUserId, String smUserId) {
        return getExistingBureauDataSm(smUserId);
    }


    @Override
    public BureauDataResponse getExistingBureauDataSm(String smUserId) {
        try {
            BureauDataResponse bureauDataResponse = new BureauDataResponse();
            bureauDataResponse.setAccountTypeMap(new HashMap<>());
            bureauDataResponse.setLenders(new ArrayList<>());

            ExperianBureauDataDto experianBureauDataDto = checkConsentValidityAndRetrieveBureauDataForExistingCreditScore(null, smUserId);
            CreditScore creditScore = experianBureauDataDto == null ? null : experianBureauDataDto.getCreditScore();
            if (Objects.isNull(creditScore)) {
                return buildBureauDataNotExist(bureauDataResponse);
            }
            return buildBureauDataExist(bureauDataResponse, creditScore, smUserId, experianBureauDataDto);
        } catch (Exception e) {
            log.error("getting error for getting existing credit score for smUserId {} exception {}", smUserId, e.getMessage());
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(BureauDataManagerImpl.class, "getExistingBureauData", "failed")).mark();
            throw new ProfileException(e.getMessage());
        }
    }


    @Override
    public BureauDataResponse getBureauDataSm(BureauDataRequestSm bureauDataRequestSm) {
        try {

            BureauDataResponse bureauDataResponse = new BureauDataResponse();
            bureauDataResponse.setAccountTypeMap(new HashMap<>());
            bureauDataResponse.setLenders(new ArrayList<>());

            if (!bureauDataRequestSm.isDoRefresh()) {
                boolean foundExistingCs = checkReportInDbForSm(bureauDataRequestSm, bureauDataResponse);
                if (foundExistingCs) {
                    bureauDataResponse.setReportValid(true);
                    return bureauDataResponse;
                }
            }

            buildBureauRequestAndCallExperian(bureauDataRequestSm, bureauDataResponse);
            return bureauDataResponse;
        } catch (Exception e) {
            log.error("getting error for getting credit score in sm for request {} exception {}", bureauDataRequestSm, e.getMessage());
            throw e;
        }
    }

    @Override
    public BureauDataForInsight getBureauDataSmV2(BureauDataRequest bureauDataRequest) {
        try {
            ExperianBureauDataDto experianBureauDataDto = bureauDataService.getBureauRawData("", bureauDataRequest.getSmUserId());
            BureauRequest bureauRequest = BureauRequest.builder()
                    .firstName(bureauDataRequest.getFirstName()).lastName(bureauDataRequest.getLastName())
                    .email(bureauDataRequest.getEmailId()).mobileNo(decrypter.decrypt(bureauDataRequest.getMobileNum()))
                    .pan(bureauDataRequest.getPan()).build();
            if (Objects.nonNull(experianBureauDataDto) && Objects.nonNull(experianBureauDataDto.getExperianBureauResponse()) &&
                    Objects.nonNull(experianBureauDataDto.getExperianBureauResponse().getShowHtmlReportForCreditReport())) {
                log.info("Insights being generated from bureau data service: {}", bureauDataRequest.getSmUserId());
                return getBureauDataForInsightFromDao(bureauDataRequest.getMerchantUserId(), bureauDataRequest.getSmUserId(), null, experianBureauDataDto, bureauRequest, ReportFetchSource.CHECK_SCORE, bureauDataRequest.getConsent());
            }
            log.info("bureau request {} for smUserId {} ", bureauDataRequest.toString(), bureauDataRequest.getSmUserId());
            return getBureauDataForInsight(bureauRequest, bureauDataRequest.getMerchantUserId(), bureauDataRequest.getSmUserId(),
                    bureauDataRequest.getLeadId(), bureauDataRequest.getConsent(), null, null);

        } catch (Exception e) {
            log.info("some exception occurred in v2 : request {} exception {} ", bureauDataRequest, e.getMessage());
            throw new ProfileException(e.getMessage());
        }
    }

    private void buildBureauRequestAndCallExperian(BureauDataRequestSm bureauDataRequestSm, BureauDataResponse bureauDataResponse) {
        BureauRequest bureauRequest = buildBureauRequestSm(bureauDataRequestSm.getSmAccountId());
        BureauResponse bureauResponse = getBureauResponseFromExperian(bureauRequest);

        if (!Objects.isNull(bureauResponse.getErrorMsg())) {
            bureauDataResponse.setError(bureauDataResponse.getError());
            bureauDataResponse.setStatus(BureauUtils.OK);
            experianFeatureService.createAndStoreExperianFeature(bureauResponse.getRawData(),
                    getReportDetails(bureauDataRequestSm.getSmAccountId(), bureauDataRequestSm.getSmAccountId(), null, bureauResponse.getId(), ReportFetchSource.CHECK_SCORE_SUPERMONEY, null), bureauResponse.getErrorMsg(), null);
            return;
        }

        buildDtoAndAddBureauData(bureauDataRequestSm.getConsent(), bureauResponse, bureauDataRequestSm.getSmAccountId());
        bureauDataResponse.setCreditScore(getCreditScore(bureauResponse.getRawData()));
        bureauDataResponse.setLastUpdatedAt(new Date().toString());
        bureauDataResponse.setStatus(BureauUtils.OK);
        ExperianReport report = getParsedReport(bureauResponse.getRawData());
        bureauInsights.generateInsights(report, bureauDataResponse);
        bureauDataResponse.setMerchant(SUPERMONEY_MERCHANT_KEY);
        bureauDataResponse.setReportValid(true);
        bureauDataResponse.setCrossMerchantConsentExists(bureauDataService.getIfExperianCrossMerchantConsentExists(bureauDataRequestSm.getSmAccountId()));
        experianFeatureService.createAndStoreExperianFeature(bureauResponse.getRawData(),
                getReportDetails(bureauDataRequestSm.getSmAccountId(), bureauDataRequestSm.getSmAccountId(), null, bureauResponse.getId(), ReportFetchSource.CHECK_SCORE_SUPERMONEY, null), bureauResponse.getErrorMsg(), null);

    }

    private BureauRequest buildBureauRequestSm(String smUserId) {
        UserData userData = getUserDataSm(smUserId);
        BureauRequest bureauRequest = new BureauRequest();

        if (userData != null) {
            Optional.ofNullable(userData.getPrimaryPhone())
                    .ifPresent(phone -> bureauRequest.setMobileNo(extract10DigitsPhone(phone)));

            Optional.ofNullable(userData.getFirstName())
                    .ifPresent(firstName -> bureauRequest.setFirstName(getEncryptedData(firstName)));

            Optional.ofNullable(userData.getLastName())
                    .ifPresent(lastName -> bureauRequest.setLastName(getEncryptedData(lastName)));
        }

        return bureauRequest;
    }

    private BureauRequest buildBureauRequestSm(String smUserId, String fullName) {
        UserData userData = getUserDataSm(smUserId);
        BureauRequest bureauRequest = new BureauRequest();

        if (userData == null) {
            return bureauRequest;
        }

        String phone = userData.getPrimaryPhone();
        if (StringUtils.isNotEmpty(phone)) {
            bureauRequest.setMobileNo(extract10DigitsPhone(phone));
        }

        if (StringUtils.isNotEmpty(fullName)) {
            try {
                String decryptedName = decrypter.decrypt(fullName);
                String[] names = splitName(decryptedName);
                bureauRequest.setFirstName(getEncryptedData(names[0]));
                bureauRequest.setLastName(getEncryptedData(names[1]));
            } catch (Exception e) {
                populateNameFromUserData(userData, bureauRequest);
            }
        } else {
            populateNameFromUserData(userData, bureauRequest);
        }

        return bureauRequest;
    }


    private String extract10DigitsPhone(String phoneNumber) {
        if (phoneNumber.startsWith("+91") && phoneNumber.length() == 13) {
            return phoneNumber.substring(3);
        }
        return phoneNumber;
    }

    private void populateNameFromUserData(UserData userData, BureauRequest bureauRequest) {
        String firstName = userData.getFirstName();
        String lastName = userData.getLastName();

        if (StringUtils.isNotEmpty(firstName)) {
            bureauRequest.setFirstName(getEncryptedData(firstName));
        }
        if (StringUtils.isNotEmpty(lastName)) {
            bureauRequest.setLastName(getEncryptedData(lastName));
        }
    }
    private String[] splitName(String fullName) {
        String[] names = fullName.trim().split("\\s+", 2);
        if (names.length == 2) return names;
        else {
            return new String[]{names[0], ""};
        }
    }

    private void buildBureauRequestAndGetDataFromExperianSm(String smUserId, BureauDataResponse bureauDataResponse, ExperianBureauDataDto experianBureauDataDto) {
        BureauResponse bureauResponse;
        if (Objects.isNull(experianBureauDataDto.getExperianBureauResponse()) ||
                Objects.isNull(experianBureauDataDto.getExperianBureauResponse().getStgOneHitId())) {
            BureauRequest bureauRequest = buildBureauRequestSm(smUserId);
            bureauResponse = getBureauResponseFromExperian(bureauRequest);
        } else {
            RefreshBureauRequest refreshBureauRequest = buildRefreshBureauRequest(experianBureauDataDto.getExperianBureauResponse().getStgOneHitId());
            bureauResponse = getBureauResponseForRefresh(refreshBureauRequest);
        }

        if (!Objects.isNull(bureauResponse.getErrorMsg())) {
            bureauDataResponse.setError(bureauDataResponse.getError());
            bureauDataResponse.setStatus(BureauUtils.OK);
            return;
        }
        buildBureauDataDtoAndAddBureauData(bureauResponse, smUserId);
        buildBureauDataResponseForSuccess(bureauDataResponse, getCreditScore(bureauResponse.getRawData()), new Date(), bureauResponse.getRawData(), experianBureauDataDto.getMerchant());

        experianFeatureService.createAndStoreExperianFeature(bureauResponse.getRawData(),
                getReportDetails(smUserId, smUserId, null, bureauResponse.getId(), ReportFetchSource.CHECK_SCORE_SUPERMONEY, null), bureauResponse.getErrorMsg(), null);

    }

    private boolean checkReportInDbForSm(BureauDataRequestSm bureauDataRequestSm, BureauDataResponse bureauDataResponse) {

        ExperianBureauDataDto experianBureauDataDto = bureauDataService.getBureauInsight(null, bureauDataRequestSm.getSmAccountId());

        if (Objects.isNull(experianBureauDataDto) || Objects.isNull(experianBureauDataDto.getCreditScore())) {
            return false;
        }

        log.info("Experian data is : {}  " + experianBureauDataDto.getId());
        long dateTime = experianBureauDataDto.getExperianBureauResponse().getCreatedAt().getTime();
        long currTime = new Date().getTime();
        long durationInMilliseconds = currTime - dateTime;
        if (durationInMilliseconds > profileServiceConfig.getExperianConfig().getTtl()) {
            return false;
        }

        buildBureauDataResponseForSuccess(bureauDataResponse,
                experianBureauDataDto.getCreditScore().getCreditScore(),
                experianBureauDataDto.getCreditScore().getCreatedAt(),
                experianBureauDataDto.getExperianBureauResponse().getShowHtmlReportForCreditReport(),
                experianBureauDataDto.getMerchant());

        bureauDataResponse.setStatus(BureauUtils.OK);
        bureauDataResponse.setCrossMerchantConsentExists(bureauDataService.getIfExperianCrossMerchantConsentExists(bureauDataRequestSm.getSmAccountId()));
        ExperianReport report = getParsedReport(experianBureauDataDto.getExperianBureauResponse().getShowHtmlReportForCreditReport());
        bureauInsights.generateInsights(report, bureauDataResponse);
        return true;
    }

    public BureauDataForInsight getBureauReportBySmUserId(String smUserId, Consent consent, String leadId, Integer monthlyIncome, String fullName) {
        try {
            return getBureauReport(smUserId, smUserId, null, leadId, consent, monthlyIncome, fullName);
        } catch (Exception e) {
            log.info("some exception occurred: {} ", e.getMessage());
            throw new ProfileException(e.getMessage());
        }
    }

    @Override
    public BureauDataForInsight getBureauReport(String merchantUserId, String smUserId, Long profileId, String leadId, Consent consent, Integer monthlyIncome) {
        try {
            ProfileDetailedResponse profile = null;
            if (!Objects.isNull(profileId)) {
                profile = profileService.getProfileById(profileId);
            }
            ExperianBureauDataDto experianBureauDataDto = bureauDataService.getBureauRawData("", smUserId);
            if (Objects.nonNull(experianBureauDataDto) && Objects.nonNull(experianBureauDataDto.getExperianBureauResponse()) &&
                    Objects.nonNull(experianBureauDataDto.getExperianBureauResponse().getShowHtmlReportForCreditReport())) {
                return getBureauDataForInsightFromDao(merchantUserId, smUserId, profile, experianBureauDataDto, null, ReportFetchSource.CHECK_SCORE, consent);
            }

            BureauRequest bureauRequest = null;
            if (Objects.isNull(profile))
                bureauRequest = buildBureauRequestSm(smUserId);
            else
                bureauRequest = buildBureauRequestFromProfile(profile);
            return getBureauDataForInsight(bureauRequest, merchantUserId, smUserId, leadId, consent, monthlyIncome, profile);
        } catch (Exception e) {
            log.info("getting error for accountId {} profileId {} error {}", merchantUserId, profileId, e.getMessage());
            throw new ProfileException(e.getMessage());
        }
    }

    @Override
    public BureauDataForInsight getBureauReport(String merchantUserId, String smUserId, Long profileId, String leadId, Consent consent, Integer monthlyIncome, String fullName) {
        try {
            ProfileDetailedResponse profile = null;
            if (!Objects.isNull(profileId)) {
                profile = profileService.getProfileById(profileId);
            }

            ExperianBureauDataDto experianBureauDataDto = bureauDataService.getBureauRawData("", smUserId);
            if (Objects.nonNull(experianBureauDataDto) && Objects.nonNull(experianBureauDataDto.getExperianBureauResponse()) &&
                    Objects.nonNull(experianBureauDataDto.getExperianBureauResponse().getShowHtmlReportForCreditReport())) {
                return getBureauDataForInsightFromDao(merchantUserId, smUserId, profile, experianBureauDataDto, null, ReportFetchSource.CHECK_SCORE,consent);
            }

            BureauRequest bureauRequest = null;
            if (Objects.isNull(profile))
                bureauRequest = buildBureauRequestSm(smUserId, fullName);
            else
                bureauRequest = buildBureauRequestFromProfile(profile);
            return getBureauDataForInsight(bureauRequest, merchantUserId, smUserId, leadId, consent, monthlyIncome, profile);
        } catch (Exception e) {
            log.info("getting error for accountId {} profileId {} error {}", merchantUserId, profileId, e.getMessage());
            throw new ProfileException(e.getMessage());
        }
    }

    private BureauDataForInsight getBureauDataForInsightFromDao(String merchantUserId, String smUserId, ProfileDetailedResponse profile,
                                                                ExperianBureauDataDto experianBureauDataDto, BureauRequest bureauRequest, ReportFetchSource reportFetchSource, Consent consent) {
        if (isOlderThan30Days(experianBureauDataDto.getExperianBureauResponse().getCreatedAt())) {
            String hitId = experianBureauDataDto.getExperianBureauResponse().getStgOneHitId() == null ?
                    null : experianBureauDataDto.getExperianBureauResponse().getStgOneHitId();
            BureauDataForInsight bureauDataForInsight = autoRefreshBureauData(profile, merchantUserId, smUserId, bureauRequest, hitId, reportFetchSource, consent);
            if (StringUtils.isNotBlank(bureauDataForInsight.getReport())) {
                return bureauDataForInsight;
            }
        }
        return buildBureauDataForReport(experianBureauDataDto.getExperianBureauResponse().getShowHtmlReportForCreditReport());
    }


    private BureauDataForInsight getBureauDataForInsight(BureauRequest bureauRequest, String merchantUserId, String smUserId, String leadId, Consent consent, Integer monthlyIncome, ProfileDetailedResponse profile) {
        if (consent == null) {
            PinakaMetricRegistry.getMetricRegistry().meter("CONSENT_NULL").mark();
            log.info("consent is null for smUserId {}", smUserId);
            throw new ProfileException("consent null for bureau fetch");
        }
        BureauResponse bureauResponse = getBureauResponseFromExperian(bureauRequest);
        if (StringUtils.isNotBlank(bureauResponse.getErrorMsg())) {
            experianFeatureService.createAndStoreExperianFeature(bureauResponse.getRawData(),
                    getReportDetails(merchantUserId, smUserId, Objects.isNull(profile) ? null : profile.getProfileId().toString(), bureauResponse.getId(), ReportFetchSource.GET_OFFER, leadId), bureauResponse.getErrorMsg(), monthlyIncome);
            return buildBureauDataForNoReport(bureauResponse.getErrorMsg());
        }
        buildDtoAndAddBureauData(consent, bureauResponse, smUserId);
        experianFeatureService.createAndStoreExperianFeature(bureauResponse.getRawData(),
                getReportDetails(merchantUserId, smUserId, Objects.isNull(profile) ? null : profile.getProfileId().toString(), bureauResponse.getId(), ReportFetchSource.GET_OFFER, leadId), bureauResponse.getErrorMsg(), monthlyIncome);
        return buildBureauDataForReport(bureauResponse.getRawData());
    }

    public BureauDataForInsight autoRefreshBureauData(ProfileDetailedResponse profile, String merchantUserId,
                                                      String smUserId, BureauRequest bureauRequest, String hitId, ReportFetchSource reportFetchSource, Consent consent) {
        BureauResponse bureauResponse;
        ExperianBureauDataDto experianBureauDataDto = checkConsentValidityAndRetrieveBureauDataForExistingCreditScore(null, smUserId);
        if (hitId == null || Objects.nonNull(experianBureauDataDto)) {
            log.info("fetching credit report for user with hit-id : {}, and smUserId : {}", hitId, smUserId);
            if (bureauRequest == null) {
                if (Objects.isNull(profile))
                    bureauRequest = buildBureauRequestSm(smUserId);
                else
                    bureauRequest = buildBureauRequestFromProfile(profile);
            }
            bureauResponse = getBureauResponseFromExperian(bureauRequest);
        } else {
            RefreshBureauRequest refreshBureauRequest = buildRefreshBureauRequest(hitId);
            bureauResponse = getBureauResponseForRefresh(refreshBureauRequest);
        }

        if (!Objects.isNull(bureauResponse.getErrorMsg())) {
            log.error("Following error while refreshing bureau report, {}, for user id {}" + bureauResponse.getErrorMsg(), smUserId);
            experianFeatureService.createAndStoreExperianFeature(bureauResponse.getRawData(),
                    getReportDetails(merchantUserId, smUserId, Objects.isNull(profile) ? null : profile.getProfileId().toString(), bureauResponse.getId(), reportFetchSource, null), bureauResponse.getErrorMsg(), null);
            return buildBureauDataForNoReport(bureauResponse.getErrorMsg());
        }
        buildDtoAndAddBureauData(consent, bureauResponse, smUserId);
        experianFeatureService.createAndStoreExperianFeature(bureauResponse.getRawData(),
                getReportDetails(merchantUserId, smUserId, Objects.isNull(profile) ? null : profile.getProfileId().toString(), bureauResponse.getId(), reportFetchSource, null), bureauResponse.getErrorMsg(), null);
        return buildBureauDataForReport(bureauResponse.getRawData());
    }

    private UserData getUserDataSm(String smUserId) {
        return userDataClient.getUserData(Merchant.SUPERMONEY, null, smUserId, PIIDataType.PLAINTEXT);
    }

    private ExperianReport getParsedReport(String rawXmlReport) {
        rawXmlReport = StringEscapeUtils.unescapeHtml4(rawXmlReport);
        ReportContext reportContext = new ReportContext();
        reportContext.setReport(rawXmlReport);
        reportContext.setReportType(EXPERIAN_REPORT);
        return reportFeatureCalculator.getParsedReport(reportContext);
    }

    private BureauDataResponse buildBureauDataExist(BureauDataResponse bureauDataResponse, CreditScore creditScore,
                                                    String smUserId, ExperianBureauDataDto bureauDataDto) {
        bureauDataResponse.setConsentValid(isConsentValidForRefreshScore(bureauDataDto.getConsent(), profileServiceConfig.getExperianConfig().getTtl()));
        ExperianReport report = getParsedReport(bureauDataDto.getExperianBureauResponse().getShowHtmlReportForCreditReport());
        bureauInsights.generateInsights(report, bureauDataResponse);
        bureauDataResponse.setStatus(BureauUtils.OK);
        bureauDataResponse.setCreditScore(creditScore.getCreditScore());
        bureauDataResponse.setLastUpdatedAt(creditScore.getCreatedAt().toString());
        bureauDataResponse.setMerchant(bureauDataDto.getMerchant());
        bureauDataResponse.setReportValid(true);
        bureauDataResponse.setCrossMerchantConsentExists(bureauDataService.getIfExperianCrossMerchantConsentExists(smUserId));
        return bureauDataResponse;
    }

    private BureauDataResponse buildBureauDataResponseForSuccess(BureauDataResponse bureauDataResponse, String creditScore, Date createdAt, String rawReport, String merchant) {
        bureauDataResponse.setCreditScore(creditScore);
        bureauDataResponse.setStatus(BureauUtils.OK);
        bureauDataResponse.setLastUpdatedAt(createdAt.toString());
        bureauDataResponse.setMerchant(merchant);
        if (Objects.nonNull(rawReport)) {
            ExperianReport report = getParsedReport(rawReport);
            bureauInsights.generateInsights(report, bureauDataResponse);
        }
        return bureauDataResponse;
    }

    private ProfileDetailedResponse createAndGetProfile(ProfileRequest profileRequest) {
        try {
            ProfileCRUDResponse profileCRUDResponse = profileService.createProfile(profileRequest);
            log.info("Profile created  -> " + profileCRUDResponse.toString());
        } catch (Exception e) {
            log.info("Unable to create profile  -> : {} ", e.getMessage());
            throw new ProfileException(ExceptionConstants.ERROR_CREATING_PROFILE, e);
        }
        try {
            return profileService.getProfileByUserId(profileRequest.getUserId(), profileRequest.getSmUserId(), false);
        } catch (Exception e) {
            log.error("Unable to get profile for: {} | {}", profileRequest.getUserId(), profileRequest.getSmUserId(), e);
            throw new ProfileException(e);
        }
    }

    @Retry
    public BureauResponse getBureauResponseFromExperian(BureauRequest bureauRequest) {
        log.info("fetching bureau data from experian for {} ", bureauRequest);
        BureauResponse bureauResponse = profileServiceConfig.isExperianMocked() ?
                getMockedBureauReport() : checkBureauScoreClient.getCreditReportByExperian(bureauRequest);
        if (StringUtils.isNotBlank(bureauResponse.getErrorMsg())) {
            log.error("getting error from experian {}", bureauResponse.getErrorMsg());
            ErrorEnum errorEnum = ErrorEnum.fromString(bureauResponse.getErrorMsg());
            PinakaMetricRegistry.getMetricRegistry().meter("FreshBureauError" + (errorEnum != null ? errorEnum.toString() : "null")).mark();
        }
        return bureauResponse;
    }

    @Retry
    public BureauResponse getBureauResponseForRefresh(RefreshBureauRequest refreshBureauRequest) {
        log.info("fetching refresh bureau data from experian for {} ", refreshBureauRequest);
        BureauResponse bureauResponse = checkBureauScoreClient.getRefreshedCreditReportByExperian(refreshBureauRequest);
        if (StringUtils.isNotBlank(bureauResponse.getErrorMsg())) {
            log.error("getting error from experian for refresh {} for refresh request {}", bureauResponse.getErrorMsg(), refreshBureauRequest.toString());
            ErrorEnum errorEnum = ErrorEnum.fromString(bureauResponse.getErrorMsg());
            PinakaMetricRegistry.getMetricRegistry().meter("RefreshBureauError" + (errorEnum != null ? errorEnum.toString() : "null")).mark();
        }
        return bureauResponse;
    }

    private ProfileDetailedResponse createProfileAndGetDetails(BureauDataRequest bureauDataRequest) {
        ProfileRequest profileRequest = buildProfileRequest(bureauDataRequest);
        ProfileDetailedResponse profileCreated = createAndGetProfile(profileRequest);
        return profileCreated;
    }


    private void buildDtoAndAddBureauData(Consent consent, BureauResponse bureauResponse, String smUserId) {
        ExperianBureauDataDto experianBureauDataDto = buildExperianBureauDataDto(consent, bureauResponse, smUserId);
        addBureauData(experianBureauDataDto, smUserId, DEFAULT_MERCHANT_KEY);
    }

    private ProfileDetailedResponse checkProfileExistOrCreate(BureauDataRequest bureauDataRequest, BureauDataResponse bureauDataResponse) {

        try {
            log.info("Upserting profile for user_id : {}  ", bureauDataRequest.getMerchantUserId());
            ProfileDetailedResponse profile = createProfileAndGetDetails(bureauDataRequest);
            return profile;
        } catch (Exception e) {
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(BureauDataManagerImpl.class, "profile", "creation", "failed")).mark();
            throw new ProfileException(e.getMessage());
        }
    }

    private void buildBureauRequestAndGetDataFromExperian(BureauDataRequest bureauDataRequest, ProfileDetailedResponse profileCreated, BureauDataResponse bureauDataResponse) {
        try {
            BureauRequest bureauRequest = buildBureauRequest(bureauDataRequest, profileCreated);
            BureauResponse bureauResponse = getBureauResponseFromExperian(bureauRequest);
            if (!Objects.isNull(bureauResponse.getErrorMsg())) {
                bureauDataResponse.setError("Phone & Pan Mismatch for user");
                bureauDataResponse.setStatus(BureauUtils.OK);
                experianFeatureService.createAndStoreExperianFeature(bureauResponse.getRawData(),
                        getReportDetails(bureauDataRequest.getMerchantUserId(), bureauDataRequest.getSmUserId(), profileCreated.getProfileId().toString(), bureauResponse.getId(), ReportFetchSource.CHECK_SCORE, null), bureauResponse.getErrorMsg(), null);
                return;
            }
            buildDtoAndAddBureauData(bureauDataRequest.getConsent(), bureauResponse, bureauDataRequest.getSmUserId());
            experianFeatureService.createAndStoreExperianFeature(bureauResponse.getRawData(),
                    getReportDetails(bureauDataRequest.getMerchantUserId(), bureauDataRequest.getSmUserId(), profileCreated.getProfileId().toString(), bureauResponse.getId(), ReportFetchSource.CHECK_SCORE, null), bureauResponse.getErrorMsg(), null);
            bureauDataResponse.setCreditScore(getCreditScore(bureauResponse.getRawData()));
            bureauDataResponse.setLastUpdatedAt(new Date().toString());
            bureauDataResponse.setStatus(BureauUtils.OK);
            ExperianReport report = getParsedReport(bureauResponse.getRawData());
            bureauInsights.generateInsights(report, bureauDataResponse);
        } catch (Exception e) {
            log.info("getting error while fetching report for request {} exception {}", bureauDataRequest, e.getMessage());
            throw new ProfileException(e.getMessage());
        }
    }

    private ExperianBureauDataDto buildExperianBureauDataDto(Consent consent, BureauResponse bureauResponse, String smUserId) {
        ExperianBureauDataDto experianBureauDataDto = new ExperianBureauDataDto();
        experianBureauDataDto.setId(smUserId);
        experianBureauDataDto.setConsent(consent);
        experianBureauDataDto.setExperianBureauResponse(new ExperianBureauResponse(bureauResponse.getRawData(), new Date(), bureauResponse.getStgOneHitId(), bureauResponse.getStgTwoHitId(), bureauResponse.getExperianUserId()));
        return experianBureauDataDto;
    }

    private BureauRequest buildBureauRequest(BureauDataRequest bureauDataRequest, ProfileDetailedResponse profile) {
        BureauRequest bureauRequest = new BureauRequest();
        bureauRequest.setFirstName(bureauDataRequest.getFirstName());
        bureauRequest.setLastName(bureauDataRequest.getLastName());
        bureauRequest.setPan(bureauDataRequest.getPan());
        bureauRequest.setMobileNo(profile.getPhoneNo());
        bureauRequest.setEmail(profile.getEmail());
        return bureauRequest;
    }

    private BureauRequest buildBureauRequestFromProfile(ProfileDetailedResponse profile) {
        BureauRequest bureauRequest = new BureauRequest();
        bureauRequest.setMobileNo(profile.getPhoneNo());
        if (!StringUtils.isBlank(profile.getFirstName())) {
            bureauRequest.setFirstName(profile.getFirstName());
        }
        if (!StringUtils.isBlank(profile.getLastName())) {
            bureauRequest.setLastName(profile.getLastName());
        }
        bureauRequest.setPan(profile.getPan());
        bureauRequest.setEmail(profile.getEmail());
        return bureauRequest;
    }

    private void buildBureauDataDtoAndAddBureauData(BureauResponse bureauResponse, String smUserId) {
        ExperianBureauDataDto experianBureauDataDto = new ExperianBureauDataDto();
        experianBureauDataDto.setId(smUserId);
        experianBureauDataDto.setExperianBureauResponse(new ExperianBureauResponse(bureauResponse.getRawData(), new Date(), bureauResponse.getStgOneHitId(), bureauResponse.getStgTwoHitId(), bureauResponse.getExperianUserId()));
        addBureauData(experianBureauDataDto, smUserId, DEFAULT_MERCHANT_KEY);
    }

    private ExperianBureauDataDto checkConsentValidityAndRetrieveBureauDataForExistingCreditScore(String rowKey, String smUserId) {
        ExperianBureauDataDto experianBureauDataDto = bureauDataService.getBureauInsight(rowKey, smUserId);
        if (Objects.isNull(experianBureauDataDto) || Objects.isNull(experianBureauDataDto.getCreditScore())) {
            return null;
        }
        log.info("Experian data is : {}  " + experianBureauDataDto.getId());
        long dateTime = experianBureauDataDto.getCreditScore().getCreatedAt().getTime();
        long currTime = new Date().getTime();
        long durationInMilliSeconds = currTime - dateTime;
        if (durationInMilliSeconds <= profileServiceConfig.getExperianConfig().getTtl()) {
            return experianBureauDataDto;
        }
        return null;
    }
}