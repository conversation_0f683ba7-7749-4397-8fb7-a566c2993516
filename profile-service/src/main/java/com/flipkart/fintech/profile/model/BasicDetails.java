package com.flipkart.fintech.profile.model;

import org.hibernate.annotations.Generated;
import org.hibernate.annotations.GenerationTime;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "basic_details")
public class BasicDetails implements Serializable {

    @Id
    @Column(name = "profile_id")
    private Long profileId;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "verified_first_name")
    private String verifiedFirstName;

    @Column(name = "verified_last_name")
    private String verifiedLastName;

    @Column(name = "name_match")
    private Integer nameMatch;

    @Column(name = "match_score")
    private Integer matchScore;

    @Column(name = "gender")
    private String gender;

    @Column(name = "dob")
    private String dob;

    public Boolean getNameMatch() {
        if (nameMatch == null) return false;
        return nameMatch == 1;
    }

    public void setNameMatch(Boolean nameMatch) {
        this.nameMatch = Boolean.TRUE.equals(nameMatch) ? 1 : 0;
    }

    public Integer getMatchScore() {
        return matchScore;
    }

    public void setMatchScore(Integer matchScore) {
        this.matchScore = matchScore;
    }

    public String getVerifiedFirstName() {
        return verifiedFirstName;
    }

    public void setVerifiedFirstName(String verifiedFirstName) {
        this.verifiedFirstName = verifiedFirstName;
    }

    public String getVerifiedLastName() {
        return verifiedLastName;
    }

    public void setVerifiedLastName(String verifiedLastName) {
        this.verifiedLastName = verifiedLastName;
    }

    public Long getProfileId() {
        return profileId;
    }

    public void setProfileId(Long profileId) {
        this.profileId = profileId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getDob() {
        return dob;
    }

    public void setDob(String dob) {
        this.dob = dob;
    }

    public String getProfileName() {
        return profileName;
    }

    public void setProfileName(String profileName) {
        this.profileName = profileName;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    @Column(name = "profile_name")
    private String profileName;

    @Column(name = "creation_date")
    @Generated(GenerationTime.INSERT)
    @Temporal(TemporalType.TIMESTAMP)
    private Date creationDate;

    @Column(name = "last_modified")
    @Generated(GenerationTime.ALWAYS)
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModified;


}
