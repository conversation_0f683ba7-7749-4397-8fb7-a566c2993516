package com.flipkart.fintech.profile.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "employment_details")
public class EmploymentDetails implements Serializable {

    @Id
    @Column(name = "profile_id")
    private Long profileId;

    public Long getProfileId() {
        return profileId;
    }

    public void setProfileId(Long profileId) {
        this.profileId = profileId;
    }

    public EmploymentType getEmploymentType() {
        return employmentType;
    }

    public void setEmploymentType(EmploymentType employmentType) {
        this.employmentType = employmentType;
    }

    @Enumerated(EnumType.STRING)
    @Column(name = "employment_type")
    private EmploymentType employmentType;

    @Getter
    @Setter
    @Column(name = "monthly_income")
    private Integer monthlyIncome;

    @Getter
    @Setter
    @Column(name = "bonus_income")
    private Integer bonusIncome;

    @Getter
    @Setter
    @Column(name = "company_name")
    private String companyName;

    @Getter
    @Setter
    @Column(name = "organization_id")
    private String organizationId;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    @Column(name = "income_source")
    private IncomeSource incomeSource;

    @Getter
    @Setter
    @Column(name = "industry_type")
    private String industryType;

    @Getter
    @Setter
    @Column(name = "industry_id")
    private String industryId;

    @Getter
    @Setter
    @Column(name = "annual_turn_over")
    private Integer annualTurnOver;

}
