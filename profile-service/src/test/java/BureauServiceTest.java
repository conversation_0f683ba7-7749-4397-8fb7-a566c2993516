import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.flipkart.fintech.pinaka.library.ReportContext;
import com.flipkart.fintech.pinaka.library.ReportFeatureCalculator;
import com.flipkart.fintech.pinaka.library.ReportType;
import com.flipkart.fintech.pinaka.library.dao.CostInflationFactorDaoImpl;
import com.flipkart.fintech.pinaka.library.dao.DivisionFactorDaoImpl;
import com.flipkart.fintech.pinaka.library.entities.ExperianReport;
import com.flipkart.fintech.pinaka.library.parser.ExperianReportParser;
import com.flipkart.fintech.pinaka.library.parser.ParserFactory;
import com.flipkart.fintech.pinaka.library.parser.ReportParser;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.profile.service.BureauDataManagerImpl;
import com.flipkart.fintech.profile.service.BureauInsightsService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.junit.Test;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;


import static com.flipkart.fintech.pinaka.library.ReportType.EXPERIAN_REPORT;
import static org.testng.AssertJUnit.assertEquals;

public class BureauServiceTest {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final MetricRegistry metricRegistry = new MetricRegistry();

    @Test
    public void testInitialUserData() throws IOException {
        InputStream inputStream = getClass().getResourceAsStream("/experianReports/file4.xml");
        String xmlContent = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        xmlContent = StringEscapeUtils.unescapeXml(xmlContent);
        ReportContext reportContext = new ReportContext();
        reportContext.setReport(xmlContent);
        reportContext.setReportType(EXPERIAN_REPORT);
        ReportFeatureCalculator scoreCalculatorService = new ReportFeatureCalculator(getParserFactory(), new CostInflationFactorDaoImpl(objectMapper), new DivisionFactorDaoImpl(objectMapper), metricRegistry);
        InitialUserDataResponse response = new BureauInsightsService().fetchUserInitialData(scoreCalculatorService.getParsedReport(reportContext));
        System.out.println(response.toString());
    }

    @Test
    public void testInitialUserData2() throws IOException {
        InputStream inputStream = getClass().getResourceAsStream("/experianReports/experianReport7.xml");
        String xmlContent = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        xmlContent = StringEscapeUtils.unescapeXml(xmlContent);
        ReportContext reportContext = new ReportContext();
        reportContext.setReport(xmlContent);
        reportContext.setReportType(EXPERIAN_REPORT);
        ReportFeatureCalculator scoreCalculatorService = new ReportFeatureCalculator(getParserFactory(), new CostInflationFactorDaoImpl(objectMapper), new DivisionFactorDaoImpl(objectMapper), metricRegistry);
        InitialUserDataResponse response = new BureauInsightsService().fetchUserInitialData(scoreCalculatorService.getParsedReport(reportContext));
        assertEquals(response.getAddressDetailResponse().getPincode(), "560103");
        System.out.println(response);
    }

    @Test
    public void testFeatureScoreCalculator() throws IOException {
        for (int i = 1; i <= 6; i++) {
            InputStream inputStream = getClass().getResourceAsStream("/experianReports/file" + i + ".xml");
            String xmlContent = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            testGenerateInsights(xmlContent, i);
        }
    }

    private void testGenerateInsights(String report, int i) {
        report = StringEscapeUtils.unescapeXml(report);
        ReportContext reportContext = new ReportContext();
        reportContext.setReport(report);
        reportContext.setReportType(EXPERIAN_REPORT);
        ReportFeatureCalculator scoreCalculatorService = new ReportFeatureCalculator(getParserFactory(), new CostInflationFactorDaoImpl(objectMapper), new DivisionFactorDaoImpl(objectMapper), metricRegistry);
        BureauDataResponse bureauDataResponse = new BureauDataResponse();
        bureauDataResponse.setAccountTypeMap(new HashMap<>());
        bureauDataResponse.setLenders(new ArrayList<>());
        new BureauInsightsService().generateInsights(scoreCalculatorService.getParsedReport(reportContext), bureauDataResponse);
        System.out.println(bureauDataResponse);

        switch (i) {
            case 1:
                assertEquals("1 Loan", bureauDataResponse.getCreditMix());
                break;
            case 2:
                assertEquals("1 Loan", bureauDataResponse.getCreditMix());
                break;
            case 3:
                assertEquals("1 Loan", bureauDataResponse.getCreditMix());
                break;
            case 4:
                assertEquals("9 Cards 5 Loans", bureauDataResponse.getCreditMix());
                break;
            case 5:
                assertEquals("1 Card", bureauDataResponse.getCreditMix());
                break;
            case 6:
                assertEquals("19 Loans 5 Cards", bureauDataResponse.getCreditMix());
                break;
        }

    }

    public ParserFactory getParserFactory() {
        ObjectMapper xmlMapper = new XmlMapper();
        xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        xmlMapper.configure(DeserializationFeature.FAIL_ON_MISSING_EXTERNAL_TYPE_ID_PROPERTY, false);
        ExperianReportParser experianReportParser = new ExperianReportParser(xmlMapper);
        Map<ReportType, ReportParser> reportTypeReportParserMap = new HashMap<>();
        reportTypeReportParserMap.put(ReportType.EXPERIAN_REPORT, experianReportParser);
        return new ParserFactory(reportTypeReportParserMap);
    }
}
