package com.flipkart.fintech.profile.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.client.CheckBureauScoreClient;
import com.flipkart.fintech.pinaka.library.ReportFeatureCalculator;
import com.flipkart.fintech.profile.Decrypter.Decrypter;
import com.flipkart.fintech.profile.api.response.BureauResponse;
import com.flipkart.fintech.profile.bureau.models.BureauDataForInsight;
import com.flipkart.fintech.profile.bureau.models.Consent;
import com.flipkart.fintech.profile.bureau.models.ExperianBureauResponse;
import com.flipkart.fintech.profile.config.ExperianConfig;
import com.flipkart.fintech.profile.config.ProfileServiceConfig;
import com.flipkart.fintech.profile.model.ExperianBureauDataDto;
import com.flipkart.fintech.profile.pubsub.CreditScorePublisher;
import com.flipkart.fintech.profile.model.ReportFetchSource;
import com.flipkart.fintech.profile.request.BureauDataRequest;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.user.data.models.UserData;
import com.flipkart.fintech.user.data.models.enums.Merchant;
import com.flipkart.fintech.user.data.models.enums.PIIDataType;
import com.flipkart.kloud.config.DynamicBucket;
import com.sumo.crisys.client.CrisysClient;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.Date;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static com.flipkart.fintech.profile.model.ReportFetchSource.CHECK_SCORE;

public class BureauDataManagerImplTest {

    @Mock
    private ProfileService profileService;
    @Mock
    private CheckBureauScoreClient checkBureauScoreClient;
    @Mock
    private BureauDataService bureauDataService;
    @Mock
    private ExperianFeatureService experianFeatureService;
    @Mock
    private ReportFeatureCalculator reportFeatureCalculator;
    @Mock
    private BureauInsights bureauInsights;
    @Mock
    private UserDataClient userDataClient;
    @Mock
    private CrisysClient crisysClient;
    @Mock
    private ProfileServiceConfig profileServiceConfig;
    @Mock
    private ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private Decrypter decrypter;
    @Mock
    private CreditScorePublisher creditScorePublisher;
    @Mock
    private DynamicBucket dynamicBucket;

    @Mock
    private BureauDataManagerImpl bureauDataManager;


    private String bureauDataRequestString;

    @Before
    public void setUp() throws IOException {
        MockitoAnnotations.initMocks(this);
        bureauDataRequestString = "{\"merchantUserId\":\"ACCDD89E7F62E5145C4BE4F58AD517DCBA6\",\"accountId\":\"ACCDD89E7F62E5145C4BE4F58AD517DCBA6\",\"smUserId\":\"SMA3CA8E5E06015442C8D524E606EFE182C\",\"firstName\":\"mICITs+fLTnyVIqnNseAuQ==\"," +
                "\"lastName\":\"RVaKM4avQ5dcaqwa8rn4YA==\",\"mobileNum\":\"MDDj49LUpAzJMX71dIko1A==\",\"consent\":{\"purpose\":\"LENDING\",\"provided\":true,\"ip\":\"*************, *************,*************\",\"deviceId\":\"TI172735235592200247925604992918693635265541621836411838628670084637\"," +
                "\"deviceInfo\":\"Mozilla/5.0 (Linux; Android 13; 2201116SI Build/TKQ1.221114.001) FKUA/Retail/2150300/Android/Mobile (Xiaomi/2201116SI/57f3296b56ae112cd1e1a651468a1251)\",\"ts\":*************},\"leadId\":\"APP2501291636077361922534791334631314817\"}";

        bureauDataManager = new BureauDataManagerImpl(profileService, checkBureauScoreClient, bureauDataService, experianFeatureService, reportFeatureCalculator,
                bureauInsights, userDataClient, profileServiceConfig, decrypter);
    }


    @Test
    @SneakyThrows
    public void getBureauDataSmV2ForRefreshCase() {
        when(decrypter.decrypt(any())).thenReturn("9999999999");
        BureauDataRequest bureauDataRequest = new ObjectMapper().readValue(bureauDataRequestString, BureauDataRequest.class);
        ExperianBureauDataDto experianBureauDataDto = new ExperianBureauDataDto();
        experianBureauDataDto.setExperianBureauResponse(new ExperianBureauResponse());
        experianBureauDataDto.getExperianBureauResponse().setCreatedAt(new Date(124, 10, 1));
        experianBureauDataDto.setId("dummyId");
        experianBureauDataDto.getExperianBureauResponse().setShowHtmlReportForCreditReport("<html></html>");
        when(bureauDataService.getBureauRawData("", bureauDataRequest.getSmUserId())).thenReturn(experianBureauDataDto);
        when(checkBureauScoreClient.getCreditReportByExperian(any())).thenReturn(new BureauResponse());
        bureauDataManager.getBureauDataSmV2(bureauDataRequest);
        verify(checkBureauScoreClient, times(1)).getCreditReportByExperian(any());
    }

    @Test
    public void testAutoRefreshBureauData_WhenHitIdIsNull_AndBureauRequestIsNull() {
        String merchantUserId = "merchant123";
        String smUserId = "smUser456";
        String hitId = null;
        ReportFetchSource reportFetchSource = CHECK_SCORE;
        UserData userData = UserData.builder().build();
        BureauResponse bureauResponse = new BureauResponse();
        bureauResponse.setRawData("rawData");
        when(userDataClient.getUserData(any(), anyString(), anyString(), any())).thenReturn(userData);
        when(checkBureauScoreClient.getCreditReportByExperian(any())).thenReturn(bureauResponse);
        BureauDataForInsight result = bureauDataManager.autoRefreshBureauData(null, merchantUserId, smUserId, null, hitId,reportFetchSource, new Consent());
        assertNotNull(result);
        assertEquals(result.getReport(), "rawData");
    }

    @Test
    public void testAutoRefreshBureauData_WhenHitIdIsNotNull() {
        String merchantUserId = "merchant123";
        String smUserId = "smUser456";
        String hitId = "hit789";
        ReportFetchSource reportFetchSource = CHECK_SCORE;
        BureauResponse bureauResponse = new BureauResponse();
        bureauResponse.setRawData("rawData");
        when(checkBureauScoreClient.getRefreshedCreditReportByExperian(any())).thenReturn(bureauResponse);
        BureauDataForInsight result = bureauDataManager.autoRefreshBureauData(null, merchantUserId, smUserId, null, hitId, reportFetchSource, new Consent());
        assertNotNull(result);
        assertEquals(result.getReport(), "rawData");
    }

    @Test
    public void testrefreshBureauDataSm_EmptyUser() {
        String smUserId = "";
        String merchantUserId = "";
        assertNull(bureauDataManager.refreshBureauDataSm(smUserId, merchantUserId));
    }

    @Test
    public void testrefreshBureauDataSm_nonEmptyOlderThan30DaysUser() {
        String smUserId = "smUser457";
        String merchantUserId = "smMerchant123";
        Consent consent = new Consent();
        long sixtyDaysAgo = System.currentTimeMillis() - (20L * 24 * 60 * 60 * 1000);
        consent.setTs(sixtyDaysAgo);
        ExperianConfig experianConfig = new ExperianConfig();
        experianConfig.setTtl(40L * 24 * 60 * 60 * 1000);
        ExperianBureauDataDto experianBureauDataDto = new ExperianBureauDataDto();
        experianBureauDataDto.setExperianBureauResponse(new ExperianBureauResponse());
        experianBureauDataDto.setConsent(consent);
        experianBureauDataDto.getExperianBureauResponse().setCreatedAt(new Date(125, 1, 9));
        experianBureauDataDto.setId("dummyId");
        experianBureauDataDto.getExperianBureauResponse().setShowHtmlReportForCreditReport("<html></html>");
        when(bureauDataService.getBureauRawData("", smUserId)).thenReturn(experianBureauDataDto);
        when(checkBureauScoreClient.getCreditReportByExperian(any())).thenReturn(new BureauResponse());
        when(profileServiceConfig.getExperianConfig()).thenReturn(experianConfig);
        assertNotNull(bureauDataManager.refreshBureauDataSm(smUserId, merchantUserId));
        verify(checkBureauScoreClient, times(1)).getCreditReportByExperian(any());
    }

    @Test
    public void testrefreshBureauDataSm_nonEmptyNotOlderThan30DaysUser() {
        String smUserId = "smUser457";
        String merchantUserId = "smMerchant123";
        Consent consent = new Consent();
        long sixtyDaysAgo = System.currentTimeMillis() - (20L * 24 * 60 * 60 * 1000);
        consent.setTs(sixtyDaysAgo);
        ExperianConfig experianConfig = new ExperianConfig();
        experianConfig.setTtl(40L * 24 * 60 * 60 * 1000);
        ExperianBureauDataDto experianBureauDataDto = new ExperianBureauDataDto();
        experianBureauDataDto.setExperianBureauResponse(new ExperianBureauResponse());
        experianBureauDataDto.getExperianBureauResponse().setCreatedAt(new Date(2025, 3, 10));
        experianBureauDataDto.setId("dummyId");
        experianBureauDataDto.getExperianBureauResponse().setShowHtmlReportForCreditReport("<html></html>");
        experianBureauDataDto.setConsent(consent);
        when(profileServiceConfig.getExperianConfig()).thenReturn(experianConfig);
        when(bureauDataService.getBureauRawData("", smUserId)).thenReturn(experianBureauDataDto);
        when(checkBureauScoreClient.getCreditReportByExperian(any())).thenReturn(new BureauResponse());
        assertNotNull(bureauDataManager.refreshBureauDataSm(smUserId, merchantUserId));
        verify(checkBureauScoreClient, times(0)).getCreditReportByExperian(any());
    }

    @Test
    public void getBureauReport__fromExperian_with_fullname(){
        Consent consent = new Consent();
        consent.setPurpose("KYC_VERIFICATION");
        consent.setProvided(true);
        consent.setIp("************");
        consent.setDeviceId("device-abc-123");
        consent.setDeviceInfo("Android 12, Samsung Galaxy S21");
        consent.setTs(System.currentTimeMillis());
        UserData userData = UserData.builder().primaryPhone("7654321098").build();

        when(profileServiceConfig.isExperianMocked()).thenReturn(true);

        when(userDataClient.getUserData(Merchant.SUPERMONEY, null, "123", PIIDataType.PLAINTEXT)).thenReturn(userData);
        when(decrypter.decrypt("encryptedName")).thenReturn("John Doe");

        BureauDataForInsight result = bureauDataManager.getBureauReport("123","123",null ,null,consent,12345,"encryptedName");
        assertNotNull(result);
        verify(userDataClient).getUserData(Merchant.SUPERMONEY, null, "123", PIIDataType.PLAINTEXT);
        verify(decrypter).decrypt("encryptedName");
    }

    @Test
    public void getBureauReport__fromExperian_with_fullnameDecryptionError(){
        Consent consent = new Consent();
        consent.setPurpose("KYC_VERIFICATION");
        consent.setProvided(true);
        consent.setIp("************");
        consent.setDeviceId("device-abc-123");
        consent.setDeviceInfo("Android 12, Samsung Galaxy S21");
        consent.setTs(System.currentTimeMillis());
        UserData userData = UserData.builder().primaryPhone("7654321098").firstName("John").lastName("Doe").build();

        when(profileServiceConfig.isExperianMocked()).thenReturn(true);

        when(userDataClient.getUserData(Merchant.SUPERMONEY, null, "123", PIIDataType.PLAINTEXT)).thenReturn(userData);
        when(decrypter.decrypt("encryptedName")).thenThrow(new NullPointerException());

        BureauDataForInsight result = bureauDataManager.getBureauReport("123","123",null ,null,consent,12345,"encryptedName");
        assertNotNull(result);
        verify(userDataClient).getUserData(Merchant.SUPERMONEY, null, "123", PIIDataType.PLAINTEXT);
    }
    @Test
    public void getBureauReport__fromExperian_without_fullname(){
        Consent consent = new Consent();
        consent.setPurpose("KYC_VERIFICATION");
        consent.setProvided(true);
        consent.setIp("************");
        consent.setDeviceId("device-abc-123");
        consent.setDeviceInfo("Android 12, Samsung Galaxy S21");
        consent.setTs(System.currentTimeMillis());
        UserData userData = UserData.builder().primaryPhone("+917654321098").firstName("John").lastName("Doe").build();

        when(profileServiceConfig.isExperianMocked()).thenReturn(true);

        when(userDataClient.getUserData(Merchant.SUPERMONEY, null, "123", PIIDataType.PLAINTEXT)).thenReturn(userData);

        BureauDataForInsight result = bureauDataManager.getBureauReport("123","123",null ,null,consent,12345,null);

        assertNotNull(result);
        verify(userDataClient).getUserData(Merchant.SUPERMONEY, null, "123", PIIDataType.PLAINTEXT);
    }
    @Test
    public void getBureauReport__fromExperian_without_lastName(){
        Consent consent = new Consent();
        consent.setPurpose("KYC_VERIFICATION");
        consent.setProvided(true);
        consent.setIp("************");
        consent.setDeviceId("device-abc-123");
        consent.setDeviceInfo("Android 12, Samsung Galaxy S21");
        consent.setTs(System.currentTimeMillis());
        UserData userData = UserData.builder().primaryPhone("+917654321098").firstName("John").lastName(null).build();

        ExperianBureauDataDto experianBureauDataDto = new ExperianBureauDataDto();
        experianBureauDataDto.setExperianBureauResponse(new ExperianBureauResponse());
        experianBureauDataDto.getExperianBureauResponse().setCreatedAt(new Date());
        experianBureauDataDto.setId("dummyId");
        experianBureauDataDto.setConsent(consent);

        when(bureauDataService.getBureauRawData(any(),any())).thenReturn(experianBureauDataDto);

        when(profileServiceConfig.isExperianMocked()).thenReturn(true);

        when(userDataClient.getUserData(Merchant.SUPERMONEY, null, "123", PIIDataType.PLAINTEXT)).thenReturn(userData);

        BureauDataForInsight result = bureauDataManager.getBureauReport("123","123",null ,null,consent,12345,null);

        assertNotNull(result);
        verify(userDataClient).getUserData(Merchant.SUPERMONEY, null, "123", PIIDataType.PLAINTEXT);
    }

    @Test
    public void getBureauReport__formDb(){
        Consent consent = new Consent();
        consent.setPurpose("KYC_VERIFICATION");
        consent.setProvided(true);
        consent.setIp("************");
        consent.setDeviceId("device-abc-123");
        consent.setDeviceInfo("Android 12, Samsung Galaxy S21");
        consent.setTs(System.currentTimeMillis());

        ExperianBureauDataDto experianBureauDataDto = new ExperianBureauDataDto();
        experianBureauDataDto.setExperianBureauResponse(new ExperianBureauResponse());
        experianBureauDataDto.getExperianBureauResponse().setCreatedAt(new Date());
        experianBureauDataDto.setId("dummyId");
        experianBureauDataDto.getExperianBureauResponse().setShowHtmlReportForCreditReport("<html></html>");
        experianBureauDataDto.setConsent(consent);

        when(bureauDataService.getBureauRawData(any(),any())).thenReturn(experianBureauDataDto);


        BureauDataForInsight result = bureauDataManager.getBureauReport("123", "123", null, null, consent, 12345, "encryptedName");

        assertNotNull(result);
        assertEquals(result.getReport(),"<html></html>");

    }


    @Test(expected = ProfileException.class)
    public void getBureauReport__formDb_exception(){
        when(bureauDataService.getBureauRawData(any(),any())).thenThrow(new ProfileException());
        BureauDataForInsight result = bureauDataManager.getBureauReport("123", "123", null, null, null, 12345, "encryptedName");
    }


    @Test
    public void getBureauReportBySmUserID__fromExperian_without_fullname(){
        Consent consent = new Consent();
        consent.setPurpose("KYC_VERIFICATION");
        consent.setProvided(true);
        consent.setIp("************");
        consent.setDeviceId("device-abc-123");
        consent.setDeviceInfo("Android 12, Samsung Galaxy S21");
        consent.setTs(System.currentTimeMillis());
        UserData userData = UserData.builder().primaryPhone("7654321098").build();

        when(profileServiceConfig.isExperianMocked()).thenReturn(true);

        when(userDataClient.getUserData(Merchant.SUPERMONEY, null, "123", PIIDataType.PLAINTEXT)).thenReturn(userData);
        when(decrypter.decrypt("encryptedName")).thenReturn("John");


        BureauDataForInsight result = bureauDataManager.getBureauReportBySmUserId("123",consent,"leadId",12345,"encryptedName");

        assertNotNull(result);
        verify(userDataClient).getUserData(Merchant.SUPERMONEY, null, "123", PIIDataType.PLAINTEXT);
    }

}

