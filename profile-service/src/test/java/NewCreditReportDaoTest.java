import com.flipkart.fintech.profile.bureau.models.Consent;
import com.flipkart.fintech.profile.bureau.models.ExperianBureauResponse;
import com.flipkart.fintech.profile.dao.NewCreditReportDaoImpl;
import com.flipkart.fintech.profile.service.ExperianFeatureService;
import com.flipkart.fintech.profile.yak.BureauDataStore;
import com.flipkart.fintech.profile.yak.entities.BureauDataEntity;
import com.google.cloud.bigtable.admin.v2.BigtableTableAdminClient;
import com.google.cloud.bigtable.data.v2.BigtableDataClient;
import com.supermoney.google.clients.bigtable.client.BigtableClient;
import com.supermoney.google.clients.bigtable.client.impl.BigtableClientImpl;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mock;

import java.io.IOException;

import static org.junit.Assert.assertEquals;


@Ignore
public class NewCreditReportDaoTest {

    private BureauDataStore newCreditReportDao;

    private BigtableClient bigtableClient;

    @Mock
    private ExperianFeatureService experianFeatureService;


    @Before
    public void setUp() throws IOException {
        String instanceId = "sumo-bigtable-preprod-1";
        String projectId = "sm-personal-loans";
        BigtableDataClient bigDatatableClient = BigtableDataClient.create(projectId, instanceId);
        BigtableTableAdminClient bigtableTableAdminClient = BigtableTableAdminClient.create(projectId, instanceId);
        bigtableClient = new BigtableClientImpl("creditReport", false, bigDatatableClient, bigtableTableAdminClient);
        this.newCreditReportDao = new NewCreditReportDaoImpl(bigtableClient, experianFeatureService);
    }

    @Test
    public void testAddAndReadBureauData() {
        BureauDataEntity bureauDataEntity = new BureauDataEntity();
        String accountId = "ACC";
        Consent consent = new Consent("test", false, "10.12.21","ABC", "vivo", 2L);
        bureauDataEntity.setExperianConsent(consent);
        bureauDataEntity.setId(accountId);
        bureauDataEntity.setExperianBureauResponse(new ExperianBureauResponse("REPORT", null, null, null, null));
        newCreditReportDao.putExperianBureauData(bureauDataEntity, accountId, null);
        BureauDataEntity bureauDataEntity1 = newCreditReportDao.getExperianBureauRawData(null, accountId);
        assertEquals(bureauDataEntity.getExperianBureauResponse().getShowHtmlReportForCreditReport(), bureauDataEntity1.getExperianBureauResponse().getShowHtmlReportForCreditReport());
    }


    @Test
    public void deleteData() {
        bigtableClient.delete("your_row_key");
    }

    @Test
    public void deleteColumnQualifier(){ bigtableClient.delete("your_row_key", "column_family", "coulumn_qualifier");}
}
