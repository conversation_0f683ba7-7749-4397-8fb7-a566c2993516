<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.flipkart.fintech</groupId>
    <artifactId>pinaka</artifactId>
    <packaging>pom</packaging>
    <version>3.4.26-SM</version>
    <modules>
        <module>pinaka-service</module>
        <module>pinaka-api</module>
        <module>pinaka-client</module>
        <module>ams-bridge</module>
        <module>profile-service</module>
        <module>offer-orchestrator</module>
        <module>pinaka-common</module>
        <module>profile-service-client</module>
        <module>profile-api</module>
        <module>ams-connector</module>
        <module>profile-page-handler</module>
        <module>page-service</module>
    </modules>
    <properties>
        <pinaka.version>3.4.24-SM</pinaka.version>
        <jacoco.version>0.7.5.201505241946</jacoco.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <guice.version>4.0</guice.version>
        <jdk.version>1.8</jdk.version>
        <ardour.api.version>1.0.83</ardour.api.version>
        <ardour.client.version>1.0.83</ardour.client.version>
        <affordability.service.client.version>5.0.3-SM</affordability.service.client.version>
        <flux.version>1.2.0</flux.version>
        <dropwizard.version>2.0.4</dropwizard.version>
        <dropwizard.hibernate.version>2.1.12</dropwizard.hibernate.version>
        <onboarding.client.version>1.5.16</onboarding.client.version>
        <junit.version>4.11</junit.version>
        <helios.proxy.lib.version>1.0.3</helios.proxy.lib.version>
        <citadel.version>3.0.8-SM</citadel.version>
        <winterfell.version>1.2.1-SM</winterfell.version>
        <liquibase.version>3.8.5</liquibase.version>
        <h2database.version>1.4.195</h2database.version>
        <underwriting.version>1.8.39</underwriting.version>
        <user.data.client.version>1.0-SNAPSHOT</user.data.client.version>
        <pandora.version>1.5.16-SM</pandora.version>
        <skipCT>true</skipCT>
        <fintech.logger.version>1.0.0</fintech.logger.version>
        <aapi.models.version>3.4.1-SM</aapi.models.version>
        <dataprovider.core.version>3.0.0-SM</dataprovider.core.version>
        <aapi.multiwidget.version>3.0.0-SM</aapi.multiwidget.version>
        <dropwizard.swagger.version>2.1.4-1</dropwizard.swagger.version>
        <pub.sub.version>1.0.15-JAVA-8</pub.sub.version>
        <events.common.version>1.0.11-JAVA-8</events.common.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.25.2</version>
            </dependency>
            <dependency>
                <groupId>com.sumo.dataplatform</groupId>
                <artifactId>event-publisher</artifactId>
                <version>1.5.18</version>
            </dependency>
            <dependency>
                <groupId>com.sumo.dataplatform</groupId>
                <artifactId>schema-registry</artifactId>
                <version>1.5.18</version>
            </dependency>
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>google-cloud-pubsub</artifactId>
                <version>1.132.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro</artifactId>
                <version>1.11.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro-maven-plugin</artifactId>
                <version>1.11.3</version>
            </dependency>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>5.7.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.flipkart.fpg</groupId>
                <artifactId>dexter-api</artifactId>
                <version>1.0.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>fk-art-snapshot</id>
            <name>Flipkart-Artifactory</name>
            <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
        </repository>
        <repository>
            <id>fk-art-release</id>
            <name>Flipkart-Artifactory</name>
            <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
        </repository>
    </repositories>

    <distributionManagement>
        <snapshotRepository>
            <id>fk-art-snapshot</id>
            <name>libs-snapshot</name>
            <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
        </snapshotRepository>
        <repository>
            <id>fk-art-release</id>
            <name>libs-rel</name>
            <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
        </repository>
    </distributionManagement>

    <pluginRepositories>
        <pluginRepository>
            <id>fk-art-snapshot</id>
            <name>libs-snapshot</name>
            <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
        </pluginRepository>
        <pluginRepository>
            <id>fk-art-release</id>
            <name>libs-rel</name>
            <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.3.0</version>
                <dependencies>
                    <dependency>
                        <groupId>de.skuzzle.enforcer</groupId>
                        <artifactId>restrict-imports-enforcer-rule</artifactId>
                        <version>2.1.0</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>check-logging-imports</id> <!-- put an explanatory ID here -->
                        <phase>process-sources</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <RestrictImports>
                                    <!-- Define an explanatory reason why these imports are prohibited -->
                                    <reason>Use @CustomLog for logging</reason>
                                    <!-- Specify a single pattern to be banned -->
                                    <bannedImports>
                                        <bannedImport>lombok.extern.slf4j.*</bannedImport>
                                        <bannedImport>org.slf4j.Logger</bannedImport>
                                    </bannedImports>
                                </RestrictImports>

                                <!-- You could have another rule instance here for restricting further imports -->
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>post-unit-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <!-- Sets the path to the file which contains the execution data. -->

                            <dataFile>target/jacoco.exec</dataFile>
                            <!-- Sets the output directory for the code coverage report. -->
                            <outputDirectory>target/site/jacoco-ut</outputDirectory>
                        </configuration>
                    </execution>
                </executions>

            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
            </plugin>
        </plugins>
    </build>

</project>

