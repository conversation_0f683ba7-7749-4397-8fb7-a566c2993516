{"application_data": {"sm_user_id": "SMA28A445EAF9134A7B9F5A9E3A23CEF041", "code": null, "offerDetails": {"bankOffer": {"charges": {"PROCESSING_FEE": {"amount": "4250", "gst": "765", "totalAmount": "5015"}, "STAMP_DUTY": {"amount": "0", "gst": "0", "totalAmount": "0"}}, "emi": {"amount": "8371", "endDate": "2025-07-23", "startDate": "2024-07-23"}, "loanAmount": "85000", "netDisbursalAmount": "79985", "roi": "32", "tenure": {"unit": "MONTH", "value": "12"}}, "consentData": {"consentType": "CI_CONSENT", "userIP": "*************, *************", "deviceId": "rvclx35utcgod3107vtc204oo691717672497425", "deviceInfo": "Mozilla/5.0 (Linux; Android 14; LE2121 Build/UKQ1.230924.001) FKUA/Retail/2040200/Android/Mobile (OnePlus/LE2121/756854f4d799b4bde06770fdd82a1c88)", "currentTimeStamp": *************}}, "user_details": {"pan": "qeybPpM/f9W0AO0wmQOG7A==", "dob": "1996-02-06", "gender": "M", "shipping_address_id": "CNTCT67AA5FA15E084CCC8D460B665", "house_number": null, "area": null, "city": null, "state": null, "pincode": null, "bin_score": "312.259", "employment_type": null, "employer_id": null, "employer_name": null, "industry_id": null, "industry_name": null, "monthly_income": null, "annual_turn_over": null, "pan_consent_provided": false, "offer_consent_provided": false, "segment": null, "subsegment": null}, "verifyPincode": {"pincode": "507115", "city": "<PERSON><PERSON><PERSON><PERSON>", "state": "TELANGANA", "valid": true}, "addressDetails": {"houseNumber": "BoWQ6Oi8D961jydFL5mFEw==", "area": "FOusHXUuOh51n6MHKdXMdXjQJjrFSLztJTXYxVEEfYg=", "pincode": "507115", "consentData": {"consentType": "CI_CONSENT", "userIP": "*************, ***************", "deviceId": "rvclx35utcgod3107vtc204oo691717672497425", "deviceInfo": "Mozilla/5.0 (Linux; Android 14; LE2121 Build/UKQ1.230924.001) FKUA/Retail/2040200/Android/Mobile (OnePlus/LE2121/756854f4d799b4bde06770fdd82a1c88)", "currentTimeStamp": 1719127951350}, "state": "Wv0yipQso33yI4CdfQ0CUw==", "city": "uoXuBGHOAJ8n0TFoSbDEPA=="}, "verifyPan": {"requestId": "b98aa010-3132-11ef-ade4-13408c380caf", "firstName": "S4Tr3cEHjOvk94JmyoUsvw==", "lastName": "GLcX2Uhb8LkG7zVnX1rgOA==", "fullName": "QBllLjsdWzZ5JUgYPdjkjDF78OiEnwQ9Yv85s2lKzX8=", "nameMatch": false, "nameMatchScore": 0, "phoneNumberMatch": null}, "getOffer": {"lenderApplicationId": "INST1719128815829462", "applicationStatus": {"status": "IN_PROGRESS", "rejectReason": ""}, "journeyState": {"subStatus": null, "ts": 1719128041, "redirectionUrl": null, "additionalDataModesAvailable": null, "applicationState": "INITIAL_OFFER_GENERATION_SUCCESS"}, "generatedOffer": {"offer": {"id": "2450", "maxSanctionedAmount": 85000.0, "validTill": 1711120041324, "stepper": 100.0, "emiFirstDate": "2024-07-23", "offerTable": [{"sanctionedAmount": {"min": 60000.0, "max": 85000.0}, "tenure": {"value": 12, "unit": "MONTH"}, "pf": {"type": "PERCENTAGE", "value": 5.0, "gst": 18.0}, "roi": 32.0}], "stampDuty": null}, "offerType": "INITIAL"}}, "lenderRedirectionUrl": "https://apply.paywithring.com/partner?channel=SUPERMONEY&channel_source=RING_WEB&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJJREVQOTE3MTI4ODExMjU4WEpXQyIsImF1ZCI6InVzZXIiLCJpc3MiOiJBdXRoIFNlcnZpY2UiLCJpYXQiOjE3MTk1MTU3ODcsImV4cCI6MTcxOTU1ODk4NywibmJmIjoxNzE5NTE1Nzg3LCJqdGkiOiI4YjRjMDc2MGExNmU3OTc1OTAzOTJkYTFmNGU2MDY5OCIsImtleV9pZCI6ImE5ZlZqMzFkamY1dDczSDJFUnFOZG42SkN5U3JvN281IiwiY2xpZW50X2lkIjoiYTlmVmozMWRqZjV0NzNIMkVScU5kbjZKQ3lTcm83bzUiLCJ0ZW5hbnQiOiJiSDQxT3k4MVpjWjZxeGdEYTZiY1JKNEJyOU9ZZHRLSSIsInJlZmVyZW5jZV9udW1iZXIiOiJJREVQOTE3MTI4ODExMjU4WEpXQyIsInJlZmVyZW5jZV90eXBlIjoidXNlciJ9.fn-Q96okfn2MpeYGAZzRyNULQVkfeDtBAiuMrfo5s1I", "lender_details": null, "expiry": null, "leadId": "APP2405231257593391415135895573237495958", "error_message": null, "consent_details_list": null, "application_created_at": "2024-06-23 13:02:31", "basicDetails": {"firstName": "S4Tr3cEHjOvk94JmyoUsvw==", "lastName": "GLcX2Uhb8LkG7zVnX1rgOA==", "pincode": "507115", "consentData": {"consentType": "CI_CONSENT", "userIP": "*************, ***************", "deviceId": "rvclx35utcgod3107vtc204oo691717672497425", "deviceInfo": "Mozilla/5.0 (Linux; Android 14; LE2121 Build/UKQ1.230924.001) FKUA/Retail/2040200/Android/Mobile (OnePlus/LE2121/756854f4d799b4bde06770fdd82a1c88)", "currentTimeStamp": 1719127914738}, "gender": "M", "employmentType": "SelfEmployed", "dob": "qdIRz+rdows1VeeKKIDPdA==", "pan": "NChz/lZgegtAqUa9nFKMgw==", "email": "j4dTzVuXi6cqgugrjp94G3rlTJBjMnRm7RVfsg+1dX0="}, "generateOffer": {"lenderApplicationId": "INST1719128815829462", "applicationStatus": {"status": "IN_PROGRESS", "rejectReason": ""}, "journeyState": {"subStatus": null, "ts": 1719128018, "redirectionUrl": null, "additionalDataModesAvailable": null, "applicationState": "INITIAL_OFFER_GENERATION_INIT"}}, "submitOffer": {"lenderApplicationId": "INST1719128815829462", "applicationStatus": {"status": "IN_PROGRESS", "rejectReason": ""}, "journeyState": {"subStatus": null, "ts": 1719515787, "redirectionUrl": "https://apply.paywithring.com/partner?channel=SUPERMONEY&channel_source=RING_WEB&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJJREVQOTE3MTI4ODExMjU4WEpXQyIsImF1ZCI6InVzZXIiLCJpc3MiOiJBdXRoIFNlcnZpY2UiLCJpYXQiOjE3MTk1MTU3ODcsImV4cCI6MTcxOTU1ODk4NywibmJmIjoxNzE5NTE1Nzg3LCJqdGkiOiI4YjRjMDc2MGExNmU3OTc1OTAzOTJkYTFmNGU2MDY5OCIsImtleV9pZCI6ImE5ZlZqMzFkamY1dDczSDJFUnFOZG42SkN5U3JvN281IiwiY2xpZW50X2lkIjoiYTlmVmozMWRqZjV0NzNIMkVScU5kbjZKQ3lTcm83bzUiLCJ0ZW5hbnQiOiJiSDQxT3k4MVpjWjZxeGdEYTZiY1JKNEJyOU9ZZHRLSSIsInJlZmVyZW5jZV9udW1iZXIiOiJJREVQOTE3MTI4ODExMjU4WEpXQyIsInJlZmVyZW5jZV90eXBlIjoidXNlciJ9.fn-Q96okfn2MpeYGAZzRyNULQVkfeDtBAiuMrfo5s1I", "additionalDataModesAvailable": null, "applicationState": "OFFER_SUBMISSION_COMPLETED"}}, "offer_id": null, "external_user_id": "ACCBF2A7EAB4F454CABB05634617216AA90V", "journey_context": "PERSONAL_LOAN_APPLICATION_JOURNEY", "product_type": "PERSONAL_LOAN", "createApplication": {"lenderApplicationId": "INST1719128815829462", "validTill": 1721720017679, "customerType": "NTB", "applicationStatus": {"status": "IN_PROGRESS", "rejectReason": ""}, "journeyState": {"subStatus": null, "ts": **********, "redirectionUrl": null, "additionalDataModesAvailable": null, "applicationState": "APPLICATION_CREATION"}}, "form_details": null, "financial_provider": "RING", "workDetails": {"industryName": {"id": "159", "title": "TRADE-RETAIL", "type": "PLAIN_TEXT"}, "income": "800000", "pincode": "507115", "consentData": {"consentType": "CI_CONSENT", "userIP": "*************, ***************", "deviceId": "rvclx35utcgod3107vtc204oo691717672497425", "deviceInfo": "Mozilla/5.0 (Linux; Android 14; LE2121 Build/UKQ1.230924.001) FKUA/Retail/2040200/Android/Mobile (OnePlus/LE2121/756854f4d799b4bde06770fdd82a1c88)", "currentTimeStamp": 1719127975457}, "incomeSource": "CASH", "organization": "Asif floor mill "}}, "sub_applications": {"sub_application_info_map": {}}, "external_user_id": "ACCBF2A7EAB4F454CABB05634617216AA90V", "sm_user_id": "SMA28A445EAF9134A7B9F5A9E3A23CEF041", "application_type": "PERSONAL_LOAN_RING", "application_id": "APP2406231302312441263721367267553834771", "parent_application_id": null, "tenant": "CALM", "state": "IN_PROGRESS", "application_state": "LENDER_JOURNEY", "workflow_id": "N_sXZ5UXIIARJhSQ", "created_at": 1719127951043, "updated_at": 1719515787764, "merchant_id": "FLIPKART"}