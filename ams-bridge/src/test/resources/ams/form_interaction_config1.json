{"type": "FORM_INTERACTION", "pageUrl": "/ams/1/bank-details", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"amount": {"type": "integer"}, "interest": {"type": "number"}, "tenure": {"type": "integer"}, "consentData": {"type": "object", "properties": {"ip": {"type": "string"}, "timestamp": {"type": "string"}}, "required": ["ip", "timestamp"]}}, "required": ["amount", "interest", "tenure", "consentData"]}, "sample": {"amount": 5000, "interest": 10.5, "tenure": 24, "consentData": {"ip": "***********", "timestamp": "13:45 11/12/2023"}}}