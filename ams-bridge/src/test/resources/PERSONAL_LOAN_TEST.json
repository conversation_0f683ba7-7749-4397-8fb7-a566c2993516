{"defaultInteraction": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/missing-page", "schema": {}}, "taskKeysList": [{"taskKey": "FORM0", "interactionKey": "FORM0"}, {"taskKey": "FORM1", "interactionKey": "FORM1"}, {"taskKey": "FORM2", "interactionKey": "FORM2"}, {"taskKey": "vkycRedirection", "interactionKey": "VKYC_REDIRECTION"}], "applicationStatesList": [{"applicationState": "GENERATE_OFFER", "interactionKey": "LOADING_SCREEN"}, {"applicationState": "POLL_OFFER", "interactionKey": "LOADING_SCREEN"}], "interactionKeyMap": {"FORM0": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/basic-details", "schema": {}}, "FORM1": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/bank-details", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"amount": {"type": "integer"}, "interest": {"type": "number"}, "tenure": {"type": "integer"}, "consentData": {"type": "object", "properties": {"ip": {"type": "string"}, "timestamp": {"type": "string"}}, "required": ["ip", "timestamp"]}}, "required": ["amount", "interest", "tenure", "consentData"]}, "sample": {"amount": 5000, "interest": 10.5, "tenure": 24, "consentData": {"ip": "***********", "timestamp": "13:45 11/12/2023"}}}, "FORM2": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/bank-details", "schema": {}}, "LOADING_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/loading-screen", "schema": {}}, "EMANDATE_REDIRECTION": {"type": "MANDATE_INTERACTION", "dataKey": "generateEmandateUrl", "readRepair": false}, "SELFIE": {"type": "DOCUMENT_INTERACTION", "schema": {}}, "VKYC_REDIRECTION": {"type": "VKYC_INTERACTION", "dataKey": "generateVkycUrl", "readRepair": false, "schema": {}}}}