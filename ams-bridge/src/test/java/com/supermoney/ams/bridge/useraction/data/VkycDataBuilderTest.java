package com.supermoney.ams.bridge.useraction.data;

import static com.supermoney.ams.bridge.utils.FileUtils.readType;
import static org.junit.Assert.*;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.VkycSubmitRequest;
import java.util.Map;
import org.junit.Test;

public class VkycDataBuilderTest {

  @Test
  public void correct() {
    VkycSubmitRequest request = readFormSubmitRequest("ams/vkyc1.json");
    Map<String, Object> data = new VkycDataBuilder(request).build();
    assertEquals(4, data.size());
  }

  private VkycSubmitRequest readFormSubmitRequest(String fileName) {
    Class<VkycSubmitRequest> type = VkycSubmitRequest.class;
    return readType(fileName, type);
  }

}