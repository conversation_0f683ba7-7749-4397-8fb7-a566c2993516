package com.supermoney.ams.bridge.lifecycle;

import static com.supermoney.ams.bridge.utils.FileUtils.readType;
import static org.junit.Assert.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.winterfell.api.request.PendingTask;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.dao.FileBasedConfigurationDao;
import com.supermoney.ams.bridge.models.LifecycleConfig;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;
import org.junit.Before;
import org.junit.Test;

public class LifecycleManagerTest {
  static class Files {
    public static final String AMS_APPLICATION_0_JSON = "ams/application0.json";
  }

  LifecycleManager lifecycleManager;

  @Before
  public void setUp() throws Exception {
    FileBasedConfigurationDao configurationDao = new FileBasedConfigurationDao();
    lifecycleManager = new LifecycleManagerImpl(configurationDao);
  }

  @Test
  public void noExpiry() {
    ApplicationDataResponse applicationDataResponse = readApplicationDataResponse(Files.AMS_APPLICATION_0_JSON);
    boolean hasExpired = lifecycleManager.hasExpired(applicationDataResponse);
    assertFalse("Application has not expired", hasExpired);
  }

  @Test
  public void offerExpiry() throws JsonProcessingException {
    ApplicationDataResponse applicationDataResponse = readApplicationDataResponse(Files.AMS_APPLICATION_0_JSON);
    applicationDataResponse.setApplicationState("OFFER_DETAILS");
    PendingTask task = applicationDataResponse.getPendingTask().get(0);
    task.setTaskKey("offer_details");
    boolean hasExpired = lifecycleManager.hasExpired(applicationDataResponse);
    assertTrue("Application has not expired", hasExpired);
  }

  @Test
  public void stateBasedExpiry() throws JsonProcessingException {
    ApplicationDataResponse applicationDataResponse = readApplicationDataResponse(Files.AMS_APPLICATION_0_JSON);
    applicationDataResponse.setApplicationState("REJECTED");
    applicationDataResponse.setPendingTask(new ArrayList<>());
    boolean hasExpired = lifecycleManager.hasExpired(applicationDataResponse);
    assertTrue("Application has not expired", hasExpired);
  }

  @Test
  public void stateBasedNoExpiry() throws JsonProcessingException {
    ApplicationDataResponse applicationDataResponse = readApplicationDataResponse(Files.AMS_APPLICATION_0_JSON);
    applicationDataResponse.setApplicationState("LENDER_PLATFORM");
    applicationDataResponse.setPendingTask(new ArrayList<>());
    boolean hasExpired = lifecycleManager.hasExpired(applicationDataResponse);
    assertFalse("Application has not expired", hasExpired);
  }

  private ApplicationDataResponse readApplicationDataResponse(String fileName) {
    Class<ApplicationDataResponse> type = ApplicationDataResponse.class;
    return readType(fileName, type);
  }

}