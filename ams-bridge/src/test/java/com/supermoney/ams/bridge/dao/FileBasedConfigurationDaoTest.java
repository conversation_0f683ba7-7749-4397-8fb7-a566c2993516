package com.supermoney.ams.bridge.dao;

import org.junit.Before;
import org.junit.Test;

public class FileBasedConfigurationDaoTest {

  private static class ApplicationType {
    public static final String IDFC = "PERSONAL_LOAN_IDFC";
  }
  ConfigurationDao configurationDao;

  @Before
  public void setUp() throws Exception {
    configurationDao = new FileBasedConfigurationDao();
  }

  @Test
  public void getConfigurationForIDFC() {
    configurationDao.getConfig(ApplicationType.IDFC);
  }

  @Test
  public void getConfigurationForMoneyView(){
    configurationDao.getConfig("PERSONAL_LOAN_MONEYVIEW");
  }

}