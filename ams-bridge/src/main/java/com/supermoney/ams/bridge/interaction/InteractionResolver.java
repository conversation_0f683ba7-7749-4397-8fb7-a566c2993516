package com.supermoney.ams.bridge.interaction;

import com.google.inject.ImplementedBy;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.ams.bridge.models.InteractionConfig;

@ImplementedBy(InteractionResolverImpl.class)
public interface InteractionResolver {

  InteractionConfig getInteraction(ApplicationState state);

  boolean isSameInteraction(ApplicationState state1, ApplicationState state2);
}
