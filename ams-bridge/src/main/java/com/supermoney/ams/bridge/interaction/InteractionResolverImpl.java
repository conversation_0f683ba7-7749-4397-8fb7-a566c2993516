package com.supermoney.ams.bridge.interaction;

import com.supermoney.ams.bridge.dao.ConfigurationDao;
import com.supermoney.ams.bridge.models.ApplicationConfig;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.ams.bridge.models.InteractionConfig;
import java.util.Optional;
import javax.inject.Inject;
import lombok.CustomLog;

@CustomLog
public class InteractionResolverImpl implements InteractionResolver {
  private final ConfigurationDao configurationDao;

  @Inject
  public InteractionResolverImpl(ConfigurationDao configurationDao) {
    this.configurationDao = configurationDao;
  }

  @Override
  public InteractionConfig getInteraction(ApplicationState state) {
    ApplicationConfig config = configurationDao.getConfig(state.getApplicationType());
    return findInteraction(state, config);
  }

  @Override
  public boolean isSameInteraction(ApplicationState state1, ApplicationState state2) {
    if (!state1.getApplicationType().equals(state2.getApplicationType())) {
      throw new IllegalArgumentException("ApplicationType mismatch");
    }
    ApplicationConfig config = configurationDao.getConfig(state1.getApplicationType());
    Optional<String> interactionKey1 = config.findInteractionKey(state1);
    Optional<String> interactionKey2 = config.findInteractionKey(state2);
    if (!interactionKey1.isPresent()) return false;
    if (!interactionKey2.isPresent()) return false;
    return interactionKey1.get().equals(interactionKey2.get());
  }

  private static InteractionConfig findInteraction(ApplicationState state,
      ApplicationConfig config) {
    Optional<InteractionConfig> interactionConfig = config.getUserInteraction(state);
    if (interactionConfig.isPresent()) {
      return interactionConfig.get();
    }
    log.info("No page interaction found for application {}, for account {}, for page {}",
        state.getApplicationId(), state.getAccountId(), config.getDefault());
    return config.getDefault();
  }

}
