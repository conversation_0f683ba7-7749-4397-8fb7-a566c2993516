package com.supermoney.ams.bridge.handlers;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserRequestActionType;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ActionType;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Params;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.supermoney.ams.bridge.models.interaction.LenderInteractionConfig;
import lombok.Builder;
import lombok.CustomLog;
import org.elasticsearch.common.inject.assistedinject.Assisted;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.supermoney.ams.bridge.utils.UrlResolverFactory.getUrlFromApplicationDataResponseAndTaskKeys;


@Builder
@CustomLog
public class LenderInteractionHandler implements InteractionHandler {

    private final LenderInteractionConfig interactionConfig;


    @Inject
    public LenderInteractionHandler(@Assisted LenderInteractionConfig interactionConfig) {
        this.interactionConfig = interactionConfig;
    }

    @Override
    public PageActionResponse getPageActionResponse(ApplicationDataResponse applicationDataResponse, MerchantUser merchantUser) {
        Action action = getAction(applicationDataResponse, merchantUser);
        Params params=new Params();
        params.setApplicationId(applicationDataResponse.getApplicationId());
        return new PageActionResponse(action, true, null, params);
    }

    private Action getAction(ApplicationDataResponse applicationDataResponse, MerchantUser merchantUser) {
        String url = createUrl(applicationDataResponse);
        // todo add from config
        Map<String, Object> params = interactionConfig.getPageParams();
        if(applicationDataResponse.getApplicationType().equals("PERSONAL_LOAN_HDFC")){
            return new Action(url, ActionType.WEB_VIEW,params);
        }
        return new Action(url, ActionType.OPEN_URL_EXTERNAL, params);
    }

    private String createUrl(ApplicationDataResponse applicationDataResponse) {
        return getUrlFromApplicationDataResponseAndTaskKeys(applicationDataResponse, interactionConfig.getKeys(), interactionConfig);
    }

    @Override
    public boolean checkReadRepair(ApplicationDataResponse applicationDataResponse) {
        return interactionConfig.getReadRepair();
    }

    @Override
    public UserActionRequest getDummyUserActionRepairRequest(ApplicationDataResponse applicationDataResponse) {
        FormSubmitRequest actionRequest = new FormSubmitRequest();
        actionRequest.setAccountId(applicationDataResponse.getExternalUserId());
        actionRequest.setSmUserId(applicationDataResponse.getSmUserId());
        actionRequest.setApplicationId(applicationDataResponse.getApplicationId());
        actionRequest.setType(UserRequestActionType.FORM);
        actionRequest.setTaskId(applicationDataResponse.getPendingTask().get(0).getTaskId());
        actionRequest.setTaskKey(applicationDataResponse.getPendingTask().get(0).getTaskKey());
        actionRequest.setProcessInstance(applicationDataResponse.getPendingTask().get(0).getProcessInstanceId());
        actionRequest.setFormData(ImmutableMap.of("refreshLenderStatus", true));
        return actionRequest;
    }
    public UserActionRequest getDummyUserActionRepairRequest(ApplicationDataResponse applicationDataResponse, Map<String,String> additionalParams) {
        try {
            FormSubmitRequest formSubmitRequest = (FormSubmitRequest) getDummyUserActionRepairRequest(applicationDataResponse);
            formSubmitRequest.setFormData(ImmutableMap.of("additionalParams", additionalParams));
            return formSubmitRequest;
        }catch(Exception e) {
            return getDummyUserActionRepairRequest(applicationDataResponse);
        }
    }
}
