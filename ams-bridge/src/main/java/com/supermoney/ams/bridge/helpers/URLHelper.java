package com.supermoney.ams.bridge.helpers;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.supermoney.ams.bridge.exceptions.InvalidMerchantException;
import com.supermoney.ams.bridge.models.MerchantJourneyConfig;

import java.util.Map;


@Singleton
public class URLHelper {
    private final Map<String, MerchantJourneyConfig> merchantJourneyUrlConfigMap;

    @Inject
    public URLHelper(Map<String, MerchantJourneyConfig> merchantJourneyUrlConfigMap) {
        this.merchantJourneyUrlConfigMap = merchantJourneyUrlConfigMap;
    }

    public String getPLStatusUrl(String merchant) throws InvalidMerchantException {
        return getMerchantJourneyUrlConfig(merchant).getStatusUrl();
    }

    public String getPLLenderCallbackUrl(String merchant) throws InvalidMerchantException {
        return getMerchantJourneyUrlConfig(merchant).getCallbackUrl();
    }

    public String getPLResumeUrl(String merchant) throws InvalidMerchantException {
        return getMerchantJourneyUrlConfig(merchant).getResumeUrl();
    }

    private MerchantJourneyConfig getMerchantJourneyUrlConfig(String merchant) throws InvalidMerchantException {
        if (merchantJourneyUrlConfigMap.containsKey(merchant)) {
            return merchantJourneyUrlConfigMap.get(merchant);
        }

        throw new InvalidMerchantException("Invalid merchant: " + merchant);
    }
}
