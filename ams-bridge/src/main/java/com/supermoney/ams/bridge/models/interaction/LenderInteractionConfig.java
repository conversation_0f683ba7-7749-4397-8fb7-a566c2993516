package com.supermoney.ams.bridge.models.interaction;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.supermoney.ams.bridge.models.InteractionConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 11/12/23
 */
@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public class LenderInteractionConfig extends InteractionConfig {
    @JsonProperty
    private String dataKey;
    @JsonProperty
    private Boolean readRepair;
    @JsonProperty
    private Map<String, Object> pageParams;
    @JsonProperty
    private List<String> keys;
}
