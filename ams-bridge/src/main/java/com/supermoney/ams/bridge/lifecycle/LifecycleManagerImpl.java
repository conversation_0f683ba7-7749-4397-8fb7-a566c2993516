package com.supermoney.ams.bridge.lifecycle;

import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.dao.ConfigurationDao;
import com.supermoney.ams.bridge.models.ApplicationConfig;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.ams.bridge.models.LifecycleConfig;
import com.supermoney.ams.bridge.utils.TimeUtil;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import org.apache.commons.lang.time.DateUtils;

public class LifecycleManagerImpl implements LifecycleManager {

  private final ConfigurationDao configurationDao;

  @Inject
  public LifecycleManagerImpl(ConfigurationDao configurationDao) {
    this.configurationDao = configurationDao;
  }

  @Override
  public boolean hasExpired(ApplicationDataResponse applicationDataResponse) {
    //todo: understand approved loan
    ApplicationState applicationState = ApplicationState.create(applicationDataResponse);
    ApplicationConfig config = configurationDao.getConfig(applicationState.getApplicationType());
    LifecycleConfig lifecycle = config.getLifecycle(applicationState);
    if (lifecycle == null || lifecycle.getTimeValue() <= 0) return false;
    Date updatedAt = applicationDataResponse.getUpdatedAt();
    Date expiryDate = TimeUtil.addTime(updatedAt, lifecycle);
    Date currentDate = new Date();
    return expiryDate.before(currentDate);
  }

}
