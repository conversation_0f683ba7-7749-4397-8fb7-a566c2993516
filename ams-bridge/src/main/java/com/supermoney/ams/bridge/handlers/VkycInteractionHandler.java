package com.supermoney.ams.bridge.handlers;

import com.flipkart.fintech.pandora.api.model.idfc.EmandateResponse;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ActionType;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Params;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.models.interaction.VkycInteractionConfig;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

public class VkycInteractionHandler implements InteractionHandler {

  private final VkycInteractionConfig interactionConfig;

  public VkycInteractionHandler(VkycInteractionConfig interactionConfig) {
    this.interactionConfig = interactionConfig;
  }

  @Override
  public PageActionResponse getPageActionResponse(ApplicationDataResponse applicationDataResponse,
      MerchantUser merchantUser) {
    Action action = getAction(applicationDataResponse, merchantUser);
    Params params=new Params();
    params.setApplicationId(applicationDataResponse.getApplicationId());
    return new PageActionResponse(action, true, null, params);
  }

  private Action getAction(ApplicationDataResponse applicationDataResponse, MerchantUser merchantUser) {
    String url = createUrl(applicationDataResponse);
    return new Action(url, ActionType.OPEN_URL_EXTERNAL);
  }

  private String createUrl(ApplicationDataResponse applicationDataResponse) {
    Object data = applicationDataResponse.getApplicationData()
        .get(interactionConfig.getDataKey());
    EmandateResponse response = ObjectMapperUtil.get().convertValue(data, EmandateResponse.class); //todo fix this
    return response.getUrl();
  }
}
