package com.supermoney.ams.bridge.models.application;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.supermoney.ams.bridge.models.LifecycleConfig;
import javax.annotation.Nullable;
import lombok.Getter;

@Getter
public class ApplicationStateKey {

  @JsonProperty
  private String applicationState;
  @JsonProperty
  private String interactionKey;
  @JsonProperty
  @Nullable
  private LifecycleConfig lifecycleConfig;

}
