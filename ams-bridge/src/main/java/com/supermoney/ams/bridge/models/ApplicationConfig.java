package com.supermoney.ams.bridge.models;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.supermoney.ams.bridge.models.application.V0ApplicationConfig;
import com.supermoney.ams.bridge.models.application.V1ApplicationConfig;
import java.util.Optional;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "version", include = JsonTypeInfo.As.PROPERTY,
    defaultImpl = V1ApplicationConfig.class)
@JsonSubTypes({
    @JsonSubTypes.Type(name = "VO", value = V0ApplicationConfig.class),
    @JsonSubTypes.Type(name = "V1", value = V1ApplicationConfig.class)
})
public interface ApplicationConfig {

  Optional<InteractionConfig> getUserInteraction(ApplicationState applicationState);

  Optional<String> findInteractionKey(ApplicationState applicationState);

  InteractionConfig getDefault();

  LifecycleConfig getLifecycle(ApplicationState applicationState);

}
