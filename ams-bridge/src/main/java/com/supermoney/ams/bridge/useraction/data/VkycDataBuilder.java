package com.supermoney.ams.bridge.useraction.data;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.VkycSubmitRequest;
import java.util.HashMap;
import java.util.Map;

public class VkycDataBuilder implements DataBuilder {

  private final VkycSubmitRequest request;

  public VkycDataBuilder(VkycSubmitRequest userActionRequest) {
    this.request = userActionRequest;
  }

  @Override
  public Map<String, Object> build() {
    Map<String, Object> data = new HashMap<>();
    data.put("reqId", request.getReqId());
    data.put("lender", request.getLender());
    data.put("status", request.getStatus());
    data.put("failureReason", request.getFailureReason());
    return data;
  }
}
