{"version": "V0", "defaultInteraction": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/missing-page-sandbox", "schema": {}}, "taskKeysList": [{"taskKey": "personal_details_node", "interactionKey": "CI_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"taskKey": "personal_details_node_addr", "interactionKey": "CI_DETAILS_ADDR", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"taskKey": "occupation_details_node", "interactionKey": "ADDITIONAL_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"taskKey": "offer_details_node", "interactionKey": "OFFER_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "sid-88FB24B0-9511-4897-9F57-5356E0C81332_addr", "interactionKey": "CI_DETAILS_ADDR", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"taskKey": "sid-B80692D8-AF40-4202-BFBF-5F92D6A96BF3", "interactionKey": "LENDER_PLATFORM"}, {"taskKey": "sid-A742AC0C-B31C-4E3A-8EFA-50A3BEE5B166", "interactionKey": "APPLICATION_COMPLETED"}, {"taskKey": "addressDetails", "interactionKey": "ADDRESS_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "workDetails", "interactionKey": "WORK_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "POLL_STATUS_TIMER", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 7}}, {"applicationState": "APP_STATUS_START", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 7}}, {"applicationState": "APP_STATUS_END", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 7}}, {"taskKey": "rejectScreen", "interactionKey": "REJECTED", "lifecycleConfig": {"timeUnit": "MINUTES", "timeValue": 1}}, {"taskKey": "retryScreen1", "interactionKey": "RETRY_SCREEN_1", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 7}}, {"taskKey": "retryScreen2", "interactionKey": "RETRY_SCREEN_2", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 7}}], "applicationStatesList": [{"applicationState": "REJECTED", "interactionKey": "REJECTED", "lifecycleConfig": {"timeUnit": "MINUTES", "timeValue": 1}}, {"applicationState": "FETCH_PINCODE_SERVICABLITY_START", "interactionKey": null, "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "FETCH_PINCODE_SERVICABLITY_END", "interactionKey": null, "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "CUSTOMER_IDENTIFICATION_START", "interactionKey": null, "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "CUSTOMER_IDENTIFICATION_END", "interactionKey": null, "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "ELIGIBLE_OFFER_START", "interactionKey": null, "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "ELIGIBLE_OFFER_END", "interactionKey": null, "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "SUBMIT_OFFER_START", "interactionKey": null, "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "SUBMIT_OFFER_END", "interactionKey": null, "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "APPLICATION_COMPLETED", "interactionKey": null}, {"applicationState": "rejected", "interactionKey": "REJECTED", "lifecycleConfig": {"timeUnit": "MINUTES", "timeValue": 1}}], "interactionKeyMap": {"BASIC_DETAILS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/basic-details-sandbox", "schema": {}}, "ADDRESS_DETAILS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/address-details", "schema": {}}, "WORK_DETAILS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/employment-details", "schema": {}}, "LOADING_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/loading-screen", "schema": {}}, "REJECTED": {"type": "FORM_INTERACTION", "readRepair": false, "isTerminalState": true, "pageUrl": "/ams/1/rejected-v2", "schema": {}}, "RETRY_SCREEN_1": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/retry-screen", "schema": {}}, "RETRY_SCREEN_2": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/retry-screen", "schema": {}}}}