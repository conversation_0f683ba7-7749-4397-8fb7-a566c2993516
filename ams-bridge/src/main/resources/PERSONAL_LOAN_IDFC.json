{"defaultInteraction": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/missing-page-idfc", "schema": {}}, "taskKeysList": [{"taskKey": "basicDetails", "interactionKey": "BASIC_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 45}}, {"taskKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interactionKey": "AADHAAR_FORM", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "kycOtpVerificationScreen", "interactionKey": "KYC_OTP_VERIFICATION_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "selfieScreen", "interactionKey": "SELFIE_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "offerScreen", "interactionKey": "OFFER_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "bankDetails", "interactionKey": "BANK_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "ckycDetails", "interactionKey": "CKYC_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "repaymentModes", "interactionKey": "REPAYMENT_MODES", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "applicationStatus", "interactionKey": "APPLICATION_STATUS"}, {"taskKey": "kfsOtpVerification", "interactionKey": "KFS_OTP_VERIFICATION", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "kfsScreen", "interactionKey": "KFS_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "applicationStatus", "interactionKey": "APPLICATION_STATUS"}, {"taskKey": "faceMatchError", "interactionKey": "FACE_MATCH_ERROR", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "repaymentSchedule", "interactionKey": "REPAYMENT_SCHEDULE", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "emandateRedirection", "interactionKey": "EMANDATE_REDIRECTION", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "vkycRedirection", "interactionKey": "VKYC_REDIRECTION", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "ekycDetails", "interactionKey": "EKYC_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "generateEtbOtpScreen", "interactionKey": "GENERATE_ETB_OTP_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "etbOtpVerification", "interactionKey": "ETB_OTP_VERIFICATION", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "onBoardRetryScreen", "interactionKey": "ONBOARD_RETRY_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "emandateRetryTimer", "interactionKey": "EMANDATE_RETRY_TIMER", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 45}}], "applicationStatesList": [{"applicationState": "VERIFY_PAN", "interactionKey": "VERIFY_PAN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 45}}, {"applicationState": "GENERATE_OFFER", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 45}}, {"applicationState": "POLL_OFFER", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 45}}, {"applicationState": "POLL_OFFER_TIMER", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 45}}, {"applicationState": "GET_APPROVAL_STATUS_TIMER", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 45}}, {"applicationState": "SEARCH_DOWNLOAD_CKYC_TIMER", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "EMANDATE_RETRY_TIMER", "interactionKey": "EMANDATE_RETRY_TIMER", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "GET_EMANDATE_STATUS_RETRY", "interactionKey": "EMANDATE_RETRY_TIMER", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "GET_APPROVAL_STATUS", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "POSITIVE_CONFIRMATION_STATUS", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "SEARCH_DOWNLOAD_CKYC", "interactionKey": "SEARCH_DOWNLOAD_CKYC", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "GENERATE_AADHAAR_OTP", "interactionKey": "GENERATE_AADHAAR_OTP", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "GET_AADHAAR_DETAILS_EKYC", "interactionKey": "GET_AADHAAR_DETAILS_EKYC", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "COMPARE_PHOTO", "interactionKey": "COMPARE_PHOTO", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "START_LOAN", "interactionKey": "START_LOAN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "UPLOAD_SELFIE_DATA", "interactionKey": "UPLOAD_SELFIE_DATA", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "GET_APPROVAL_STATUS", "interactionKey": "GET_APPROVAL_STATUS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "INITIATE_PENNY_DROP", "interactionKey": "INITIATE_PENNY_DROP", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "REDIRECT_TO_EMANDATE", "interactionKey": "REDIRECT_TO_EMANDATE", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "GET_EMANDATE_STATUS", "interactionKey": "EMANDATE_RETRY_TIMER", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "GENERATE_KFS_OTP", "interactionKey": "GENERATE_KFS_OTP", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "VALIDATE_KFS_OTP", "interactionKey": "VALIDATE_KFS_OTP", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "POSITIVE_CONFIRMATION", "interactionKey": "POSITIVE_CONFIRMATION", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "GENERATE_SELFIE_TOKEN", "interactionKey": "GENERATE_SELFIE_TOKEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "MATCH_SELFIE_DATA", "interactionKey": "MATCH_SELFIE_DATA", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "ONBOARD_LOAN", "interactionKey": "ONBOARD_LOAN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "KFS_SCREEN", "interactionKey": "KFS_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "CKYC_DETAILS", "interactionKey": "CKYC_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "EKYC_DETAILS", "interactionKey": "EKYC_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "REPAYMENT_MODES", "interactionKey": "REPAYMENT_MODES", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "APPLICATION_STATUS", "interactionKey": "APPLICATION_STATUS"}, {"applicationState": "KFS_OTP_VERIFICATION", "interactionKey": "KFS_OTP_VERIFICATION", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "ERROR_SCREEN", "interactionKey": "ERROR_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "FACE_MATCH_ERROR", "interactionKey": "FACE_MATCH_ERROR", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "REJECTED", "interactionKey": "REJECTED"}, {"applicationState": "REPAYMENT_SCHEDULE", "interactionKey": "REPAYMENT_SCHEDULE", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "GENERATE_ETB_OTP_SCREEN", "interactionKey": "GENERATE_ETB_OTP_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "ETB_OTP_VERIFICATION", "interactionKey": "ETB_OTP_VERIFICATION", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "ONBOARD_RETRY_SCREEN", "interactionKey": "ONBOARD_RETRY_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "EMANDATE_REDIRECTION", "interactionKey": "EMANDATE_REDIRECTION", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}], "interactionKeyMap": {"BASIC_DETAILS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/basic-details", "schema": {}}, "OFFER_DETAILS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/bank-details", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"amount": {"type": "integer"}, "interest": {"type": "number"}, "tenure": {"type": "integer"}, "consentData": {"type": "object", "properties": {"ip": {"type": "string"}, "timestamp": {"type": "string"}}, "required": ["ip", "timestamp"]}}, "required": ["amount", "interest", "tenure", "consentData"]}, "sample": {"amount": 5000, "interest": 10.5, "tenure": 24, "consentData": {"ip": "***********", "timestamp": "13:45 11/12/2023"}}}, "BANK_DETAILS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/bank-details", "schema": {}}, "AADHAAR_FORM": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/aadhaar-form", "schema": {}}, "KYC_OTP_VERIFICATION_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/kyc-otp-verification-screen", "schema": {}}, "SELFIE_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/selfie-screen", "schema": {}}, "LOADING_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/loading-screen", "schema": {}}, "OFFER_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/offer-screen", "schema": {}}, "EMANDATE_REDIRECTION": {"type": "MANDATE_INTERACTION", "dataKey": "generateEmandateUrl", "readRepair": true, "schema": {}}, "KFS_SCREEN": {"type": "REPAIRABLE_FORM_INTERACTION", "readRepair": true, "pageUrl": "/ams/1/kfs", "schema": {}}, "APPLICATION_STATUS": {"type": "REPAIRABLE_FORM_INTERACTION", "readRepair": true, "pageUrl": "/ams/1/application-status", "schema": {}}, "REPAYMENT_MODES": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/repayment-modes", "schema": {}}, "KFS_OTP_VERIFICATION": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/kfs-otp", "schema": {}}, "ERROR_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/error", "schema": {}}, "CKYC_DETAILS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/kyc-details", "schema": {}}, "EKYC_DETAILS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/ekyc-details", "schema": {}}, "VKYC_REDIRECTION": {"type": "VKYC_INTERACTION", "dataKey": "generateVcipUrl", "readRepair": false, "schema": {}}, "REJECTED": {"type": "FORM_INTERACTION", "isTerminalState": true, "pageUrl": "/ams/1/rejected-v2", "schema": {}}, "EMANDATE_RETRY_TIMER": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/emandate-loading", "schema": {}}, "GENERATE_ETB_OTP_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/etb-otp", "schema": {}}, "ETB_OTP_VERIFICATION": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/etb-otp-verify", "schema": {}}, "ONBOARD_RETRY_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/onboard-retry", "schema": {}}}}