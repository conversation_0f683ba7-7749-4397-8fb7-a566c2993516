{"defaultInteraction": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/missing-page-sandbox", "schema": {}}, "taskKeysList": [{"taskKey": "lender<PERSON><PERSON><PERSON>", "interactionKey": "LENDER_JOURNEY"}, {"interactionKey": "SEND_LEAD_TO_MPOCKKET", "taskKey": "sendLeadToMpockkett"}, {"taskKey": "retryScreen", "interactionKey": "RETRY_SCREEN"}, {"taskKey": "inProgress", "interactionKey": "CUSTOM_SCREEN"}, {"taskKey": "userConfirmation", "interactionKey": "CUSTOM_SCREEN"}], "applicationStatesList": [{"applicationState": "LENDER_JOURNEY", "interactionKey": "LENDER_JOURNEY"}, {"applicationState": "SUCCESS", "interactionKey": "SUCCESS"}, {"applicationState": "REJECTED", "interactionKey": "REJECTED"}], "interactionKeyMap": {"LENDER_JOURNEY": {"type": "LENDER_INTERACTION", "dataKey": "lenderRedirectionUrl", "keys": ["createApplicationLender", "submitOffer", "lender<PERSON><PERSON><PERSON>"], "pageParams": {"killCurrentPage": true}, "readRepair": true, "schema": {}}, "SUCCESS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/success", "schema": {}}, "REJECTED": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/rejected-v2", "isTerminalState": true, "schema": {}}, "CUSTOM_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/success", "readRepair": true, "schema": {}}, "RETRY_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/retry-screen", "schema": {}}}}