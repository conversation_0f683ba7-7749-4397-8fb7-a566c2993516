{"defaultInteraction": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/missing-page-sandbox", "schema": {}}, "taskKeysList": [{"taskKey": "ckycDetails", "interactionKey": "CKYC_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "landingPage", "interactionKey": "LANDING_PAGE"}, {"taskKey": "namePage", "interactionKey": "NAME_PAGE"}, {"taskKey": "addressDetails", "interactionKey": "ADDRESS_DETAILS"}, {"taskKey": "reviewPage", "interactionKey": "REVIEW_PAGE"}, {"taskKey": "retryScreenStatus", "interactionKey": "REVIEW_PAGE"}, {"taskKey": "panPage", "interactionKey": "PAN_PAGE", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"taskKey": "workDetails", "interactionKey": "WORK_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"taskKey": "offerScreen", "interactionKey": "OFFER_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"taskKey": "selectEmi", "interactionKey": "SELECT_EMI", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"taskKey": "ready<PERSON>or<PERSON><PERSON><PERSON>", "interactionKey": "PREPARE_SELFIE", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"taskKey": "selfieScreen", "interactionKey": "SELFIE_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "aadharScreen", "interactionKey": "AADHAR_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "digilo<PERSON>", "interactionKey": "DIGILOCKER", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"taskKey": "offerDetails", "interactionKey": "OFFER_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"taskKey": "lender<PERSON><PERSON><PERSON>", "interactionKey": "LENDER_JOURNEY"}, {"taskKey": "loadingPage", "interactionKey": "LOADING_SCREEN"}, {"taskKey": "aaScreen", "interactionKey": "AA_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 21}}, {"taskKey": "rejectScreen", "interactionKey": "REJECT_SCREEN"}, {"taskKey": "applicationStatus", "interactionKey": "APPLICATION_STATUS"}, {"taskKey": "retryScreen", "interactionKey": "RETRY_SCREEN"}, {"taskKey": "retryScreenOffer", "interactionKey": "RETRY_SCREEN_OFFER"}, {"taskKey": "kfsSetup", "interactionKey": "KFS_SCREEN_CREDIT_LINE"}, {"taskKey": "loanAgreementCreditLine", "interactionKey": "LOAN_AGREEMENT_CREDIT_LINE"}, {"taskKey": "withdrawalStatus", "interactionKey": "WITHDRAWAL_STATUS"}, {"taskKey": "transactionStatus", "interactionKey": "TRANSACTION_STATUS"}, {"taskKey": "accountSelectionPage", "interactionKey": "MANDATE_SETUP"}, {"taskKey": "mandateResume", "interactionKey": "MANDATE_RESUME"}, {"taskKey": "autoPilotPage", "interactionKey": "AUTO_PAY_PAGE"}, {"taskKey": "retryUpload", "interactionKey": "PROBLEM_WITH_KYC"}, {"taskKey": "mandateSetup", "interactionKey": "MANDATE_SETUP_LANDING_PAGE"}, {"taskKey": "mandateSuccess", "interactionKey": "WITHDRAWAL_BANK_SUCCESS"}, {"taskKey": "success", "interactionKey": "SUCCESS"}, {"taskKey": "alreadyHaveALoan", "interactionKey": "ALREADY_HAVE_A_LOAN"}], "applicationStatesList": [{"applicationState": "LANDING_PAGE", "interactionKey": "LANDING_PAGE", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "NAME_PAGE", "interactionKey": "NAME_PAGE", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "ADDRESS_DETAILS", "interactionKey": "ADDRESS_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "REVIEW_PAGE", "interactionKey": "REVIEW_PAGE", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "PAN_PAGE", "interactionKey": "PAN_PAGE", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "WORK_DETAILS", "interactionKey": "WORK_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "CREATE_APPLICATION_LENDER", "interactionKey": "CREATE_APPLICATION_LENDER", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "CREATE_APPLICATION", "interactionKey": "CREATE_APPLICATION", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "SELECT_EMI", "interactionKey": "SELECT_EMI", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "PREPARE_SELFIE", "interactionKey": "PREPARE_SELFIE", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "SELFIE_SCREEN", "interactionKey": "SELFIE_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "AADHAR_SCREEN", "interactionKey": "AADHAR_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "DIGILOCKER", "interactionKey": "DIGILOCKER", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 1}}, {"applicationState": "GENERATE_OFFER", "interactionKey": "GENERATE_OFFER", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "GET_STATUS_START", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "GET_STATUS_END", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "GET_OFFER_START", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "GET_OFFER", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "CREATE_APPLICATION_END", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "CREATE_APPLICATION_START", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "POLL_OFFER_TIMER", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "CKYC_DETAILS", "interactionKey": "CKYC_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 30}}, {"applicationState": "CREATE_APP_TIMER", "interactionKey": "LOADING_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "RETRY_SCREEN", "interactionKey": "RETRY_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 7}}, {"applicationState": "OFFER_SCREEN", "interactionKey": "OFFER_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "OFFER_DETAILS", "interactionKey": "OFFER_DETAILS", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "INIT_AA", "interactionKey": "INIT_AA", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 7}}, {"applicationState": "AA_SCREEN", "interactionKey": "AA_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 21}}, {"applicationState": "GET_LENDER_STATUS_AA", "interactionKey": "GET_LENDER_STATUS_AA", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 7}}, {"applicationState": "SUBMIT_OFFER", "interactionKey": null, "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 31}}, {"applicationState": "REJECT_SCREEN", "interactionKey": "REJECT_SCREEN", "lifecycleConfig": {"timeUnit": "DAYS", "timeValue": 15}}, {"applicationState": "REJECTED", "interactionKey": "REJECTED", "lifecycleConfig": {"timeUnit": "MINUTES", "timeValue": 1}}, {"applicationState": "LENDER_JOURNEY", "interactionKey": "LENDER_JOURNEY"}, {"applicationState": "LENDER_PLATFORM", "interactionKey": "LENDER_JOURNEY"}, {"applicationState": "KFS", "interactionKey": "KFS_SCREEN_CREDIT_LINE"}, {"applicationState": "LOAN_AGREEMENT_CREDIT_LINE", "interactionKey": "LOAN_AGREEMENT_CREDIT_LINE"}, {"applicationState": "WITHDRAWAL_STATUS", "interactionKey": "WITHDRAWAL_STATUS"}, {"applicationState": "TRANSACTION_STATUS", "interactionKey": "TRANSACTION_STATUS"}, {"applicationState": "MANDATE_RESUME", "interactionKey": "MANDATE_RESUME"}, {"applicationState": "AUTO_PAY_PAGE", "interactionKey": "AUTO_PAY_PAGE"}, {"applicationState": "RETRY_UPLOAD", "interactionKey": "PROBLEM_WITH_KYC"}, {"applicationState": "MANDATE_SETUP", "interactionKey": "MANDATE_SETUP_LANDING_PAGE"}, {"applicationState": "MANDATE_SUCCESS", "interactionKey": "WITHDRAWAL_BANK_SUCCESS"}, {"applicationState": "SUCCESS", "interactionKey": "SUCCESS"}, {"applicationState": "ALREADY_HAVE_A_LOAN", "interactionKey": "ALREADY_HAVE_A_LOAN"}], "interactionKeyMap": {"LANDING_PAGE": {"type": "FORM_INTERACTION", "pageUrl": "/ams/2/landing-page", "schema": {}}, "NAME_PAGE": {"type": "FORM_INTERACTION", "pageUrl": "/ams/2/name-page", "schema": {}}, "ADDRESS_DETAILS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/3/address-page", "schema": {}}, "REVIEW_PAGE": {"type": "FORM_INTERACTION", "pageUrl": "/ams/2/review-page", "schema": {}}, "LOADING_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/2/loading-screen", "schema": {}}, "OFFER_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/offer-screen-generic", "schema": {}}, "OFFER_DETAILS": {"type": "REPAIRABLE_FORM_INTERACTION", "pageUrl": "/ams/1/offer-details", "userDiscardAllowed": true, "readRepair": true, "schema": {}}, "LENDER_JOURNEY": {"type": "LENDER_INTERACTION", "dataKey": "lenderRedirectionUrl", "keys": ["createApplicationLender", "submitOffer", "lender<PERSON><PERSON><PERSON>"], "pageParams": {"killCurrentPage": true}, "readRepair": true, "schema": {}}, "AA_SCREEN": {"type": "LENDER_INTERACTION", "dataKey": "aaRedirectionUrl", "keys": ["initAa"], "pageParams": {"killCurrentPage": true}, "readRepair": true, "schema": {}}, "PAN_PAGE": {"type": "FORM_INTERACTION", "pageUrl": "/ams/2/pan-page", "schema": {}}, "SELECT_EMI": {"type": "FORM_INTERACTION", "pageUrl": "/ams/2/emi-selection-page", "schema": {}}, "SELFIE_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/selfie-screen", "schema": {}}, "AADHAR_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/face-authentication-landing", "schema": {}}, "CKYC_DETAILS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/kyc-details", "schema": {}}, "REJECT_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/reject-screen-sandbox", "schema": {}}, "WORK_DETAILS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/employment-details", "schema": {}}, "APPLICATION_STATUS_SANDBOX": {"type": "FORM_INTERACTION", "readRepair": true, "pageUrl": "/ams/1/application-status", "schema": {}}, "REJECTED": {"type": "FORM_INTERACTION", "readRepair": false, "isTerminalState": true, "pageUrl": "/ams/1/rejected-v2", "schema": {}}, "RETRY_SCREEN": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/retry-screen", "schema": {}}, "RETRY_SCREEN_OFFER": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/retry-screen", "schema": {}}, "KFS_SCREEN_CREDIT_LINE": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/kfs-screen-credit-line", "schema": {}}, "LOAN_AGREEMENT_CREDIT_LINE": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/loan-agreement-credit-line", "schema": {}}, "MANDATE_RESUME": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/select-account", "schema": {}}, "MANDATE_SETUP": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/start-mandate", "schema": {}}, "AUTO_PAY_PAGE": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/auto-pay", "schema": {}}, "PROBLEM_WITH_KYC": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/validation-failed", "schema": {}}, "FACE_AUTHENTICATION": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/face-authentication-landing", "schema": {}}, "WITHDRAWAL_STATUS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/withdrawal-status", "schema": {}}, "TRANSACTION_STATUS": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/transaction-status", "schema": {}}, "MANDATE_SETUP_LANDING_PAGE": {"type": "FORM_INTERACTION", "pageUrl": "/ams/1/mandate-landing-page", "schema": {}}, "WITHDRAWAL_BANK_SUCCESS": {"type": "FORM_INTERACTION", "pageUrl": "", "schema": {}}, "SUCCESS": {"type": "FORM_INTERACTION", "pageUrl": "", "schema": {}}, "ALREADY_HAVE_A_LOAN": {"type": "FORM_INTERACTION", "pageUrl": "", "schema": {}}}}