package com.flipkart.ams;

import org.junit.Test;

import static org.junit.Assert.*;

public class LEAD_APPLICATION_TYPESTest {

    @Test
    public void testEnumValues_ShouldContainAllExpectedTypes() {
        // Act
        LEAD_APPLICATION_TYPES[] types = LEAD_APPLICATION_TYPES.values();
        
        // Assert
        assertEquals("Enum should have exactly 4 values", 4, types.length);
        
        // Verify all expected values exist
        assertTrue("Should contain LEAD", containsType(types, LEAD_APPLICATION_TYPES.LEAD));
        assertTrue("Should contain LEAD_V2", containsType(types, LEAD_APPLICATION_TYPES.LEAD_V2));
        assertTrue("Should contain LEAD_V3", containsType(types, LEAD_APPLICATION_TYPES.LEAD_V3));
        assertTrue("Should contain LEAD_V4", containsType(types, LEAD_APPLICATION_TYPES.LEAD_V4));
    }

    @Test
    public void testEnumValueOf_LEAD() {
        // Act
        LEAD_APPLICATION_TYPES type = LEAD_APPLICATION_TYPES.valueOf("LEAD");
        
        // Assert
        assertEquals(LEAD_APPLICATION_TYPES.LEAD, type);
        assertEquals("LEAD", type.name());
    }

    @Test
    public void testEnumValueOf_LEAD_V2() {
        // Act
        LEAD_APPLICATION_TYPES type = LEAD_APPLICATION_TYPES.valueOf("LEAD_V2");
        
        // Assert
        assertEquals(LEAD_APPLICATION_TYPES.LEAD_V2, type);
        assertEquals("LEAD_V2", type.name());
    }

    @Test
    public void testEnumValueOf_LEAD_V3() {
        // Act
        LEAD_APPLICATION_TYPES type = LEAD_APPLICATION_TYPES.valueOf("LEAD_V3");
        
        // Assert
        assertEquals(LEAD_APPLICATION_TYPES.LEAD_V3, type);
        assertEquals("LEAD_V3", type.name());
    }

    @Test
    public void testEnumValueOf_LEAD_V4() {
        // Act
        LEAD_APPLICATION_TYPES type = LEAD_APPLICATION_TYPES.valueOf("LEAD_V4");
        
        // Assert
        assertEquals(LEAD_APPLICATION_TYPES.LEAD_V4, type);
        assertEquals("LEAD_V4", type.name());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testEnumValueOf_InvalidValue_ShouldThrowException() {
        // Act
        LEAD_APPLICATION_TYPES.valueOf("INVALID_TYPE");
    }

    @Test(expected = NullPointerException.class)
    public void testEnumValueOf_NullValue_ShouldThrowException() {
        // Act
        LEAD_APPLICATION_TYPES.valueOf(null);
    }

    @Test
    public void testEnumOrdinal_ShouldReturnCorrectOrder() {
        // Assert
        assertEquals("LEAD should be at ordinal 0", 0, LEAD_APPLICATION_TYPES.LEAD.ordinal());
        assertEquals("LEAD_V2 should be at ordinal 1", 1, LEAD_APPLICATION_TYPES.LEAD_V2.ordinal());
        assertEquals("LEAD_V3 should be at ordinal 2", 2, LEAD_APPLICATION_TYPES.LEAD_V3.ordinal());
        assertEquals("LEAD_V4 should be at ordinal 3", 3, LEAD_APPLICATION_TYPES.LEAD_V4.ordinal());
    }

    @Test
    public void testEnumName_ShouldReturnCorrectNames() {
        // Assert
        assertEquals("LEAD", LEAD_APPLICATION_TYPES.LEAD.name());
        assertEquals("LEAD_V2", LEAD_APPLICATION_TYPES.LEAD_V2.name());
        assertEquals("LEAD_V3", LEAD_APPLICATION_TYPES.LEAD_V3.name());
        assertEquals("LEAD_V4", LEAD_APPLICATION_TYPES.LEAD_V4.name());
    }

    @Test
    public void testEnumToString_ShouldReturnCorrectStringRepresentation() {
        // Assert
        assertEquals("LEAD", LEAD_APPLICATION_TYPES.LEAD.toString());
        assertEquals("LEAD_V2", LEAD_APPLICATION_TYPES.LEAD_V2.toString());
        assertEquals("LEAD_V3", LEAD_APPLICATION_TYPES.LEAD_V3.toString());
        assertEquals("LEAD_V4", LEAD_APPLICATION_TYPES.LEAD_V4.toString());
    }

    @Test
    public void testEnumEquality() {
        // Arrange
        LEAD_APPLICATION_TYPES type1 = LEAD_APPLICATION_TYPES.LEAD_V4;
        LEAD_APPLICATION_TYPES type2 = LEAD_APPLICATION_TYPES.valueOf("LEAD_V4");
        
        // Assert
        assertEquals("Same enum values should be equal", type1, type2);
        assertTrue("Same enum values should be equal", type1 == type2);
    }

    @Test
    public void testEnumInequality() {
        // Assert
        assertNotEquals("Different enum values should not be equal", 
            LEAD_APPLICATION_TYPES.LEAD, LEAD_APPLICATION_TYPES.LEAD_V2);
        assertNotEquals("Different enum values should not be equal", 
            LEAD_APPLICATION_TYPES.LEAD_V3, LEAD_APPLICATION_TYPES.LEAD_V4);
    }

    @Test
    public void testEnumCompareTo() {
        // Assert - ordinal comparison
        assertTrue("LEAD should be less than LEAD_V2", 
            LEAD_APPLICATION_TYPES.LEAD.compareTo(LEAD_APPLICATION_TYPES.LEAD_V2) < 0);
        assertTrue("LEAD_V2 should be less than LEAD_V3", 
            LEAD_APPLICATION_TYPES.LEAD_V2.compareTo(LEAD_APPLICATION_TYPES.LEAD_V3) < 0);
        assertTrue("LEAD_V3 should be less than LEAD_V4", 
            LEAD_APPLICATION_TYPES.LEAD_V3.compareTo(LEAD_APPLICATION_TYPES.LEAD_V4) < 0);
        
        assertEquals("Same enum values should compare as equal", 
            0, LEAD_APPLICATION_TYPES.LEAD_V4.compareTo(LEAD_APPLICATION_TYPES.LEAD_V4));
    }

    @Test
    public void testEnumInSwitch() {
        // Test that enum can be used in switch statements
        for (LEAD_APPLICATION_TYPES type : LEAD_APPLICATION_TYPES.values()) {
            String result = getTypeDescription(type);
            assertNotNull("Switch should handle all enum values", result);
            assertFalse("Description should not be empty", result.isEmpty());
        }
    }

    @Test
    public void testEnumSerialization() {
        // Test that enum values can be converted to string and back
        for (LEAD_APPLICATION_TYPES type : LEAD_APPLICATION_TYPES.values()) {
            String name = type.name();
            LEAD_APPLICATION_TYPES restored = LEAD_APPLICATION_TYPES.valueOf(name);
            assertEquals("Enum should serialize and deserialize correctly", type, restored);
        }
    }

    @Test
    public void testEnumHashCode() {
        // Test that enum values have consistent hash codes
        LEAD_APPLICATION_TYPES type1 = LEAD_APPLICATION_TYPES.LEAD_V4;
        LEAD_APPLICATION_TYPES type2 = LEAD_APPLICATION_TYPES.valueOf("LEAD_V4");
        
        assertEquals("Equal enum values should have same hash code", 
            type1.hashCode(), type2.hashCode());
    }

    @Test
    public void testEnumGetDeclaringClass() {
        // Test that all enum values belong to the correct class
        for (LEAD_APPLICATION_TYPES type : LEAD_APPLICATION_TYPES.values()) {
            assertEquals("All enum values should belong to LEAD_APPLICATION_TYPES class",
                LEAD_APPLICATION_TYPES.class, type.getDeclaringClass());
        }
    }

    @Test
    public void testEnumValuesArray_ShouldBeNewInstanceEachTime() {
        // Test that values() returns a new array each time (defensive copy)
        LEAD_APPLICATION_TYPES[] array1 = LEAD_APPLICATION_TYPES.values();
        LEAD_APPLICATION_TYPES[] array2 = LEAD_APPLICATION_TYPES.values();
        
        assertNotSame("values() should return new array each time", array1, array2);
        assertArrayEquals("values() should return same content", array1, array2);
    }

    @Test
    public void testEnumValuesArray_ModificationShouldNotAffectOriginal() {
        // Test that modifying the returned array doesn't affect the enum
        LEAD_APPLICATION_TYPES[] array = LEAD_APPLICATION_TYPES.values();
        int originalLength = array.length;
        
        // Modify the array
        array[0] = null;
        
        // Get a new array and verify it's unchanged
        LEAD_APPLICATION_TYPES[] newArray = LEAD_APPLICATION_TYPES.values();
        assertEquals("Original enum should be unchanged", originalLength, newArray.length);
        assertNotNull("Original enum values should be intact", newArray[0]);
    }

    // Helper methods
    private boolean containsType(LEAD_APPLICATION_TYPES[] types, LEAD_APPLICATION_TYPES target) {
        for (LEAD_APPLICATION_TYPES type : types) {
            if (type == target) {
                return true;
            }
        }
        return false;
    }

    private String getTypeDescription(LEAD_APPLICATION_TYPES type) {
        switch (type) {
            case LEAD:
                return "Original lead application type";
            case LEAD_V2:
                return "Lead application type version 2";
            case LEAD_V3:
                return "Lead application type version 3";
            case LEAD_V4:
                return "Lead application type version 4";
            default:
                return "Unknown type";
        }
    }
}
