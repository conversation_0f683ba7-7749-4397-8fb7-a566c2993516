<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.flipkart.fintech</groupId>
    <artifactId>pinaka</artifactId>
    <version>3.4.26-SM</version>
  </parent>

  <artifactId>ams-connector</artifactId>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>ams-bridge</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>com.flipkart.fintech.pinaka.common</groupId>
      <artifactId>pinaka-common</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>winterfell-client</artifactId>
      <version>${winterfell.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.glassfish.jersey.connectors</groupId>
          <artifactId>jersey-apache-connector</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>org.projectlombok</artifactId>
          <groupId>lombok</groupId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>citadel-client</artifactId>
      <version>${citadel.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.glassfish.jersey.connectors</groupId>
          <artifactId>jersey-apache-connector</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>org.projectlombok</artifactId>
          <groupId>lombok</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>

</project>