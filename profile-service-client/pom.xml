<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.flipkart.fintech</groupId>
        <artifactId>pinaka</artifactId>
        <version>3.4.26-SM</version>
    </parent>
    <artifactId>profile-service-client</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.26</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.google.inject</groupId>
            <artifactId>guice</artifactId>
            <version>3.0</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.inject</groupId>
                    <artifactId>javax.inject</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>aopalliance</groupId>
                    <artifactId>aopalliance</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-client</artifactId>
            <version>2.44</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>javax.ws.rs-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.core</groupId>
                    <artifactId>jersey-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.hk2</groupId>
                    <artifactId>hk2-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.hk2.external</groupId>
                    <artifactId>javax.inject</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.hk2</groupId>
                    <artifactId>hk2-locator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.14.2</version>
        </dependency>

        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>2.0.1</version>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>1.1.0.Final</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.3.2</version>
        </dependency>

        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>profile-service</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

<!--    <build>-->
<!--        <plugins>-->
<!--            &lt;!&ndash; Compiler plugin to ensure Java 8 compatibility &ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <version>3.8.1</version>-->
<!--                <configuration>-->
<!--                    <source>8</source>-->
<!--                    <target>8</target>-->
<!--                </configuration>-->
<!--            </plugin>-->

<!--            &lt;!&ndash; Transformer plugin to publish artifact for Java 17 compatibility &ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.eclipse.transformer</groupId>-->
<!--                <artifactId>transformer-maven-plugin</artifactId>-->
<!--                <extensions>true</extensions>-->
<!--                <configuration>-->
<!--                    <rules>-->
<!--                        <jakartaDefaults>true</jakartaDefaults>-->
<!--                    </rules>-->
<!--                </configuration>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>default-jar</id>-->
<!--                        <goals>-->
<!--                            <goal>jar</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <artifact>-->
<!--                                <groupId>${project.groupId}</groupId>-->
<!--                                <artifactId>${project.artifactId}</artifactId>-->
<!--                                <version>${project.version}</version>-->
<!--                            </artifact>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--    </build>-->

</project>