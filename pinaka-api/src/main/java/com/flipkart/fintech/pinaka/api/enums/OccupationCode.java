package com.flipkart.fintech.pinaka.api.enums;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 05/07/18.
 */
public enum OccupationCode {
    ENGINEER("Engineer"),
    ARCHITECT("Architect"),
    CONSULTANT("Consultant"),
    ACCOUNTANT("Accountant"),
    BANKER("Banker"),
    OTHERS("Others"),
    STUDENT("Student"),
    EXECUTIVE("Executive"),
    LAWYER("Lawyer");

    private String title;

    OccupationCode(String title){
        this.title = title;
    }

    public String getTitle(){
        return this.title;
    }
}
