package com.flipkart.fintech.pinaka.api.request.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pinaka.api.enums.ApplicationUpdateContext;
import com.flipkart.fintech.stratum.api.enums.altData.UploadDataType;
import com.google.common.collect.Lists;
import lombok.*;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
public class UpdateDevicePermissionsRequest extends ApplicationUpdateRequest {

    @NonNull
    private List<UploadDataType> accessGiven;
    @NonNull
    private List<UploadDataType> accessDenied;

    public UpdateDevicePermissionsRequest() {
        super(ApplicationUpdateContext.UPDATE_DEVICE_PERMISSIONS);
        this.accessGiven = Lists.newArrayList();
        this.accessDenied = Lists.newArrayList();
    }
}
