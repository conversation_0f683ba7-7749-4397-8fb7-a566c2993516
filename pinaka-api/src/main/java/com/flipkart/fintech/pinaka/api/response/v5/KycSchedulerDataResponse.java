package com.flipkart.fintech.pinaka.api.response.v5;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.stratum.api.models.kycScheduler.KycScheduleStatus;
import com.flipkart.fintech.stratum.api.models.kycScheduler.KycSchedulerApplicationData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.LowerCaseWithUnderscoresStrategy.class)
public class KycSchedulerDataResponse {
    private String applicationReferenceId;
    private KycScheduleStatus status;
    private KycSchedulerApplicationData applicationData;
    private String formKey;
}
