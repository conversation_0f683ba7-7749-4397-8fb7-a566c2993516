package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pinaka.api.enums.FormFieldType;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 11/08/18.
 */
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "fieldType"
)
@JsonSubTypes({@JsonSubTypes.Type(
        name = "TEXT",
        value = TextFieldDetails.class
), @JsonSubTypes.Type(
        name = "DROPDOWN",
        value = DropdownFieldDetails.class
), @JsonSubTypes.Type(
        name = "ADDRESS",
        value = AddressFieldDetails.class
), @JsonSubTypes.Type(
        name = "NUMBER",
        value = NumberFieldDetails.class
)})
@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class FormFieldDetails {
    private FormFieldType fieldType;

    private String label;

    private String name;

    protected FormFieldDetails(FormFieldType fieldType){
        this.fieldType = fieldType;
    }

    public FormFieldType getFieldType() {
        return fieldType;
    }

    public void setFieldType(FormFieldType fieldType) {
        this.fieldType = fieldType;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
