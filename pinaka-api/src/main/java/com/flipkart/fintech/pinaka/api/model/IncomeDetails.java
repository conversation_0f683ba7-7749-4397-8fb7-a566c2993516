package com.flipkart.fintech.pinaka.api.model;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>r on 04/07/18.
 */
public class IncomeDetails {

    private IncomeType incomeType;
    private BigDecimal fixedAmount;
    private Frequency frequency;

    public IncomeType getIncomeType() {
        return incomeType;
    }

    public void setIncomeType(IncomeType incomeType) {
        this.incomeType = incomeType;
    }

    public BigDecimal getFixedAmount() {
        return fixedAmount;
    }

    public void setFixedAmount(BigDecimal fixedAmount) {
        this.fixedAmount = fixedAmount;
    }

    public Frequency getFrequency() {
        return frequency;
    }

    public void setFrequency(Frequency frequency) {
        this.frequency = frequency;
    }


    public enum  IncomeType {
        NET_SALARY,
        EARNED_INCOME;
    }

    public enum Frequency {
        MONTHLY,
        YEARLY
    }
}
