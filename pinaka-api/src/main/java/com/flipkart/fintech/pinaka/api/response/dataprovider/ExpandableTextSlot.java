package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import lombok.Data;

import java.util.List;

@Data
public class ExpandableTextSlot extends Slot {
    private Image image;
    private List<ExpandableTextValue> expandableTextValueList;

    public ExpandableTextSlot() {
        super(SlotType.EXPANDABLE_TEXT);
    }
}
