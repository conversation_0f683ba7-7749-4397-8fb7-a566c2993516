package com.flipkart.fintech.pinaka.api.request.v3;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PennyDropNameMatchRequest {

    @NotNull
    private String merchantAccountId;

    private String parentApplicationId;

    private String financialProvider;

    @NotNull
    private String customerName;

    private int usedAttempts;

    private String externalRefId;

    private String context;

}
