package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.AddressType;
import com.flipkart.sensitive.annotation.SensitiveField;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 22/08/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Address {

    @NotNull
    @JsonProperty("address_line1")
    @SensitiveField
    private String addressLine1;

    @JsonProperty("address_line2")
    @SensitiveField
    private String addressLine2;

    @NotNull
    @JsonProperty("city")
    private String city;

    @NotNull
    @JsonProperty("postal_code")
    private String postalCode;

    @NotNull
    @JsonProperty("state")
    private String state;

    @JsonProperty("address_type")
    private AddressType addressType;

    @JsonProperty("address_id")
    private String id;

    private String name;

    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public AddressType getAddressType() {
        return addressType;
    }

    public void setAddressType(AddressType addressType) {
        this.addressType = addressType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "Address{" +
                "addressLine1='" + addressLine1 + '\'' +
                ", addressLine2='" + addressLine2 + '\'' +
                ", city='" + city + '\'' +
                ", postalCode='" + postalCode + '\'' +
                ", state='" + state + '\'' +
                ", addressType=" + addressType +
                ", id='" + id + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
