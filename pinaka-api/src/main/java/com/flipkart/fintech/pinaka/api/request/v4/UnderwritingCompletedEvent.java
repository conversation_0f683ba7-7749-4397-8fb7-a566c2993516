package com.flipkart.fintech.pinaka.api.request.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
public class UnderwritingCompletedEvent {
    private static final String eventName = "UNDERWRITING_COMPLETED";
    private String applicationReferenceId;
    private String merchantUserId;
    private ApplicationStatus applicationStatus;
    private Double limit;
    private String rejectReason;
}
