package com.flipkart.fintech.pinaka.api.response.dataprovider;

import java.util.Map;


public class MarkupMessage {

    private String description;
    private Map<String, Object> styleSheet;
    private Image icon;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, Object> getStyleSheet() {
        return styleSheet;
    }

    public void setStyleSheet(Map<String, Object> styleSheet) {
        this.styleSheet = styleSheet;
    }

    public Image getIcon() {
        return icon;
    }

    public void setIcon(Image icon) {
        this.icon = icon;
    }
}
