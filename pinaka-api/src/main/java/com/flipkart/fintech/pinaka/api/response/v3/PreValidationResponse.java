package com.flipkart.fintech.pinaka.api.response.v3;

import com.flipkart.fintech.pinaka.api.enums.RejectReason;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreValidationResponse {
    RejectReason rejectReason = RejectReason.OTHER;
    Boolean success = false;
    int age;
    String pincode;
}
