package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 17/08/17.
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class  ApplicationCreateResponse {

    @JsonProperty("tracking_id")
    private String trackingId;

    @JsonProperty("external_id")
    private String externalId;

    @JsonProperty("landing_url")
    private String landingUrl;

    @JsonProperty("screenType")
    private String screenType;

    private boolean killCurrentPage;


    public String getTrackingId() {
        return trackingId;
    }

    public void setTrackingId(String trackingId) {
        this.trackingId = trackingId;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getLandingUrl() {
        return landingUrl;
    }

    public void setLandingUrl(String landingUrl) {
        this.landingUrl = landingUrl;
    }


	public String getScreenType() {
		return screenType;
	}

	public void setScreenType(String screenType) {
		this.screenType = screenType;
	}

    public boolean isKillCurrentPage() {
        return killCurrentPage;
    }

    public void setKillCurrentPage(boolean killCurrentPage) {
        this.killCurrentPage = killCurrentPage;
    }

}
