package com.flipkart.fintech.pinaka.api.response;

import com.flipkart.fintech.pinaka.api.model.LenderConfig;

public class LenderConfigResponse {

    private LenderConfig lenderConfig;

    public LenderConfig getLenderConfig() {
        return lenderConfig;
    }

    public void setLenderConfig(LenderConfig lenderConfig) {
        this.lenderConfig = lenderConfig;
    }

    @Override
    public String toString() {
        return "LenderConfigResponse{" +
                "lenderConfig=" + lenderConfig +
                '}';
    }
}
