package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApplicationStatusUpdateRequest {

    @JsonProperty
    private String caseReferenceId;

    @JsonProperty
    private String c1Number;

    @JsonProperty
    private String cardSerno;
}
