package com.flipkart.fintech.pinaka.api.response.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormField {
    private String name;
    private String label;
    private Boolean mandatory;
    private Boolean disabled;
    private String url;
    private String regex;
    private String houseNumber;
    private String area;
    private String city;
    private String state;
    private String pincode;
    private String placeholder;
    private String defaultValue;
    private String minValue;
    private String maxValue;
    private ViewType viewType;
    private String title;
    private String subtitle;
    private List<Option> options;
    private Map<String, Object> params;
    private FormFieldType formFieldType;
    private Boolean offline;
    private Type actionType;
    private BigDecimal interest;
    private String note;
    private int tenureStepSize;
    private int rowCount;
    private List<Charges> charges;
    private TenureUnit tenureUnit;
    private BigDecimal emiThreshold;
    private String defaultIdValue;
}
