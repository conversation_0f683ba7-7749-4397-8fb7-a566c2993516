package com.flipkart.fintech.pinaka.api.request.v6.useraction;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MandateSubmitRequest extends UserActionRequest {

    /* Sample Decrypted String:
            ams-bridge/src/test/resources/ams/mandate_response_success.json
     */
    private String mandateResponseString;

}
