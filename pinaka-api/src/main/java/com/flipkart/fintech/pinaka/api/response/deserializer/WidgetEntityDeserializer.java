package com.flipkart.fintech.pinaka.api.response.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.flipkart.fintech.pinaka.api.response.idfc.WidgetEntity;
import com.flipkart.fintech.pinaka.api.response.v6.FetchBulkDataResponseV2;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.AccordionWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class WidgetEntityDeserializer extends StdDeserializer<WidgetEntity> {

    public WidgetEntityDeserializer() {
        this(null);

    }

    public WidgetEntityDeserializer(Class<?> vc) {
        super(vc);
    }


    @Override
    public WidgetEntity deserialize(JsonParser jsonParser, DeserializationContext context)
            throws IOException, JsonProcessingException {
        ObjectMapper mapper = (ObjectMapper) jsonParser.getCodec();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(AccordionWidgetData.class, new AccordianWidgetDeserializer());
        mapper.registerModule(module);
        JsonNode widgetEntityNode = mapper.readTree(jsonParser);
        JsonNode slotIdNode = widgetEntityNode.get("slotId");
        JsonNode widgetTypeNode = widgetEntityNode.get("widgetType");
        JsonNode widgetDataNode = widgetEntityNode.get("widgetData");
        WidgetEntity widgetEntity = null;
        if (widgetTypeNode != null) {
            String widgetTypeV4 = mapper.treeToValue(widgetTypeNode, String.class);
            WidgetData widgetData = (WidgetData) mapper.treeToValue(widgetDataNode,
                    WidgetTypeV4.valueOf(widgetTypeV4).getClazz());
            widgetEntity = new WidgetEntity(mapper.treeToValue(slotIdNode, Integer.class),
                    WidgetTypeV4.valueOf(widgetTypeV4), widgetData);
        }
        return widgetEntity;
    }


}

