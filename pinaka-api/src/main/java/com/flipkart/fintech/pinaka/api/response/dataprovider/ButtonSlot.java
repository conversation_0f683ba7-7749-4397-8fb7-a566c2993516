package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import com.flipkart.fintech.pinaka.api.response.styles.ButtonStyle;

/**
 * Created by rajat.mathur on 10/12/18.
 */
public class ButtonSlot extends Slot {

    @JsonProperty("enabled_button_style")
    private ButtonStyle enabledButtonStyle;
    @JsonProperty("disabled_button_style")
    private ButtonStyle disabledButtonStyle;

    private String context;
    private Boolean enabled = Boolean.TRUE;

    public ButtonSlot() {
        super(SlotType.BUTTON);
    }

    private Action action ;

    public Action getAction() {
        return action;
    }

    public void setAction(Action action) {
        this.action = action;
    }

    public ButtonStyle getDisabledButtonStyle() {
        return disabledButtonStyle;
    }

    public void setDisabledButtonStyle(ButtonStyle disabledButtonStyle) {
        this.disabledButtonStyle = disabledButtonStyle;
    }

    public ButtonStyle getEnabledButtonStyle() {
        return enabledButtonStyle;
    }

    public void setEnabledButtonStyle(ButtonStyle enabledButtonStyle) {
        this.enabledButtonStyle = enabledButtonStyle;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }
}
