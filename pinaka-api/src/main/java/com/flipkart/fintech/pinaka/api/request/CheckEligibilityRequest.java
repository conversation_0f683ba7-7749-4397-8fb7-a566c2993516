package com.flipkart.fintech.pinaka.api.request;

import com.flipkart.fintech.pinaka.api.enums.Channel;
import com.flipkart.fintech.pinaka.api.enums.SubApplicationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CheckEligibilityRequest {
    SubApplicationType subApplicationType;
    Channel channel;
    Boolean isFullKycCompleted;
    boolean upgradationAllowed = Boolean.TRUE;

}
