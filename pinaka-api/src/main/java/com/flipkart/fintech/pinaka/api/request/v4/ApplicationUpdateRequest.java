package com.flipkart.fintech.pinaka.api.request.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pinaka.api.enums.ApplicationUpdateContext;
import lombok.*;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(
        property = "context",
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        use = JsonTypeInfo.Id.NAME,
        visible = true
)
@JsonSubTypes({@JsonSubTypes.Type(
        name = "EKYC_PAN_AADHAAR_SUBMIT",
        value = EkycPanAadhaarSubmitRequest.class
), @JsonSubTypes.Type(
        name = "UPDATE_DEVICE_PERMISSIONS",
        value = UpdateDevicePermissionsRequest.class
), @JsonSubTypes.Type(
        name = "EKYC_SUBMIT_OTP",
        value = EkycSubmitOtpRequest.class
), @JsonSubTypes.Type(
        name = "EKYC_RESEND_OTP",
        value = ApplicationUpdateRequest.class
), @JsonSubTypes.Type(
        name = "KYC_REVIEW_AND_SUBMIT",
        value = ApplicationUpdateRequest.class
), @JsonSubTypes.Type(
        name = "KYC_DISCARD",
        value = ApplicationUpdateRequest.class
), @JsonSubTypes.Type(
        name = "KYC_RETRY",
        value = ApplicationUpdateRequest.class
), @JsonSubTypes.Type(
        name = "CONDITIONAL_APPROVAL_PAGE",
        value = ApplicationUpdateRequest.class
)
})
public class ApplicationUpdateRequest {
    @NonNull
    private ApplicationUpdateContext context;
}
