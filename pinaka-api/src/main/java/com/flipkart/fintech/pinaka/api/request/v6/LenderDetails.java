package com.flipkart.fintech.pinaka.api.request.v6;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LenderDetails {
    private String lender;
    private String lenderApplicationId;
    private List<com.flipkart.fintech.pandora.api.model.response.plOnboarding.Offer> generatedOffer;
    private List<Offer> submittedOffer;
    private long applicationValidTill;
    private long lenderApplicationValidity;
    private String applicationSubmittedTimestamp;
}
