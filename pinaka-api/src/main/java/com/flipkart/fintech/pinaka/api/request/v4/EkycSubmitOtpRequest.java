package com.flipkart.fintech.pinaka.api.request.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pinaka.api.enums.ApplicationUpdateContext;
import com.flipkart.fintech.stratum.api.models.common.EncryptionKeyData;
import lombok.*;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
public class EkycSubmitOtpRequest extends ApplicationUpdateRequest {
    @NonNull
    private String otp;
    private EncryptionKeyData encryptionKeyData;

    public EkycSubmitOtpRequest() {
        super(ApplicationUpdateContext.EKYC_SUBMIT_OTP);
        this.otp = "";
    }
}
