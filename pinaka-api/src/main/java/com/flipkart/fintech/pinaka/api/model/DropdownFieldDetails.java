package com.flipkart.fintech.pinaka.api.model;

import com.flipkart.fintech.pinaka.api.enums.FormFieldType;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 12/08/18.
 */
public class DropdownFieldDetails extends FormFieldDetails {

    public DropdownFieldDetails() {
        super(FormFieldType.DROPDOWN);
    }

    private List<DropdownValue> values;

    public List<DropdownValue> getValues() {
        return values;
    }

    public void setValues(List<DropdownValue> values) {
        this.values = values;
    }

    public static class DropdownValue{
        private String id;
        private String title;

        public DropdownValue(){

        }

        public DropdownValue(String id, String title){
            this.id = id;
            this.title = title;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }
    }
}
