package com.flipkart.fintech.pinaka.api.enums;

public enum JourneyState {
    NO_APPLICATION_STATE, NACH_PENDING, WAITING_FOR_PERMISSIONS, KYC_PENDING,
    NO_TRIGGER_STATE, KYC_REUPLOAD, APPROVED, TTL_EXPIRY, APPLICATION_IN_PROGRESS,
    ETCC_APPLICATION_CREATED, ETCC_FETCH_CONSENT, ETCC_USER_INPUT, ETCC_APPLY_CONSENT,
    ETB_PA_FETCH_CONSENT, ETB_PA_USER_INPUT, ETB_PA_APPLY_CONSENT,
    ETB_NPA_FETCH_CONSENT, ETB_NPA_USER_INPUT, ETB_NPA_APPLY_CONSENT,
    NTB_USER_INPUT, NTB_APPLY_CONSENT, NTB_NO_APPLICATION_STATE, ETB_NO_APPLICATION_STATE,
    ETS_USER_INPUT, ETS_APPLY_CONSENT, ETS_NO_APPLICATION_STATE, ETS_TTL_EXPIRY,
    ETCC_NO_APPLICATION_STATE, NTB_TTL_EXPIRY, ETB_TTL_EXPIRY, ETCC_TTL_EXPIRY,
    KYC_SCHEDULED,EKYC_PASSED,VIDEO_KYC_ELIGIBLE, WAITING_FOR_BUREAU_CONSENT, WAITING_FOR_SMS_PERMISSIONS,
    WAITING_FOR_TS_CONSENT, WAITING_FOR_TS_CONSENT_VERIFICATION, WAITING_FOR_PENNY_DROP,
    WAITING_FOR_ENHANCED_DUE_DILIGENCE, CONDITIONALLY_APPROVED, REJECTED, CARD_LINKING_IN_PROGRESS, CARD_LINKING_CONSENT,
    //CBC NPS RELATED STATES (for triggering timeline see CbcNpsStatus.java)
    NPS_APPLICATION_IN_PROGRESS, NPS_NO_TRIGGER_STATE, NPS_IPA_REJECTION, NPS_FINAL_REJECTION, NPS_APPROVED,
    NPS_FIRST_POST_APPROVAL, NPS_SECOND_POST_APPROVAL, NPS_SUBSEQUENT_POST_APPROVAL,
    DEVICE_DATA_COLLECTION_IN_PROGRESS,PENNY_DROP_PENDING, NO_TRIGGER, KYC_FORM_FILL_IN_PROGRESS,
    KYC_DATA_REVIEW_PENDING;
}
