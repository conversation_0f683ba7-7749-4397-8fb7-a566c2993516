package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.affordability.underwriting.model.CreditConfig;
import com.flipkart.fintech.pinaka.api.enums.ConsentType;
import com.flipkart.fintech.pinaka.api.enums.JourneyContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
public class SaveConsentRequestWithLimit extends SaveConsentRequest{
    private CreditConfig creditConfig;

    @Builder(builderMethodName = "saveConsentRequestWithLimit")
    public SaveConsentRequestWithLimit(JourneyContext journeyContext, ConsentType consentType, long timestamp, CreditConfig creditConfig) {
        super(journeyContext, consentType, timestamp);
        this.creditConfig = creditConfig;
    }
}
