package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.model.LeadDetails.LeadState;

import java.util.List;
import java.util.Map;

import com.flipkart.fintech.pinaka.library.Feature;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class LeadObject {
  private LeadState stateOfLead;
  private String leadId;
  private Map<String, Object> applicationData;
  private ApplicationUserData applicationUserData;
  @JsonProperty("userProfile")
  private UserProfileObject userProfile;
  private Map<Feature, String> experianData;
  private String experianReport;
  private Double binScore;
  private Double losV2CremoBand;
  private Double idfcCremoBand;
  private Double granularCremoBand;
  private Integer monthlyIncome;
  private List<Lender> lendersToFilterOut;
}
