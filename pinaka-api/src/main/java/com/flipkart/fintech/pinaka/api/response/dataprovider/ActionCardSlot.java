package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import com.flipkart.fintech.pinaka.api.response.styles.StyledText;

public class ActionCardSlot extends Slot {
    private StyledText title;
    private StyledText subtitle;
    private Action action;
    private Image icon;
    private Image actionImage;

    //Note : adding for CBC use case to have flexibility to power them from pinaka (they are nullable)
    private String popupTitle;
    private String popupSubTitle;
    private String popupDismissText;
    private String popupContinueText;

    public ActionCardSlot() {
        super(SlotType.ACTION_CARD);
    }

    public ActionCardSlot(String viewType) {
        super(SlotType.ACTION_CARD, viewType);
    }

    public StyledText getTitle() {
        return title;
    }

    public void setTitle(StyledText title) {
        this.title = title;
    }

    public StyledText getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(StyledText subtitle) {
        this.subtitle = subtitle;
    }

    public Action getAction() {
        return action;
    }

    public void setAction(Action action) {
        this.action = action;
    }

    public Image getIcon() {
        return icon;
    }

    public void setIcon(Image icon) {
        this.icon = icon;
    }

    public Image getActionImage() {
        return actionImage;
    }

    public void setActionImage(Image actionImage) {
        this.actionImage = actionImage;
    }

    public String getPopupTitle() {
        return popupTitle;
    }

    public void setPopupTitle(String popupTitle) {
        this.popupTitle = popupTitle;
    }

    public String getPopupSubTitle() {
        return popupSubTitle;
    }

    public void setPopupSubTitle(String popupSubTitle) {
        this.popupSubTitle = popupSubTitle;
    }

    public String getPopupDismissText() {
        return popupDismissText;
    }

    public void setPopupDismissText(String popupDismissText) {
        this.popupDismissText = popupDismissText;
    }

    public String getPopupContinueText() {
        return popupContinueText;
    }

    public void setPopupContinueText(String popupContinueText) {
        this.popupContinueText = popupContinueText;
    }
}
