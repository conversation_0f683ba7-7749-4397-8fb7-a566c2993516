package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.model.UnderwritingRuleConfig;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> - 09/11/19
 */
@Data
public class WhitelistResponse {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("whitelist_name")
    private String whitelistName;

    @JsonProperty("whitelist_desc")
    private String whitelistDesc;

    @JsonProperty("product")
    private String productType;

    private String lender;

    private boolean enabled;

    @JsonProperty("rule_config")
    private List<UnderwritingRuleConfig> ruleConfig;

}
