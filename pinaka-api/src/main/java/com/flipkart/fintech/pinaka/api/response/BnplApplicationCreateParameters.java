package com.flipkart.fintech.pinaka.api.response;

import com.flipkart.fintech.pinaka.api.enums.UploadDataType;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.b on 14/02/18
 */
public class BnplApplicationCreateParameters {

    private String privacyPolicyUrl;

    private String tncUrl;

    private boolean userVerified;

    private List<UploadDataType> mandatoryPermissions;

    private List<UploadDataType> optionalPermissions;

    public String getPrivacyPolicyUrl() {
        return privacyPolicyUrl;
    }

    public void setPrivacyPolicyUrl(String privacyPolicyUrl) {
        this.privacyPolicyUrl = privacyPolicyUrl;
    }

    public String getTncUrl() {
        return tncUrl;
    }

    public void setTncUrl(String tncUrl) {
        this.tncUrl = tncUrl;
    }

    public boolean isUserVerified() {
        return userVerified;
    }

    public void setUserVerified(boolean userVerified) {
        this.userVerified = userVerified;
    }

    public List<UploadDataType> getMandatoryPermissions() {
        return mandatoryPermissions;
    }

    public void setMandatoryPermissions(List<UploadDataType> mandatoryPermissions) {
        this.mandatoryPermissions = mandatoryPermissions;
    }

    public List<UploadDataType> getOptionalPermissions() {
        return optionalPermissions;
    }

    public void setOptionalPermissions(List<UploadDataType> optionalPermissions) {
        this.optionalPermissions = optionalPermissions;
    }

    @Override
    public String toString() {
        return "BnplApplicationCreateParameters{" +
                "privacyPolicyUrl='" + privacyPolicyUrl + '\'' +
                ", tncUrl='" + tncUrl + '\'' +
                ", userVerified=" + userVerified +
                ", mandatoryPermissions=" + mandatoryPermissions.toString() +
                ", optionalPermissions=" + optionalPermissions.toString() +
                '}';
    }
}
