package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@ToString
public class MarkupMessagesSlot extends Slot {

    public MarkupMessagesSlot() {
        super(SlotType.MARKUP_MESSAGES);
    }

    private Map<String, Object> baseStyleSheet;
    private List<MarkupMessage> messages;
    private String backgroundColor;
    private Map<String, Object> additional;

    public Map<String, Object> getBaseStyleSheet() {
        return baseStyleSheet;
    }

    public void setBaseStyleSheet(Map<String, Object> baseStyleSheet) {
        this.baseStyleSheet = baseStyleSheet;
    }

    public List<MarkupMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<MarkupMessage> messages) {
        this.messages = messages;
    }

    public Map<String, Object> getAdditional() {
        return additional;
    }

    public void setAdditional(Map<String, Object> additional) {
        this.additional = additional;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }
}
