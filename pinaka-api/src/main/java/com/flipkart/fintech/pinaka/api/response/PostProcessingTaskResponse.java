package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * <AUTHOR>
 * @since 03/07/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PostProcessingTaskResponse {

    private boolean isTaskCompleted;
    private String referenceId;
    private String accountId;

    public boolean isTaskCompleted() {
        return isTaskCompleted;
    }

    public void setTaskCompleted(boolean taskCompleted) {
        isTaskCompleted = taskCompleted;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    @Override
    public String toString() {
        return "PostProcessingTaskResponse{" +
                "isTaskCompleted=" + isTaskCompleted +
                ", referenceId='" + referenceId + '\'' +
                ", accountId='" + accountId + '\'' +
                '}';
    }
}
