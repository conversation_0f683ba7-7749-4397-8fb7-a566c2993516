package com.flipkart.fintech.pinaka.api.response;

import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;
import com.flipkart.fintech.pinaka.api.enums.Channel;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 02/01/20.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationEntityResponse {
    private String            applicationRefId;
    private Lender            financialProvider;
    private String            merchantId;
    private String            merchantUserId;
    private ProductType       productType;
    private ApplicationStatus status;
    private Boolean           fullKycCompleted;
    private Channel           channel;

}
