package com.flipkart.fintech.pinaka.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class GenericApiActionResponse {
    public String getLandingUrl() {
        return landingUrl;
    }

    public void setLandingUrl(String landingUrl) {
        this.landingUrl = landingUrl;
    }

    @JsonProperty("landing_url")
    String landingUrl;

    @Override
    public String toString() {
        return "GenericApiActionResponse{" +
                "landingUrl='" + landingUrl + '\'' +
                '}';
    }
}
