package com.flipkart.fintech.pinaka.api.model;


import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.library.Feature;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import lombok.*;

import java.util.List;
import java.util.Map;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LeadDetails {

    private LeadState stateOfLead;

    private Map<String, Object> applicationData;
    private ApplicationUserData applicationUserData;

    private String leadId;

    private ProfileDetailedResponse userProfile;

    private Map<Feature, String> experianData;
    private String experianReport;
    private UserScores userScores;
    private Integer monthlyIncome;
    private Integer bonusIncome = 0;
    private List<Lender>  lendersToFilterOut;

    public static enum LeadState {
        NAME_PAGE,
        REVIEW_PAGE_1,
        REVIEW_PAGE_2,
        BASIC_DETAILS,
        VERIFY_PINCODE_START,
        VERIFY_PINCODE_END,
        VERIFY_PAN_START,
        VERIFY_PAN_END,
        PULL_EXPERIAN_START,
        PULL_EXPERIAN_END,
        CREATE_PROFILE_START,
        CREATE_PROFILE_END,
        LEAD_V3_NAME_PAGE,
        LEAD_V3_PAGE_1,
        LEAD_V3_PAGE_2,
        LEAD_V4_LANDING_PAGE,
        LEAD_V4_PAGE_1,
        LEAD_V4_PAGE_2
    }
}
