package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import lombok.Data;

import java.math.BigDecimal;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LenderConfig {

    private Lender lender;

    private String preApprovalTncKey;

    private String postApprovalTncKey;

    private String consentTextKey;

    private Boolean hasAdditionalForm;

    private Boolean isPaylaterSupported;

    private Boolean isEmiSupported;

    private BigDecimal interestRate;

    private BigDecimal lateFees;

    private Boolean isShowKycDetails;

    private Boolean isPrimaryDataEditable;

    private int nameMatchThreshold;

    private boolean isNameValidationEnabled;

    private boolean isPanValidationRequired;

    private int minimumAgeRequired;

    private EnhancedDueDiligenceConfig enhancedDueDiligenceConfig;

}
