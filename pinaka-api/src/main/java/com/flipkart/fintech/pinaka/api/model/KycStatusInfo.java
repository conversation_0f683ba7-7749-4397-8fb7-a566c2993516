package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.stratum.api.enums.kyc.KycType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 11/11/20.
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class KycStatusInfo {

    private KycType kycType;
    private String  expiryStatus;
    private String  subtitle;
    private String  validUpto;
    private boolean upgradeApplicable;
    private String  disclaimer;
}
