package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.FormFieldType;

/**
 * Created by kunal.keshwani on 01/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CheckboxFormField extends FormField {

    public CheckboxFormField() {
        super(FormFieldType.CHECKBOX);
    }

    private String preKnowMoreText;
    private String knowMoreText;
    private KnowMoreContent knowMoreContent;
    private boolean agreed;

    public String getPreKnowMoreText() {
        return preKnowMoreText;
    }

    public void setPreKnowMoreText(String preKnowMoreText) {
        this.preKnowMoreText = preKnowMoreText;
    }

    public String getKnowMoreText() {
        return knowMoreText;
    }

    public void setKnowMoreText(String knowMoreText) {
        this.knowMoreText = knowMoreText;
    }

    public KnowMoreContent getKnowMoreContent() {
        return knowMoreContent;
    }

    public void setKnowMoreContent(KnowMoreContent knowMoreContent) {
        this.knowMoreContent = knowMoreContent;
    }

    public boolean isAgreed() {
        return agreed;
    }

    public void setAgreed(boolean agreed) {
        this.agreed = agreed;
    }

    @Override
    public String toString() {
        return "CheckboxFormField{" +
                "preKnowMoreText='" + preKnowMoreText + '\'' +
                ", knowMoreText='" + knowMoreText + '\'' +
                ", knowMoreContent=" + knowMoreContent +
                ", agreed=" + agreed +
                '}';
    }
}
