package com.flipkart.fintech.pinaka.api.request.v6.documents;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pinaka.api.request.v6.documents.Document.DocumentTypes;
import java.util.Map;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * This is the abstract class for uploading any document type
 */
@Data
@SuperBuilder
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    property = "documentType",
    visible = true
)
@JsonSubTypes({
    @JsonSubTypes.Type(name = DocumentTypes.AADHAAR_XML, value = AadhaarXml.class),
    @JsonSubTypes.Type(name = DocumentTypes.KYC_IMAGE, value = KycImage.class),
    @JsonSubTypes.Type(name = DocumentTypes.SELFIE_IMAGE, value = SelfieImage.class)
})
public abstract class Document {
  @NotNull
  private String documentType;
  @NotNull
  private byte[] base64EncodedData;

  public Document(String documentType, @NotNull byte[] base64EncodedData) {
    DocumentTypes.validate(documentType);
    this.documentType = documentType;
    this.base64EncodedData = base64EncodedData;
  }

  public Map<String, String> getMetaData() {
    Map<String, String> metaData = getDocumentSpecificMetaData();
    metaData.put("documentType", documentType);
    return metaData;
  }

  protected abstract Map<String, String> getDocumentSpecificMetaData();


  /**
   * This is not an enum as new DocumentTypes will get added in future.
   * If the parameters remain the same then these can be moved to a config
   * and thus, can dynamically add new document types without doing any code changes.
   */
  public static class DocumentTypes {
    public static final String AADHAAR_XML = "AADHAAR_XML";
    public static final String SELFIE_IMAGE = "SELFIE_IMAGE";
    public static final String KYC_IMAGE = "KYC_IMAGE";

    public static void validate(String documentType) {

    }
  }

}
