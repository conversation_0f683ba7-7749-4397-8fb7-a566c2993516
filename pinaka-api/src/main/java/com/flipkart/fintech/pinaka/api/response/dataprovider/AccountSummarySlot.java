package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.flipkart.fintech.pinaka.api.request.dataprovider.SlotType;
import com.flipkart.fintech.pinaka.api.response.styles.StyledText;
import lombok.ToString;

import java.util.List;

@ToString
public class AccountSummarySlot extends Slot {
    public AccountSummarySlot() {
        super(SlotType.ACCOUNT_SUMMARY);
    }
    private StyledText availableCredit;
    private StyledText availableCreditValue;
    private StyledText totalCredit;
    private StyledText totalCreditValue;
    private String currencySymbol;
    private String backgroundColor;
    private Image backgroundImage;
    private String color;
    private List<AccountSummaryValue> accountSummaryValueList;

    public StyledText getAvailableCredit() {
        return availableCredit;
    }

    public void setAvailableCredit(StyledText availableCredit) {
        this.availableCredit = availableCredit;
    }

    public StyledText getAvailableCreditValue() {
        return availableCreditValue;
    }

    public void setAvailableCreditValue(StyledText availableCreditValue) {
        this.availableCreditValue = availableCreditValue;
    }

    public StyledText getTotalCreditValue() {
        return totalCreditValue;
    }

    public void setTotalCreditValue(StyledText totalCreditValue) {
        this.totalCreditValue = totalCreditValue;
    }

    public String getCurrencySymbol() {
        return currencySymbol;
    }

    public void setCurrencySymbol(String currencySymbol) {
        this.currencySymbol = currencySymbol;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public List<AccountSummaryValue> getAccountSummaryValueList() {
        return accountSummaryValueList;
    }

    public void setAccountSummaryValueList(List<AccountSummaryValue> accountSummaryValueList) {
        this.accountSummaryValueList = accountSummaryValueList;
    }

    public StyledText getTotalCredit() {
        return totalCredit;
    }

    public void setTotalCredit(StyledText totalCredit) {
        this.totalCredit = totalCredit;
    }

    public Image getBackgroundImage() {
        return backgroundImage;
    }

    public void setBackgroundImage(Image backgroundImage) {
        this.backgroundImage = backgroundImage;
    }
}
