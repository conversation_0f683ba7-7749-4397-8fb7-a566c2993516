package com.flipkart.fintech.pinaka.api.response.dataprovider;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pinaka.api.enums.FormFieldType;
import com.flipkart.fintech.pinaka.api.response.TrackableComponent;

/**
 * Created by rajat.mathur on 07/01/19.
 */
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "fieldType"
)
@JsonSubTypes({@JsonSubTypes.Type(
        name = "DROPDOWN",
        value = DropDownFormField.class
), @JsonSubTypes.Type(
        name = "ADDRESS",
        value = AddressSelectionFormField.class
), @JsonSubTypes.Type(
        name = "PAN_NUMBER",
        value = PanNumberFormField.class
), @JsonSubTypes.Type(
        name = "DATE",
        value = DateFormField.class
), @JsonSubTypes.Type(
        name = "TEXT",
        value = TextBoxFormField.class
), @JsonSubTypes.Type(
        name = "OTP",
        value = OtpFormField.class
), @JsonSubTypes.Type(
        name = "CREDIT_CARD",
        value = CreditCardFormField.class
), @JsonSubTypes.Type(
        name = "SAVED_CARD",
        value = SavedCardFormField.class
), @JsonSubTypes.Type(
        name = "ADDRESS_SELECTION",
        value = AddressFormField.class
), @JsonSubTypes.Type(
        name = "CHECKBOX",
        value = CheckboxFormField.class
), @JsonSubTypes.Type(
        name = "SLOT_PICKER",
        value = SlotPickerFormField.class
),@JsonSubTypes.Type(
        name = "CONTACT",
        value = ContactFormField.class
),@JsonSubTypes.Type(
        name = "ADDRESS_PICKER",
        value = AddressPickerFormField.class
),@JsonSubTypes.Type(
        name = "FULL_NAME_TEXT_BOX",
        value = FullNameTextBoxFormField.class
),@JsonSubTypes.Type(
        name = "PRICE_TEXT_BOX",
        value = PriceTextBoxFormField.class)
})
@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class FormField extends TrackableComponent {

    private final FormFieldType formFieldType;
    private String name;
    private String label;
    private boolean isDisabled;
    private boolean mandatory;

    //New field added as per new address UI contract
    private Integer grids;

    FormField(FormFieldType formFieldType) {
        this.formFieldType = formFieldType;
    }

    public FormFieldType getFormFieldType() {
        return formFieldType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public boolean isMandatory() {
        return mandatory;
    }

    public void setMandatory(boolean mandatory) {
        this.mandatory = mandatory;
    }

    public boolean isDisabled() {
        return isDisabled;
    }

    public void setDisabled(boolean disabled) {
        isDisabled = disabled;
    }

    public Integer getGrids() {
        return grids;
    }

    public void setGrids(Integer grids) {
        this.grids = grids;
    }
}
