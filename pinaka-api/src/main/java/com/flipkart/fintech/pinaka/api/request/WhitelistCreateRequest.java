package com.flipkart.fintech.pinaka.api.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.UnderwritingRuleConfig;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 07/11/17.
 */

@Data
public class WhitelistCreateRequest {

    @NotNull
    @JsonProperty("whitelist_name")
    private String whitelistName;

    @NotNull
    @JsonProperty("whitelist_desc")
    private String whitelistDesc;

    @NotNull
    @JsonProperty("product_type")
    private ProductType productType;

    @NotNull
    private String lender;

    @JsonProperty("rule_config")
    private List<UnderwritingRuleConfig> ruleConfig;

    private boolean enabled = true;
}
