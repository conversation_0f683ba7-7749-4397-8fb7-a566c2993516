package com.flipkart.fintech.pinaka.api.response;

import com.flipkart.fintech.pinaka.api.enums.KycServicibilityType;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PincodeFetchResponse {

    private ProductType productType;
    private Lender lender;
    private String pincode;
    private String metaData;
    private boolean blacklisted;
    private boolean presentInCache;
    private KycServicibilityType kycServicibilityType;
}
