package com.flipkart.fintech.pinaka.api.request.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.PostProcessingTaskType;

/**
 * <AUTHOR>
 * @since 03/07/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PostProcessingTaskRequest {

    private String trackingId;

    private PostProcessingTaskType taskType;

    public String getTrackingId() {
        return trackingId;
    }

    public void setTrackingId(String trackingId) {
        this.trackingId = trackingId;
    }

    public PostProcessingTaskType getTaskType() {
        return taskType;
    }

    public void setTaskType(PostProcessingTaskType taskType) {
        this.taskType = taskType;
    }

    @Override
    public String toString() {
        return "PostProcessingTaskRequest{" +
                "trackingId='" + trackingId + '\'' +
                ", taskType=" + taskType +
                '}';
    }
}
