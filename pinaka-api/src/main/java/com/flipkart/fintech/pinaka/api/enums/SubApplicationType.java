package com.flipkart.fintech.pinaka.api.enums;

import com.fasterxml.jackson.annotation.JsonC<PERSON>;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 07/09/17.
 */
public enum SubApplicationType {

    ELIGIBILITY_CHECK,
    KYC,
    BUREAU,
    DEVICE_DATA,
    CHECK_ELIGIBILITY_WITH_LENDER,
    CHECK_ELIGIBILITY_WITH_UNDERWRITING,
    SEND_OTP,
    VERIFY_OTP,
    PAN_VALIDATION,
    KYC_CONFIRMATION,
    USER_CONFIRMATION,
    SETUP_AUTOPAY,
    USER_PERMISSIONS,
    SMS_PERMISSIONS,
    INSIGHTS_GENERATION,
    SMS_INSIGHTS_GENERATION,
    BUREAU_INSIGHTS_GENERATION,
    ELIGIBILITY_CHECK_WITH_ALT_DATA,
    ACCOUNT_ACCESS_CODE_GENERATION,
    USER_CONSENT,
    LENDER_APPROVED,
    VAL<PERSON>ATE_COHORT,
    FETCH_DETAILS_CONSENT,
    APPLICATION_DATA,
    FETCH_DEMOGRAPHIC_DATA,
    APPLY_CONSENT,
    APPLICATION_SUBMISSION,
    PAYMENT_INSTRUMENT_GENERATION,
    CREMO_DATA,
    KYC_SCHEDULER,
    VIDEO_KYC_ELIGIBILITY,
    TRUSTING_SOCIAL_INSIGHTS_GENERATION,
    PENNY_DROP,
    PRE_CHECK_ELIGIBILITY_VALIDATIONS,
    CREDIT_SETTINGS,
    EMI_CONVERSION,
    FRAUDULENT_DATA,
    LIMIT_CONSENT;

    @JsonCreator
    public static SubApplicationType fromString(String type) {
        return SubApplicationType.valueOf(type);
    }
}
