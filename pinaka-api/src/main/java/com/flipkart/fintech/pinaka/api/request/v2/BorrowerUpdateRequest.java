package com.flipkart.fintech.pinaka.api.request.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.common.enums.JourneyContext;
import com.flipkart.fintech.common.enums.ProductType;
import com.flipkart.fintech.common.model.change_entity.ChangeEntityType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BorrowerUpdateRequest {
    private Double eligibleAmount;
    private String cohort;
    private ChangeEntityType entityType;
    private String entityId;
    private String userId;
    private ProductType productType;
    private String lender;
    private String existingState;
    private String nextState;
    private String clientId;
    private JourneyContext journeyContext;
    private Long timestamp;
}
